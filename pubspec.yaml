name: flowkar
description: "A new Flutter project."
publish_to: 'none' 
version: 1.0.35+35

environment:
  sdk: ^3.5.4

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter 
    
  # State Management
  flutter_bloc: ^8.1.6
  bloc: ^8.1.4
  
  # API
  retrofit: ^4.4.1
  dio: ^5.7.0
  
  # Data Models
  freezed: ^2.5.7
  json_annotation: ^4.9.0
  
  # Database
  # flutter_secure_storage: ^9.2.2
  
  # Utils
  intl: ^0.19.0
  dartz: ^0.10.1
  equatable: ^2.0.5
  flutter_screenutil: ^5.9.3
  google_fonts: ^6.2.1
  shimmer: ^3.0.0
  cached_network_image: ^3.3.1
  flutter_svg: ^2.0.14
  flutter_cache_manager: ^3.4.1
  lottie: ^3.1.3
  pinput: ^5.0.0
  pretty_dio_logger: ^1.4.0
  toastification: ^2.3.0
  pincode_input_fields: ^1.0.3
  image_picker: ^1.1.2
  persistent_bottom_nav_bar: ^6.2.1
  animations: ^2.0.11
  hive: ^2.2.3
  overlay_support: ^2.1.0
  path_provider: ^2.1.5
  loading_indicator: ^3.1.1
  flutter_staggered_animations: ^1.1.1
  flick_video_player: ^0.9.0
  rect_getter: ^1.1.0
  video_player: ^2.10.0
  visibility_detector: ^0.4.0+2
  socket_io_client: ^3.0.2
  draggable_scrollbar: ^0.1.0
  liquid_pull_to_refresh: ^3.0.1
  cron: ^0.6.1
  flutter_staggered_grid_view: ^0.4.0
  ai_barcode_scanner: ^6.0.1
  share_plus: ^10.1.4
  http: ^1.3.0
  image: ^4.5.2
  video_editor: ^3.0.0
  smooth_page_indicator: ^1.2.0+3
  social_media_caption_formatter: ^0.0.1
  flutter_local_notifications: ^19.0.0
  typewritertext: ^3.0.9



  stream_transform: ^2.1.1
  # flowkar_video_thumbnail:
  #   path: package/flowkar_video_thumbnail/  
  asset_picker:
    path: package/asset_picker/
  story_editor:
    path: package/story_editor/ 
  uuid: ^4.4.2
  scrollable_positioned_list: ^0.3.8
  flutter_dotenv: ^5.1.0
  onesignal_flutter: ^5.2.9
  firebase_core: ^3.8.0
  firebase_crashlytics: ^4.3.1
  internet_connection_checker: ^1.0.0+1
  any_link_preview: ^3.0.3
  url_launcher: ^6.3.1

  detectable_text_field: ^3.0.2
  wechat_camera_picker: ^4.3.7
  pro_image_editor: ^8.1.6
  # ffmpeg_kit_flutter_min: ^6.0.3
  floating_action_bubble: ^1.1.4
  flutter_speed_dial: ^7.0.0

  just_audio: ^0.9.43
  flutter_inappwebview: ^6.1.5
  flutter_hooks: ^0.20.5
  # image_cropper: 
    # path: package/flowkar_image_cropper_master/image_cropper
  readmore: ^3.0.0
  flutter_rating_bar: ^4.0.1
  dotted_border: ^2.1.0
  custom_image_crop: ^0.1.1
  in_app_update: ^4.2.3
  package_info_plus: ^8.3.0
  scroll_app_bar_2_0_0_custom_fix: ^2.0.3
  fl_chart: ^0.70.2
  multi_progress_bar: ^0.1.9
  primer_progress_bar: ^0.5.0
  connectivity_plus: ^6.1.3
  carousel_slider: ^5.0.0
  tab_indicator_styler: ^2.0.0
  permission_asker: ^1.0.0+1
  vibration: ^3.1.3
  easy_video_editor: ^0.0.6
  flutter_sound: ^9.28.0
  permission_handler: ^6.1.3
  waveform_flutter: ^1.2.0
  audio_waveforms: ^1.3.0
  just_waveform: ^0.0.7
  rxdart: ^0.28.0
  app_links: ^6.4.0
  modal_bottom_sheet: ^3.0.0
  geolocator: ^13.0.4
  flutter_popup: ^3.3.4
  figma_squircle_updated: ^1.0.1
  get: ^4.7.2
  flutter_keyboard_visibility: ^6.0.0
  flutter_webrtc: ^0.12.12+hotfix.1
  flutter_test:
    sdk: flutter
  wakelock_plus: ^1.3.2
  syncfusion_flutter_calendar: ^29.1.38
  country_picker: ^2.0.27
  shadow_widget: ^0.1.1
  quick_actions: ^1.1.0
  pip: ^0.0.3
  flutter_linkify: ^6.0.0

  
 
  


 # Code Generation
flutter_gen:
  line_length: 160
  integrations:
    flutter_svg: true
    lottie: true
  assets:
    enabled: true
  output: lib/core/generated
  

dev_dependencies:
   # Code Generation
  build_runner: ^2.4.13
  flutter_lints: ^5.0.0
  retrofit_generator: ^9.1.5
  freezed_annotation: ^2.4.4
  json_serializable: ^6.8.0
  intl_utils: ^2.8.7
  flutter_gen_runner: ^5.8.0
  webview_flutter: ^4.8.0

flutter_intl:
  enabled: true
  class_name: Lang
  main_locale: en
  arb_dir: lib/core/l10n 
  output_dir: lib/core/generated 


flutter:
  uses-material-design: true
  assets: 
    - .env
    - assets/images/pngs/
    - assets/images/pngs/authentication/
    - assets/images/icons/
    - assets/images/icons/bottom_bar/
    - assets/images/svg/
    - assets/images/icons/social/
    - assets/images/pngs/social_connect/
    - assets/images/pngs/exception/
    - assets/images/svg/authentication/
    - assets/images/pngs/demo/
    - assets/images/svg/other/
    - assets/images/icons/other/
    - assets/images/icons/home_feed/
    - assets/images/pngs/other/
    - assets/images/svg/upload_post/
    - assets/images/svg/home_feed/
    - assets/images/svg/setting/
    - assets/images/pngs/onboarding/
    - assets/images/svg/onboarding/
    - assets/images/pngs/setting/
    - assets/images/svg/exception/
    - assets/images/svg/subscription/
    - assets/images/pngs/subscription/
    - assets/images/pngs/home_feed/
    - assets/images/pngs/beta_user_tester/
    - assets/images/svg/analytics/
    - assets/images/svg/analytics/social_platforms/
    - assets/images/svg/profile/
    - assets/images/pngs/wallet/
    - assets/images/svg/reward_leader/
    # - assets/images/svg/social_connect/ 
    # - assets/images/jpgs/