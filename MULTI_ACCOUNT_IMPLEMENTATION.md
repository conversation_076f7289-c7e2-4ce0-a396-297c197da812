# Multi-Account Support Implementation

## Overview
This document outlines the complete implementation of Instagram-like multi-account support for the Flowkar app. The implementation allows users to:

- Store multiple user accounts locally
- Switch between accounts seamlessly
- Add new accounts without affecting existing ones
- Display account information in an Instagram-like bottom sheet

## Key Components

### 1. Data Models

#### StoredUserAccount
```dart
class StoredUserAccount {
  final int brandId;
  final String name;
  final String profileImage;
  final String username;
  final int userId;
  final String token;
}
```

#### StoredAccountsList
```dart
class StoredAccountsList {
  final List<StoredUserAccount> accounts;
  final int currentAccountIndex;
}
```

### 2. Core Services

#### MultiAccountManager (`lib/core/services/multi_account_manager.dart`)
- **Purpose**: Manages all multi-account operations
- **Key Methods**:
  - `addAccount()`: Adds or updates an account
  - `switchToAccount()`: Switches to a specific account by index
  - `getStoredAccounts()`: Retrieves all stored accounts
  - `getCurrentAccount()`: Gets the currently active account
  - `removeAccount()`: Removes an account from storage

### 3. UI Components

#### MultiAccountBottomSheet (`lib/features/authentication/presentation/widgets/multi_account_bottom_sheet.dart`)
- **Purpose**: Instagram-like bottom sheet for account switching
- **Features**:
  - Displays all stored accounts with profile images, names, usernames, and brand IDs
  - Shows current account with a check mark indicator
  - "Add Other Account" button for adding new accounts
  - Modern, responsive design with proper spacing and styling

#### Enhanced SignInPage (`lib/features/authentication/presentation/pages/signin_page.dart`)
- **Purpose**: Handles both regular login and add account flows
- **Features**:
  - `isAddingAccount` parameter to differentiate between flows
  - Custom app bar for add account mode
  - Success callback for account addition completion

### 4. State Management

#### AuthBloc Events
- `LoadStoredAccountsEvent`: Loads stored accounts from storage
- `AddAccountLoginEvent`: Handles login for adding new accounts
- `SwitchToStoredAccountEvent`: Switches to a selected stored account
- `RemoveStoredAccountEvent`: Removes an account from storage

#### AuthBloc State
- `storedAccountsList`: Current list of stored accounts
- `isLoadingStoredAccounts`: Loading state for account operations
- `isAddingAccount`: State for add account flow
- `isSwitchUserAccountLoading`: State for account switching

## Implementation Flow

### 1. Account Storage (Login/Registration)
```dart
// During login, account is automatically stored
final storedAccount = MultiAccountManager.createFromLoginResponse(
  brandId: userStatusResult.brandId ?? 0,
  name: result.name,
  profileImage: result.profileImage,
  username: result.username,
  userId: result.userId,
  token: result.token,
);
await MultiAccountManager.addAccount(storedAccount);
```

### 2. Account Switching UI
```dart
// In UserProfileScreen, tap on username opens bottom sheet
showModalBottomSheet(
  context: context,
  isScrollControlled: true,
  backgroundColor: Colors.transparent,
  builder: (context) {
    return MultiAccountBottomSheet(
      onAccountSelected: (selectedAccount) {
        _switchToAccount(selectedAccount);
      },
      onAddAccountTapped: () {
        _navigateToAddAccount();
      }
    );
  }
);
```

### 3. Account Switching Logic
```dart
Future<void> _switchToAccount(StoredUserAccount selectedAccount) async {
  // Find account index
  final accountsList = await MultiAccountManager.getStoredAccounts();
  final accountIndex = accountsList.accounts.indexWhere(
    (account) => account.brandId == selectedAccount.brandId && 
                 account.userId == selectedAccount.userId,
  );

  // Trigger account switch
  context.read<AuthBloc>().add(SwitchToStoredAccountEvent(
    accountIndex: accountIndex,
    context: context,
  ));
}
```

### 4. Data Cleanup and Refresh
When switching accounts, the system:
1. Clears all user-specific data from BLoCs
2. Updates local preferences with new account data
3. Updates socket connections with new token
4. Refreshes all data for the new account
5. Updates UI with new account information

## Key Features

### ✅ Instagram-like UI
- Modern bottom sheet design with drag handle
- Profile images with check mark indicators for current account
- Clean typography and spacing
- Responsive design for different screen sizes

### ✅ Seamless Account Switching
- Instant switching between stored accounts
- Complete data cleanup and refresh
- Preserved account data integrity
- Smooth animations and transitions

### ✅ Add Account Flow
- Dedicated login screen for adding accounts
- Non-disruptive to existing accounts
- Success feedback and navigation
- Automatic account storage

### ✅ Data Management
- Secure local storage of account information
- Duplicate account handling (updates existing)
- Account removal functionality
- Current account tracking

## Usage Instructions

### For Users:
1. **Switch Accounts**: Tap on username in profile screen → Select account from list
2. **Add Account**: Tap on username → Tap "Add Other Account" → Login with new credentials
3. **Current Account**: Indicated with check mark in account list

### For Developers:
1. **Load Accounts**: `context.read<AuthBloc>().add(LoadStoredAccountsEvent())`
2. **Switch Account**: `context.read<AuthBloc>().add(SwitchToStoredAccountEvent(accountIndex: index))`
3. **Add Account**: Navigate to `SignInPage(isAddingAccount: true)`

## Testing

Run the integration tests to verify functionality:
```bash
flutter test test/multi_account_integration_test.dart
```

## Future Enhancements

- Account removal from UI (swipe to delete)
- Account reordering
- Account nicknames/labels
- Biometric authentication for account switching
- Account sync across devices
