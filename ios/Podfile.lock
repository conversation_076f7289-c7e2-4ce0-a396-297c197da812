PODS:
  - app_links (0.0.2):
    - Flutter
  - audio_session (0.0.1):
    - Flutter
  - audio_waveforms (0.0.1):
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - easy_video_editor (0.0.5):
    - Flutter
  - emoji_picker_flutter (0.0.1):
    - Flutter
  - Firebase/CoreOnly (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - Firebase/Crashlytics (11.10.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.10.0)
  - firebase_core (3.13.0):
    - Firebase/CoreOnly (= 11.10.0)
    - Flutter
  - firebase_crashlytics (4.3.5):
    - Firebase/Crashlytics (= 11.10.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.10.0):
    - FirebaseCoreInternal (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - FirebaseCoreInternal (11.10.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseRemoteConfigInterop (11.15.0)
  - FirebaseSessions (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseCoreExtension (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_sound (9.28.0):
    - Flutter
    - flutter_sound_core (= 9.28.0)
  - flutter_sound_core (9.28.0)
  - flutter_webrtc (0.12.6):
    - Flutter
    - WebRTC-SDK (= 125.6422.06)
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMLKit/BarcodeScanning (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 6.0.0)
  - GoogleMLKit/MLKitCore (7.0.0):
    - MLKitCommon (~> 12.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (3.5.0)
  - image_gallery_saver (2.0.2):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - insta_assets_crop (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - just_waveform (0.0.1):
    - Flutter
    - FlutterMacOS
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - MLImage (1.0.0-beta6)
  - MLKitBarcodeScanning (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitCommon (12.0.0):
    - GoogleDataTransport (~> 10.0)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/Logger (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitVision (8.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta6)
    - MLKitCommon (~> 12.0)
  - mobile_scanner (6.0.2):
    - Flutter
    - GoogleMLKit/BarcodeScanning (~> 7.0.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - onesignal_flutter (5.3.2):
    - Flutter
    - OneSignalXCFramework (= 5.2.10)
  - OneSignalXCFramework (5.2.10):
    - OneSignalXCFramework/OneSignalComplete (= 5.2.10)
  - OneSignalXCFramework/OneSignal (5.2.10):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalLiveActivities
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalComplete (5.2.10):
    - OneSignalXCFramework/OneSignal
    - OneSignalXCFramework/OneSignalInAppMessages
    - OneSignalXCFramework/OneSignalLocation
  - OneSignalXCFramework/OneSignalCore (5.2.10)
  - OneSignalXCFramework/OneSignalExtension (5.2.10):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalInAppMessages (5.2.10):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLiveActivities (5.2.10):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLocation (5.2.10):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalNotifications (5.2.10):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalOSCore (5.2.10):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalOutcomes (5.2.10):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalUser (5.2.10):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - "permission_handler (5.1.0+2)":
    - Flutter
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - quick_actions_ios (0.0.1):
    - Flutter
  - sensors_plus (0.0.1):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - vibration (3.0.0):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - video_thumbnail (0.0.1):
    - Flutter
    - libwebp
  - wakelock_plus (0.0.1):
    - Flutter
  - WebRTC-SDK (125.6422.06)
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - audio_waveforms (from `.symlinks/plugins/audio_waveforms/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - easy_video_editor (from `.symlinks/plugins/easy_video_editor/ios`)
  - emoji_picker_flutter (from `.symlinks/plugins/emoji_picker_flutter/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - Flutter (from `Flutter`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_sound (from `.symlinks/plugins/flutter_sound/ios`)
  - flutter_webrtc (from `.symlinks/plugins/flutter_webrtc/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - insta_assets_crop (from `.symlinks/plugins/insta_assets_crop/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - just_waveform (from `.symlinks/plugins/just_waveform/darwin`)
  - mobile_scanner (from `.symlinks/plugins/mobile_scanner/ios`)
  - onesignal_flutter (from `.symlinks/plugins/onesignal_flutter/ios`)
  - OneSignalXCFramework (< 6.0, >= 5.0.0)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler (from `.symlinks/plugins/permission_handler/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - quick_actions_ios (from `.symlinks/plugins/quick_actions_ios/ios`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - vibration (from `.symlinks/plugins/vibration/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - video_thumbnail (from `.symlinks/plugins/video_thumbnail/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - flutter_sound_core
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GTMSessionFetcher
    - libwebp
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - nanopb
    - OneSignalXCFramework
    - OrderedSet
    - PromisesObjC
    - PromisesSwift
    - WebRTC-SDK

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  audio_waveforms:
    :path: ".symlinks/plugins/audio_waveforms/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  easy_video_editor:
    :path: ".symlinks/plugins/easy_video_editor/ios"
  emoji_picker_flutter:
    :path: ".symlinks/plugins/emoji_picker_flutter/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  Flutter:
    :path: Flutter
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_sound:
    :path: ".symlinks/plugins/flutter_sound/ios"
  flutter_webrtc:
    :path: ".symlinks/plugins/flutter_webrtc/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  insta_assets_crop:
    :path: ".symlinks/plugins/insta_assets_crop/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  just_waveform:
    :path: ".symlinks/plugins/just_waveform/darwin"
  mobile_scanner:
    :path: ".symlinks/plugins/mobile_scanner/ios"
  onesignal_flutter:
    :path: ".symlinks/plugins/onesignal_flutter/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler:
    :path: ".symlinks/plugins/permission_handler/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  quick_actions_ios:
    :path: ".symlinks/plugins/quick_actions_ios/ios"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  vibration:
    :path: ".symlinks/plugins/vibration/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  video_thumbnail:
    :path: ".symlinks/plugins/video_thumbnail/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  app_links: 76b66b60cc809390ca1ad69bfd66b998d2387ac7
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  audio_waveforms: a6dde7fe7c0ea05f06ffbdb0f7c1b2b2ba6cedcf
  camera_avfoundation: be3be85408cd4126f250386828e9b1dfa40ab436
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  easy_video_editor: 3b4b38c40be7344935f79698115ee36077df8a57
  emoji_picker_flutter: ece213fc274bdddefb77d502d33080dc54e616cc
  Firebase: 1fe1c0a7d9aaea32efe01fbea5f0ebd8d70e53a2
  firebase_core: 2d4534e7b489907dcede540c835b48981d890943
  firebase_crashlytics: 961a0812ba79ed8f89a8d5d1e3763daa6267a87a
  FirebaseCore: 8344daef5e2661eb004b177488d6f9f0f24251b7
  FirebaseCoreExtension: 6f357679327f3614e995dc7cf3f2d600bdc774ac
  FirebaseCoreInternal: ef4505d2afb1d0ebbc33162cb3795382904b5679
  FirebaseCrashlytics: 84b073c997235740e6a951b7ee49608932877e5c
  FirebaseInstallations: 9980995bdd06ec8081dfb6ab364162bdd64245c3
  FirebaseRemoteConfigInterop: 1c6135e8a094cc6368949f5faeeca7ee8948b8aa
  FirebaseSessions: 9b3b30947b97a15370e0902ee7a90f50ef60ead6
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_keyboard_visibility: 4625131e43015dbbe759d9b20daaf77e0e3f6619
  flutter_local_notifications: a5a732f069baa862e728d839dd2ebb904737effb
  flutter_sound: b9236a5875299aaa4cef1690afd2f01d52a3f890
  flutter_sound_core: 427465f72d07ab8c3edbe8ffdde709ddacd3763c
  flutter_webrtc: 57f32415b8744e806f9c2a96ccdb60c6a627ba33
  geolocator_apple: ab36aa0e8b7d7a2d7639b3b4e48308394e8cef5e
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMLKit: eff9e23ec1d90ea4157a1ee2e32a4f610c5b3318
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_gallery_saver: 14711d79da40581063e8842a11acf1969d781ed7
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  insta_assets_crop: 960856014dc16c08c92b02caf56d197fdea56051
  just_audio: 4e391f57b79cad2b0674030a00453ca5ce817eed
  just_waveform: be2cb6dec86096cb972bf8b73c893fe16bf1cff4
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  MLImage: 0ad1c5f50edd027672d8b26b0fee78a8b4a0fc56
  MLKitBarcodeScanning: 0a3064da0a7f49ac24ceb3cb46a5bc67496facd2
  MLKitCommon: 07c2c33ae5640e5380beaaa6e4b9c249a205542d
  MLKitVision: 45e79d68845a2de77e2dd4d7f07947f0ed157b0e
  mobile_scanner: af8f71879eaba2bbcb4d86c6a462c3c0e7f23036
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  onesignal_flutter: 1e2a4aa3ae27fa887222232836560cf116a0a5e0
  OneSignalXCFramework: 1a3b28dfbff23aabce585796d23c1bef37772774
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler: a6eb0c0a10e733fc3e1dcea4e2d426beb7c91064
  photo_manager: d2fbcc0f2d82458700ee6256a15018210a81d413
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  quick_actions_ios: 4b07fb49d8d8f3518d7565fbb7a91014067a7d82
  sensors_plus: 6a11ed0c2e1d0bd0b20b4029d3bad27d96e0c65b
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  vibration: 69774ad57825b11c951ee4c46155f455d7a592ce
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  video_thumbnail: b637e0ad5f588ca9945f6e2c927f73a69a661140
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  WebRTC-SDK: 79942c006ea64f6fb48d7da8a4786dfc820bc1db
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: 6fcc80505d3e4e4f136a14735446c0ca8c86ba75

COCOAPODS: 1.16.2
