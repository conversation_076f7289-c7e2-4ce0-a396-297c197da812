import UIKit
import Flutter
import app_links

@main
@objc class AppDelegate: FlutterAppDelegate {

    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        GeneratedPluginRegistrant.register(with: self)

        // Handle universal link (cold start)
        if let url = AppLinks.shared.getLink(launchOptions: launchOptions) {
            print("[Deep Links] App launched with universal link (cold start): \(url)")
            AppLinks.shared.handleLink(url: url)
        }

        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }

    // Handle deep links using custom URL scheme (e.g., flowkar://)
    override func application(
        _ application: UIApplication,
        open url: URL,
        options: [UIApplication.OpenURLOptionsKey: Any] = [:]
    ) -> Bool {
        print("[Deep Links] Opened via custom scheme: \(url)")
        AppLinks.shared.handleLink(url: url)
        return true // return true to indicate the link was handled
    }

    // Handle universal links (e.g., https://api.flowkar.com/...) when app is in background or foreground
    override func application(
        _ application: UIApplication,
        continue userActivity: NSUserActivity,
        restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void
    ) -> Bool {
        print("[Deep Links] Received NSUserActivity: \(userActivity)")

        if userActivity.activityType == NSUserActivityTypeBrowsingWeb,
           let url = userActivity.webpageURL {
            print("[Deep Links] Continued via universal link: \(url)")
            AppLinks.shared.handleLink(url: url)
            return true
        }

        return super.application(application, continue: userActivity, restorationHandler: restorationHandler)
    }
}
