import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:path_provider/path_provider.dart';

Future<String?> takePicture({
  required contentKey,
  required BuildContext context,
  required bool saveToGallery,
  required String fileName,
}) async {
  try {
    /// Widget को image में convert करें
    RenderRepaintBoundary boundary =
        contentKey.currentContext.findRenderObject() as RenderRepaintBoundary;

    ui.Image image = await boundary.toImage(pixelRatio: 3);

    ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    Uint8List pngBytes = byteData!.buffer.asUint8List();

    /// File बनाएं
    final String dir = (await getApplicationDocumentsDirectory()).path;
    String imagePath = '$dir/${fileName}_${DateTime.now()}.png';
    File capturedFile = File(imagePath);
    await capturedFile.writeAsBytes(pngBytes);

    if (saveToGallery) {
      final result = await ImageGallerySaver.saveImage(pngBytes,
          quality: 100, name: "${fileName}_${DateTime.now()}.png");
      if (result != null) {
        return imagePath; // Gallery में save करने पर भी image का path return होगा
      } else {
        return null; // अगर save नहीं हुआ तो null return करें
      }
    } else {
      return imagePath; // Directly image का path return करें
    }
  } catch (e) {
    debugPrint('exception => $e');
    return null; // Error होने पर null return करें
  }
}

Future takeVideo({
  required videoFile,
  required String fileName,
  required bool saveToGallery,
}) async {
  try {
    // Get the directory path
    final String dir = (await getApplicationDocumentsDirectory()).path;
    String videoPath = '$dir/${DateTime.now()}';

    // Copy the video file to the application directory
    File savedVideo = await videoFile.copy(videoPath);

    if (saveToGallery) {
      // Save the video to the gallery
      final result = await ImageGallerySaver.saveFile(savedVideo.path);
      if (result != null && result['isSuccess']) {
        return true;
      } else {
        return false;
      }
    } else {
      return videoPath;
    }
  } catch (e) {
    debugPrint('Exception => $e');
    return false;
  }
}

Future takeVideoSave(
    {required contentKey, required saveToGallery, required fileName}) async {
  try {
    /// converter widget to image
    RenderRepaintBoundary boundary =
        contentKey.currentContext.findRenderObject();

    ui.Image image = await boundary.toImage(pixelRatio: 3);

    /// create file
    final String dir = (await getApplicationDocumentsDirectory()).path;
    String videoPath = '$dir/${fileName}_${DateTime.now()}.mp4';
    File capturedFile = File(videoPath);

    if (saveToGallery) {
      final result = await ImageGallerySaver.saveFile(
        "$capturedFile",
        name: "${fileName}_${DateTime.now()}.mp4",
      );
      if (result != null) {
        return true;
      } else {
        return false;
      }
    } else {
      return videoPath;
    }
  } catch (e) {
    debugPrint('exception => $e');
    return false;
  }
}
