import 'package:flutter/material.dart';
import 'package:story_editor/src/presentation/widgets/animated_onTap_button.dart';

class ToolButton extends StatelessWidget {
  final Function() onTap;
  final Widget child;
  final Color? backGroundColor;
  final EdgeInsets? padding;
  final Function()? onLongPress;
  final Color colorBorder;
  final bool? borderHide;
  final double? height;
  final double? width;
  final double? topPadding;
  final double? horizontalPadding;
  final bool storyButton;

  const ToolButton(
      {super.key,
      required this.onTap,
      required this.child,
      this.backGroundColor,
      this.padding,
      this.onLongPress,
      this.colorBorder = Colors.white,
      this.borderHide = false,
      this.height,
      this.width,
      this.topPadding,
      this.horizontalPadding,
      this.storyButton = false});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: topPadding ?? 8),
      child: AnimatedOnTapButton(
        onTap: onTap,
        onLongPress: onLongPress,
        child: Padding(
          padding: padding ?? EdgeInsets.symmetric(horizontal: horizontalPadding ?? 10),
          child: Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(90),
            // elevation: 1,
            shadowColor: Colors.black.withOpacity(0.5),
            child: Container(
              height: height ?? 33,
              width: storyButton ? width : width ?? 33,
              decoration: borderHide!
                  ? null
                  : BoxDecoration(
                      color: backGroundColor ?? Colors.transparent,
                      // shape: BoxShape.circle,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Colors.white, width: 1.5)),
              child: Transform.scale(
                scale: 0.8,
                child: child,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
