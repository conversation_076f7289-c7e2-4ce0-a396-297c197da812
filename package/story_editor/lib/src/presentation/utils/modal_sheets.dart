// ignore_for_file: no_leading_underscores_for_local_identifiers, unused_element

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:story_editor/src/domain/providers/notifiers/control_provider.dart';
import 'package:story_editor/src/domain/providers/notifiers/draggable_widget_notifier.dart';
import 'package:story_editor/src/domain/providers/notifiers/painting_notifier.dart';
import 'package:story_editor/src/domain/providers/notifiers/text_editing_notifier.dart';
import 'package:story_editor/src/presentation/widgets/animated_onTap_button.dart';
import 'package:story_editor/story_editor.dart';

/// custom exit dialog
exitDialog({required context, required ThemeType themeType, bool? isTextEditing}) async {
  return (await showDialog(
        context: context,
        builder: (BuildContext ctx) {
          var width = MediaQuery.of(context).size.width;
          var height = MediaQuery.of(context).size.height;

          return AlertDialog(
            contentPadding: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16.0),
            ),
            content: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 0),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 0),
                alignment: Alignment.center,
                height: height * 0.25,
                width: 300.h,
                decoration: BoxDecoration(
                    shape: BoxShape.rectangle,
                    color: themeType == ThemeType.light ? Colors.white : Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                          color: themeType == ThemeType.light ? Colors.black : Colors.black,
                          offset: const Offset(0, 1),
                          blurRadius: 4),
                    ]),
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: height * 0.02),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Text(
                        isTextEditing == true ? 'Discard Edits?' : 'Discard Story?',
                        style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.w600,
                            color: themeType == ThemeType.light ? Colors.black : Colors.black,
                            letterSpacing: 0.5),
                      ),
                      const SizedBox(height: 20),
                      Text(
                        isTextEditing == true
                            ? "If you go back now, you'll lose all the edits you've made."
                            : "Do you want to Discard the story?",
                        style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w400,
                            color: themeType == ThemeType.light ? Colors.black : Colors.black,
                            letterSpacing: 0.1),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Cancel Button
                          AnimatedOnTapButton(
                            onTap: () {
                              Navigator.pop(ctx, false);
                            },
                            child: Container(
                              height: 50,
                              width: MediaQuery.of(context).size.width * 0.3,
                              decoration: BoxDecoration(
                                  border: Border.all(color: Theme.of(context).primaryColor),
                                  borderRadius: BorderRadius.circular(15)),
                              child: Center(
                                child: Text(
                                  'Cancel',
                                  style: TextStyle(
                                      fontSize: 16,
                                      color:
                                          themeType == ThemeType.light ? Colors.black : Theme.of(context).primaryColor,
                                      fontWeight: FontWeight.bold,
                                      letterSpacing: 0.5),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: width * 0.01),
                          // Discard Button
                          AnimatedOnTapButton(
                            onTap: isTextEditing == true
                                ? () async {
                                    _resetDefaults(context: context);
                                    Navigator.of(ctx).pop(true);
                                  }
                                : () {
                                    Navigator.of(ctx).pop(true);
                                    Navigator.of(ctx).pop(true);
                                    _resetDefaults(context: context);
                                    // Navigator.pop(context);
                                    // Navigator.pop(context);
                                  },
                            child: Container(
                              height: 50,
                              width: MediaQuery.of(context).size.width * 0.3,
                              decoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(15)),
                              child: const Center(
                                child: Text(
                                  'Discard',
                                  style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      letterSpacing: 0.1),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      )) ??
      false;
}

_resetDefaults({required BuildContext context}) {
  final _paintingProvider = Provider.of<PaintingNotifier>(context, listen: false);
  final _widgetProvider = Provider.of<DraggableWidgetNotifier>(context, listen: false);
  final _controlProvider = Provider.of<ControlNotifier>(context, listen: false);
  final _editingProvider = Provider.of<TextEditingNotifier>(context, listen: false);
  _paintingProvider.lines.clear();
  _widgetProvider.draggableWidget.clear();
  _widgetProvider.setDefaults();
  _paintingProvider.resetDefaults();
  _editingProvider.setDefaults();
  _controlProvider.mediaPath = '';
  // Navigator.pop(context);
}

_dispose({required context, required message}) {
  _resetDefaults(context: context);
  // Navigator.of(context).pop(true);
}
