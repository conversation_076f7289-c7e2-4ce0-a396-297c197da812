// ignore_for_file: library_private_types_in_public_api, no_leading_underscores_for_local_identifiers

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:oktoast/oktoast.dart';
import 'package:provider/provider.dart';
import 'package:story_editor/src/domain/providers/notifiers/control_provider.dart';
import 'package:story_editor/src/domain/providers/notifiers/draggable_widget_notifier.dart';
import 'package:story_editor/src/domain/providers/notifiers/painting_notifier.dart';
import 'package:story_editor/src/domain/providers/notifiers/text_editing_notifier.dart';
import 'package:story_editor/src/domain/sevices/save_as_image.dart';
import 'package:story_editor/src/presentation/utils/constants/item_type.dart';
import 'package:story_editor/src/presentation/utils/constants/text_animation_type.dart';
import 'package:story_editor/src/presentation/utils/modal_sheets.dart';
import 'package:story_editor/src/presentation/widgets/animated_onTap_button.dart';
import 'package:story_editor/src/presentation/widgets/tool_button.dart';

class TopTools extends StatefulWidget {
  final GlobalKey contentKey;
  final BuildContext context;
  final Function? renderWidget;
  const TopTools({super.key, required this.contentKey, required this.context, this.renderWidget});

  @override
  _TopToolsState createState() => _TopToolsState();
}

class _TopToolsState extends State<TopTools> {
  bool _createVideo = true;
  bool _showLoader = false;
  @override
  Widget build(BuildContext context) {
    return Consumer3<ControlNotifier, PaintingNotifier, DraggableWidgetNotifier>(
      builder: (_, controlNotifier, paintingNotifier, itemNotifier, __) {
        bool isContentAdded = controlNotifier.isTextEditing || itemNotifier.draggableWidget.isNotEmpty || paintingNotifier.lines.isNotEmpty;
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 40),
          decoration: const BoxDecoration(color: Colors.transparent),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              /// Setting Or Close Button
              if (controlNotifier.mediaPath.isEmpty)
                // Settings Button
                ToolButton(
                    borderHide: true,
                    onTap: () async {
                      // exitDialog(
                      //         context: widget.context,
                      //         contentKey: widget.contentKey,
                      //         themeType: controlNotifier.themeType)
                      //     .then((res) {
                      //   if (res) Navigator.pop(context);
                      // });
                    },
                    child:
                        //  const Icon(
                        //   Icons.settings_outlined,
                        //   color: Colors.white,
                        //   size: 30,
                        // )
                        //     const ImageIcon(
                        //   AssetImage('assets/icons/setting.png',
                        //       package: 'story_editor'),
                        //   color: Colors.white,
                        //   size: 50,
                        // ),
                        const SizedBox())
              else
                ToolButton(
                    borderHide: true,
                    backGroundColor: Colors.black12,
                    onTap: () async {
                      controlNotifier.mediaPath = '';
                      itemNotifier.draggableWidget.removeAt(0);
                      controlNotifier.giphyKey = '';
                      _resetDefaults(context: context);
                    },
                    child: const Icon(
                      Icons.arrow_back_ios_new_rounded,
                      color: Colors.white,
                      size: 26,
                    )),
              const Spacer(),

              Row(
                children: [
                  if (controlNotifier.mediaPath.isEmpty)
                    Padding(
                      padding: const EdgeInsets.only(left: 200),
                      child: _selectColor(
                          controlProvider: controlNotifier,
                          onTap: () {
                            if (controlNotifier.gradientIndex >= controlNotifier.gradientColors!.length - 1) {
                              setState(() {
                                controlNotifier.gradientIndex = 0;
                              });
                            } else {
                              setState(() {
                                controlNotifier.gradientIndex += 1;
                              });
                            }
                          }),
                    ),

                  if (controlNotifier.mediaPath.isEmpty)
                    const SizedBox.shrink()
                  else
                    ToolButton(
                      borderHide: false,
                      backGroundColor: Colors.black12,
                      onTap: () => controlNotifier.isTextEditing = !controlNotifier.isTextEditing,
                      child: Center(
                        child: Text(
                          "Aa",
                          style: GoogleFonts.montserrat(fontSize: 20, color: Colors.white, fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                  // if (controlNotifier.mediaPath.isNotEmpty)
                  //   Consumer4<ControlNotifier, ScrollNotifier,
                  //           DraggableWidgetNotifier, PaintingNotifier>(
                  //       builder: (_, controlNotifier, scrollNotifier,
                  //           itemNotifier, paintingNotifier, __) {
                  //     return Padding(
                  //       padding: EdgeInsets.only(top: _size.height * 0.01),
                  //       child: Container(
                  //         height: 35,
                  //         width: 35,
                  //         decoration: BoxDecoration(
                  //             borderRadius: BorderRadius.circular(10),
                  //             border:
                  //                 Border.all(width: 1.4, color: Colors.white)),
                  //         child: ClipRRect(
                  //             borderRadius: BorderRadius.circular(8),
                  //             child: GestureDetector(
                  //               onTap: () {
                  //                 /// scroll to gridView page

                  //                 scrollNotifier.pageController.animateToPage(1,
                  //                     duration:
                  //                         const Duration(milliseconds: 300),
                  //                     curve: Curves.ease);
                  //               },
                  //               child: const CoverThumbnail(
                  //                 thumbnailQuality: 1080,
                  //               ),
                  //             )),
                  //       ),
                  //     );
                  //   }),

                  // text Tt
                  // if (controlNotifier.mediaPath.isEmpty)
                  //   const SizedBox.shrink()
                  // else
                  //   ToolButton(
                  //       borderHide: false,
                  //       backGroundColor: Colors.black12,
                  //       // backGroundColor: controlNotifier.enableTextShadow
                  //       //     ? Colors.white
                  //       //     : Colors.black12,
                  //       onTap: () {
                  //         controlNotifier.enableTextShadow =
                  //             !controlNotifier.enableTextShadow;
                  //       },
                  //       child: const Icon(Icons.text_fields_sharp,
                  //           color:
                  //               //  controlNotifier.enableTextShadow
                  //               //     ? Colors.black
                  //               //     :
                  //               Colors.white,
                  //           size: 30)),

                  //***********************************************
                  // ToolButton(
                  //     child: const ImageIcon(
                  //       AssetImage('assets/icons/stickers.png',
                  //           package: 'vs_story_designer'),
                  //       color: Colors.white,
                  //       size: 20,
                  //     ),
                  //     backGroundColor: Colors.black12,
                  //     onTap: () => createGiphyItem(
                  //         context: context,
                  //         giphyKey: controlNotifier.giphyKey)),

                  if (controlNotifier.mediaPath.isEmpty)
                    const SizedBox.shrink()
                  else
                    ToolButton(
                        borderHide: false,
                        backGroundColor: Colors.black12,
                        onTap: () {
                          controlNotifier.isPainting = true;
                          //createLinePainting(context: context);
                        },
                        child: const Padding(
                          padding: EdgeInsets.all(5.0),
                          child: ImageIcon(
                            AssetImage('assets/icons/draw.png', package: 'story_editor'),
                            color: Colors.white,
                            size: 10,
                          ),
                        )),
                ],
              ),
              // ToolButton(
              //   child: ImageIcon(
              //     const AssetImage('assets/icons/photo_filter.png',
              //         package: 'vs_story_designer'),
              //     color: controlNotifier.isPhotoFilter ? Colors.black : Colors.white,
              //     size: 20,
              //   ),
              //   backGroundColor:  controlNotifier.isPhotoFilter ? Colors.white70 : Colors.black12,
              //   onTap: () => controlNotifier.isPhotoFilter =
              //   !controlNotifier.isPhotoFilter,
              // ),
              if (controlNotifier.mediaPath.isEmpty)
                ToolButton(
                    borderHide: true,
                    backGroundColor: Colors.black12,
                    onTap: () async {
                      exitDialog(isTextEditing: isContentAdded, context: widget.context, themeType: controlNotifier.themeType);
                    },
                    child: Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 28.sp,
                    ))
              else
                ToolButton(
                  borderHide: false,
                  backGroundColor: Colors.black12,
                  onTap: () async {
                    setState(() {
                      _showLoader = true;
                      Future.delayed(
                        const Duration(seconds: 2),
                        () {
                          setState(() {
                            _showLoader = false;
                          });
                        },
                      );
                    });
                    if (paintingNotifier.lines.isNotEmpty || itemNotifier.draggableWidget.isNotEmpty) {
                      // Determine if a video should be created
                      for (var element in itemNotifier.draggableWidget) {
                        if (element.type == ItemType.gif || element.animationType != TextAnimationType.none) {
                          _createVideo = true;
                        }
                      }

                      if (_createVideo == false) {
                        // If video needs to be created, call the renderWidget function
                        debugPrint('Creating video');
                        takeVideo(videoFile: controlNotifier.mediaPath, saveToGallery: true, fileName: controlNotifier.folderName);
                        // await widget.renderWidget!();
                        showToast('Video successfully saved');
                      } else {
                        // If only an image is needed, take a picture
                        debugPrint('Creating image');
                        var response = await takePicture(
                          contentKey: widget.contentKey,
                          context: context,
                          saveToGallery: true,
                          fileName: controlNotifier.folderName,
                        );
                        if (response != null) {
                          showToast('Image successfully saved');
                        } else {
                          showToast('Failed to save image');
                        }
                      }

                      // Dismiss the loading dialog
                      // ignore: use_build_context_synchronously
                      Navigator.of(context, rootNavigator: true).pop();
                    } else {
                      // If nothing is designed, show a message
                      showToast('Design something to save image or video');
                    }
                  },
                  child: _showLoader
                      ? const Padding(
                          padding: EdgeInsets.all(8),
                          child: CircularProgressIndicator(),
                        )
                      : const Padding(
                          padding: EdgeInsets.all(3.5),
                          child: ImageIcon(
                            AssetImage('assets/icons/download.png', package: 'story_editor'),
                            color: Colors.white,
                          ),
                        ),
                )
            ],
          ),
        );
      },
    );
  }

  _resetDefaults({required BuildContext context}) {
    final _paintingProvider = Provider.of<PaintingNotifier>(context, listen: false);
    final _widgetProvider = Provider.of<DraggableWidgetNotifier>(context, listen: false);
    final _controlProvider = Provider.of<ControlNotifier>(context, listen: false);
    final _editingProvider = Provider.of<TextEditingNotifier>(context, listen: false);
    _paintingProvider.lines.clear();
    _widgetProvider.draggableWidget.clear();
    _widgetProvider.setDefaults();
    _paintingProvider.resetDefaults();
    _editingProvider.setDefaults();
    _controlProvider.mediaPath = '';
  }

  /// gradient color selector
  Widget _selectColor({onTap, controlProvider}) {
    return Padding(
      padding: const EdgeInsets.only(left: 5, right: 5, top: 8),
      child: AnimatedOnTapButton(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(1),
          decoration: const BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
          ),
          child: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              gradient: LinearGradient(begin: Alignment.topLeft, end: Alignment.bottomRight, colors: controlProvider.gradientColors![controlProvider.gradientIndex]),
              shape: BoxShape.circle,
            ),
          ),
        ),
      ),
    );
  }
}
