// ignore_for_file: no_leading_underscores_for_local_identifiers

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:provider/provider.dart';
import 'package:story_editor/src/presentation/text_editor_view/flowkar_bottom_sheet.dart';
import 'package:vs_media_picker/vs_media_picker.dart';
import 'package:story_editor/src/domain/providers/notifiers/control_provider.dart';
import 'package:story_editor/src/domain/providers/notifiers/draggable_widget_notifier.dart';
import 'package:story_editor/src/domain/providers/notifiers/painting_notifier.dart';
import 'package:story_editor/src/domain/providers/notifiers/scroll_notifier.dart';
import 'package:story_editor/src/domain/sevices/save_as_image.dart';
import 'package:story_editor/src/presentation/text_editor_view/text_form_field.dart';
import 'package:story_editor/src/presentation/widgets/tool_button.dart';

class BottomTools extends StatefulWidget {
  final GlobalKey contentKey;
  final Function(String imageUri, bool insta, bool facebook) onDone;
  final Function(String imageUri, String title) onDoneHighlight;

  final Widget? onDoneButtonStyle;
  final Function? renderWidget;
  final bool isfacebook;
  final bool isInstagram;

  /// editor background color
  final Color? editorBackgroundColor;
  const BottomTools(
      {super.key,
      required this.contentKey,
      required this.onDone,
      required this.onDoneHighlight,
      this.renderWidget,
      this.onDoneButtonStyle,
      this.editorBackgroundColor,
      required this.isfacebook,
      required this.isInstagram});

  @override
  _BottomToolsState createState() => _BottomToolsState();
}

class _BottomToolsState extends State<BottomTools> {
  bool _isLoading = false;
  TextEditingController highlightController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    var _size = MediaQuery.of(context).size;
    return Consumer4<ControlNotifier, ScrollNotifier, DraggableWidgetNotifier, PaintingNotifier>(
      builder: (_, controlNotifier, scrollNotifier, itemNotifier, paintingNotifier, __) {
        bool isContentAdded = controlNotifier.isTextEditing ||
            itemNotifier.draggableWidget.isNotEmpty ||
            paintingNotifier.lines.isNotEmpty;
        return Container(
          height: 95,
          // width: double.infinity,
          decoration: const BoxDecoration(color: Color.fromARGB(0, 184, 11, 11)),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              /// preview gallery
              Flexible(
                child: Container(
                  // width: _size.width / 3,
                  height: _size.height / 3,
                  padding: EdgeInsets.only(left: _size.width * 0.01),
                  alignment: Alignment.centerLeft,
                  child: SizedBox(
                    // child: _preViewContainer(
                    /// if [model.imagePath] is null/empty return preview image
                    child: !isContentAdded || controlNotifier.isTextEditing
                        ? controlNotifier.isTextEditing
                            ? const SizedBox.shrink()
                            : Padding(
                                padding: EdgeInsets.only(left: _size.width * 0.05),
                                child: Container(
                                  height: 45,
                                  width: 45,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      border: Border.all(width: 1.4, color: Colors.white)),
                                  child: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: GestureDetector(
                                        onTap: () {
                                          /// scroll to gridView page
                                          if (controlNotifier.mediaPath.isEmpty) {
                                            scrollNotifier.pageController.animateToPage(1,
                                                duration: const Duration(milliseconds: 300), curve: Curves.ease);
                                          }
                                        },
                                        child: const CoverThumbnail(
                                          thumbnailQuality: 150,
                                        ),
                                      )),
                                ),
                              )

                        /// return clear [imagePath] provider
                        // Upload Story
                        : Padding(
                            padding: EdgeInsets.symmetric(horizontal: _size.width * 0.02),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Flexible(
                                  child: ToolButton(
                                      height: 50,
                                      storyButton: true,
                                      backGroundColor: Theme.of(context).primaryColor,
                                      onTap: () async {
                                        // if (!_isLoading) {
                                        //   setState(() {
                                        //     _isLoading = true;
                                        //   });
                                        //   var media = await takePicture(
                                        //       contentKey: widget.contentKey,
                                        //       context: context,
                                        //       saveToGallery: false,
                                        //       fileName: controlNotifier.folderName);
                                        //   widget.onDone(media!);
                                        //   setState(() {
                                        //     _isLoading = false;
                                        //   });
                                        // }
                                        // showModalBottomSheet(
                                        //   useRootNavigator: true,
                                        //   context: context,
                                        //   isDismissible: true,
                                        //   isScrollControlled: true,
                                        //   builder: (BuildContext ctx) {
                                        //     return _buildAddStoryOrHighlight(_size, controlNotifier.folderName, ctx);
                                        //   },
                                        // );
                                        showModalBottomSheet(
                                          useRootNavigator: true,
                                          context: context,
                                          isDismissible: true,
                                          isScrollControlled: true,
                                          builder: (BuildContext ctx) {
                                            return StatefulBuilder(
                                              builder: (BuildContext context, StateSetter setModalState) {
                                                return _buildAddStoryOrHighlight(
                                                    _size, controlNotifier.folderName, ctx, setModalState);
                                              },
                                            );
                                          },
                                        );
                                      },
                                      child: Row(
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        // mainAxisSize: MainAxisSize.min,
                                        children: [
                                          _isLoading
                                              ? const LoadingIndicator(
                                                  indicatorType: Indicator.ballClipRotateMultiple,
                                                  colors: [Colors.white],
                                                  strokeWidth: 4,
                                                  pathBackgroundColor: Colors.black)
                                              : Text(
                                                  "Your Story",
                                                  style: GoogleFonts.ubuntu(
                                                    color: Colors.white,
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w700,
                                                  ),
                                                )
                                        ],
                                      )),
                                ),

                                // > Add highlight button
                                Flexible(
                                  child: ToolButton(
                                    height: 50,
                                    storyButton: true,
                                    borderHide: false,
                                    backGroundColor: Colors.black12,
                                    onTap: () async {
                                      showModalBottomSheet(
                                        useRootNavigator: true,
                                        context: context,
                                        isDismissible: true,
                                        isScrollControlled: true,
                                        builder: (BuildContext ctx) {
                                          return _buildAddHighlight(_size, controlNotifier.folderName, ctx);
                                        },
                                      );
                                    },
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                                      // mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text("Add As Highlight",
                                            style: GoogleFonts.ubuntu(
                                                color: Colors.white, fontSize: 16, fontWeight: FontWeight.w700)),
                                      ],
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ),
                  ),
                ),
              ),
              !isContentAdded
                  ? Flexible(
                      flex: 3,
                      child: controlNotifier.middleBottomWidget != null
                          ? Container(
                              width: _size.width / 3,
                              height: 80,
                              alignment: Alignment.centerLeft,
                              child: controlNotifier.middleBottomWidget)
                          : Text(
                              "Flowkar",
                              style: GoogleFonts.sacramento(
                                  fontSize: 45, fontWeight: FontWeight.w500, color: Colors.white),
                            ))
                  : const SizedBox.shrink(),

              !isContentAdded
                  ? Padding(
                      padding: EdgeInsets.only(right: _size.width * 0.05),
                      child: SizedBox(width: _size.width * 0.1),
                    )
                  : const SizedBox.shrink()
            ],
          ),
        );
      },
    );
  }

  Widget _buildAddHighlight(Size _size, String folderName, BuildContext ctx) {
    return Form(
      key: _formKey,
      child: FlowkarBottomSheet(
        child: Padding(
          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Center(
                    child: Text("Highlight", style: GoogleFonts.ubuntu(fontSize: 19.sp, fontWeight: FontWeight.w600))),
                const SizedBox(height: 16),
                FlowkarTextFormField(
                  controller: highlightController,
                  context: context,
                  borderDecoration: OutlineInputBorder(borderRadius: BorderRadius.circular(12.r)),
                  hintText: "Add Highlight Title",
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'This field is required';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () async {
                    if (_formKey.currentState!.validate()) {
                      if (!_isLoading) {
                        setState(() {
                          _isLoading = true;
                        });
                        var media = await takePicture(
                            contentKey: widget.contentKey,
                            context: context,
                            saveToGallery: false,
                            fileName: folderName);
                        // fileName:
                        //     controlNotifier.folderName);
                        widget.onDoneHighlight(media!, highlightController.text);
                        Navigator.pop(ctx);
                        setState(() {
                          _isLoading = false;
                        });
                      }
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                  ),
                  child: SizedBox(
                    width: _size.width * 1,
                    height: _size.height * 0.06,
                    child: Center(
                      child: _isLoading
                          ? const LoadingIndicator(
                              indicatorType: Indicator.ballClipRotateMultiple,
                              colors: [Colors.white],
                              strokeWidth: 4,
                              pathBackgroundColor: Colors.black)
                          : Text(
                              ' Share Story With Highlight ',
                              style:
                                  GoogleFonts.ubuntu(color: Colors.white, fontSize: 16.sp, fontWeight: FontWeight.w600),
                            ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Set<String> selectedPlatforms = {}; // Default selection

// Platform configuration based on widget properties
  List<Map<String, dynamic>> getPlatforms() {
    List<Map<String, dynamic>> allPlatforms = [];

    // Add Instagram if enabled
    // if (widget.isInstagram) {
    allPlatforms.add({
      'name': 'Instagram',
      'icon': "assets/icons/insta.png",
      'color': Color(0xffE4405F),
      'isAsset': true,
      'isEnabled': widget.isInstagram,
    });
    // }

    // Add Facebook if enabled
    // if (widget.isfacebook) {
    allPlatforms.add({
      'name': 'Facebook',
      'icon': "assets/icons/ic_facebook.png",
      'color': Color(0xff4267B2),
      'isAsset': true,
      'isEnabled': widget.isfacebook,
    });
    // }

    return allPlatforms;
  }

  Widget _buildAddStoryOrHighlight(Size _size, String folderName, BuildContext ctx, StateSetter setModalState) {
    final platforms = getPlatforms(); // Get enabled platforms

    return Form(
      key: _formKey,
      child: FlowkarBottomSheet(
        child: Padding(
          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Padding(
            padding: const EdgeInsets.all(0.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                    height: 4.h,
                    width: 40.w,
                    decoration: BoxDecoration(color: Colors.grey[300], borderRadius: BorderRadius.circular(5.r))),
                const SizedBox(height: 16),
                Center(
                    child: Text("Share With", style: GoogleFonts.ubuntu(fontSize: 19.sp, fontWeight: FontWeight.w600))),
                const SizedBox(height: 16),

                // Show message if no platforms are enabled
                if (platforms.isEmpty)
                  Container(
                    padding: EdgeInsets.all(20.w),
                    child: Column(
                      children: [
                        Icon(
                          Icons.block,
                          size: 48.sp,
                          color: Colors.grey[400],
                        ),
                        SizedBox(height: 16.h),
                        Text(
                          "No platforms available",
                          style: GoogleFonts.ubuntu(
                            fontSize: 16.sp,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(height: 8.h),
                        Text(
                          "Please enable at least one platform to share",
                          style: GoogleFonts.ubuntu(
                            fontSize: 14.sp,
                            color: Colors.grey[500],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),

                // Show platforms if available
                if (platforms.isNotEmpty)
                  Container(
                    constraints: BoxConstraints(maxHeight: 300.h),
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: platforms.length,
                      itemBuilder: (context, index) {
                        final platform = platforms[index];
                        final isSelected = selectedPlatforms.contains(platform['name']);
                        final isEnabled = platform['isEnabled'] ?? true;

                        return Opacity(
                          opacity: isEnabled ? 1.0 : 0.5,
                          child: Container(
                            height: 70.h,
                            clipBehavior: Clip.antiAlias,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                              color: Colors.white,
                              boxShadow: [
                                BoxShadow(
                                  color: Color(0xff063336).withOpacity(isEnabled ? 0.1 : 0.05),
                                  blurRadius: 16,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            margin: EdgeInsets.only(bottom: 12.h),
                            child: InkWell(
                              onTap: isEnabled
                                  ? () {
                                      setModalState(() {
                                        if (isSelected) {
                                          selectedPlatforms.remove(platform['name']);
                                        } else {
                                          selectedPlatforms.add(platform['name']);
                                        }
                                      });
                                    }
                                  : null, // Disable tap if not enabled
                              child: Row(
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Stack(
                                        children: [
                                          Container(
                                            width: 34.w,
                                            padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 5.h),
                                            decoration: BoxDecoration(
                                              color: platform['color'],
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Color(0xff000000).withOpacity(0.25),
                                                  blurRadius: 6,
                                                  spreadRadius: 0,
                                                  offset: const Offset(4, 0),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Align(
                                            alignment: Alignment.center,
                                            child: Container(
                                                margin: EdgeInsets.only(left: 10.w),
                                                width: 46.w,
                                                height: 46.h,
                                                padding: EdgeInsets.all(platform['name'] == 'Facebook' ? 7.w : 4.w),
                                                decoration: BoxDecoration(
                                                  borderRadius: BorderRadius.circular(100.r),
                                                  color: Colors.white,
                                                  boxShadow: [
                                                    BoxShadow(
                                                      color: Color(0xff000000).withOpacity(0.25),
                                                      blurRadius: 6,
                                                      spreadRadius: 0,
                                                      offset: const Offset(4, 0),
                                                    ),
                                                  ],
                                                ),
                                                child: platform['isAsset']
                                                    ?
                                                    // SvgPicture.asset(platform['icon'], package: 'story_editor')
                                                    Image.asset(platform['icon'], package: 'story_editor')
                                                    : Image.asset(platform['icon'], package: 'story_editor')
                                                //  Icon(
                                                //     platform['icon'],
                                                //     color: isEnabled ? platform['color'] : Colors.grey[400],
                                                //     size: 24.sp,
                                                //   ),
                                                ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  Flexible(
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Flexible(
                                          child: Padding(
                                            padding: EdgeInsets.only(left: 10.w),
                                            child: Text(
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 1,
                                              platform['name'],
                                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                                    fontWeight: FontWeight.w600,
                                                    fontSize: 16.sp,
                                                    color: isSelected ? Theme.of(context).primaryColor : null,
                                                  ),
                                            ),
                                          ),
                                        ),
                                        Padding(
                                          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 1.5.h),
                                          child: Icon(
                                            isSelected ? Icons.check_circle_rounded : Icons.circle_outlined,
                                            color: isSelected
                                                ? Theme.of(context).primaryColor
                                                : Theme.of(context).primaryColor.withOpacity(0.5),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                const SizedBox(height: 16),

                // Show selected platforms count if any platforms are available
                // if (platforms.isNotEmpty && selectedPlatforms.isNotEmpty)
                //   Padding(
                //     padding: EdgeInsets.only(bottom: 16.h),
                //     child: Text(
                //       "${selectedPlatforms.length} platform${selectedPlatforms.length > 1 ? 's' : ''} selected",
                //       style: GoogleFonts.ubuntu(
                //         fontSize: 14.sp,
                //         color: Theme.of(context).primaryColor,
                //         fontWeight: FontWeight.w500,
                //       ),
                //     ),
                //   ),

                Padding(
                  padding: EdgeInsets.only(bottom: Platform.isIOS ? 16.h : 8.h),
                  child: ElevatedButton(
                    onPressed: () async {
                      if (_formKey.currentState!.validate()) {
                        if (!_isLoading) {
                          setModalState(() {
                            _isLoading = true;
                          });
                          var media = await takePicture(
                              contentKey: widget.contentKey,
                              context: context,
                              saveToGallery: false,
                              fileName: folderName);

                          // Pass selected platforms to callback
                          widget.onDone(
                            media ?? '',
                            selectedPlatforms.contains('Instagram'), // Instagram selected check
                            selectedPlatforms.contains('Facebook'), // Facebook selected check
                          );
                          Navigator.pop(ctx);
                          setState(() {
                            _isLoading = false;
                          });
                        }
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                    ),
                    child: SizedBox(
                      width: _size.width * 1,
                      height: _size.height * 0.06,
                      child: Center(
                        child: _isLoading
                            ? const LoadingIndicator(
                                indicatorType: Indicator.ballClipRotateMultiple,
                                colors: [Colors.white],
                                strokeWidth: 4,
                                pathBackgroundColor: Colors.black)
                            : Text(
                                // platforms.isEmpty
                                //     ? 'No Platforms Available'
                                //     : selectedPlatforms.isEmpty
                                //         ? 'Select Platform to Share'
                                //         :
                                ' Share Story ',
                                style: GoogleFonts.ubuntu(
                                    color: Colors.white, fontSize: 16.sp, fontWeight: FontWeight.w600),
                              ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
