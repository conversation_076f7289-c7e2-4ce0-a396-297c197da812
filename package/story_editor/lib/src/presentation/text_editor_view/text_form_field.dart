// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FlowkarTextFormField extends StatelessWidget {
  FlowkarTextFormField(
      {super.key,
      required this.context,
      this.validator,
      this.width,
      this.alignment,
      this.textStyle,
      this.isObscure = false,
      this.isCapitalized = false,
      this.maxLines = 1,
      this.isLabelEnabled = true,
      this.readOnly = false,
      this.controller,
      this.inputAction,
      this.focusNode,
      this.label,
      this.hint = "",
      this.onTap,
      this.prefix,
      this.maxLength,
      this.suffixIcon,
      this.autofillHints,
      this.inputFormatters,
      this.prefixIcon,
      this.onFieldSubmitted,
      this.onChanged,
      this.textInputAction,
      this.textInputType,
      this.hintStyle,
      this.hintText,
      this.obscureText = false,
      this.suffix,
      this.fillColor,
      this.filled = false,
      this.borderDecoration,
      this.enabled = true,
      this.labelText,
      this.required = false,
      this.autovalidateMode,
      this.onSaved,
      this.lableStyle,
      this.initialValue,
      this.contentPadding,
      this.height});

  TextEditingController? controller;

  final BuildContext context;

  final Alignment? alignment;

  final double? width;

  final double? height;

  final TextInputAction? inputAction;

  final String? label;

  final String hint;

  final bool isObscure;

  final bool isCapitalized;

  final TextStyle? textStyle;

  final int maxLines;

  final int? maxLength;

  final ValueChanged<String>? onChanged;

  final bool obscureText;

  final bool isLabelEnabled;

  final String? Function(String?)? validator;

  final Function(String)? onFieldSubmitted;

  final bool readOnly;

  final Widget? suffixIcon;

  final String? hintText;

  final TextStyle? lableStyle;

  final TextStyle? hintStyle;

  final Widget? prefix;

  final Widget? prefixIcon;

  final void Function()? onTap;

  final Iterable<String>? autofillHints;

  final FocusNode? focusNode;

  final List<TextInputFormatter>? inputFormatters;

  final TextInputAction? textInputAction;

  final TextInputType? textInputType;

  final Widget? suffix;

  final Color? fillColor;

  final bool? filled;

  final InputBorder? borderDecoration;

  final bool? enabled;

  final String? labelText;

  final bool? required;

  final AutovalidateMode? autovalidateMode;

  final String? initialValue;

  final void Function(String?)? onSaved;

  final EdgeInsetsGeometry? contentPadding;

  @override
  Widget build(BuildContext context) {
    return alignment != null
        ? Align(
            alignment: alignment ?? Alignment.center,
            child: textFormFieldWidget,
          )
        : textFormFieldWidget;
  }

  Widget get textFormFieldWidget => SizedBox(
        width: width ?? double.maxFinite,
        child: TextFormField(
          enabled: enabled,
          onSaved: onSaved,
          initialValue: initialValue,
          onChanged: onChanged,
          obscureText: obscureText,
          inputFormatters: inputFormatters,
          autofillHints: autofillHints,
          onTap: onTap,
          maxLines: maxLines,
          maxLength: maxLength,
          onFieldSubmitted: onFieldSubmitted,
          focusNode: focusNode,
          controller: controller,
          readOnly: readOnly,
          style: textStyle ?? Theme.of(context).textTheme.bodyMedium,
          textCapitalization: isCapitalized ? TextCapitalization.words : TextCapitalization.none,
          textInputAction: textInputAction,
          keyboardType: textInputType,
          decoration: decoration,
          validator: validator,
          enableSuggestions: true,
          cursorOpacityAnimates: true,
          autovalidateMode: autovalidateMode ?? AutovalidateMode.disabled,
        ),
      );
  InputDecoration get decoration => InputDecoration(
        label: Text(labelText ?? ''),
        labelStyle: lableStyle ?? Theme.of(context).textTheme.bodySmall,
        alignLabelWithHint: true,
        hintText: hintText ?? "",
        hintStyle: hintStyle,
        prefixIcon: prefix,
        prefix: prefixIcon,
        suffixIcon: suffix,
        floatingLabelBehavior: isLabelEnabled ? FloatingLabelBehavior.always : FloatingLabelBehavior.never,
        isDense: true,
        contentPadding: contentPadding ?? EdgeInsets.only(left: 24.0.w, top: 20.5.h, bottom: 12.5.h, right: 18.0.w),
        fillColor: fillColor,
        filled: filled,
        border: borderDecoration ??
            OutlineInputBorder(
              borderRadius: BorderRadius.circular(20.r),
              borderSide: const BorderSide(
                color: Color(0XFFE0E0E0),
                width: 1,
              ),
            ),
        enabledBorder: borderDecoration ??
            OutlineInputBorder(
              borderRadius: BorderRadius.circular(20.r),
              borderSide: const BorderSide(
                color: Color(0XFFE0E0E0),
                width: 1,
              ),
            ),
        errorMaxLines: 2,
        counter: const Offstage(),
        focusedBorder: borderDecoration ??
            OutlineInputBorder(
              borderRadius: BorderRadius.circular(20.r),
              borderSide: const BorderSide(
                color: Color(0XFFE0E0E0),
                width: 1,
              ),
            ),
      );
}
