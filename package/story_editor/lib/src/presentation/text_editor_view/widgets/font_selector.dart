// ignore_for_file: no_leading_underscores_for_local_identifiers

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:story_editor/src/domain/providers/notifiers/control_provider.dart';
import 'package:story_editor/src/domain/providers/notifiers/text_editing_notifier.dart';
import 'package:story_editor/src/presentation/utils/constants/font_family.dart';
import 'package:story_editor/src/presentation/widgets/animated_onTap_button.dart';

class FontSelector extends StatelessWidget {
  const FontSelector({super.key});

  @override
  Widget build(BuildContext context) {
    var _size = MediaQuery.of(context).size;
    return Consumer2<TextEditingNotifier, ControlNotifier>(
      builder: (context, editorNotifier, controlNotifier, child) {
        return Container(
          height: _size.height * 0.06,
          width: _size.width,
          alignment: Alignment.center,
          child: PageView.builder(
            controller: editorNotifier.fontFamilyController,
            itemCount: controlNotifier.fontList!.length,
            onPageChanged: (index) {
              editorNotifier.fontFamilyIndex = index;
              HapticFeedback.heavyImpact();
            },
            physics: const BouncingScrollPhysics(),
            allowImplicitScrolling: true,
            pageSnapping: false,
            itemBuilder: (context, index) {
              return AnimatedOnTapButton(
                onTap: () {
                  editorNotifier.fontFamilyIndex = index;
                  editorNotifier.fontFamilyController.jumpToPage(index);
                },
                child: Container(
                  height: _size.height * 0.26,
                  // width: _size.width * 0.08,
                  alignment: Alignment.center,
                  margin: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                      color: index == editorNotifier.fontFamilyIndex ? Colors.white : Colors.black.withOpacity(0.4), borderRadius: BorderRadius.circular(10), border: Border.all(color: Colors.white)),
                  child: Center(
                    child: Text(
                      'Aa',
                      style: AppFonts.getTextThemeENUM(controlNotifier.fontList![index]).bodyLarge!.merge(const TextStyle(
                          // fontFamily: controlNotifier.fontList![index],
                          // package: controlNotifier.isCustomFontList
                          //     ? null
                          //     : 'vs_story_designer'
                          )).copyWith(color: index == editorNotifier.fontFamilyIndex ? Theme.of(context).primaryColor : Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}
