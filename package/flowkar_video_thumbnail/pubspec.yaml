name: flow<PERSON>_video_thumbnail
description: A flutter plugin for creating a thumbnail from a local video file or from a video URL.
version: 0.5.3
# author: <PERSON> <<EMAIL>>, <PERSON><PERSON> <tairs.r<PERSON><PERSON><PERSON>@chi.lv>, <PERSON><PERSON><PERSON> <j<PERSON><PERSON>@gmail.com>, <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON>, <PERSON>, <PERSON><PERSON>, Ajb Coder 
# homepage: https://github.com/justsoft
# repository: https://github.com/justsoft/video_thumbnail
# issue_tracker: https://github.com/justsoft/video_thumbnail/issues

environment:
  sdk: '>=2.16.0 <3.0.0'
  flutter: ">=1.10.0"

dependencies:
  flutter:
    sdk: flutter

dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://www.dartlang.org/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # This section identifies this Flutter project as a plugin project.
  # The androidPackage and pluginClass identifiers should not ordinarily
  # be modified. They are used by the tooling to maintain consistency when
  # adding or updating assets for this project.
  plugin:
    platforms:
      android:
        package: xyz.justsoft.video_thumbnail
        pluginClass: VideoThumbnailPlugin
      ios:
        pluginClass: VideoThumbnailPlugin

  # To add assets to your plugin package, add an assets section, like this:
  # assets:
  #  - images/a_dot_burr.jpeg
  #  - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your plugin package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
