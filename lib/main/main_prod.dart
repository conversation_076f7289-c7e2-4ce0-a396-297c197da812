import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/firebase/firebase_config.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:path_provider/path_provider.dart';

Future<void> main() async {
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle().copyWith(
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Color(0XFF563D39),
        systemNavigationBarIconBrightness: Brightness.light),
  );
  try {
    await dotenv.load(fileName: ".env");
  } catch (e) {
    Logger.lOG("Error loading .env file: $e");
  }
  await initializeFirebase();
  WidgetsFlutterBinding.ensureInitialized();
  ErrorWidget.builder = (FlutterErrorDetails details) => CustomErrorWidget(
        errorMessage: details.exception.toString(),
      );
  FlavorConfig.initialize(
    flavor: Flavor.prod,
    env: EnvConfig.prodConfig,
  );
  log("prod ENV: ${FlavorConfig.instance.flavor}");

  final Directory appDocDir = await getApplicationDocumentsDirectory();
  Hive.init(appDocDir.path);

  runZonedGuarded(
    () async {
      FlutterError.onError = (errorDetails) {
        if (kReleaseMode) {
          FirebaseCrashlytics.instance.recordFlutterError(errorDetails);
        }
      };
      await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]).then((value) async {
        await Hive.openBox('Flowkar').then(
          (value) => runApp(
            OverlaySupport.global(child: FlowkarApp(prefs: value)),
          ),
        );
      });
    },
    (Object error, StackTrace stack) {
      if (kReleaseMode) {
        FirebaseCrashlytics.instance.recordError(error, stack);
      }
    },
  );
}
