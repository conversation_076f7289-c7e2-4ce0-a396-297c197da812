// import 'package:flowkar/core/constants/api_constants.dart';

class EnvConfig {
  final String baseUrl;
  final String apiKey;
  final bool enableLogging;

  EnvConfig({
    required this.baseUrl,
    required this.apiKey,
    required this.enableLogging,
  });

  static EnvConfig devConfig = EnvConfig(
    baseUrl: 'https://dev.flowkar.com/api/',
    apiKey: 'https://dev.flowkar.com',
    // baseUrl: 'https://staging.flowkar.com/api/',
    // apiKey: 'https://staging.flowkar.com',
    // baseUrl: 'https://api.flowkar.com/api/',
    // apiKey: 'https://api.flowkar.com',
    enableLogging: true,
  );

  static EnvConfig qaConfig = EnvConfig(
    baseUrl: 'url',
    apiKey: 'qa-api-key',
    enableLogging: true,
  );

  static EnvConfig prodConfig = EnvConfig(
    baseUrl: 'https://api.flowkar.com/api/',
    apiKey: 'https://api.flowkar.com',
    enableLogging: false,
  );
}
