import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:flowkar/core/utils/exports.dart';

class SocketService {
  static final SocketService _instance = SocketService._internal();

  factory SocketService() => _instance;

  SocketService._internal();

  late io.Socket _socket;
  bool isSocketConnected = false;

  static void initializeSocket() {
    if (_instance.isSocketConnected) {
      Logger.lOG('Socket is already connected');
      return;
    }

    _instance._socket = io.io(
      APIConfig.mainbaseURL,
      io.OptionBuilder().setTransports(['websocket', 'polling']).setPath('/socket.io/').enableForceNew().build(),
    );

    _instance._socket.on('connect', (_) {
      Logger.lOG('Connected to socket server at ${_instance._socket.io.uri}');
      _instance.isSocketConnected = true;
    });

    _instance._socket.on('disconnect', (_) {
      Logger.lOG('Disconnected from socket server');
      _instance.isSocketConnected = false;
    });

    _instance._socket.on('error', (error) {
      Logger.lOG('Socket error: $error');
    });

    _instance._socket.on('reconnect_attempt', (_) {
      Logger.lOG('Attempting to reconnect to socket server');
    });

    _instance._socket.on('reconnect', (_) {
      Logger.lOG('Reconnected to socket server');
    });

    _instance._socket.on('reconnect_error', (error) {
      Logger.lOG('Socket reconnect error: $error');
    });

    _instance._socket.on('reconnect_failed', (_) {
      Logger.lOG('Socket reconnect failed');
    });
  }

  static void emit(String event, Map<String, dynamic> data) {
    if (_instance.isSocketConnected) {
      _instance._socket.emit(event, data);
      Logger.lOG('EVENT_NAME: $event REQUEST_DATA: $data');
    } else {
      Logger.lOG('Socket is not connected. Cannot emit event.');
    }
  }

  static void response(String event, Function(dynamic) callback) {
    _instance._socket.off(event);

    if (_instance.isSocketConnected) {
      _instance._socket.on(event, (data) {
        Logger.lOG('EVENT_NAME: $event DATA: $data');
        callback(data);
      });
    } else {
      Logger.lOG('Socket is not connected. Cannot add event listener.');
    }
  }

  static void closeConnection() {
    if (_instance.isSocketConnected) {
      _instance._socket.disconnect();
      _instance._socket.close();
      Logger.lOG('Disconnected from socket server');
      _instance.isSocketConnected = false;
    }
  }
}
