import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/analytics/bloc/analytics_bloc.dart';
import 'package:flowkar/features/authentication/bloc/auth_bloc.dart';
import 'package:flowkar/features/feedback_screen/bloc/feedback_bloc.dart';
import 'package:flowkar/features/join_beta_tester/bloc/join_beta_tester_bloc.dart';
import 'package:flowkar/features/profile_screen/bloc/profile_bloc.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/discover/bloc/discover_bloc.dart';
import 'package:flowkar/features/notification/bloc/notification_bloc.dart';
import 'package:flowkar/features/reward_leader/bloc/leader_bloc.dart';
import 'package:flowkar/features/setting_screen/bloc/setting_bloc.dart';
import 'package:flowkar/features/setting_screen/page/panding_aprovel_post/bloc/panding_aprovel_post_bloc.dart';
import 'package:flowkar/features/update_version/bloc/version_bloc.dart';
import 'package:flowkar/features/sm_chat/bloc/sm_chat_bloc.dart';
import 'package:flowkar/features/user_management/bloc/user_management_bloc.dart';
import 'package:flowkar/features/social_connect/bloc/social_connec_bloc.dart';
import 'package:flowkar/features/survey_form/bloc/survey_bloc.dart';
import 'package:flowkar/features/upload_post/bloc/post_bloc.dart';
import 'package:flowkar/features/wallet/bloc/wallet_bloc.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
// import 'package:internet_connection_checker/internet_connection_checker.dart';

class BlocProviders extends StatelessWidget {
  final Widget child;

  const BlocProviders({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<LocaleBloc>(
          create: (context) => LocaleBloc()..add(SetLocale(locale: Locale('en'))),
        ),
        BlocProvider<ThemeBloc>(
          create: (context) => ThemeBloc()
            ..add(
              InitializeTheme(isDarkThemeOn: false, followSystemTheme: true),
            ),
        ),
        BlocProvider<PostBloc>(
          create: (context) => PostBloc(PostState()),
        ),
        BlocProvider(
          create: (context) => SocialConnecBloc(),
        ),
        BlocProvider(
          create: (context) => AuthBloc(AuthState())..add(AuthInitial()),
        ),
        BlocProvider(
          create: (context) => HomeFeedBloc(HomeFeedState())..add(HomeFeedInitial()),
        ),
        BlocProvider(
          create: (context) => DiscoverBloc()..add(DiscoverInitialEvent()),
        ),
        BlocProvider(
          create: (context) => SettingBloc(),
        ),
        BlocProvider(
          create: (context) => NotificationBloc()..add(GetNotificationinitialEvent()),
        ),
        BlocProvider<ConnectivityBloc>(
          create: (context) => ConnectivityBloc(InternetConnectionChecker()),
        ),
        BlocProvider(
          create: (context) => UserProfileBloc(),
        ),
        BlocProvider<SmChatBloc>(
          create: (context) => SmChatBloc()..add(SmChatInitialEvent()),
        ),
        BlocProvider(
          create: (context) => SettingBloc(),
        ),
        BlocProvider(
          create: (context) => UserManagementBloc(UserManagementState()),
        ),
        BlocProvider(
          create: (context) => SurveyBloc(SurveyState()),
        ),
        BlocProvider(create: (context) => FeedbackBloc(FeedbackState())),
        BlocProvider(
          create: (context) => VersionBloc(VersionState())..add(VersionUpdateEvent()),
        ),
        BlocProvider(
          create: (context) => JoinBetaTesterBloc(JoinBetaTesterState()),
        ),
        BlocProvider(
          create: (context) => AnalyticsBloc(AnalyticsState()),
        ),
        BlocProvider(
          create: (context) => WalletBloc(),
        ),
        BlocProvider(
          create: (context) => LeaderboardBloc(),
        ),
        BlocProvider(
          create: (context) => PandingAprovelPostBloc(),
        ),
        // BlocProvider(
        //   create: (context) => ReelsBloc(ReelsState()),
        // ),
      ],
      child: child,
    );
  }
}
