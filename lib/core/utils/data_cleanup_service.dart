import 'dart:io';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/config/cache_config.dart';
import 'package:flowkar/core/socket/socket_service.dart';
import 'package:path_provider/path_provider.dart';

/// Service responsible for comprehensive data cleanup during logout
///
/// This service ensures that ALL user data is completely removed from the device
/// when the user logs out, including:
/// - Hive storage (preferences and user data)
/// - Global ValueNotifiers and variables
/// - Cache managers (images, videos, stories)
/// - Socket connections
/// - Temporary files and user-generated content
/// - Comment count notifiers and other dynamic data
class DataCleanupService {
  /// Clears all application data including storage, cache, global variables, and temporary files
  ///
  /// This method should be called during logout to ensure complete data cleanup.
  /// It performs the following operations:
  /// 1. Clears all Hive storage data
  /// 2. Resets global ValueNotifiers to default values
  /// 3. Empties all cache managers
  /// 4. Closes socket connections
  /// 5. Removes temporary files
  /// 6. Clears application documents (user-generated content)
  /// 7. Resets global variables
  /// 8. Clears dynamic notifier maps
  static Future<void> clearAllData() async {
    try {
      Logger.lOG("Starting comprehensive data cleanup...");

      // 1. Clear Hive storage (preferences)
      await _clearHiveStorage();

      // 2. Reset global ValueNotifiers
      _resetGlobalNotifiers();

      // 3. Clear cache managers
      await _clearCacheManagers();

      // 4. Close socket connections
      _closeSocketConnections();

      // 5. Clear temporary files
      await _clearTemporaryFiles();

      // 6. Clear application documents (user-generated content)
      await _clearApplicationDocuments();

      // 7. Reset global variables
      _resetGlobalVariables();

      // 8. Clear comment count notifiers
      _clearCommentCountNotifiers();

      // 8. Reset BLoC states (Note: This should be called from the UI layer)
      // Individual BLoCs should be reset by their respective screens/widgets

      Logger.lOG("Data cleanup completed successfully");
    } catch (error) {
      Logger.lOG("Error during data cleanup: $error");
    }
  }

  /// Clear all Hive storage data
  static Future<void> _clearHiveStorage() async {
    try {
      Logger.lOG("Clearing Hive storage...");

      // Clear all preferences
      Prefobj.preferences?.clear();

      //Explicitly delete critical keys
      await Prefobj.preferences?.delete(Prefkeys.AUTHTOKEN);
      await Prefobj.preferences?.delete(Prefkeys.TMPAUTHTOKEN);
      await Prefobj.preferences?.delete(Prefkeys.LOG_IN_USER_ID);
      await Prefobj.preferences?.delete(Prefkeys.USER_ID);
      await Prefobj.preferences?.delete(Prefkeys.NAME);
      await Prefobj.preferences?.delete(Prefkeys.PROFILE);
      await Prefobj.preferences?.delete(Prefkeys.STATUS);
      await Prefobj.preferences?.delete(Prefkeys.USERTYPE);
      await Prefobj.preferences?.delete(Prefkeys.SUBSCRIPTION);
      await Prefobj.preferences?.delete(Prefkeys.BRANDID);
      await Prefobj.preferences?.delete(Prefkeys.USERNAME);
      await Prefobj.preferences?.delete(Prefkeys.USEREMAIL);
      await Prefobj.preferences?.delete(Prefkeys.ISOPTIN);

      // Clear social platform preferences
      await Prefobj.preferences?.delete(Prefkeys.VIMEO);
      await Prefobj.preferences?.delete(Prefkeys.FACEBOOK);
      await Prefobj.preferences?.delete(Prefkeys.INSTAGRAM);
      await Prefobj.preferences?.delete(Prefkeys.THREAD);
      await Prefobj.preferences?.delete(Prefkeys.LINKEDIN);
      await Prefobj.preferences?.delete(Prefkeys.PINTEREST);
      await Prefobj.preferences?.delete(Prefkeys.TUMBLR);
      await Prefobj.preferences?.delete(Prefkeys.REDDIT);
      await Prefobj.preferences?.delete(Prefkeys.YOUTUBE);
      await Prefobj.preferences?.delete(Prefkeys.TIKTOK);
      await Prefobj.preferences?.delete(Prefkeys.X);
      await Prefobj.preferences?.delete(Prefkeys.TELEGRAM);
      await Prefobj.preferences?.delete(Prefkeys.MASTODON);
      await Prefobj.preferences?.delete(Prefkeys.OAUTH_TUMBLER);
      await Prefobj.preferences?.delete(Prefkeys.OAUTH_SECRET_TUMBLER);
      // Clear multi-account storage
      await Prefobj.preferences?.delete(Prefkeys.STORED_ACCOUNTS);
      await Prefobj.preferences?.delete(Prefkeys.CURRENT_ACCOUNT_INDEX);
      await Prefobj.preferences?.delete(Prefkeys.LOG_IN_USER_ID);
      // Roll Permisiion
      await Prefobj.preferences?.delete(Prefkeys.PRM_POST);
      await Prefobj.preferences?.delete(Prefkeys.PRM_MESSAGE);
      await Prefobj.preferences?.delete(Prefkeys.PRM_ANALYTICS);
      await Prefobj.preferences?.delete(Prefkeys.PRM_USER_MANGEMENT);
      await Prefobj.preferences?.delete(Prefkeys.PRM_BRAND_MANGEMENT);
      await Prefobj.preferences?.delete(Prefkeys.PRM_BLOCK_UNBLOCK);
      await Prefobj.preferences?.delete(Prefkeys.PRM_FEEDBACK);
      await Prefobj.preferences?.clear();
      // Prefobj.preferences?.close();
      Logger.lOG("Hive storage cleared");
    } catch (error) {
      Logger.lOG("Error clearing Hive storage: $error");
    }
  }

  /// Reset all global ValueNotifiers to their default values
  static void _resetGlobalNotifiers() {
    try {
      Logger.lOG("Resetting global notifiers...");

      // Reset social platform status
      socialPlatformsStatus.value = {
        'VIMEO': false,
        'FACEBOOK': false,
        'INSTAGRAM': false,
        'THREAD': false,
        'LINKEDIN': false,
        'PINTEREST': false,
        'TUMBLR': false,
        'REDDIT': false,
        'YOUTUBE': false,
        'TIKTOK': false,
        'X': false,
        'TELEGRAM': false,
        'MASTODON': false,
      };

      // Reset individual social platform notifiers
      vimeoNotifier.value = false;
      facebookNotifier.value = false;
      instagramNotifier.value = false;
      threadNotifier.value = false;
      linkedInNotifier.value = false;
      pinterestNotifier.value = false;
      tumblrNotifier.value = false;
      redditNotifier.value = false;
      youtubeNotifier.value = false;
      tiktokNotifier.value = false;
      xNotifier.value = false;
      telegramNotifier.value = false;
      mastodonNotifier.value = false;

      // Reset other global notifiers
      shouldInitialize.value = true;
      brandNameNotifier.value = "";
      profileImageNotifier.value = Assets.images.svg.other.svgUserProfile.path;
      progressNotifier.value = 0.0;
      scrollTopNotifier.value = false;

      Logger.lOG("Global notifiers reset");
    } catch (error) {
      Logger.lOG("Error resetting global notifiers: $error");
    }
  }

  /// Clear all cache managers
  static Future<void> _clearCacheManagers() async {
    try {
      Logger.lOG("Clearing cache managers...");

      // Clear story cache
      await kCacheManager.emptyCache();

      // Clear profile image cache
      await kpfCacheManager.emptyCache();

      // Clear default cache manager (for other cached images/videos)
      await DefaultCacheManager().emptyCache();

      Logger.lOG("Cache managers cleared");
    } catch (error) {
      Logger.lOG("Error clearing cache managers: $error");
    }
  }

  /// Close socket connections
  static void _closeSocketConnections() {
    try {
      Logger.lOG("Closing socket connections...");
      SocketService.closeConnection();
      Logger.lOG("Socket connections closed");
    } catch (error) {
      Logger.lOG("Error closing socket connections: $error");
    }
  }

  /// Clear temporary files and directories
  static Future<void> _clearTemporaryFiles() async {
    try {
      Logger.lOG("Clearing temporary files...");

      final Directory tempDir = await getTemporaryDirectory();

      if (tempDir.existsSync()) {
        // List all files and directories in temp directory
        final List<FileSystemEntity> entities = tempDir.listSync();

        for (FileSystemEntity entity in entities) {
          try {
            if (entity is File) {
              await entity.delete();
            } else if (entity is Directory) {
              await entity.delete(recursive: true);
            }
          } catch (e) {
            Logger.lOG("Error deleting temp entity ${entity.path}: $e");
          }
        }
      }

      Logger.lOG("Temporary files cleared");
    } catch (error) {
      Logger.lOG("Error clearing temporary files: $error");
    }
  }

  /// Clear application documents directory (user-generated content)
  static Future<void> _clearApplicationDocuments() async {
    try {
      Logger.lOG("Clearing application documents...");

      final Directory appDocDir = await getApplicationDocumentsDirectory();

      if (appDocDir.existsSync()) {
        // List all files and directories in app documents directory
        // final List<FileSystemEntity> entities = appDocDir.listSync();

        // for (FileSystemEntity entity in entities) {
        //   try {
        //     // Skip the Hive database files as they are handled separately
        //     if (entity.path.contains('Flowkar') && entity.path.contains('.hive')) {
        //       continue;
        //     }

        //     if (entity is File) {
        //       await entity.delete();
        //     } else if (entity is Directory) {
        //       // Only delete directories that don't contain Hive files
        //       if (!entity.path.contains('Flowkar')) {
        //         await entity.delete(recursive: true);
        //       }
        //     }
        //   } catch (e) {
        //     Logger.lOG("Error deleting app doc entity ${entity.path}: $e");
        //   }
        // }
      }

      Logger.lOG("Application documents cleared");
    } catch (error) {
      Logger.lOG("Error clearing application documents: $error");
    }
  }

  /// Reset global variables
  static void _resetGlobalVariables() {
    try {
      Logger.lOG("Resetting global variables...");

      // Reset message screen variables
      isMessageScreen.value = false;
      massageUserID.value = 0;
      touser.value = 0;

      Logger.lOG("Global variables reset");
    } catch (error) {
      Logger.lOG("Error resetting global variables: $error");
    }
  }

  /// Clear comment count notifiers map
  static void _clearCommentCountNotifiers() {
    try {
      Logger.lOG("Clearing comment count notifiers...");
      commentCountNotifiers.clear();
      Logger.lOG("Comment count notifiers cleared");
    } catch (error) {
      Logger.lOG("Error clearing comment count notifiers: $error");
    }
  }

  /// Clears user-specific data for account switching without full logout
  ///
  /// This method is specifically designed for account switching scenarios where:
  /// - Authentication tokens should be preserved (handled by AuthBloc)
  /// - Socket connections should be maintained (handled by AuthBloc)
  /// - Only user-specific cached data and UI state should be cleared
  /// - Global notifiers should be reset to prepare for new user data
  static Future<void> clearUserDataForAccountSwitch() async {
    try {
      Logger.lOG("Starting user data cleanup for account switch...");

      // 1. Reset global ValueNotifiers (except authentication-related ones)
      _resetGlobalNotifiersForAccountSwitch();

      // 2. Clear cache managers (images, videos, stories)
      await _clearCacheManagers();

      // 3. Clear temporary files
      await _clearTemporaryFiles();

      // 4. Clear comment count notifiers
      _clearCommentCountNotifiers();

      // 5. Reset global variables
      _resetGlobalVariablesForAccountSwitch();

      Logger.lOG("User data cleanup for account switch completed successfully");
    } catch (error) {
      Logger.lOG("Error during user data cleanup for account switch: $error");
    }
  }

  /// Reset global ValueNotifiers for account switching (preserves auth-related notifiers)
  static void _resetGlobalNotifiersForAccountSwitch() {
    try {
      Logger.lOG("Resetting global notifiers for account switch...");

      // Reset individual social platform notifiers
      vimeoNotifier.value = false;
      facebookNotifier.value = false;
      instagramNotifier.value = false;
      threadNotifier.value = false;
      linkedInNotifier.value = false;
      pinterestNotifier.value = false;
      tumblrNotifier.value = false;
      redditNotifier.value = false;
      youtubeNotifier.value = false;
      tiktokNotifier.value = false;
      xNotifier.value = false;

      // Reset UI-related notifiers
      shouldInitialize.value = true;
      progressNotifier.value = 0.0;
      scrollTopNotifier.value = false;

      // Note: brandNameNotifier and profileImageNotifier will be updated by AuthBloc
      // after successful account switch

      Logger.lOG("Global notifiers reset for account switch");
    } catch (error) {
      Logger.lOG("Error resetting global notifiers for account switch: $error");
    }
  }

  /// Reset global variables for account switching
  static void _resetGlobalVariablesForAccountSwitch() {
    try {
      Logger.lOG("Resetting global variables for account switch...");

      // Reset message-related variables
      isMessageScreen.value = false;
      massageUserID.value = 0;
      touser.value = 0;

      Logger.lOG("Global variables reset for account switch");
    } catch (error) {
      Logger.lOG("Error resetting global variables for account switch: $error");
    }
  }

  /// Verify that critical data has been cleared (for testing purposes)
  static bool verifyDataCleared() {
    try {
      // Check if critical preferences are cleared
      final authToken = Prefobj.preferences?.get(Prefkeys.AUTHTOKEN);
      final userId = Prefobj.preferences?.get(Prefkeys.USER_ID);
      final profile = Prefobj.preferences?.get(Prefkeys.PROFILE);

      // Check if global notifiers are reset
      final socialPlatformsReset = socialPlatformsStatus.value.values.every((status) => status == false);
      final profileImageReset = profileImageNotifier.value == Assets.images.svg.other.svgUserProfile.path;
      final brandNameReset = brandNameNotifier.value.isEmpty;

      // Check if comment notifiers are cleared
      final commentNotifiersCleared = commentCountNotifiers.isEmpty;

      final isDataCleared = authToken == null &&
          userId == null &&
          profile == null &&
          socialPlatformsReset &&
          profileImageReset &&
          brandNameReset &&
          commentNotifiersCleared;

      Logger.lOG("Data cleanup verification: ${isDataCleared ? 'PASSED' : 'FAILED'}");
      Logger.lOG("Auth token cleared: ${authToken == null}");
      Logger.lOG("User ID cleared: ${userId == null}");
      Logger.lOG("Profile cleared: ${profile == null}");
      Logger.lOG("Social platforms reset: $socialPlatformsReset");
      Logger.lOG("Profile image reset: $profileImageReset");
      Logger.lOG("Brand name reset: $brandNameReset");
      Logger.lOG("Comment notifiers cleared: $commentNotifiersCleared");

      return isDataCleared;
    } catch (error) {
      Logger.lOG("Error verifying data cleanup: $error");
      return false;
    }
  }
}
