import 'package:flowkar/core/utils/exports.dart';
import 'dart:async';

class Prefobj {
  static Box? preferences; // Static reference to preferences box
  static final _storageQueue = <Future<void>>[];
  static bool _isProcessingQueue = false;
}

/// Optimized storage operations to reduce UI blocking
class OptimizedStorage {
  /// Batch multiple storage operations to reduce I/O overhead
  static Future<void> batchPut(Map<String, dynamic> data) async {
    if (Prefobj.preferences == null) return;

    final futures = data.entries.map((entry) => Prefobj.preferences!.put(entry.key, entry.value));

    await Future.wait(futures);
  }

  /// Queue storage operations to prevent blocking
  static Future<void> queuedPut(String key, dynamic value) async {
    final operation = _performPut(key, value);
    Prefobj._storageQueue.add(operation);

    if (!Prefobj._isProcessingQueue) {
      _processQueue();
    }

    return operation;
  }

  static Future<void> _performPut(String key, dynamic value) async {
    await Prefobj.preferences?.put(key, value);
  }

  static Future<void> _processQueue() async {
    Prefobj._isProcessingQueue = true;

    while (Prefobj._storageQueue.isNotEmpty) {
      final batch = Prefobj._storageQueue.take(5).toList();
      Prefobj._storageQueue.removeRange(0, batch.length.clamp(0, Prefobj._storageQueue.length));

      await Future.wait(batch);
    }

    Prefobj._isProcessingQueue = false;
  }
}

class Prefkeys {
  static const String AUTHTOKEN = 'auth_token';
  static const String TMPAUTHTOKEN = 'tmp_auth_token';
  static const String FIRSTTIME = 'first_time';
  static const String SKIPSOCIALCONNECT = 'skip_social';
  static const String LIGHTDARK = 'light_dark';
  static const String FOLLOW_SYSTEM = 'follow_system';

  static const String USER_ID = 'user_ID';
  static const String LOG_IN_USER_ID = 'log_in_user_ID';

  static const String VIMEO = 'vimeo';
  static const String FACEBOOK = 'facebook';
  static const String INSTAGRAM = 'instagram';
  static const String THREAD = 'thread';
  static const String LINKEDIN = 'linkedin';
  static const String PINTEREST = 'pinterest';
  static const String TUMBLR = 'tumblr';
  static const String REDDIT = 'reddit';
  static const String YOUTUBE = 'youtube';
  static const String TIKTOK = 'tiktok';
  static const String BLUSKY = 'blusky';
  static const String X = 'x';
  static const String TELEGRAM = 'telegram';
  static const String MASTODON = 'mastodon';
  static const String OAUTH_TUMBLER = 'oauth_token';
  static const String OAUTH_SECRET_TUMBLER = 'oauth_token_secret';
  // static const String DAILYMOTION = 'dailymotion';
  static const String PROFILE = 'profile';
  static const String SUBSCRIPTIONID = 'subscription_id';
  // static const String USERID = 'user_id';
  static const String STATUS = 'status';
  static const String USERTYPE = 'user_type';
  static const String SUBSCRIPTION = 'subscription';
  static const String BRANDID = 'brand_id';
  static const String USERNAME = 'user_name';
  static const String USEREMAIL = 'user_email';
  static const String NAME = 'name';

  static const String ISOPTIN = 'is_opt_in';
  static const String BETA_POST_DISMISSED_AT = 'beta_post_dismissed_at';
  // Multi-account support keys
  static const String STORED_ACCOUNTS = 'stored_accounts';
  static const String CURRENT_ACCOUNT_INDEX = 'current_account_index';

  static const String LEADERBOARD_LAST_CALL_DATE = 'leaderboard_last_call_date';
  // Roll Permisiion
  static const String PRM_POST = 'post';
  static const String PRM_MESSAGE = 'message';
  static const String PRM_ANALYTICS = 'analytics';
  static const String PRM_USER_MANGEMENT = 'user_management';
  static const String PRM_BRAND_MANGEMENT = 'brand_management';
  static const String PRM_BLOCK_UNBLOCK = 'block_unblock';
  static const String PRM_FEEDBACK = 'feedback';
}
