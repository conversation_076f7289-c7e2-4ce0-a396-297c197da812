import 'package:flowkar/core/utils/exports.dart';

class LoadingAnimationWidget extends StatelessWidget {
  final Indicator? indicatorType;
  final double? height;
  final double? width;
  const LoadingAnimationWidget(
      {super.key, this.indicatorType, this.height, this.width});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        height: height ?? 100.h,
        width: width ?? 100.w,
        child: LoadingIndicator(
            indicatorType: indicatorType ?? Indicator.ballClipRotateMultiple,
            colors: [Theme.of(context).colorScheme.primary],
            strokeWidth: 4,
            pathBackgroundColor: Colors.black),
      ),
    );
  }
}
