import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

/// Performance monitoring utility to track app performance improvements
class PerformanceMonitor {
  static final Map<String, DateTime> _startTimes = {};
  static final Map<String, List<int>> _durations = {};
  static bool _isEnabled = kDebugMode;

  /// Enable or disable performance monitoring
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }

  /// Start timing an operation
  static void startTimer(String operationName) {
    if (!_isEnabled) return;
    _startTimes[operationName] = DateTime.now();
  }

  /// End timing an operation and log the duration
  static void endTimer(String operationName) {
    if (!_isEnabled) return;
    
    final startTime = _startTimes[operationName];
    if (startTime == null) {
      developer.log('Warning: Timer for $operationName was not started');
      return;
    }

    final duration = DateTime.now().difference(startTime).inMilliseconds;
    _startTimes.remove(operationName);

    // Store duration for analysis
    _durations.putIfAbsent(operationName, () => []).add(duration);

    // Log performance
    developer.log(
      'Performance: $operationName took ${duration}ms',
      name: 'PerformanceMonitor',
    );

    // Warn about slow operations
    if (duration > 1000) {
      developer.log(
        'WARNING: Slow operation detected - $operationName took ${duration}ms',
        name: 'PerformanceMonitor',
      );
    }
  }

  /// Time a function execution
  static Future<T> timeFunction<T>(
    String operationName,
    Future<T> Function() function,
  ) async {
    startTimer(operationName);
    try {
      final result = await function();
      endTimer(operationName);
      return result;
    } catch (e) {
      endTimer(operationName);
      rethrow;
    }
  }

  /// Time a synchronous function execution
  static T timeFunctionSync<T>(
    String operationName,
    T Function() function,
  ) {
    startTimer(operationName);
    try {
      final result = function();
      endTimer(operationName);
      return result;
    } catch (e) {
      endTimer(operationName);
      rethrow;
    }
  }

  /// Get performance statistics for an operation
  static Map<String, dynamic> getStats(String operationName) {
    final durations = _durations[operationName];
    if (durations == null || durations.isEmpty) {
      return {'error': 'No data for operation: $operationName'};
    }

    durations.sort();
    final count = durations.length;
    final sum = durations.reduce((a, b) => a + b);
    final average = sum / count;
    final median = count % 2 == 0
        ? (durations[count ~/ 2 - 1] + durations[count ~/ 2]) / 2
        : durations[count ~/ 2].toDouble();

    return {
      'operation': operationName,
      'count': count,
      'average': average.round(),
      'median': median.round(),
      'min': durations.first,
      'max': durations.last,
      'total': sum,
    };
  }

  /// Get all performance statistics
  static Map<String, Map<String, dynamic>> getAllStats() {
    final stats = <String, Map<String, dynamic>>{};
    for (final operation in _durations.keys) {
      stats[operation] = getStats(operation);
    }
    return stats;
  }

  /// Clear all performance data
  static void clearStats() {
    _durations.clear();
    _startTimes.clear();
  }

  /// Log a summary of all performance statistics
  static void logSummary() {
    if (!_isEnabled) return;

    developer.log('=== Performance Summary ===', name: 'PerformanceMonitor');
    
    final allStats = getAllStats();
    if (allStats.isEmpty) {
      developer.log('No performance data available', name: 'PerformanceMonitor');
      return;
    }

    for (final entry in allStats.entries) {
      final stats = entry.value;
      developer.log(
        '${entry.key}: avg=${stats['average']}ms, '
        'median=${stats['median']}ms, '
        'count=${stats['count']}, '
        'min=${stats['min']}ms, '
        'max=${stats['max']}ms',
        name: 'PerformanceMonitor',
      );
    }
  }

  /// Monitor memory usage (basic implementation)
  static void logMemoryUsage(String context) {
    if (!_isEnabled) return;
    
    // This is a basic implementation - in production you might want to use
    // more sophisticated memory monitoring tools
    developer.log(
      'Memory check at: $context',
      name: 'MemoryMonitor',
    );
  }

  /// Check if an operation is consistently slow
  static bool isOperationSlow(String operationName, {int thresholdMs = 500}) {
    final durations = _durations[operationName];
    if (durations == null || durations.length < 3) return false;

    final recentDurations = durations.length > 10 
        ? durations.sublist(durations.length - 10)
        : durations;

    final slowCount = recentDurations.where((d) => d > thresholdMs).length;
    return slowCount > recentDurations.length * 0.7; // 70% of recent operations are slow
  }

  /// Get recommendations for performance improvements
  static List<String> getRecommendations() {
    final recommendations = <String>[];
    
    for (final operation in _durations.keys) {
      if (isOperationSlow(operation)) {
        recommendations.add(
          'Consider optimizing "$operation" - consistently taking > 500ms'
        );
      }
      
      final stats = getStats(operation);
      if (stats['max'] > 2000) {
        recommendations.add(
          'Peak performance issue in "$operation" - max time ${stats['max']}ms'
        );
      }
    }
    
    return recommendations;
  }
}

/// Extension to easily add performance monitoring to any function
extension PerformanceMonitorExtension on Future {
  Future<T> withPerformanceMonitoring<T>(String operationName) async {
    return PerformanceMonitor.timeFunction(operationName, () async => await this as T);
  }
}

/// Mixin for classes that want to easily add performance monitoring
mixin PerformanceMonitorMixin {
  void startPerformanceTimer(String operation) {
    PerformanceMonitor.startTimer('${runtimeType}_$operation');
  }

  void endPerformanceTimer(String operation) {
    PerformanceMonitor.endTimer('${runtimeType}_$operation');
  }

  Future<T> timeOperation<T>(String operation, Future<T> Function() function) {
    return PerformanceMonitor.timeFunction('${runtimeType}_$operation', function);
  }
}
