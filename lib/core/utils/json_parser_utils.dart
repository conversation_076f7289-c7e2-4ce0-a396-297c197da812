import 'dart:convert';
import 'package:flutter/foundation.dart';

/// Optimized JSON parsing utilities to prevent UI blocking
class JsonParserUtils {
  /// Parse JSON in background isolate for large responses
  static Future<T> parseInBackground<T>(
    String jsonString,
    T Function(Map<String, dynamic>) fromJson,
  ) async {
    if (jsonString.length < 10000) {
      // For small JSON, parse on main thread
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return fromJson(json);
    }

    // For large JSON, use compute to parse in background
    return await compute(_parseJsonIsolate<T>, {
      'jsonString': jsonString,
      'fromJson': fromJson,
    });
  }

  /// Parse list of JSON objects in background
  static Future<List<T>> parseListInBackground<T>(
    String jsonString,
    T Function(Map<String, dynamic>) fromJson,
  ) async {
    if (jsonString.length < 50000) {
      // For small lists, parse on main thread
      final List<dynamic> jsonList = jsonDecode(jsonString);
      return jsonList.map((json) => fromJson(json as Map<String, dynamic>)).toList();
    }

    // For large lists, use compute to parse in background
    return await compute(_parseJsonListIsolate<T>, {
      'jsonString': jsonString,
      'fromJson': fromJson,
    });
  }

  /// Batch parse multiple JSON objects
  static Future<List<T>> batchParse<T>(
    List<String> jsonStrings,
    T Function(Map<String, dynamic>) fromJson,
  ) async {
    if (jsonStrings.length <= 10) {
      // For small batches, parse sequentially
      return jsonStrings.map((json) => fromJson(jsonDecode(json) as Map<String, dynamic>)).toList();
    }

    // For large batches, use compute
    return await compute(_batchParseIsolate<T>, {
      'jsonStrings': jsonStrings,
      'fromJson': fromJson,
    });
  }

  /// Stream parse large JSON arrays to reduce memory usage
  static Stream<T> streamParse<T>(
    String jsonString,
    T Function(Map<String, dynamic>) fromJson,
  ) async* {
    final List<dynamic> jsonList = jsonDecode(jsonString);

    for (int i = 0; i < jsonList.length; i++) {
      yield fromJson(jsonList[i] as Map<String, dynamic>);

      // Yield control back to UI thread every 10 items
      if (i % 10 == 0) {
        await Future.delayed(Duration.zero);
      }
    }
  }
}

/// Isolate function for parsing single JSON object
T _parseJsonIsolate<T>(Map<String, dynamic> params) {
  final String jsonString = params['jsonString'];
  final T Function(Map<String, dynamic>) fromJson = params['fromJson'];

  final Map<String, dynamic> json = jsonDecode(jsonString);
  return fromJson(json);
}

/// Isolate function for parsing JSON list
List<T> _parseJsonListIsolate<T>(Map<String, dynamic> params) {
  final String jsonString = params['jsonString'];
  final T Function(Map<String, dynamic>) fromJson = params['fromJson'];

  final List<dynamic> jsonList = jsonDecode(jsonString);
  return jsonList.map((json) => fromJson(json as Map<String, dynamic>)).toList();
}

/// Isolate function for batch parsing
List<T> _batchParseIsolate<T>(Map<String, dynamic> params) {
  final List<String> jsonStrings = params['jsonStrings'];
  final T Function(Map<String, dynamic>) fromJson = params['fromJson'];

  return jsonStrings.map((json) => fromJson(jsonDecode(json) as Map<String, dynamic>)).toList();
}

/// Optimized list operations to prevent O(n²) complexity
class ListUtils {
  /// Efficiently filter duplicates using Set for O(n) complexity
  static List<T> filterDuplicates<T, K>(
    List<T> existingList,
    List<T> newList,
    K Function(T) keyExtractor,
  ) {
    final existingKeys = existingList.map(keyExtractor).toSet();
    return newList.where((item) => !existingKeys.contains(keyExtractor(item))).toList();
  }

  /// Merge lists efficiently with duplicate checking
  static List<T> mergeListsEfficiently<T, K>(
    List<T> existingList,
    List<T> newList,
    K Function(T) keyExtractor,
  ) {
    final filtered = filterDuplicates(existingList, newList, keyExtractor);
    return [...existingList, ...filtered];
  }

  /// Paginate large lists to prevent memory issues
  static List<T> paginateList<T>(List<T> list, int page, int pageSize) {
    final startIndex = (page - 1) * pageSize;
    final endIndex = (startIndex + pageSize).clamp(0, list.length);

    if (startIndex >= list.length) return [];
    return list.sublist(startIndex, endIndex);
  }

  /// Clean up old items from large lists to prevent memory bloat
  static List<T> cleanupOldItems<T>(List<T> list, int maxItems) {
    if (list.length <= maxItems) return list;

    // Keep only the most recent items
    return list.sublist(list.length - maxItems);
  }
}

/// Memory management utilities
class MemoryUtils {
  /// Check if list is getting too large and needs cleanup
  static bool shouldCleanupList(List list, int threshold) {
    return list.length > threshold;
  }

  /// Get memory-safe batch size based on item complexity
  static int getOptimalBatchSize<T>(List<T> items) {
    if (items.isEmpty) return 50;

    // Estimate complexity based on type
    if (T.toString().contains('Post') || T.toString().contains('Video')) {
      return 20; // Complex objects with media
    } else if (T.toString().contains('Chat') || T.toString().contains('Message')) {
      return 50; // Medium complexity
    } else {
      return 100; // Simple objects
    }
  }
}
