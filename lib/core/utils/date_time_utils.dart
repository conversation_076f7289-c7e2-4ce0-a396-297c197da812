import 'package:intl/date_symbol_data_local.dart';
import 'package:intl/intl.dart';

const String dateTimeFormatPattern = 'dd/MM/yyyy';

const String dateTimeFormatPatternWithTime24 = 'dd-MM-yyyy HH:mm'; // 24-hour format
const String dateTimeFormatPatternWithTime12 = 'dd-MM-yyyy hh:mm a'; // 12-hour format with AM/PM
const String customDateTimeFormatPattern = 'MMM d, yyyy h:mm a'; // Aug 9, 2024 2:32 pm

const String dayMonthPattern = 'EEEE, d MMM'; // Sunday, 12 Jun
const String timeOnlyPattern = 'h:mm a'; // 12:00 AM

extension DateTimeExtension on DateTime {
  String format([
    String pattern = dateTimeFormatPattern,
    String? locale,
  ]) {
    if (locale != null && locale.isNotEmpty) {
      initializeDateFormatting(locale); // Initialize date formatting for the specified locale
    }
    return DateFormat(pattern, locale).format(this); // Format the date using the provided pattern and locale
  }

  // Method to format using the 'dd-MM-yyyy HH:mm:ss' pattern (24-hour format)
  String formatWithTime24([String? locale]) {
    return format(dateTimeFormatPatternWithTime24, locale);
  }

  // Method to format using the 'dd-MM-yyyy hh:mm:ss a' pattern (12-hour format with AM/PM)
  String formatWithTime12([String? locale]) {
    return format(dateTimeFormatPatternWithTime12, locale);
  }

  // Custom method to format using the 'MMM d, yyyy h:mm a' pattern (e.g., Aug 9, 2024 2:32 pm)
  String formatWithCustomPattern([String? locale]) {
    return format(customDateTimeFormatPattern, locale);
  }
}

extension TimeAgo on DateTime {
  String createdTimeAgo() {
    final now = DateTime.now();
    final difference = now.difference(this);

    if (difference.inSeconds < 60) {
      return '${difference.inSeconds} seconds ago';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hours ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      return '${(difference.inDays / 7).floor()} weeks ago';
    } else if (difference.inDays < 365) {
      return '${(difference.inDays / 30).floor()} months ago';
    } else {
      return '${(difference.inDays / 365).floor()} years ago';
    }
  }
}

extension DateTimeChatExtension on DateTime {
  String formatForChatMessage() {
    final now = DateTime.now().toUtc().add(timeZoneOffset); // Adjust for timezone

    if (_isSameDay(this, now)) {
      return 'Today at ${DateFormat('hh:mm a').format(this)}';
    } else if (_isSameDay(this, now.subtract(const Duration(days: 1)))) {
      return 'Yesterday at ${DateFormat('hh:mm a').format(this)}';
    } else {
      return DateFormat('EEE \'at\' hh:mm a').format(this); // Example: "Mon at 03:45 PM"
    }
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year && date1.month == date2.month && date1.day == date2.day;
  }

  // Method to format using the 'EEEE, d MMMM' pattern (e.g., Sunday, 12 Jun)
  String formatWithDayMonth([String? locale]) {
    return format(dayMonthPattern, locale);
  }

  // Method to format using the 'h:mm a' pattern (e.g., 12:00 AM)
  String formatWithTimeOnly([String? locale]) {
    return format(timeOnlyPattern, locale);
  }
}
