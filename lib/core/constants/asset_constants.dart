import 'package:flowkar/core/generated/assets.gen.dart';

class AssetConstants {
  // onboarding
  static String svgOnboarding = Assets.images.pngs.onboarding.pngOnbording.path;
  static String svgOnbordingRightArrow = Assets.images.svg.onboarding.svgOnbordingRightArrow.path;
  static String svgOnboarding2 = Assets.images.svg.onboarding.svgOnbording2.path;
  static String svgOnboarding3 = Assets.images.svg.onboarding.svgOnbording3.path;
  static String svgOnboarding4 = Assets.images.svg.onboarding.svgOnbording4.path;
  static String pngOnbordingRightArrow = Assets.images.pngs.onboarding.pngOnbordingRightArrow.path;
  static String pngOnboarding2 = Assets.images.pngs.onboarding.pngOnbording2.path;
  static String pngOnboarding3 = Assets.images.pngs.onboarding.pngOnbording3.path;
  static String pngOnboarding4 = Assets.images.pngs.onboarding.pngOnbording4.path;

  // icons
  static String icSearch = Assets.images.icons.other.icSearch.path;
  static String icClose = Assets.images.icons.other.icClose.path;
  static String icMore = Assets.images.icons.other.icMore.path;
  static String icHastag = Assets.images.icons.other.icHastag.path;
  static String icElips = Assets.images.icons.other.icElips.path;
  static String icDelete = Assets.images.icons.other.icDelete.path;

  //pngs
  static String pngNotFound = Assets.images.svg.other.svgNodatafound.path;
  static String flowkar = Assets.images.pngs.flowkar.path;

  //pngs/other
  static String pngPlaceholder = Assets.images.pngs.other.pngPlaceholder.path;

  //pngs/exception
  static String pngNoResultFound = Assets.images.pngs.exception.pngNoResultFound.path;
  static String pngNoNotification = Assets.images.pngs.exception.pngNoNotification.path;
  static String pngNoInternet = Assets.images.pngs.exception.pngNoInternet.path;
  static String pngNoMessage = Assets.images.pngs.exception.pngNoMessage.path;

  //pngs / Authentication
  static String pngPassword = Assets.images.pngs.authentication.pngPassword.path;
  static String pngEmail = Assets.images.svg.authentication.svgEmail.path;
  static String pngSeen = Assets.images.pngs.authentication.pngSeen.path;
  static String pngHide = Assets.images.pngs.authentication.pngHide.path;
  static String pngForgotPasswordHeader = Assets.images.pngs.authentication.pngForgotPasswordHeader.path;
  static String pngBack = Assets.images.svg.authentication.icBackArrow.path;
  static String pngCreateNewPassword = Assets.images.pngs.authentication.pngCreateNewPassword.path;
  static String pngVerifyEmail = Assets.images.pngs.authentication.pngVerifyEmail.path;
  static String pngCreateAccount = Assets.images.pngs.authentication.pngCreateAccount.path;
  static String pngUser = Assets.images.svg.other.svgUserProfile.path;
  static String pngUserReomve = Assets.images.pngs.other.pngUserBg.path;
  static String pngUserEditImage = Assets.images.svg.other.svgEditImageUserProfile.path;
  static String pngUserDeleteImage = Assets.images.svg.other.svgDeletedImageUserProfile.path;

  //pngs / discover
}
