// import 'dart:async';
// import 'dart:convert';
// import 'package:flowkar/core/utils/exports.dart';
// import 'package:flowkar/features/home_feed_screen/model/post_response_model.dart';
// import 'package:flowkar/features/home_feed_screen/model/video_response_model.dart';
// import 'package:flowkar/features/story_feature_demo/Story_view_demo/story_view/models/story_model.dart';
// import 'package:flowkar/features/chat/model/chat_message_list_model.dart';
// import 'package:flowkar/features/discover/model/search_user_model.dart';

// /// Streaming API client for progressive data loading
// class StreamingApiClient {
//   final Dio _dio;

//   StreamingApiClient(this._dio);

//   /// Stream posts progressively as they are parsed
//   Stream<PostData> streamPosts({
//     required int page,
//     required String type,
//     int batchSize = 3,
//   }) async* {
//     try {
//       final response = await _dio.get('/posts/', queryParameters: {
//         'page': page,
//         'type': type,
//       });

//       if (response.statusCode == 200) {
//         final data = response.data;
//         final results = data['results'];
//         final posts = results['posts'] as List<dynamic>;

//         // Stream posts in batches
//         for (int i = 0; i < posts.length; i += batchSize) {
//           final batch = posts.skip(i).take(batchSize);

//           for (final postJson in batch) {
//             try {
//               final post = await compute<Map<String, dynamic>, PostData>(_parsePostData, postJson);
//               yield post;
//             } catch (e) {
//               Logger.lOG('Error parsing post: $e');
//               continue;
//             }
//           }

//           // Small delay to allow UI updates
//           await Future.delayed(const Duration(milliseconds: 30));
//         }
//       }
//     } catch (e) {
//       Logger.lOG('Error streaming posts: $e');
//       rethrow;
//     }
//   }

//   /// Stream videos progressively
//   Stream<VideoData> streamVideos({
//     required int page,
//     required String type,
//     int batchSize = 2, // Smaller batch for videos
//   }) async* {
//     try {
//       final response = await _dio.get('/videos/', queryParameters: {
//         'page': page,
//         'type': type,
//       });

//       if (response.statusCode == 200) {
//         final data = response.data;
//         final results = data['results'];
//         final videos = results['data'] as List<dynamic>;

//         for (int i = 0; i < videos.length; i += batchSize) {
//           final batch = videos.skip(i).take(batchSize);

//           for (final videoJson in batch) {
//             try {
//               final video = await compute<Map<String, dynamic>, VideoData>(_parseVideoData, videoJson);
//               yield video;
//             } catch (e) {
//               Logger.lOG('Error parsing video: $e');
//               continue;
//             }
//           }

//           await Future.delayed(const Duration(milliseconds: 50));
//         }
//       }
//     } catch (e) {
//       Logger.lOG('Error streaming videos: $e');
//       rethrow;
//     }
//   }

//   /// Stream chat messages progressively
//   Stream<ChatMessageData> streamChatMessages({
//     required int userId,
//     required int page,
//     int batchSize = 5,
//   }) async* {
//     try {
//       final response = await _dio.get('/chat/messages/', queryParameters: {
//         'user_id': userId,
//         'page': page,
//       });

//       if (response.statusCode == 200) {
//         final data = response.data;
//         final results = data['results'];
//         final messages = results['data'] as List<dynamic>;

//         for (int i = 0; i < messages.length; i += batchSize) {
//           final batch = messages.skip(i).take(batchSize);

//           for (final messageJson in batch) {
//             try {
//               final message = ChatMessageData.fromJson(messageJson);
//               yield message;
//             } catch (e) {
//               Logger.lOG('Error parsing message: $e');
//               continue;
//             }
//           }

//           await Future.delayed(const Duration(milliseconds: 20));
//         }
//       }
//     } catch (e) {
//       Logger.lOG('Error streaming messages: $e');
//       rethrow;
//     }
//   }

//   /// Stream stories progressively
//   Stream<NewStory> streamStories({
//     int batchSize = 2,
//   }) async* {
//     try {
//       final response = await _dio.get('/stories/');

//       if (response.statusCode == 200) {
//         final data = response.data;
//         final stories = data['data'] as List<dynamic>;

//         for (int i = 0; i < stories.length; i += batchSize) {
//           final batch = stories.skip(i).take(batchSize);

//           for (final storyJson in batch) {
//             try {
//               final story = await compute<Map<String, dynamic>, NewStory>(_parseStoryData, storyJson);
//               yield story;
//             } catch (e) {
//               Logger.lOG('Error parsing story: $e');
//               continue;
//             }
//           }

//           await Future.delayed(const Duration(milliseconds: 40));
//         }
//       }
//     } catch (e) {
//       Logger.lOG('Error streaming stories: $e');
//       rethrow;
//     }
//   }

//   /// Stream search results progressively
//   Stream<SearchUserData> streamSearchUsers({
//     required String query,
//     int batchSize = 4,
//   }) async* {
//     try {
//       final response = await _dio.get('/search/users/', queryParameters: {
//         'q': query,
//       });

//       if (response.statusCode == 200) {
//         final data = response.data;
//         final users = data['data'] as List<dynamic>;

//         for (int i = 0; i < users.length; i += batchSize) {
//           final batch = users.skip(i).take(batchSize);

//           for (final userJson in batch) {
//             try {
//               final user = SearchUserData.fromJson(userJson);
//               yield user;
//             } catch (e) {
//               Logger.lOG('Error parsing user: $e');
//               continue;
//             }
//           }

//           await Future.delayed(const Duration(milliseconds: 25));
//         }
//       }
//     } catch (e) {
//       Logger.lOG('Error streaming search users: $e');
//       rethrow;
//     }
//   }

//   /// Progressive loading with chunked response processing
//   Future<T> loadProgressively<T>({
//     required String endpoint,
//     required Map<String, dynamic> queryParameters,
//     required T Function(Map<String, dynamic>) parser,
//     required void Function(double progress) onProgress,
//     int chunkSize = 1024, // Bytes
//   }) async {
//     try {
//       final response = await _dio.get(
//         endpoint,
//         queryParameters: queryParameters,
//         options: Options(
//           responseType: ResponseType.stream,
//         ),
//       );

//       final stream = response.data.stream as Stream<List<int>>;
//       final chunks = <int>[];
//       int totalBytes = 0;
//       final contentLength = int.tryParse(response.headers.value('content-length') ?? '0') ?? 0;

//       await for (final chunk in stream) {
//         chunks.addAll(chunk);
//         totalBytes += chunk.length;

//         if (contentLength > 0) {
//           final progress = totalBytes / contentLength;
//           onProgress(progress.clamp(0.0, 1.0));
//         }
//       }

//       final jsonString = utf8.decode(chunks);
//       final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;

//       return parser(jsonData);
//     } catch (e) {
//       Logger.lOG('Error in progressive loading: $e');
//       rethrow;
//     }
//   }
// }

// // Isolate functions for parsing data
// PostData _parsePostData(Map<String, dynamic> json) {
//   return PostData.fromJson(json);
// }

// VideoData _parseVideoData(Map<String, dynamic> json) {
//   return VideoData.fromJson(json);
// }

// NewStory _parseStoryData(Map<String, dynamic> json) {
//   return NewStory.fromJson(json);
// }

// /// Mixin for easy streaming API integration
// mixin StreamingApiMixin {
//   StreamingApiClient? _streamingClient;

//   void initializeStreamingClient(Dio dio) {
//     _streamingClient = StreamingApiClient(dio);
//   }

//   StreamingApiClient get streamingClient => _streamingClient!;
// }

// /// Progressive loading controller for managing multiple streams
// class ProgressiveLoadingController {
//   final Map<String, StreamSubscription> _subscriptions = {};
//   final Map<String, List<dynamic>> _loadedData = {};
//   final StreamController<ProgressiveLoadingEvent> _eventController = StreamController.broadcast();

//   Stream<ProgressiveLoadingEvent> get events => _eventController.stream;

//   /// Start progressive loading for a specific data type
//   void startLoading<T>({
//     required String key,
//     required Stream<T> dataStream,
//     required void Function(List<T>) onUpdate,
//     int maxItems = 1000,
//   }) {
//     // Cancel existing subscription if any
//     _subscriptions[key]?.cancel();

//     // Initialize data list
//     _loadedData[key] = <T>[];

//     _eventController.add(ProgressiveLoadingEvent.started(key));

//     _subscriptions[key] = dataStream.listen(
//       (item) {
//         final currentList = _loadedData[key] as List<T>;
//         currentList.add(item);

//         // Limit list size to prevent memory issues
//         if (currentList.length > maxItems) {
//           currentList.removeRange(0, currentList.length - maxItems);
//         }

//         onUpdate(List.from(currentList));
//         _eventController.add(ProgressiveLoadingEvent.itemAdded(key, currentList.length));
//       },
//       onError: (error) {
//         _eventController.add(ProgressiveLoadingEvent.error(key, error.toString()));
//       },
//       onDone: () {
//         _eventController.add(ProgressiveLoadingEvent.completed(key));
//       },
//     );
//   }

//   /// Stop loading for a specific key
//   void stopLoading(String key) {
//     _subscriptions[key]?.cancel();
//     _subscriptions.remove(key);
//     _eventController.add(ProgressiveLoadingEvent.stopped(key));
//   }

//   /// Clear all loaded data
//   void clearAll() {
//     for (final subscription in _subscriptions.values) {
//       subscription.cancel();
//     }
//     _subscriptions.clear();
//     _loadedData.clear();
//   }

//   /// Get current loaded items for a key
//   List<T> getLoadedItems<T>(String key) {
//     return List<T>.from(_loadedData[key] ?? <T>[]);
//   }

//   void dispose() {
//     clearAll();
//     _eventController.close();
//   }
// }

// /// Events for progressive loading
// class ProgressiveLoadingEvent {
//   final String key;
//   final ProgressiveLoadingEventType type;
//   final dynamic data;

//   const ProgressiveLoadingEvent._(this.key, this.type, this.data);

//   factory ProgressiveLoadingEvent.started(String key) =>
//       ProgressiveLoadingEvent._(key, ProgressiveLoadingEventType.started, null);

//   factory ProgressiveLoadingEvent.itemAdded(String key, int count) =>
//       ProgressiveLoadingEvent._(key, ProgressiveLoadingEventType.itemAdded, count);

//   factory ProgressiveLoadingEvent.completed(String key) =>
//       ProgressiveLoadingEvent._(key, ProgressiveLoadingEventType.completed, null);

//   factory ProgressiveLoadingEvent.error(String key, String error) =>
//       ProgressiveLoadingEvent._(key, ProgressiveLoadingEventType.error, error);

//   factory ProgressiveLoadingEvent.stopped(String key) =>
//       ProgressiveLoadingEvent._(key, ProgressiveLoadingEventType.stopped, null);
// }

// enum ProgressiveLoadingEventType {
//   started,
//   itemAdded,
//   completed,
//   error,
//   stopped,
// }
