import 'package:flowkar/core/utils/exports.dart';

class MyAppThemeHelper {
  static const Color primaryColor = Color(0XFF563D39);
  static const Color secondaryColor = Color(0xFFE8E2DF);
  // static const Color secondaryColor = Color(0XFF563D39);
  static const Color whiteColor = Color(0xFFFFFFFF);

  static ThemeData get lightTheme {
    return ThemeData(
      colorScheme: ColorScheme.light(
        primary: primaryColor,
        secondary: secondaryColor,
        onPrimary: whiteColor,
        onSecondary: whiteColor,
        surface: whiteColor,
        onSurface: Colors.black,
      ),
      brightness: Brightness.light,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      textTheme: GoogleFonts.ubuntuTextTheme().apply(
        bodyColor: primaryColor,
        displayColor: primaryColor,
      ),
      appBarTheme: AppBarTheme(
        color: primaryColor,
        titleTextStyle: GoogleFonts.ubuntu(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: whiteColor,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0),
          borderSide: BorderSide(color: primaryColor.withOpacity(0.2), width: 1),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0),
          borderSide: BorderSide(color: primaryColor.withOpacity(0.2), width: 1),
        ),
        errorMaxLines: 2,
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0),
          borderSide: BorderSide(color: primaryColor.withOpacity(0.2), width: 2),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          disabledBackgroundColor: primaryColor.withOpacity(0.5),
          backgroundColor: primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
          ),
        ),
      ),
    );
  }

  // static ThemeData get darkTheme {
  //   return ThemeData(
  //     colorScheme: ColorScheme.dark(
  //       primary: primaryColor,
  //       secondary: secondaryColor,
  //       onPrimary: whiteColor,
  //       onSecondary: Colors.black,
  //       surface: Color(0xFF1E1E1E),
  //       onSurface: whiteColor,
  //     ),
  //     brightness: Brightness.dark,
  //     splashColor: Colors.transparent,
  //     highlightColor: Colors.transparent,
  //     textTheme: GoogleFonts.ubuntuTextTheme().apply(
  //       bodyColor: whiteColor,
  //       displayColor: whiteColor,
  //     ),
  //     appBarTheme: AppBarTheme(
  //       color: primaryColor,
  //       titleTextStyle: GoogleFonts.ubuntu(
  //         fontSize: 20,
  //         fontWeight: FontWeight.bold,
  //         color: whiteColor,
  //       ),
  //     ),
  //     inputDecorationTheme: InputDecorationTheme(
  //       border: OutlineInputBorder(
  //         borderRadius: BorderRadius.circular(8.0),
  //         borderSide: BorderSide(color: primaryColor, width: 1),
  //       ),
  //       enabledBorder: OutlineInputBorder(
  //         borderRadius: BorderRadius.circular(8.0),
  //         borderSide: BorderSide(color: primaryColor, width: 1),
  //       ),
  //       errorMaxLines: 2,
  //       focusedBorder: OutlineInputBorder(
  //         borderRadius: BorderRadius.circular(8.0),
  //         borderSide: BorderSide(color: primaryColor, width: 2),
  //       ),
  //     ),
  //     elevatedButtonTheme: ElevatedButtonThemeData(
  //       style: ElevatedButton.styleFrom(
  //         disabledBackgroundColor: primaryColor.withOpacity(0.5),
  //         backgroundColor: primaryColor,
  //         shape: RoundedRectangleBorder(
  //           borderRadius: BorderRadius.circular(16.0),
  //         ),
  //       ),
  //     ),
  //   );
  // }
}

class CustomColors {
  final Color primaryColor;
  final Color borderColor;
  final Color socialContainer;
  final Color socialButton;
  final Color white;
  final Color black;
  final Color dividerColor;
  final Color uploadPostSocialBTN;
  final Color tabcolor;
  final Color fillcolor;
  final Color greylite;
  final Color greyContainerBg;
  final Color transparent;
  final Color subtitlecolor;
  final Color subHeadingcolor;
  final Color authsubtitlecolor;
  final Color messagecolor;
  final Color otpBorderColor;
  final Color selectImageBgColor;
  final Color bottomsheetHendalColor;
  final Color settingCardShadowColor;
  final Color settingCardIconBGColor;
  final Color logoutTextColor;
  final Color blueColor;
  final Color greydivider;
  final Color disableButtonColor;
  final Color greenColor;

  // final Color shimmerColor;

  const CustomColors({
    required this.primaryColor,
    required this.borderColor,
    required this.socialContainer,
    required this.socialButton,
    required this.white,
    required this.black,
    required this.dividerColor,
    required this.uploadPostSocialBTN,
    required this.tabcolor,
    required this.fillcolor,
    required this.greylite,
    required this.greyContainerBg,
    required this.transparent,
    required this.subtitlecolor,
    required this.subHeadingcolor,
    required this.authsubtitlecolor,
    required this.messagecolor,
    required this.otpBorderColor,
    required this.selectImageBgColor,
    required this.bottomsheetHendalColor,
    required this.settingCardShadowColor,
    required this.settingCardIconBGColor,
    required this.logoutTextColor,
    required this.blueColor,
    required this.greydivider,
    required this.disableButtonColor,
    required this.greenColor,
    // required this.shimmerColor,
  });

  static const light = CustomColors(
    primaryColor: Color(0XFF563D39),
    borderColor: Color(0xff563D39),
    socialContainer: Color(0xffEFEBE9),
    socialButton: Colors.white,
    white: Colors.white,
    black: Colors.black,
    dividerColor: Color(0xff555555),
    otpBorderColor: Color(0xffE0E0E0),
    uploadPostSocialBTN: Color(0xffF5F5F5),
    tabcolor: Color(0xffDFDCDB),
    fillcolor: Color(0xffEFEFF0),
    greylite: Color(0xff929290),
    greyContainerBg: Color(0xffF4F4F4),
    subtitlecolor: Color(0xff292D32),
    authsubtitlecolor: Color(0xffAA8882),
    subHeadingcolor: Color(0xff74655E),
    messagecolor: Color(0XFFF1EFEF),
    selectImageBgColor: Color(0XFFF8F8F8),
    transparent: Colors.transparent,
    bottomsheetHendalColor: Color(0XFFD9D9D9),
    settingCardShadowColor: Color(0XFF063336),
    settingCardIconBGColor: Color(0XFFB9C1D1),
    logoutTextColor: Color(0XFFEC2A38),
    blueColor: Colors.blue,
    greydivider: Color(0xffF0F0F0),
    disableButtonColor: Color(0xffDAD6D6),
    greenColor: Color(0xff00AE0C),
  );

  static const dark = CustomColors(
    primaryColor: Color(0xFF1E1E1E),
    borderColor: Color(0xff563D39),
    socialContainer: Color.fromARGB(77, 239, 235, 233),
    socialButton: Colors.white38,
    white: Colors.white38,
    black: Colors.white,
    dividerColor: Color(0xff555555),
    otpBorderColor: Color(0xffE0E0E0),
    uploadPostSocialBTN: Color(0xffF5F5F5),
    tabcolor: Color(0xffDFDCDB),
    fillcolor: Color(0xffEFEFF0),
    greylite: Color(0xff929290),
    transparent: Colors.transparent,
    greyContainerBg: Color(0xffF4F4F4),
    subtitlecolor: Color(0xff292D32),
    authsubtitlecolor: Color(0xffAA8882),
    subHeadingcolor: Color(0xff74655E),
    selectImageBgColor: Color(0XFFF8F8F8),
    messagecolor: Color(0XFFF1EFEF),
    bottomsheetHendalColor: Color(0XFFD9D9D9),
    settingCardShadowColor: Color(0XFF063336),
    settingCardIconBGColor: Color(0XFFB9C1D1),
    logoutTextColor: Color(0XFFEC2A38),
    blueColor: Colors.blue,
    greydivider: Color(0xffF0F0F0),
    disableButtonColor: Color(0xffDAD6D6),
    greenColor: Color(0xff00AE0C),
  );
}

extension ThemeDataCustomColors on ThemeData {
  CustomColors get customColors => brightness == Brightness.dark ? CustomColors.dark : CustomColors.light;
}
