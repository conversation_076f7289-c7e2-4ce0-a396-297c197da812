import 'dart:convert';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/authentication/model/stored_user_account_model.dart';

class MultiAccountManager {
  static const String _storedAccountsKey = Prefkeys.STORED_ACCOUNTS;
  static const String _currentAccountIndexKey = Prefkeys.CURRENT_ACCOUNT_INDEX;

  /// Get all stored accounts
  static Future<StoredAccountsList?> getStoredAccounts() async {
    try {
      final String? accountsJson = Prefobj.preferences?.get(_storedAccountsKey);
      if (accountsJson != null && accountsJson.isNotEmpty) {
        final Map<String, dynamic> json = jsonDecode(accountsJson);
        return StoredAccountsList.fromJson(json);
      }
      return null;
    } catch (e) {
      Logger.lOG("Error getting stored accounts: $e");
      return null;
    }
  }

  /// Save accounts list to storage
  static Future<void> saveStoredAccounts(StoredAccountsList accountsList) async {
    try {
      final String accountsJson = jsonEncode(accountsList.toJson());
      await Prefobj.preferences?.put(_storedAccountsKey, accountsJson);
      await Prefobj.preferences?.put(_currentAccountIndexKey, accountsList.currentAccountIndex);
      Logger.lOG("Stored accounts saved successfully");
    } catch (e) {
      Logger.lOG("Error saving stored accounts: $e");
    }
  }

  /// Add a new account to the stored accounts list
  static Future<void> addAccount(StoredUserAccount newAccount, {bool setAsCurrent = true}) async {
    try {
      StoredAccountsList? existingAccounts = await getStoredAccounts();

      List<StoredUserAccount> accounts = existingAccounts?.accounts ?? [];
      int currentIndex = existingAccounts?.currentAccountIndex ?? 0;

      // Check if account already exists (by brandId and userId)
      final existingIndex = accounts.indexWhere(
        (account) => account.brandId == newAccount.brandId && account.userId == newAccount.userId,
      );

      if (existingIndex != -1) {
        // Update existing account
        accounts[existingIndex] = newAccount;
        Logger.lOG("Updated existing account for brandId: ${newAccount.brandId}");

        // If updating existing account and setAsCurrent is true, set it as current
        if (setAsCurrent) {
          currentIndex = existingIndex;
        }
      } else {
        // Add new account
        accounts.add(newAccount);
        Logger.lOG("Added new account for brandId: ${newAccount.brandId}");

        // Only set as current if this is the first account or setAsCurrent is true
        if (setAsCurrent || accounts.length == 1) {
          currentIndex = accounts.length - 1;
        }
        // If setAsCurrent is false, preserve the existing current account index
      }

      final updatedAccountsList = StoredAccountsList(
        accounts: accounts,
        currentAccountIndex: currentIndex,
      );

      await saveStoredAccounts(updatedAccountsList);

      // Only update current user preferences if this account is set as current
      if (setAsCurrent || (existingIndex != -1 && existingIndex == currentIndex)) {
        await _updateCurrentUserPreferences(newAccount);
      }
    } catch (e) {
      Logger.lOG("Error adding account: $e");
    }
  }

  /// Switch to a specific account by index
  static Future<bool> switchToAccount(int accountIndex) async {
    try {
      StoredAccountsList? accountsList = await getStoredAccounts();

      if (accountsList == null || accountIndex < 0 || accountIndex >= accountsList.accounts.length) {
        Logger.lOG("Invalid account index or no accounts found");
        return false;
      }

      final updatedAccountsList = accountsList.copyWith(currentAccountIndex: accountIndex);
      await saveStoredAccounts(updatedAccountsList);

      // Update current user preferences with selected account data
      final selectedAccount = accountsList.accounts[accountIndex];
      await _updateCurrentUserPreferences(selectedAccount);

      Logger.lOG("Switched to account: ${selectedAccount.name} (Brand ID: ${selectedAccount.brandId})");
      return true;
    } catch (e) {
      Logger.lOG("Error switching account: $e");
      return false;
    }
  }

  /// Get current active account
  static Future<StoredUserAccount?> getCurrentAccount() async {
    try {
      StoredAccountsList? accountsList = await getStoredAccounts();
      return accountsList?.currentAccount;
    } catch (e) {
      Logger.lOG("Error getting current account: $e");
      return null;
    }
  }

  /// Remove an account from stored accounts
  static Future<void> removeAccount(int accountIndex) async {
    try {
      StoredAccountsList? accountsList = await getStoredAccounts();

      if (accountsList == null || accountIndex < 0 || accountIndex >= accountsList.accounts.length) {
        Logger.lOG("Invalid account index or no accounts found");
        return;
      }

      List<StoredUserAccount> accounts = List.from(accountsList.accounts);
      accounts.removeAt(accountIndex);

      // Adjust current account index if necessary
      int newCurrentIndex = accountsList.currentAccountIndex;
      if (accountIndex == accountsList.currentAccountIndex) {
        // If removing current account, switch to first available account
        newCurrentIndex = accounts.isNotEmpty ? 0 : -1;
      } else if (accountIndex < accountsList.currentAccountIndex) {
        // If removing account before current, adjust index
        newCurrentIndex = accountsList.currentAccountIndex - 1;
      }

      final updatedAccountsList = StoredAccountsList(
        accounts: accounts,
        currentAccountIndex: newCurrentIndex,
      );

      await saveStoredAccounts(updatedAccountsList);
      Logger.lOG("Account removed successfully");
    } catch (e) {
      Logger.lOG("Error removing account: $e");
    }
  }

  /// Remove an account from stored accounts by user ID
  static Future<bool> removeAccountByUserId(int userId) async {
    try {
      StoredAccountsList? accountsList = await getStoredAccounts();

      if (accountsList == null || accountsList.accounts.isEmpty) {
        Logger.lOG("No accounts found to remove");
        return false;
      }

      // Find the account with the specified user ID
      final accountIndex = accountsList.accounts.indexWhere(
        (account) => account.userId == userId,
      );

      if (accountIndex == -1) {
        Logger.lOG("Account with user ID $userId not found");
        return false;
      }

      List<StoredUserAccount> accounts = List.from(accountsList.accounts);
      final removedAccount = accounts.removeAt(accountIndex);

      // Adjust current account index if necessary
      int newCurrentIndex = accountsList.currentAccountIndex;
      if (accountIndex == accountsList.currentAccountIndex) {
        // If removing current account, switch to first available account
        newCurrentIndex = accounts.isNotEmpty ? 0 : -1;
      } else if (accountIndex < accountsList.currentAccountIndex) {
        // If removing account before current, adjust index
        newCurrentIndex = accountsList.currentAccountIndex - 1;
      }

      final updatedAccountsList = StoredAccountsList(
        accounts: accounts,
        currentAccountIndex: newCurrentIndex,
      );

      await saveStoredAccounts(updatedAccountsList);
      Logger.lOG("Account removed successfully: ${removedAccount.name} (User ID: $userId)");
      return true;
    } catch (e) {
      Logger.lOG("Error removing account by user ID: $e");
      return false;
    }
  }

  /// Clear all stored accounts (used during logout)
  static Future<void> clearAllAccounts() async {
    try {
      await Prefobj.preferences?.delete(_storedAccountsKey);
      await Prefobj.preferences?.delete(_currentAccountIndexKey);
      Logger.lOG("All stored accounts cleared");
    } catch (e) {
      Logger.lOG("Error clearing stored accounts: $e");
    }
  }

  /// Update current user preferences with account data
  static Future<void> _updateCurrentUserPreferences(StoredUserAccount account) async {
    try {
      await OptimizedStorage.batchPut({
        Prefkeys.BRANDID: account.brandId,
        Prefkeys.NAME: account.name,
        Prefkeys.PROFILE: account.profileImage,
        Prefkeys.USERNAME: account.username,
        Prefkeys.USER_ID: account.userId.toString(),
        Prefkeys.AUTHTOKEN: account.token,
      });
      Logger.lOG("Updated current user preferences for account: ${account.name}");
    } catch (e) {
      Logger.lOG("Error updating current user preferences: $e");
    }
  }

  /// Create StoredUserAccount from current login response
  static StoredUserAccount createFromLoginResponse({
    required int brandId,
    required String name,
    required String profileImage,
    required String username,
    required int userId,
    required String token,
  }) {
    return StoredUserAccount(
      brandId: brandId,
      name: name,
      profileImage: profileImage,
      username: username,
      userId: userId,
      token: token,
    );
  }

  /// Check if there are any stored accounts
  static Future<bool> hasStoredAccounts() async {
    try {
      StoredAccountsList? accountsList = await getStoredAccounts();
      return accountsList != null && accountsList.accounts.isNotEmpty;
    } catch (e) {
      Logger.lOG("Error checking stored accounts: $e");
      return false;
    }
  }

  /// Update current account data (useful after profile edits)
  static Future<void> updateCurrentAccountData({String? name, String? username, String? profileImage}) async {
    try {
      StoredAccountsList? accountsList = await getStoredAccounts();
      if (accountsList == null || accountsList.accounts.isEmpty) {
        Logger.lOG("No stored accounts found to update");
        return;
      }

      final currentAccount = accountsList.currentAccount;
      if (currentAccount == null) {
        Logger.lOG("No current account found to update");
        return;
      }

      // Create updated account with new data
      final updatedAccount = currentAccount.copyWith(
        name: name ?? currentAccount.name,
        username: username ?? currentAccount.username,
        profileImage: profileImage ?? currentAccount.profileImage,
      );

      // Update the account in the list
      final updatedAccounts = List<StoredUserAccount>.from(accountsList.accounts);
      updatedAccounts[accountsList.currentAccountIndex] = updatedAccount;

      final updatedAccountsList = StoredAccountsList(
        accounts: updatedAccounts,
        currentAccountIndex: accountsList.currentAccountIndex,
      );

      await saveStoredAccounts(updatedAccountsList);
      Logger.lOG("Current account data updated successfully");
    } catch (e) {
      Logger.lOG("Error updating current account data: $e");
    }
  }

  /// Get account count
  static Future<int> getAccountCount() async {
    try {
      StoredAccountsList? accountsList = await getStoredAccounts();
      return accountsList?.accounts.length ?? 0;
    } catch (e) {
      Logger.lOG("Error getting account count: $e");
      return 0;
    }
  }
}
