import 'dart:io';
import 'package:flowkar/core/utils/exports.dart';
import 'package:pip/pip.dart';

/// Service class to handle Picture-in-Picture (PIP) functionality
/// Provides platform-specific PIP configuration and state management
class PipService {
  static final PipService _instance = PipService._internal();
  factory PipService() => _instance;
  PipService._internal();

  final Pip _pip = Pip();
  bool _isInitialized = false;
  bool _isPipActive = false;

  /// Callback for PIP state changes
  Function(PipState state, String? error)? onPipStateChanged;

  /// Initialize PIP service with platform-specific configuration
  Future<bool> initialize() async {
    try {
      // Check if device supports PIP
      final isSupported = await _pip.isSupported();
      if (!isSupported) {
        Logger.lOG('PIP is not supported on this device');
        debugPrint('PIP is not supported on this device');
        return false;
      }

      // Setup platform-specific PIP options
      final options = _getPlatformSpecificOptions();
      final setupResult = await _pip.setup(options);

      if (setupResult) {
        // Register state change observer
        await _pip.registerStateChangedObserver(
          PipStateChangedObserver(
            onPipStateChanged: (state, error) {
              _handlePipStateChange(state, error);
              onPipStateChanged?.call(state, error);
            },
          ),
        );

        _isInitialized = true;
        Logger.lOG('PIP service initialized successfully for portrait mode (9:16)');
        debugPrint('PIP service initialized successfully for portrait mode (9:16)');
        return true;
      } else {
        Logger.lOG('Failed to setup PIP');
        debugPrint('Failed to setup PIP');
        return false;
      }
    } catch (e) {
      Logger.lOG('Error initializing PIP service: $e');
      debugPrint('Error initializing PIP service: $e');
      return false;
    }
  }

  /// Get platform-specific PIP options
  PipOptions _getPlatformSpecificOptions() {
    if (Platform.isAndroid) {
      return PipOptions(
        autoEnterEnabled: true,
        aspectRatioX: 9, // Changed to 9:16 for portrait mode
        aspectRatioY: 16, // Changed to 9:16 for portrait mode
        sourceRectHintLeft: 0,
        sourceRectHintTop: 0,
        sourceRectHintRight: 720, // Adjusted for portrait dimensions
        sourceRectHintBottom: 1280, // Adjusted for portrait dimensions
        seamlessResizeEnabled: true,
        useExternalStateMonitor: true,
        externalStateMonitorInterval: 100,
      );
    } else if (Platform.isIOS) {
      return PipOptions(
        autoEnterEnabled: true,
        sourceContentView: 0,
        aspectRatioX: 9,
        aspectRatioY: 16,
        contentView: 0,
        preferredContentWidth: 480,
        preferredContentHeight: 270,
        controlStyle: 2, // Hide play/pause and progress bar
      );
    } else {
      return PipOptions(autoEnterEnabled: true);
    }
  }

  /// Start PIP mode
  Future<bool> startPip() async {
    if (!_isInitialized) {
      Logger.lOG('PIP service not initialized');
      debugPrint('PIP service not initialized');
      return false;
    }

    try {
      final result = await _pip.start();
      if (result) {
        Logger.lOG('PIP started successfully');
        debugPrint('PIP started successfully');
        return true;
      } else {
        Logger.lOG('Failed to start PIP');
        debugPrint('Failed to start PIP');
        return false;
      }
    } catch (e) {
      Logger.lOG('Error starting PIP: $e');
      debugPrint('Error starting PIP: $e');
      return false;
    }
  }

  /// Stop PIP mode
  Future<void> stopPip() async {
    if (!_isInitialized) {
      Logger.lOG('PIP service not initialized');
      debugPrint('PIP service not initialized');
      return;
    }

    try {
      await _pip.stop();
      Logger.lOG('PIP stopped');
      debugPrint('PIP stopped');
    } catch (e) {
      Logger.lOG('Error stopping PIP: $e');
      debugPrint('Error stopping PIP: $e');
    }
  }

  /// Check if PIP is currently active
  Future<bool> isPipActive() async {
    if (!_isInitialized) return false;

    try {
      return await _pip.isActived();
    } catch (e) {
      Logger.lOG('Error checking PIP status: $e');
      debugPrint('Error checking PIP status: $e');
      return false;
    }
  }

  /// Check if device supports PIP
  Future<bool> isSupported() async {
    try {
      return await _pip.isSupported();
    } catch (e) {
      Logger.lOG('Error checking PIP support: $e');
      debugPrint('Error checking PIP support: $e');
      return false;
    }
  }

  /// Check if auto-enter PIP is supported
  Future<bool> isAutoEnterSupported() async {
    try {
      return await _pip.isAutoEnterSupported();
    } catch (e) {
      Logger.lOG('Error checking auto-enter PIP support: $e');
      debugPrint('Error checking auto-enter PIP support: $e');
      return false;
    }
  }

  /// Handle PIP state changes
  void _handlePipStateChange(PipState state, String? error) {
    switch (state) {
      case PipState.pipStateStarted:
        _isPipActive = true;
        Logger.lOG('PIP state: Started');
        debugPrint('PIP state: Started');
        break;
      case PipState.pipStateStopped:
        _isPipActive = false;
        Logger.lOG('PIP state: Stopped');
        debugPrint('PIP state: Stopped');
        break;
      case PipState.pipStateFailed:
        _isPipActive = false;
        Logger.lOG('PIP state: Failed - $error');
        debugPrint('PIP state: Failed - $error');
        break;
    }
  }

  /// Update PIP configuration
  Future<bool> updateConfiguration(PipOptions options) async {
    if (!_isInitialized) {
      Logger.lOG('PIP service not initialized');
      debugPrint('PIP service not initialized');
      return false;
    }

    try {
      return await _pip.setup(options);
    } catch (e) {
      Logger.lOG('Error updating PIP configuration: $e');
      debugPrint('Error updating PIP configuration: $e');
      return false;
    }
  }

  /// Dispose PIP service and clean up resources
  Future<void> dispose() async {
    if (!_isInitialized) return;

    try {
      await _pip.unregisterStateChangedObserver();
      await _pip.dispose();
      _isInitialized = false;
      _isPipActive = false;
      Logger.lOG('PIP service disposed');
      debugPrint('PIP service disposed');
    } catch (e) {
      Logger.lOG('Error disposing PIP service: $e');
      debugPrint('Error disposing PIP service: $e');
    }
  }

  /// Get current PIP active status (cached)
  bool get isPipActiveCached => _isPipActive;

  /// Get initialization status
  bool get isInitialized => _isInitialized;
}
