import 'dart:io';

import 'package:flowkar/core/socket/socket_service.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/routes_observer.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/notification/one_signal_notification/one_signal_config.dart';

class FlowkarApp extends StatefulWidget {
  const FlowkarApp({super.key, required this.prefs});
  final Box prefs;

  @override
  State<FlowkarApp> createState() => _FlowkarAppState();
}

final GlobalKey<ScaffoldMessengerState> rootScaffoldMessengerKey = GlobalKey<ScaffoldMessengerState>();

class _FlowkarAppState extends State<FlowkarApp> {
  bool isLoading = true;
  String? initialRoute;

  @override
  void initState() {
    super.initState();
    _initializeNotifications();
    SocketService.initializeSocket();
  }

  Future<void> _initializeNotifications() async {
    await initializeOneSignalNotification();
  }

  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused) {
      // App is in the background
      SocketService.closeConnection();
    } else if (state == AppLifecycleState.resumed) {
      // App is in the foreground
      SocketService.initializeSocket();
      Future.delayed(const Duration(seconds: 2), () {
        Logger.lOG("RECONNECT");
        if (Prefobj.preferences?.get(Prefkeys.AUTHTOKEN) != null) {
          SocketService.emit(APIConfig.joinSocket, {
            'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
          });
          SocketService.response(
            APIConfig.joinSocket,
            (joinSocket) {},
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    Prefobj.preferences = widget.prefs;
    return BlocProviders(
      child: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themeState) {
          return BlocBuilder<LocaleBloc, LocaleState>(
            builder: (context, localeState) {
              return BlocBuilder<ConnectivityBloc, ConnectivityState>(
                builder: (context, connectivityState) {
                  return AnnotatedRegion<SystemUiOverlayStyle>(
                    value: SystemUiOverlayStyle(
                      statusBarColor: Colors.transparent,
                      statusBarIconBrightness:
                          // themeState.isDarkThemeOn
                          //     ? Brightness.light:
                          Brightness.dark,
                      systemNavigationBarColor: ThemeData().customColors.borderColor,
                      systemNavigationBarIconBrightness: Brightness.light,
                    ),
                    child: ScreenUtilInit(
                      designSize: Size(
                        MediaQuery.of(context).size.width,
                        MediaQuery.of(context).size.height,
                      ),
                      minTextAdapt: true,
                      splitScreenMode: true,
                      builder: (context, child) {
                        return ToastificationWrapper(
                          child: MaterialApp(
                            navigatorObservers: [routeObserver],
                            scaffoldMessengerKey: rootScaffoldMessengerKey,
                            debugShowCheckedModeBanner: false,
                            navigatorKey: NavigatorService.navigatorKey,
                            locale: localeState.locale,
                            supportedLocales: Lang.delegate.supportedLocales,
                            theme: MyAppThemeHelper.lightTheme,
                            themeMode: ThemeMode.light,
                            localizationsDelegates: const [
                              Lang.delegate,
                              GlobalMaterialLocalizations.delegate,
                              GlobalWidgetsLocalizations.delegate,
                              GlobalCupertinoLocalizations.delegate,
                            ],
                            title: 'Flowkar',
                            initialRoute: AppRoutes.initialRoute,
                            routes: AppRoutes.routes,
                            builder: (context, child) {
                              return MediaQuery(
                                data: MediaQuery.of(context).copyWith(
                                  highContrast: true,
                                  displayFeatures: MediaQuery.of(context).displayFeatures,
                                  gestureSettings: MediaQuery.of(context).gestureSettings,
                                  textScaler: TextScaler.noScaling,
                                  invertColors: false,
                                  boldText: false,
                                ),
                                child: SafeArea(
                                    top: false,
                                    bottom: Platform.isIOS ? false : true,
                                    left: false,
                                    right: false,
                                    child: child!),
                              );
                            },
                          ),
                        );
                      },
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}
