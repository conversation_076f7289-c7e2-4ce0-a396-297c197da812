part of 'wallet_bloc.dart';

class WalletState extends Equatable {
  final bool isLoading;
  final WalletModel? walletModel;
  final String errorMessage;

  const WalletState({
    required this.isLoading,
    this.walletModel,
    required this.errorMessage,
  });

  factory WalletState.initial() {
    return const WalletState(
      isLoading: false,
      walletModel: null,
      errorMessage: '',
    );
  }

  WalletState copyWith({
    bool? isLoading,
    WalletModel? walletModel,
    String? errorMessage,
  }) {
    return WalletState(
      isLoading: isLoading ?? this.isLoading,
      walletModel: walletModel ?? this.walletModel,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [isLoading, walletModel, errorMessage];
}
