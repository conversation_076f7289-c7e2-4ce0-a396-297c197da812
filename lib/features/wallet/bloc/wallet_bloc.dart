import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/wallet/model/wallet_model.dart';

part 'wallet_event.dart';
part 'wallet_state.dart';

class WalletBloc extends Bloc<WalletEvent, WalletState> {
  final ApiClient apiClient = ApiClient(Dio());

  WalletBloc() : super(WalletState.initial()) {
    on<GetWalletDataEvent>(_onGetWalletData);
  }

  Future<void> _onGetWalletData(GetWalletDataEvent event, Emitter<WalletState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      emit(state.copyWith(isLoading: true));

      final walletModel = await apiClient.getWalletData(
        logInUserId: loginuserId,
        brand: brandId.toString(),
      );

      if (walletModel.status == true) {
        emit(state.copyWith(isLoading: false, walletModel: walletModel));
      } else {
        emit(state.copyWith(isLoading: false));
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isLoading: false));
    }
  }
}
