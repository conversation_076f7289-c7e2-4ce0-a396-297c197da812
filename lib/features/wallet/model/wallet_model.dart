WalletModel deserializeWalletModel(Map<String, dynamic> json) => WalletModel.fromJson(json);

class WalletModel {
  final bool status;
  final String message;
  final WalletData data;

  WalletModel({
    required this.status,
    required this.message,
    required this.data,
  });

  factory WalletModel.fromJson(Map<String, dynamic> json) {
    return WalletModel(
      status: json['status'],
      message: json['message'],
      data: WalletData.fromJson(json['data']),
    );
  }

  WalletModel copyWith({
    bool? status,
    String? message,
    WalletData? data,
  }) {
    return WalletModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

class WalletData {
  final int id;
  final String name;
  final String profilePicture;
  final int points;
  final int coins;
  final List<RewardTransaction> rewardTransaction;

  WalletData({
    required this.id,
    required this.name,
    required this.profilePicture,
    required this.points,
    required this.coins,
    required this.rewardTransaction,
  });

  factory WalletData.fromJson(Map<String, dynamic> json) {
    return WalletData(
      id: json['id'],
      name: json['name'],
      profilePicture: json['profile_picture'],
      points: json['points'],
      coins: json['coins'],
      rewardTransaction: (json['reward_transaction'] as List).map((e) => RewardTransaction.fromJson(e)).toList(),
    );
  }

  WalletData copyWith({
    int? id,
    String? name,
    String? profilePicture,
    int? points,
    int? coins,
    List<RewardTransaction>? rewardTransaction,
  }) {
    return WalletData(
      id: id ?? this.id,
      name: name ?? this.name,
      profilePicture: profilePicture ?? this.profilePicture,
      points: points ?? this.points,
      coins: coins ?? this.coins,
      rewardTransaction: rewardTransaction ?? this.rewardTransaction,
    );
  }
}

class RewardTransaction {
  final int points;
  final String transactionType;

  RewardTransaction({
    required this.points,
    required this.transactionType,
  });

  factory RewardTransaction.fromJson(Map<String, dynamic> json) {
    return RewardTransaction(
      points: json['points'],
      transactionType: json['transaction_type'],
    );
  }

  RewardTransaction copyWith({
    int? points,
    String? transactionType,
  }) {
    return RewardTransaction(
      points: points ?? this.points,
      transactionType: transactionType ?? this.transactionType,
    );
  }
}
