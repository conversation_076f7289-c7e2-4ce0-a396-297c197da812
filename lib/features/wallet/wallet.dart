// ignore_for_file: deprecated_member_use

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/wallet/bloc/wallet_bloc.dart';
import 'package:flowkar/features/wallet/model/wallet_model.dart';
import 'package:flowkar/features/wallet/widget/wallet_shimmer.dart';
import 'package:flowkar/features/widgets/custom/custom_conditional_scroll_physics.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';

class WalletScreen extends StatefulWidget {
  const WalletScreen({super.key});

  @override
  State<WalletScreen> createState() => _WalletScreenState();
}

class _WalletScreenState extends State<WalletScreen> {
  final ScrollController _scrollController = ScrollController();
  @override
  void initState() {
    super.initState();
    context.read<WalletBloc>().add(GetWalletDataEvent());
  }

  Future<void> _refreshFeed() async {
    final state = context.read<WalletBloc>().state;

    if (state.walletModel?.data != null) {
      state.walletModel?.data.rewardTransaction.clear();
    }

    context.read<WalletBloc>().add(GetWalletDataEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: BlocBuilder<WalletBloc, WalletState>(
        builder: (context, state) {
          if (state.isLoading) {
            return const WalletShimmerScreen();
          }
          return LiquidPullToRefresh(
            color: Theme.of(context).primaryColor.withOpacity(0.5),
            showChildOpacityTransition: false,
            backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
            onRefresh: _refreshFeed,
            child: ListView(
              controller: _scrollController,
              physics: ConditionalAlwaysScrollPhysics(controller: _scrollController),
              children: [
                _buildPointsCard(state),
                buildSizedBoxH(36),
                _buildHistorySection(state),
              ],
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          Lang.current.lbl_wallet,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  Widget _buildPointsCard(WalletState state) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Stack(
          alignment: Alignment.topCenter,
          children: [
            CustomImageView(
              width: MediaQuery.of(context).size.width / 1.05,
              imagePath: Assets.images.pngs.wallet.pngWalletPointsBg.path,
            ),
            Positioned(
              top: 18.h,
              child: Column(
                children: [
                  Container(
                    height: 73.h,
                    width: 73.w,
                    decoration: BoxDecoration(
                      color: Theme.of(context).customColors.white,
                      borderRadius: BorderRadius.circular(26.r),
                      border: Border.all(color: Theme.of(context).customColors.white, width: 8.w),
                    ),
                    clipBehavior: Clip.antiAlias,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(18.r),
                      child: CustomImageView(
                        margin: EdgeInsets.all(state.walletModel?.data.profilePicture.isEmpty ?? true ? 8.w : 0),
                        imagePath: state.walletModel?.data.profilePicture.isEmpty ?? true
                            ? AssetConstants.pngUser
                            : state.walletModel?.data.profilePicture,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        Positioned(
          top: 122.w,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 120.w,
                child: Column(
                  children: [
                    Text(abbreviateNumber(state.walletModel?.data.points ?? 0),
                        style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                            fontSize: 32.sp, color: Theme.of(context).customColors.white, fontWeight: FontWeight.w700)),
                    buildSizedBoxH(10.h),
                    Text("Your Points",
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontSize: 13.sp, color: Theme.of(context).customColors.white, fontWeight: FontWeight.w400)),
                  ],
                ),
              ),
              buildSizedBoxW(25.w),
              SizedBox(
                height: 85.h,
                child: VerticalDivider(
                  thickness: 1,
                  color: Theme.of(context).customColors.white,
                  width: 1.w,
                  endIndent: 8.h,
                  indent: 8.h,
                ),
              ),
              buildSizedBoxW(25.w),
              SizedBox(
                width: 120.w,
                child: Column(
                  children: [
                    Text(abbreviateNumber(state.walletModel?.data.coins ?? 0),
                        style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                            fontSize: 32.sp, color: Theme.of(context).customColors.white, fontWeight: FontWeight.w700)),
                    buildSizedBoxH(10.h),
                    Text("Your Coins",
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontSize: 13.sp, color: Theme.of(context).customColors.white, fontWeight: FontWeight.w400)),
                  ],
                ),
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget _buildHistorySection(WalletState state) {
    return Container(
      constraints: BoxConstraints(minHeight: MediaQuery.of(context).size.height / 1.8),
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.vertical(top: Radius.circular(30.r)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("History",
              style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w400, fontSize: 18.sp)),
          buildSizedBoxH(8),
          state.walletModel?.data.rewardTransaction.isEmpty ?? true
              ? Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: MediaQuery.of(context).size.height * 0.03),
                  child: ExceptionWidget(
                    imagePath: Assets.images.svg.exception.svgNodatafound.path,
                    showButton: false,
                    title: Lang.of(context).lbl_no_data_found,
                    subtitle: "",
                  ),
                )
              : ListView.separated(
                  itemCount: state.walletModel?.data.rewardTransaction.length ?? 0,
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.only(top: 8.h, bottom: 16.h),
                  itemBuilder: (context, index) {
                    final item = state.walletModel?.data.rewardTransaction[index];

                    return _buildHistoryItem(item);
                  },
                  separatorBuilder: (context, index) =>
                      Divider(color: Theme.of(context).primaryColor.withOpacity(0.3), thickness: 1.0, height: 1.h),
                ),
        ],
      ),
    );
  }

  Widget _buildHistoryItem(RewardTransaction? item) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 12.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              item?.transactionType ?? "",
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 14.sp),
            ),
          ),
          buildSizedBoxH(8),
          Text(
            "+${item?.points.toString() ?? "0"}",
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontSize: 18.sp,
                  color: Colors.green,
                  fontWeight: FontWeight.w500,
                ),
          ),
        ],
      ),
    );
  }
}
