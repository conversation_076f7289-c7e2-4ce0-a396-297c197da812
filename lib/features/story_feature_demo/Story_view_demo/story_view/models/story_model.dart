// // import 'package:story_view_demo/const.dart';
// // import 'package:story_view_demo/story_view/controller/story_controller.dart';
// // import 'package:story_view_demo/story_view/widgets/story_view.dart';

// class NewStoryModel {
//   NewStoryModel({
//     this.status,
//     this.message,
//     this.data,
//   });

//   NewStoryModel.fromJson(dynamic json) {
//     status = json['status'];
//     message = json['message'];
//     data = json['data'] != null ? NewStory.fromJson(json['data']) : null;
//   }

//   bool? status;
//   String? message;
//   NewStory? data;
// }

// class NewStory {
//   final int userId;
//   final String username;
//   final String userprofile;
//   final List<NewStoryItem> stories;

//   NewStory({
//     required this.userId,
//     required this.username,
//     required this.userprofile,
//     required this.stories,
//   });

//   factory NewStory.fromJson(Map<String, dynamic> json) {
//     return NewStory(
//       userId: json['user_id'],
//       username: json['username'],
//       userprofile: json['userprofile'] ?? '',
//       stories: (json['stories'] as List)
//           .map((story) => NewStoryItem.fromJson(story))
//           .toList(),
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'user_id': userId,
//       'username': username,
//       'userprofile': userprofile,
//       'stories': stories.map((e) => e.toJson()).toList(),
//     };
//   }
// }

// class NewStoryItem {
//   final int storyId;
//   final String storyType;
//   final String storyFile;
//   final bool isLiked;

//   NewStoryItem({
//     required this.storyId,
//     required this.storyType,
//     required this.storyFile,
//     required this.isLiked,
//   });

//   factory NewStoryItem.fromJson(Map<String, dynamic> json) {
//     return NewStoryItem(
//       storyId: json['story_id'],
//       storyType: json['storytype'],
//       storyFile: json['storyfile'],
//       isLiked: json['is_liked'] ?? false,
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'story_id': storyId,
//       'storytype': storyType,
//       'storyfile': storyFile,
//       'is_liked': isLiked,
//     };
//   }
// }

// class User {
//   User({
//     this.id,
//     this.username,
//     this.profile,
//   });

//   User.fromJson(dynamic json) {
//     id = json['id'];
//     username = json['username'];
//     profile = json['profile'];
//   }

//   num? id;
//   String? username;
//   String? profile;

//   Map<String, dynamic> toJson() {
//     final map = <String, dynamic>{};
//     map['id'] = id;
//     map['username'] = username;
//     map['profile'] = profile;
//     return map;
//   }
// }
NewStoryModel deserializeNewStoryModel(Map<String, dynamic> json) =>
    NewStoryModel.fromJson(json);

class NewStoryModel {
  bool? status;
  String? message;
  List<NewStory>? data;

  NewStoryModel({this.status, this.message, this.data});

  NewStoryModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <NewStory>[];
      json['data'].forEach((v) {
        data!.add(NewStory.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  // CopyWith method
  NewStoryModel copyWith({
    bool? status,
    String? message,
    List<NewStory>? data,
  }) {
    return NewStoryModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

class NewStory {
  int? userId;
  String? username;
  String? userprofile;
  List<Stories>? stories;

  NewStory({this.userId, this.username, this.userprofile, this.stories});

  NewStory.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'];
    username = json['username'];
    userprofile = json['userprofile'];
    if (json['stories'] != null) {
      stories = <Stories>[];
      json['stories'].forEach((v) {
        stories!.add(Stories.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['user_id'] = userId;
    data['username'] = username;
    data['userprofile'] = userprofile;
    if (stories != null) {
      data['stories'] = stories!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  // CopyWith method
  NewStory copyWith({
    int? userId,
    String? username,
    String? userprofile,
    List<Stories>? stories,
  }) {
    return NewStory(
      userId: userId ?? this.userId,
      username: username ?? this.username,
      userprofile: userprofile ?? this.userprofile,
      stories: stories ?? this.stories,
    );
  }
}

class Stories {
  int? storyId;
  String? title;
  String? storytype;
  String? storyfile;
  bool? isLiked;

  Stories(
      {this.storyId, this.title, this.storytype, this.storyfile, this.isLiked});

  Stories.fromJson(Map<String, dynamic> json) {
    storyId = json['story_id'];
    title = json['title'];
    storytype = json['storytype'];
    storyfile = json['storyfile'];
    isLiked = json['is_liked'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['story_id'] = storyId;
    data['title'] = title;
    data['storytype'] = storytype;
    data['storyfile'] = storyfile;
    data['is_liked'] = isLiked;
    return data;
  }

  // CopyWith method
  Stories copyWith({
    int? storyId,
    String? title,
    String? storytype,
    String? storyfile,
    bool? isLiked,
  }) {
    return Stories(
      storyId: storyId ?? this.storyId,
      title: title ?? this.title,
      storytype: storytype ?? this.storytype,
      storyfile: storyfile ?? this.storyfile,
      isLiked: isLiked ?? this.isLiked,
    );
  }
}
