class GetSinglStoryModel {
  int? count;
  String? next;
  String? previous;
  Results? results;

  GetSinglStoryModel({this.count, this.next, this.previous, this.results});

  GetSinglStoryModel.fromJson(Map<String, dynamic> json) {
    count = json['count'];
    next = json['next'];
    previous = json['previous'];
    results =
        json['results'] != null ? Results.fromJson(json['results']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['count'] = count;
    data['next'] = next;
    data['previous'] = previous;
    if (results != null) {
      data['results'] = results!.toJson();
    }
    return data;
  }
}

class Results {
  bool? status;
  String? message;
  List<SingleStoryData>? data;

  Results({this.status, this.message, this.data});

  Results.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <SingleStoryData>[];
      json['data'].forEach((v) {
        data!.add(SingleStoryData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SingleStoryData {
  int? id;
  int? userId;
  List<String>? storyData;
  String? username;
  String? profileImage;
  String? music;
  bool? isDeleted;
  bool? isArchieved;
  bool? isHighlighted;
  String? createdOn;
  bool? isLiked;
  List<Likes>? likes;
  List<Views>? views;
  int? totalViews;

  SingleStoryData({
    this.id,
    this.userId,
    this.storyData,
    this.username,
    this.profileImage,
    this.music,
    this.isDeleted,
    this.isArchieved,
    this.isHighlighted,
    this.createdOn,
    this.isLiked,
    this.likes,
    this.views,
    this.totalViews,
  });

  SingleStoryData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    storyData = json['story_data'].cast<String>();
    username = json['username'];
    profileImage = json['profile_image'];
    music = json['music'];
    isDeleted = json['is_deleted'];
    isArchieved = json['is_archieved'];
    isHighlighted = json['is_highlighted'];
    createdOn = json['created_on'];
    isLiked = json['is_liked'];
    if (json['likes'] != null) {
      likes = <Likes>[];
      json['likes'].forEach((v) {
        likes!.add(Likes.fromJson(v));
      });
    }
    if (json['views'] != null) {
      views = <Views>[];
      json['views'].forEach((v) {
        views!.add(Views.fromJson(v));
      });
    }
    totalViews = json['total_views'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['story_data'] = storyData;
    data['username'] = username;
    data['profile_image'] = profileImage;
    data['music'] = music;
    data['is_deleted'] = isDeleted;
    data['is_archieved'] = isArchieved;
    data['is_highlighted'] = isHighlighted;
    data['created_on'] = createdOn;
    data['is_liked'] = isLiked;
    if (likes != null) {
      data['likes'] = likes!.map((v) => v.toJson()).toList();
    }
    if (views != null) {
      data['views'] = views!.map((v) => v.toJson()).toList();
    }
    data['total_views'] = totalViews;
    return data;
  }

  SingleStoryData copyWith(
      {int? id,
      int? userId,
      List<String>? storyData,
      String? username,
      String? profileImage,
      String? music,
      bool? isDeleted,
      bool? isArchieved,
      bool? isHighlighted,
      String? createdOn,
      bool? isLiked,
      List<Likes>? likes,
      List<Views>? views,
      int? totalViews,
      bool? storyLike}) {
    return SingleStoryData(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      storyData: storyData ?? this.storyData,
      username: username ?? this.username,
      profileImage: profileImage ?? this.profileImage,
      music: music ?? this.music,
      isDeleted: isDeleted ?? this.isDeleted,
      isArchieved: isArchieved ?? this.isArchieved,
      isHighlighted: isHighlighted ?? this.isHighlighted,
      createdOn: createdOn ?? this.createdOn,
      isLiked: isLiked ?? this.isLiked,
      likes: likes ?? this.likes,
      views: views ?? this.views,
      totalViews: totalViews ?? this.totalViews,
    );
  }
}

class Likes {
  String? username;
  String? profileImage;

  Likes({this.username, this.profileImage});

  Likes.fromJson(Map<String, dynamic> json) {
    username = json['username'];
    profileImage = json['profile_image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['username'] = username;
    data['profile_image'] = profileImage;
    return data;
  }
}

class Views {
  String? username;
  String? profileImage;

  Views({this.username, this.profileImage});

  Views.fromJson(Map<String, dynamic> json) {
    username = json['username'];
    profileImage = json['profile_image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['username'] = username;
    data['profile_image'] = profileImage;
    return data;
  }
}
