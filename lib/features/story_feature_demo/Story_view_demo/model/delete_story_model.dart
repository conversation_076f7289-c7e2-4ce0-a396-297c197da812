import 'package:json_annotation/json_annotation.dart';

part 'delete_story_model.g.dart';

DeleteStoryModel deserializeDeleteStoryModel(Map<String, dynamic> json) => DeleteStoryModel.fromJson(json);

@JsonSerializable()
class DeleteStoryModel {
  final bool status;
  final String message;

  DeleteStoryModel({
    required this.status,
    required this.message,
  });

  factory DeleteStoryModel.fromJson(Map<String, dynamic> json) => _$DeleteStoryModelFromJson(json);

  Map<String, dynamic> toJson() => _$DeleteStoryModelToJson(this);
}
