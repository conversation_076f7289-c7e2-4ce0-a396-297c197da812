import 'package:flowkar/core/utils/exports.dart';

part 'setting_event.dart';
part 'setting_state.dart';

class SettingBloc extends Bloc<SettingEvent, SettingState> {
  SettingBloc() : super(const SettingState()) {
    on<ToggleDarkModeEvent>(_onToggleDarkMode);
  }

  void _onToggleDarkMode(
      ToggleDarkModeEvent event, Emitter<SettingState> emit) async {
    emit(SettingState(isDarkMode: !state.isDarkMode));
  }
}
