import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/discover/widget/discover_page_shimmer.dart';
import 'package:flowkar/features/setting_screen/page/save_post/presentation/saved_post_detail_widget.dart';
import 'package:flowkar/features/widgets/custom/custom_conditional_scroll_physics.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'dart:io';

class SavePostPage extends StatefulWidget {
  const SavePostPage({super.key});
  static Widget builder(BuildContext context) {
    return SavePostPage();
  }

  @override
  State<SavePostPage> createState() => _SavePostPageState();
}

class _SavePostPageState extends State<SavePostPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    final state = context.read<HomeFeedBloc>().state;
    if (state.savedposts.isNotEmpty) {
      state.savedposts.clear();
    }
    context.read<HomeFeedBloc>().add(SavedPostApiEvent(page: 1));
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildSavepostAppBar(),
      body: BlocListener<ConnectivityBloc, ConnectivityState>(
        listener: (context, connectivityState) {
          if (connectivityState.isReconnected) {
            final state = context.read<HomeFeedBloc>().state;
            if (state.savedposts.isNotEmpty) {
              state.savedposts.clear();
            }
            context.read<HomeFeedBloc>().add(SavedPostApiEvent(page: 1));
          }
        },
        child: BlocBuilder<HomeFeedBloc, HomeFeedState>(
          builder: (context, state) {
            return BlocBuilder<ConnectivityBloc, ConnectivityState>(
              builder: (context, connectivityState) {
                if (!connectivityState.isConnected && state.savedposts.isEmpty) {
                  return Padding(padding: EdgeInsets.symmetric(horizontal: 16.w), child: DiscoverPageShimmer());
                } else if (state.isSavePostloding) {
                  return Padding(padding: EdgeInsets.symmetric(horizontal: 16.w), child: DiscoverPageShimmer());
                } else if (state.savedposts.isEmpty) {
                  return ListView(
                    physics: AlwaysScrollableScrollPhysics(),
                    children: [
                      buildSizedBoxH(MediaQuery.of(context).size.height / 6),
                      ExceptionWidget(
                        imagePath: Assets.images.pngs.pngNoPost.path,
                        showButton: false,
                        title: 'Nothing saved yet',
                        subtitle: "",
                      ),
                    ],
                  );
                } else {
                  return _recentpostview(state, connectivityState);
                }
              },
            );
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildSavepostAppBar() {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          Lang.of(context).lbl_saved_post,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  Widget _recentpostview(HomeFeedState state, ConnectivityState connectivityState) {
    return LiquidPullToRefresh(
      color: Theme.of(context).primaryColor.withOpacity(0.5),
      showChildOpacityTransition: false,
      backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      onRefresh: () {
        return Future.delayed(Duration.zero, () {
          final state = context.read<HomeFeedBloc>().state;
          if (state.savedposts.isNotEmpty) {
            state.savedposts.clear();
          }
          context.read<HomeFeedBloc>().add(SavedPostApiEvent(page: 1));
        });
      },
      child: NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification scrollInfo) {
          if (scrollInfo is ScrollEndNotification) {
            // Check if we're near the bottom of the list
            bool isNearBottom = scrollInfo.metrics.pixels >= scrollInfo.metrics.maxScrollExtent - 50;

            if (isNearBottom) {
              // Check if there's more data to load
              final nextPage = state.savepostModel?.next;
              final isLoading = state.isSavePostLoadingMore;
              final hasMoreData = nextPage != null;

              if (hasMoreData && !isLoading && connectivityState.isConnected) {
                // Load next page
                context.read<HomeFeedBloc>().add(SavedPostApiEvent(page: state.savepospage + 1));
              }
            }
          }
          return false;
        },
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            StaggeredGridView.countBuilder(
              controller: _scrollController,
              physics: ConditionalAlwaysScrollPhysics(controller: _scrollController),
              padding: EdgeInsets.only(top: 0, bottom: Platform.isAndroid ? 70.h : 55.h, left: 14.w, right: 14.w),
              shrinkWrap: true,
              crossAxisCount: 2,
              mainAxisSpacing: 2.0,
              crossAxisSpacing: 2.0,
              staggeredTileBuilder: (int index) {
                return StaggeredTile.count(1, index.isEven ? 1.3 : 1);
              },
              itemCount: state.savedposts.length,
              itemBuilder: (context, index) {
                final filePath = state.savedposts[index].files.isNotEmpty
                    ? state.savedposts[index].files.first
                    : Assets.images.pngs.other.pngPlaceholder.path;

                return Container(
                  clipBehavior: Clip.antiAlias,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(18.0.r),
                      border: Border.all(color: Theme.of(context).primaryColor.withOpacity(0.1), width: 2)),
                  child: GestureDetector(
                    onTap: () {
                      PersistentNavBarNavigator.pushNewScreen(context,
                          screen: SavedUserPostListScreen(initialIndex: index, savedPosts: state.savedposts));
                    },
                    child: isVideo(filePath)
                        ? state.savedposts[index].thumbnailFiles.isNotEmpty
                            ? Stack(
                                fit: StackFit.expand,
                                children: [
                                  CustomImageView(
                                    width: double.infinity,
                                    imagePath: state.savedposts[index].thumbnailFiles.first,
                                    fit: BoxFit.cover,
                                    radius: BorderRadius.circular(16.0.r),
                                  ),
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      CustomImageView(
                                        margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                                        imagePath: Assets.images.svg.other.svgPlayIconWhite.path,
                                        color: Colors.white70,
                                        height: 30.h,
                                        width: 30.w,
                                      ),
                                    ],
                                  ),
                                ],
                              )
                            : Container(
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade300,
                                  image: DecorationImage(
                                    image: AssetImage(Assets.images.pngs.other.pngPlaceholder.path),
                                  ),
                                  borderRadius: BorderRadius.circular(16.0.r),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                                      child: CustomImageView(
                                        height: 30.h,
                                        width: 30.w,
                                        imagePath: Assets.images.svg.other.icPlayVideo.path,
                                        radius: BorderRadius.circular(16.0.r),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                        : CustomImageView(
                            imagePath: state.savedposts[index].files.first,
                            fit: BoxFit.cover,
                            radius: BorderRadius.circular(20.r),
                          ),
                  ),
                );
              },
            ),
            // Loading indicator for pagination
            Positioned(
              bottom: Platform.isAndroid ? 50.h : 0.h,
              left: 0,
              right: 0,
              child: Visibility(
                visible: (state.isLoadingMore) && !state.isSavePostloding && connectivityState.isConnected,
                child: Container(
                  color: Colors.transparent,
                  height: 50.h,
                  child: Center(
                    child: CupertinoActivityIndicator(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
