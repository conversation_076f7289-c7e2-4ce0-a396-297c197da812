// ignore_for_file: deprecated_member_use

import 'dart:async';

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/comment_bottom_sheet_widget.dart';
import 'package:flowkar/features/setting_screen/page/save_post/model/save_post_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class SavedUserPostListScreen extends StatefulWidget {
  final List<SavePostData>? savedPosts;
  final int? initialIndex;
  const SavedUserPostListScreen({super.key, this.savedPosts, this.initialIndex});

  static Widget builder(BuildContext context) {
    return const SavedUserPostListScreen();
  }

  @override
  State<SavedUserPostListScreen> createState() => _SavedUserPostListScreenState();
}

class _SavedUserPostListScreenState extends State<SavedUserPostListScreen> with SingleTickerProviderStateMixin {
  late ItemScrollController _itemScrollController;
  late ItemPositionsListener _itemPositionsListener;
  int _initialIndex = 0;
  final int _threshold = 5;
  @override
  void initState() {
    super.initState();
    _itemScrollController = ItemScrollController();
    _itemPositionsListener = ItemPositionsListener.create();
    _initialIndex = widget.initialIndex ?? 0;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_initialIndex >= 0 && _initialIndex < (widget.savedPosts?.length ?? 0)) {
        _scrollToInitialIndex();
      }
    });
    _itemPositionsListener.itemPositions.addListener(_scrollListener);
  }

  void _scrollToInitialIndex() {
    _itemScrollController.jumpTo(
      index: _initialIndex,
    );
  }

  Future<void> _refreshFeed() async {
    final state = context.read<HomeFeedBloc>().state;
    if (state.savedposts.isNotEmpty) {
      state.savedposts.clear();
    }
    scheduleMicrotask(() => context.read<HomeFeedBloc>().add(SavedPostApiEvent(page: 1)));
  }

  void _scrollListener() {
    // Get the current visible items
    final visibleItems = _itemPositionsListener.itemPositions.value;
    if (visibleItems.isNotEmpty) {
      // Get the index of the last visible item
      final lastVisibleIndex = visibleItems.last.index;

      // Check if we're near the end of the list
      if (lastVisibleIndex >= (widget.savedPosts?.length ?? 0) - _threshold) {
        final state = context.read<HomeFeedBloc>().state;
        if (state.savepostModel?.next == null) {
          return;
        } else {
          if (!state.isLoadingMore) {
            context.read<HomeFeedBloc>().add(SavedPostApiEvent(page: state.savepospage + 1));
          }
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themestate) {
          return BlocBuilder<HomeFeedBloc, HomeFeedState>(
            builder: (context, state) {
              return Scaffold(
                appBar: _buildSavepostAppBar(),
                body: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(child: _buildSavePost(state, themestate)),
                    BlocBuilder<ConnectivityBloc, ConnectivityState>(
                      builder: (context, connectivityState) {
                        return Visibility(
                          visible: state.isLoadingMore && connectivityState.isConnected,
                          child: Container(
                            color: Colors.transparent,
                            height: 50.h,
                            child:
                                Center(child: CupertinoActivityIndicator(color: Theme.of(context).colorScheme.primary)),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildSavepostAppBar() {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          "Posts",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  Widget _buildSavePost(HomeFeedState state, ThemeState themestate) {
    return BlocListener<ConnectivityBloc, ConnectivityState>(
      listener: (context, connectivityState) {
        if (connectivityState.isReconnected) {
          if (context.read<HomeFeedBloc>().state.savedposts.isEmpty) {
            _refreshFeed();
          }
        }
      },
      child: LiquidPullToRefresh(
        color: Theme.of(context).primaryColor.withOpacity(0.5),
        showChildOpacityTransition: false,
        backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
        onRefresh: _refreshFeed,
        child: BlocBuilder<ConnectivityBloc, ConnectivityState>(
          builder: (context, connectivityState) {
            if (!connectivityState.isConnected && state.savedposts.isEmpty) {
              return HomeFeedShimmer();
            } else if (state.isSavePostloding) {
              return HomeFeedShimmer();
            } else if (state.savedposts.isEmpty) {
              return ListView(
                physics: AlwaysScrollableScrollPhysics(),
                children: [
                  buildSizedBoxH(MediaQuery.of(context).size.height / 6),
                  ExceptionWidget(
                    imagePath: Assets.images.pngs.pngNoPost.path,
                    showButton: false,
                    title: 'Nothing saved yet',
                    subtitle: "",
                  ),
                ],
              );
            } else {
              return ScrollablePositionedList.builder(
                padding: EdgeInsets.zero,
                itemCount: state.savedposts.length,
                itemScrollController: _itemScrollController,
                itemPositionsListener: _itemPositionsListener,
                physics: const ClampingScrollPhysics(),
                itemBuilder: (context, index) {
                  final postIndex = index;
                  final post = state.savedposts[postIndex];
                  return Padding(
                    padding: EdgeInsets.symmetric(vertical: 0.h),
                    child: PostWidget(
                      width: post.width,
                      height: post.height,
                      userByIDpost: false,
                      userByIDvideo: false,
                      userVideo: false,
                      userpost: false,
                      state: state,
                      latestcomments: post.latestComment.toString(),
                      taggedIn: post.taggedIn,
                      index: postIndex,
                      userId: post.user.userId,
                      postId: post.id,
                      profileImage: post.user.profileImage.toString(),
                      name: post.user.name,
                      username: post.user.username,
                      postMedia: post.files,
                      thumbnailImage: post.thumbnailFiles.isNotEmpty ? post.thumbnailFiles : [],
                      title: post.title == "''" || post.title.isEmpty ? '' : post.title,
                      caption:
                          "${post.title == "''" || post.title.isEmpty ? '' : post.title}${post.description == '' || post.description.isEmpty ? '' : post.title == "''" || post.title.isEmpty ? post.description : "\n${post.description}"}",
                      likes: post.likes.toString(),
                      comments: post.commentsCount.toString(),
                      postTime: post.createdAt,
                      isLiked: post.isLiked,
                      isSaved: post.isSaved,
                      screenType: "User Profile",
                      doubleTap: () {
                        if (post.isLiked == false) {
                          context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: post.id));
                        }
                      },
                      likeonTap: () {
                        context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: post.id));
                      },
                      commentonTap: () {
                        showModalBottomSheet(
                          context: context,
                          useRootNavigator: true,
                          isScrollControlled: true,
                          builder: (context) => CommentsBottomSheet(postId: post.id),
                        );
                        // NavigatorService.pushNamed(AppRoutes.commentBottomSheet, arguments: [post.id]);
                      },
                      shareonTap: () {},
                      saveonTap: () {
                        NavigatorService.goBack();
                        context.read<HomeFeedBloc>().add(SavedPostSocketEvent(postId: post.id.toString()));
                      },
                    ),
                  );
                },
              );
            }
          },
        ),
      ),
    );
  }
}
