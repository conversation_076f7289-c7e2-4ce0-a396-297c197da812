SavePostModel deserializeSavePostModel(Map<String, dynamic> json) => SavePostModel.fromJson(json);

class SavePostModel {
  final int count;
  final String? next;
  final String? previous;
  final List<SavePostData> results;

  SavePostModel({
    required this.count,
    this.next,
    this.previous,
    required this.results,
  });

  SavePostModel copyWith({
    int? count,
    String? next,
    String? previous,
    List<SavePostData>? results,
  }) {
    return SavePostModel(
      count: count ?? this.count,
      next: next ?? this.next,
      previous: previous ?? this.previous,
      results: results ?? this.results,
    );
  }

  factory SavePostModel.fromJson(Map<String, dynamic> json) {
    return SavePostModel(
      count: json['count'],
      next: json['next'],
      previous: json['previous'],
      results: (json['results']['data'] as List).map((item) => SavePostData.fromJson(item)).toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'count': count,
      'next': next,
      'previous': previous,
      'results': results.map((item) => item.toJson()).toList(),
    };
  }
}

class SavePostData {
  final int id;
  final String title;
  final String description;
  final String location;
  final int likes;
  final int dislikes;
  final int commentsCount;
  final dynamic taggedIn;
  final String createdAt;
  final List<String> files;
  final int width;
  final int height;
  final List<String> thumbnailFiles;
  final String latestComment;
  final UserData user;
  final bool isLiked;
  final bool isSaved;

  SavePostData({
    required this.id,
    required this.title,
    required this.description,
    required this.location,
    required this.likes,
    required this.dislikes,
    required this.commentsCount,
    required this.taggedIn,
    required this.createdAt,
    required this.files,
    required this.width,
    required this.height,
    required this.thumbnailFiles,
    required this.latestComment,
    required this.user,
    required this.isLiked,
    required this.isSaved,
  });

  SavePostData copyWith({
    int? id,
    String? title,
    String? description,
    String? location,
    int? likes,
    int? dislikes,
    int? commentsCount,
    Map<String, dynamic>? taggedIn,
    String? createdAt,
    List<String>? files,
    int? width,
    int? height,
    List<String>? thumbnailFiles,
    String? latestComment,
    UserData? user,
    bool? isLiked,
    bool? isSaved,
  }) {
    return SavePostData(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      likes: likes ?? this.likes,
      dislikes: dislikes ?? this.dislikes,
      commentsCount: commentsCount ?? this.commentsCount,
      taggedIn: taggedIn ?? this.taggedIn,
      createdAt: createdAt ?? this.createdAt,
      files: files ?? this.files,
      width: width ?? this.width,
      height: height ?? this.height,
      thumbnailFiles: thumbnailFiles ?? this.thumbnailFiles,
      latestComment: latestComment ?? this.latestComment,
      user: user ?? this.user,
      isLiked: isLiked ?? this.isLiked,
      isSaved: isSaved ?? this.isSaved,
    );
  }

  factory SavePostData.fromJson(Map<String, dynamic> json) {
    return SavePostData(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      location: json['location'],
      likes: json['likes'],
      dislikes: json['dislikes'],
      commentsCount: json['comments_count'],
      taggedIn: json['tagged_in'] ?? {},
      createdAt: json['created_at'],
      files: List<String>.from(json['files'] ?? []),
      width: json['width'],
      height: json['height'],
      thumbnailFiles: List<String>.from(json['thumbail_files'] ?? []),
      latestComment: json['latest_comment'] ?? '',
      user: UserData.fromJson(json['user']),
      isLiked: json['is_liked'],
      isSaved: json['is_saved'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'location': location,
      'likes': likes,
      'dislikes': dislikes,
      'comments_count': commentsCount,
      'tagged_in': taggedIn,
      'created_at': createdAt,
      'files': files,
      'width': width,
      'height': height,
      'thumbail_files': thumbnailFiles,
      'latest_comment': latestComment,
      'user': user.toJson(),
      'is_liked': isLiked,
      'is_saved': isSaved,
    };
  }
}

class UserData {
  final int userId;
  final String username;
  final String name;
  final String profileImage;

  UserData({
    required this.userId,
    required this.username,
    required this.name,
    required this.profileImage,
  });

  UserData copyWith({
    int? userId,
    String? username,
    String? name,
    String? profileImage,
  }) {
    return UserData(
      userId: userId ?? this.userId,
      username: username ?? this.username,
      name: name ?? this.name,
      profileImage: profileImage ?? this.profileImage,
    );
  }

  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
      userId: json['user_id'],
      username: json['username'],
      name: json['name'],
      profileImage: json['profile_image'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'username': username,
      'name': name,
      'profile_image': profileImage,
    };
  }
}
