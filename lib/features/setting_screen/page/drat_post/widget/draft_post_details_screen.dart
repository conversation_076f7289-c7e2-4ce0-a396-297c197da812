import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/caption_preview_widget.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_manager.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_player.dart';
import 'package:intl/intl.dart';
import '../../../../home_feed_screen/model/draft_post_model.dart';

class DraftPostDetailsScreen extends StatefulWidget {
  final Results? appointment;
  final DraftPostData postInfo;

  const DraftPostDetailsScreen({
    super.key,
    required this.appointment,
    required this.postInfo,
  });

  @override
  State<DraftPostDetailsScreen> createState() => _DraftPostDetailsScreenState();
}

class _DraftPostDetailsScreenState extends State<DraftPostDetailsScreen> {
  late FlickMultiManager flickMultiManager;
  final PageController _postController = PageController();
  int currentMediaIndex = 0;

  @override
  void initState() {
    super.initState();
    flickMultiManager = FlickMultiManager();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildSizedBoxH(8.h),
            _buildUserDetail(),
            _buildPostMedia(),
            _buildPostDetail(),
            _buildPlatformSection(),
            _buildDraftInfoSection(),
          ],
        ),
      ),
    );
  }

  /// ---------------------- AppBar ----------------------
  PreferredSizeWidget _buildAppBar() {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: NavigatorService.goBack,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          "Draft Post",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w700,
                fontSize: 18.sp,
              ),
        ),
        Spacer(),

        /// Upload button
        BlocBuilder<HomeFeedBloc, HomeFeedState>(
          builder: (context, state) {
            return CustomElevatedButton(
              width: 110.w,
              height: 40.h,
              onPressed: () {
                showDialog(
                  barrierDismissible: state.isUploadDraftPostLoading,
                  context: context,
                  builder: (context) {
                    return BlocBuilder<HomeFeedBloc, HomeFeedState>(
                      builder: (context, state) {
                        return CustomAlertDialog(
                          title: "Upload Draft Post",
                          subtitle: "Are you sure you want to upload this draft post?",
                          onConfirmButtonPressed: () {
                            context.read<HomeFeedBloc>().add(UploadDraftPostAPIEvent(
                                draftPostId: widget.postInfo.id ?? 0, context: context, draftPostscreen: true));
                          },
                          confirmButtonText: "Upload",
                          isLoading: state.isUploadDraftPostLoading,
                        );
                      },
                    );
                  },
                );
              },
              text: Lang.of(context).lbl_upload_post,
              fontSize: 13.sp,
              // buttonTextStyle: Theme.of(context).textTheme.labelLarge?.copyWith(
              //       fontWeight: FontWeight.w500,
              //       fontSize: 16.sp,
              //       color: Colors.white,
              //     ),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(8.r),
                boxShadow: [BoxShadow(blurRadius: 6, color: Colors.black12)],
              ),
            );
          },
        ),
      ],
    );
  }

  /// ---------------------- User Info ----------------------
  Widget _buildUserDetail() {
    final profilePic = widget.appointment?.profilePicture;
    final isDefault = profilePic == AssetConstants.pngUser || (profilePic?.isEmpty ?? true);

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 18.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.white,
        boxShadow: [BoxShadow(color: Theme.of(context).customColors.black.withOpacity(0.2), blurRadius: 2)],
        borderRadius: BorderRadius.only(topLeft: Radius.circular(20.r), topRight: Radius.circular(20.r)),
      ),
      child: Row(
        children: [
          Padding(
            padding: const EdgeInsets.all(4.0),
            child: Container(
              height: 43.0.h,
              width: 43.0.w,
              padding: EdgeInsets.all(isDefault ? 8 : 2),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Theme.of(context).primaryColor.withOpacity(0.2), width: 2),
              ),
              child: CustomImageView(
                radius: BorderRadius.circular(100.0),
                fit: isDefault ? BoxFit.contain : BoxFit.cover,
                imagePath: isDefault ? AssetConstants.pngUserReomve : profilePic,
              ),
            ),
          ),
          buildSizedBoxW(8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.appointment?.username ?? "",
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w700,
                        color: Color(0xff292D32),
                      ),
                ),
                buildSizedBoxH(1),
                Text(
                  "@${widget.appointment?.username}",
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w700,
                        color: Color(0xff575353),
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// ---------------------- Media ----------------------
  Widget _buildPostMedia() {
    return ConstrainedBox(
      constraints: BoxConstraints(maxHeight: 440.h, maxWidth: MediaQuery.of(context).size.width),
      child: PageView.builder(
        controller: _postController,
        onPageChanged: (index) => setState(() => currentMediaIndex = index),
        itemCount: widget.postInfo.files?.length ?? 0,
        itemBuilder: (context, index) {
          final mediaUrl = widget.postInfo.files?[index];
          final isThumbAvailable = (widget.postInfo.thumbnailFiles?.length ?? 0) > index &&
              (widget.postInfo.thumbnailFiles?[index].isNotEmpty ?? false);

          if (isVideo(mediaUrl ?? "")) {
            return FlickMultiPlayer(
              key: ObjectKey(mediaUrl),
              url: mediaUrl,
              flickMultiManager: flickMultiManager,
              image: isThumbAvailable ? widget.postInfo.thumbnailFiles![index] : AssetConstants.pngPlaceholder,
            );
          } else {
            return AspectRatio(
              aspectRatio: 1,
              child: CustomImageView(
                imagePath: mediaUrl,
                fit: BoxFit.cover,
                width: double.infinity,
              ),
            );
          }
        },
      ),
    );
  }

  /// ---------------------- Caption ----------------------
  Widget _buildPostDetail() {
    final post = widget.postInfo;
    // final title = (post.title == "''" || post.title?.isEmpty == true) ? '' : post.title;
    final description =
        "${(post.title == "''" || (post.title?.isEmpty ?? true)) ? '' : "${post.title}"}${(post.description == '' || (post.description?.isEmpty ?? true)) ? '' : (post.title == "''" || (post.title?.isEmpty ?? true)) ? post.description : "\n${post.description}"}";
    // (post.description?.isEmpty ?? true) ? '' : "\n${post.description}";

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 18.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
      width: double.infinity,
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.white,
        boxShadow: [BoxShadow(color: Theme.of(context).customColors.black.withOpacity(0.2), blurRadius: 2)],
        borderRadius: BorderRadius.only(bottomLeft: Radius.circular(20.r), bottomRight: Radius.circular(20.r)),
      ),
      // child: CaptionPreviewWidget(
      //   caption: "$title$description",
      //   comment: "",
      //   commentonTap: () {},
      //   isTextPost: false,
      // ),
      child: CaptionPreviewWidget(
        caption: description,
        comment: "",
        commentonTap: () {},
        isTextPost: false,
      ),
    );
  }

  /// ---------------------- Draft Platforms ----------------------
  Widget _buildPlatformSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildSizedBoxH(20.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Text(
            "Selected Platforms",
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700, fontSize: 16.sp),
          ),
        ),
        buildSizedBoxH(10.h),
        _buildDraftPlatforms(widget.postInfo),
      ],
    );
  }

  Widget _buildDraftPlatforms(DraftPostData postData) {
    final platforms = {
      'facebook': postData.facebook,
      'instagram': postData.instagram,
      'linkedin': postData.linkedin,
      'pinterest': postData.pinterest,
      'vimeo': postData.vimeo,
      'youtube': postData.youtube,
      'dailymotion': postData.dailymotion,
      'tiktok': postData.tiktok,
      'threads': postData.twitter,
      'x': postData.x,
      'mastodon': postData.mastadon,
      'tumblr': postData.tumblr,
      'reddit': postData.reddit,
    };

    final active = platforms.entries.where((e) => e.value == true).map((e) => e.key).toList();

    if (active.isEmpty) {
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Text(
          "No platforms selected",
          style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Colors.grey[600], fontSize: 13.sp),
        ),
      );
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 18.w),
      child: Wrap(
        spacing: 8.w,
        runSpacing: 8.h,
        children: active
            .map((platform) => CustomImageView(
                  imagePath: getPlatformImageUrl(platform),
                  height: 32.h,
                  width: 32.w,
                ))
            .toList(),
      ),
    );
  }

  String getPlatformImageUrl(String platform) {
    switch (platform.toLowerCase()) {
      case 'vimeo':
        return Assets.images.icons.social.v.path;
      case 'instagram':
        return Assets.images.icons.social.insta.path;
      case 'linkedin':
        return Assets.images.icons.social.linkedin.path;
      case 'pinterest':
        return Assets.images.icons.social.icPintrest.path;
      case 'reddit':
        return Assets.images.icons.social.icReddit.path;
      case 'tumblr':
        return Assets.images.icons.social.icTumblr.path;
      case 'threads':
        return Assets.images.icons.social.icThread.path;
      case 'facebook':
        return Assets.images.icons.social.facebook.path;
      case 'tiktok':
        return Assets.images.icons.social.icTictok.path;
      case 'youtube':
        return Assets.images.icons.social.icYoutube.path;
      case 'x':
        return Assets.images.icons.social.twitter.path;
      case 'mastodon':
        return Assets.images.icons.social.svgMastodon.path;
      default:
        return Assets.images.icons.other.icFlowkar.path;
    }
  }

  /// ---------------------- Draft Info Section ----------------------
  Widget _buildDraftInfoSection() {
    final post = widget.postInfo;
    final hasLocation = post.location?.isNotEmpty ?? false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildSizedBoxH(20.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Text(
            "Draft Information",
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700, fontSize: 16.sp),
          ),
        ),
        if (hasLocation)
          Padding(
            padding: EdgeInsets.only(top: 13.h),
            child: _buildDetailRow('Location', post.location!, image: Assets.images.icons.other.icLocation.path),
          ),
        buildSizedBoxH(14.h),
        _buildDetailRow(
          'Created Date',
          DateFormat('dd MMM, yyyy HH:mm a').format(DateTime.parse(post.createdAt ?? "")),
          icon: Icons.edit_outlined,
        ),
        buildSizedBoxH(14.h),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value, {String? image, IconData? icon}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (image != null && image.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(top: 5.h),
              child: CustomImageView(imagePath: image, height: 19.h, width: 19.w),
            )
          else if (icon != null)
            Padding(
              padding: EdgeInsets.only(top: 5.h),
              child: Icon(icon, size: 24.sp, color: Theme.of(context).primaryColor),
            ),
          buildSizedBoxW(12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(label,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Colors.grey[600], fontSize: 13.sp)),
                buildSizedBoxH(2.0),
                Text(value,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
