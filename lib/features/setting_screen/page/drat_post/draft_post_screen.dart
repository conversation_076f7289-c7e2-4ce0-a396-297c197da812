// ignore_for_file: deprecated_member_use

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/profile_screen/presentation/widget/scheduled_post_screen_shimmer.dart';
import 'package:flowkar/features/setting_screen/page/drat_post/widget/draft_post_details_screen.dart';
import 'package:flowkar/features/widgets/custom/custom_conditional_scroll_physics.dart';
import 'package:flutter/cupertino.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';

class DraftPostScreen extends StatefulWidget {
  const DraftPostScreen({super.key});

  @override
  State<DraftPostScreen> createState() => _DraftPostScreenState();
}

class _DraftPostScreenState extends State<DraftPostScreen> {
  late ScrollController _scrollController;

  @override
  void initState() {
    // context.read<HomeFeedBloc>().add(GetNewStoryApiEvent());
    super.initState();

    _scrollController = ScrollController()..addListener(_handlePagination);
  }

  void _handlePagination() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      final state = context.read<HomeFeedBloc>().state;
      if (!state.isLoadingMore) {
        context.read<HomeFeedBloc>().add(GetAllPostApiEvent(page: state.postPage + 1));
      }
      if (!state.isVideoLoadingMore) {
        context.read<HomeFeedBloc>().add(GetAllVideoApiEvent(page: state.videoPage + 1));
      }
    }
  }

  Future<void> refresh() async {
    final state = context.read<HomeFeedBloc>().state;

    if (state.draftPosts.isNotEmpty) {
      state.draftPosts.clear();
    }

    context.read<HomeFeedBloc>().add(GetDraftPostAPIEvent(draftPostPage: 1));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildDraftPostAppBar(),
      body: BlocListener<ConnectivityBloc, ConnectivityState>(
        listener: (context, connectivityState) {
          if (connectivityState.isReconnected) {
            context.read<HomeFeedBloc>().add(GetDraftPostAPIEvent(draftPostPage: 1));
          }
        },
        child: BlocBuilder<HomeFeedBloc, HomeFeedState>(
          builder: (context, state) {
            if (state.isGetDraftPostLoading) {
              return const ScheduledPostShimmer();
            } else if (state.draftPosts.isEmpty) {
              return _buildNoDraftPostFound(context);
            } else {
              return Stack(
                children: [
                  LiquidPullToRefresh(
                    color: Theme.of(context).primaryColor.withOpacity(0.5),
                    showChildOpacityTransition: false,
                    backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
                    onRefresh: refresh,
                    child: ListView.builder(
                      controller: _scrollController,
                      physics: ConditionalAlwaysScrollPhysics(controller: _scrollController),
                      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                      itemCount: state.draftPosts.length,
                      itemBuilder: (context, index) {
                        final draft = state.draftPosts[index];
                        final firstFile = (draft.files != null && draft.files!.isNotEmpty) ? draft.files?.first : null;
                        final imagePath = firstFile != null && !isVideo(firstFile)
                            ? firstFile
                            : (draft.thumbnailFiles?.isNotEmpty ?? false)
                                ? draft.thumbnailFiles?.first
                                : Assets.images.pngs.other.pngPlaceholder.path;

                        return InkWell(
                          onTap: () {
                            PersistentNavBarNavigator.pushNewScreen(context,
                                screen: DraftPostDetailsScreen(
                                    appointment: state.draftPostModel?.results, postInfo: draft));
                          },
                          child: _buildDraftPostWidget(
                            context,
                            username: state.draftPostModel?.results?.name ?? "",
                            handle: "@${state.draftPostModel?.results?.username ?? "unknown"}",
                            date: draft.scheduledAt?.split("T").first ?? "",
                            time: draft.scheduledAt?.split("T").last ?? "",
                            imagePath: imagePath ?? "",
                            postDescription: draft.description ?? "",
                            postTitle: (draft.title != null && (draft.title == "''" || draft.title!.isEmpty))
                                ? ''
                                : draft.title ?? '',
                            location: draft.location ?? "Unknown Location",
                            industry: "",
                            onPressed: () {
                              showDialog(
                                barrierDismissible: state.isUploadDraftPostLoading,
                                context: context,
                                builder: (context) {
                                  return BlocBuilder<HomeFeedBloc, HomeFeedState>(
                                    builder: (context, state) {
                                      return CustomAlertDialog(
                                        title: "Upload Draft Post",
                                        subtitle: "Are you sure you want to upload this draft post?",
                                        onConfirmButtonPressed: () {
                                          context.read<HomeFeedBloc>().add(UploadDraftPostAPIEvent(
                                              draftPostId: draft.id ?? 0, context: context, draftPostscreen: false));
                                        },
                                        confirmButtonText: "Upload",
                                        isLoading: state.isUploadDraftPostLoading,
                                      );
                                    },
                                  );
                                },
                              );
                            },
                            onDeletePressed: () {
                              showDialog(
                                barrierDismissible: state.isdeleteDraftPostLoading,
                                context: context,
                                builder: (context) {
                                  return BlocBuilder<HomeFeedBloc, HomeFeedState>(
                                    builder: (context, state) {
                                      return CustomAlertDialog(
                                        imagePath: Assets.images.svg.setting.svgDailogDeleteAccount.path,
                                        title: "Delete Draft Post",
                                        subtitle: "Are you sure you want to delete this draft post?",
                                        confirmButtonText: "Delete",
                                        onConfirmButtonPressed: () {
                                          context.read<HomeFeedBloc>().add(
                                              DeleteDraftPostAPIEvent(draftPostId: draft.id ?? 0, context: context));
                                        },
                                        isLoading: state.isdeleteDraftPostLoading,
                                      );
                                    },
                                  );
                                },
                              );
                            },
                          ),
                        );
                      },
                    ),
                  ),
                  if (state.isGetDraftPostLoadingMore)
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: SizedBox(
                        height: 50.h,
                        child: Center(
                            child: CupertinoActivityIndicator(
                          color: Theme.of(context).colorScheme.primary,
                        )),
                      ),
                    ),
                ],
              );
            }
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildDraftPostAppBar() {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () => NavigatorService.goBack(),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          Lang.of(context).lbl_draft_posts,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  Widget _buildNoDraftPostFound(BuildContext context) {
    return ListView(
      physics: AlwaysScrollableScrollPhysics(),
      children: [
        buildSizedBoxH(MediaQuery.of(context).size.height / 6),
        ExceptionWidget(
          showButton: false,
          imagePath: Assets.images.pngs.pngNoPost.path,
          title: Lang.of(context).lbl_no_draft_post,
          subtitle: "",
        ),
      ],
    );
  }

  Widget _buildDraftPostWidget(
    BuildContext context, {
    required Function() onPressed,
    required Function() onDeletePressed,
    required String username,
    required String handle,
    required String date,
    required String time,
    required String imagePath,
    required String postDescription,
    required String postTitle,
    required String location,
    required String industry,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// Header with avatar and delete
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  ValueListenableBuilder<String>(
                    valueListenable: profileImageNotifier,
                    builder: (context, imagePath, child) {
                      return Container(
                        height: 36.h,
                        width: 36.w,
                        decoration: BoxDecoration(
                          border: Border.all(color: Theme.of(context).primaryColor.withOpacity(0.2), width: 2),
                          borderRadius: BorderRadius.circular(100.r),
                        ),
                        padding: EdgeInsets.all(1),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(100.r),
                          child: CustomImageView(
                            fit: BoxFit.contain,
                            imagePath: imagePath,
                            alignment: Alignment.center,
                          ),
                        ),
                      );
                    },
                  ),
                  buildSizedBoxW(8),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        username,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w700,
                            ),
                      ),
                      Text(
                        handle,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontSize: 10.sp,
                              color: Colors.grey,
                            ),
                      ),
                    ],
                  )
                ],
              ),
              GestureDetector(
                onTap: onDeletePressed,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 13.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Text(
                    Lang.of(context).lbl_delete_draft,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontSize: 10.sp,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
              ),
            ],
          ),

          Divider(thickness: 0.5, color: Colors.grey[300]),
          buildSizedBoxH(12),

          /// Post title and description
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8.r),
                child: CustomImageView(
                  imagePath: imagePath,
                  width: 50.w,
                  height: 50.h,
                  fit: BoxFit.cover,
                ),
              ),
              buildSizedBoxW(8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (postTitle.isNotEmpty && postTitle != "''")
                      Text(postTitle,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 12.sp)),
                    Text(postDescription,
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 10.sp)),
                  ],
                ),
              ),
            ],
          ),
          if (location.isNotEmpty) buildSizedBoxH(12),

          /// Location
          if (location.isNotEmpty)
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomImageView(imagePath: Assets.images.icons.other.icLocation.path),
                buildSizedBoxW(8),
                Flexible(
                    child: Text(location,
                        maxLines: 2, overflow: TextOverflow.ellipsis, style: Theme.of(context).textTheme.bodySmall)),
              ],
            ),
          if (industry.isNotEmpty) buildSizedBoxH(8),

          /// Industry
          if (industry.isNotEmpty)
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomImageView(imagePath: Assets.images.icons.other.icLocation.path),
                buildSizedBoxW(8),
                Flexible(
                  child: Text(industry,
                      maxLines: 2, overflow: TextOverflow.ellipsis, style: Theme.of(context).textTheme.bodySmall),
                ),
              ],
            ),
          buildSizedBoxH(16),

          /// Upload button
          CustomElevatedButton(
            height: 50.h,
            width: double.infinity,
            onPressed: onPressed,
            text: Lang.of(context).lbl_upload_post,
            buttonTextStyle: Theme.of(context).textTheme.labelLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: 16.sp,
                  color: Colors.white,
                ),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: BorderRadius.circular(8.r),
              boxShadow: [BoxShadow(blurRadius: 6, color: Colors.black12)],
            ),
          ),
        ],
      ),
    );
  }
}
