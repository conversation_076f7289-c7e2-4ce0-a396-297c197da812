import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/authentication/bloc/auth_bloc.dart';
import 'package:flowkar/features/widgets/common/flowkar_background/flowkar_background.dart';
import 'dart:io';

class UserTypeSelectionSeetingScreen extends StatefulWidget {
  const UserTypeSelectionSeetingScreen({super.key});
  static Widget builder(BuildContext context) => const UserTypeSelectionSeetingScreen();

  @override
  State<UserTypeSelectionSeetingScreen> createState() => _UserTypeSelectionSeetingScreenState();
}

class _UserTypeSelectionSeetingScreenState extends State<UserTypeSelectionSeetingScreen> {
  String selectedUserType = 'User';
  String selectedIndustry = '';
  String? userTypeError;
  String? industryError;
  @override
  void initState() {
    context.read<AuthBloc>().add(GEtUserAndIndustryTypeAPI());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          return BackgroundImage(
            imagePath: Assets.images.pngs.authentication.pngAuthBg1.path,
            child: SafeArea(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Column(
                  children: [
                    buildSizedBoxH(100),
                    _buildTitleText(),
                    buildSizedBoxH(100),
                    // **Types of User Dropdown**
                    GestureDetector(
                      // onTap: () => _showBottomSheet(
                      //   'User Type',
                      //   state.getUserAndIndustryType?.data?.userTypes?.map((e) => e.typeName).toList() ?? [],
                      //   (value) => setState(() {
                      //     selectedUserType = value;
                      //     userTypeError = null; // Remove error if selected
                      //   }),
                      //   state,
                      // ),
                      onTap: () => _showBottomSheet(
                        'User Type',
                        (value) => setState(() {
                          selectedUserType = value;
                          userTypeError = null;
                          if (value == 'User') {
                            selectedIndustry = ''; // Reset industry when User is selected
                          }
                        }),
                      ),
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12.r),
                          border: Border.all(
                              color:
                                  userTypeError != null ? Theme.of(context).colorScheme.error : Colors.grey.shade300),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              selectedUserType.isEmpty ? 'Types of User' : selectedUserType,
                              style: TextStyle(fontSize: 16.sp),
                            ),
                            Icon(Icons.keyboard_arrow_down_rounded, size: 24.sp, color: Theme.of(context).primaryColor),
                          ],
                        ),
                      ),
                    ),
                    if (userTypeError != null) // **Show error below the container**
                      Padding(
                        padding: EdgeInsets.only(top: 5.h, left: 5.w),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            userTypeError!,
                            style: TextStyle(color: Theme.of(context).colorScheme.error, fontSize: 14.sp),
                          ),
                        ),
                      ),
                    if (selectedUserType != 'User') ...[
                      buildSizedBoxH(20),

                      // **Types of Industry Dropdown**
                      GestureDetector(
                        onTap: () => _showBottomSheet(
                          'Industry',
                          (value) => setState(() {
                            selectedIndustry = value;
                            industryError = null; // Remove error if selected
                          }),
                        ),
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12.r),
                            border: Border.all(
                                color:
                                    industryError != null ? Theme.of(context).colorScheme.error : Colors.grey.shade300),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                selectedIndustry.isEmpty ? 'Types of Industry' : selectedIndustry,
                                style: TextStyle(fontSize: 16.sp),
                              ),
                              Icon(Icons.keyboard_arrow_down_rounded,
                                  size: 24.sp, color: Theme.of(context).primaryColor),
                            ],
                          ),
                        ),
                      ),
                      if (industryError != null)
                        Padding(
                          padding: EdgeInsets.only(top: 5.h, left: 5.w),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              industryError!,
                              style: TextStyle(color: Theme.of(context).colorScheme.error, fontSize: 14.sp),
                            ),
                          ),
                        ),
                    ],
                    buildSizedBoxH(75),
                    Padding(
                      padding: EdgeInsets.only(bottom: Platform.isIOS ? 0 : MediaQuery.of(context).viewPadding.bottom),
                      child: CustomElevatedButton(
                        width: 159.w,
                        text: 'Next',
                        iconSpacing: 20.w,
                        isDisabled: state.isUploadLoadong,
                        isLoading: state.isUploadLoadong,
                        brderRadius: 10.r,
                        // rightIcon: CustomImageView(
                        //   imagePath: Assets
                        //       .images.svg.authentication.icBackIcon.path,
                        // ),
                        // onPressed: () {
                        //   setState(() {
                        //     userTypeError = selectedUserType.isEmpty ? 'Please select a user type' : null;
                        //     industryError = selectedIndustry.isEmpty ? 'Please select an industry type' : null;
                        //   });

                        //   if (selectedUserType.isNotEmpty && selectedIndustry.isNotEmpty) {
                        //     context.read<AuthBloc>().add(
                        //           SelectUserAndIndustryType(industry: selectedIndustry, type: selectedUserType),
                        //         );
                        //   }
                        // },
                        onPressed: () {
                          setState(() {
                            userTypeError = selectedUserType.isEmpty ? 'Please select a user type' : null;
                            industryError = (selectedUserType != 'User' && selectedIndustry.isEmpty)
                                ? 'Please select an industry type'
                                : null;
                          });

                          if (selectedUserType.isNotEmpty &&
                              (selectedUserType == 'User' || selectedIndustry.isNotEmpty)) {
                            context.read<AuthBloc>().add(
                                  SelectUserAndIndustryType(
                                      industry: selectedIndustry.isEmpty ? "" : selectedIndustry,
                                      type: selectedUserType,
                                      isChangeUserType: true),
                                );
                          }
                        },
                      ),
                    ),
                    buildSizedBoxH(16),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          "User Type",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  // Title Text Widget

  Widget _buildTitleText() {
    return Align(
      alignment: Alignment.center,
      child: Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: ' Switch ',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 34.sp,
                  ),
            ),
            TextSpan(
              text: 'your ',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w400,
                    fontSize: 34.sp,
                  ),
            ),
            TextSpan(
              text: 'Account Type',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 34.sp,
                  ),
            ),
          ],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  void _showBottomSheet(String title, Function(String) onSelect) {
    showModalBottomSheet(
        context: context,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
        ),
        builder: (context) {
          return BlocBuilder<AuthBloc, AuthState>(
            builder: (context, state) {
              final List<dynamic> options = title == 'User Type'
                  ? (state.getUserAndIndustryType?.data?.userTypes?.map((e) => e.typeName).toList() ?? [])
                  : (state.getUserAndIndustryType?.data?.industries?.map((e) => e.industryName).toList() ?? []);
              return state.isgetUserAndIndustryLoadong && options.isEmpty
                  ? LoadingAnimationWidget()
                  : Container(
                      padding: EdgeInsets.symmetric(vertical: 20.h),
                      margin: EdgeInsets.only(bottom: Platform.isIOS ? 0 : MediaQuery.of(context).viewPadding.bottom),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 40.w,
                            height: 4.h,
                            decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(2.r),
                            ),
                          ),
                          buildSizedBoxH(20.h),
                          buildSizedBoxW(double.infinity),
                          Text(
                            'Select $title',
                            style: TextStyle(
                              fontSize: 18.sp,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF593C3C),
                            ),
                          ),
                          SizedBox(height: 20.h),
                          Expanded(
                            child: ListView(
                              children: options
                                  .map(
                                    (option) => InkWell(
                                      onTap: () {
                                        onSelect(option);
                                        Navigator.pop(context);
                                      },
                                      child: Container(
                                        width: double.infinity,
                                        padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 20.w),
                                        child: Text(
                                          option,
                                          style: TextStyle(
                                            fontSize: 16.sp,
                                            color: Theme.of(context).primaryColor,
                                          ),
                                        ),
                                      ),
                                    ),
                                  )
                                  .toList(),
                            ),
                          )
                        ],
                      ),
                    );
            },
          );
        });
  }
}
