import 'package:json_annotation/json_annotation.dart';

part 'scheduled_posts_web_model.g.dart';

ScheduledPostsWebModel deserializeScheduledPostsWebModel(Map<String, dynamic> json) =>
    ScheduledPostsWebModel.fromJson(json);

@JsonSerializable(explicitToJson: true)
class ScheduledPostsWebModel {
  final bool status;
  final String message;
  final int today;
  final int tomorrow;
  @JsonKey(name: 'this_month')
  final int thisMonth;
  final List<ScheduledPostWebData> data;

  ScheduledPostsWebModel({
    required this.status,
    required this.message,
    required this.today,
    required this.tomorrow,
    required this.thisMonth,
    required this.data,
  });

  factory ScheduledPostsWebModel.fromJson(Map<String, dynamic> json) => _$ScheduledPostsWebModelFromJson(json);

  Map<String, dynamic> toJson() => _$ScheduledPostsWebModelToJson(this);

  ScheduledPostsWebModel copyWith({
    bool? status,
    String? message,
    int? today,
    int? tomorrow,
    int? thisMonth,
    List<ScheduledPostWebData>? data,
  }) {
    return ScheduledPostsWebModel(
      status: status ?? this.status,
      message: message ?? this.message,
      today: today ?? this.today,
      tomorrow: tomorrow ?? this.tomorrow,
      thisMonth: thisMonth ?? this.thisMonth,
      data: data ?? this.data,
    );
  }
}

@JsonSerializable(explicitToJson: true)
class ScheduledPostWebData {
  final int id;
  final String title;
  final String description;
  @JsonKey(name: 'is_scheduled')
  final bool isScheduled;
  @JsonKey(name: 'scheduled_at')
  final String scheduledAt;
  @JsonKey(name: 'created_at')
  final String createdAt;
  final List<String> files;
  @JsonKey(name: 'thumbnail_files')
  final List<String> thumbnailFiles;
  final User user;
  @JsonKey(name: 'is_liked')
  final bool isLiked;
  final Map<String, bool> platform;

  ScheduledPostWebData({
    required this.id,
    required this.title,
    required this.description,
    required this.isScheduled,
    required this.scheduledAt,
    required this.createdAt,
    required this.files,
    required this.thumbnailFiles,
    required this.user,
    required this.isLiked,
    required this.platform,
  });

  factory ScheduledPostWebData.fromJson(Map<String, dynamic> json) => _$ScheduledPostWebDataFromJson(json);

  Map<String, dynamic> toJson() => _$ScheduledPostWebDataToJson(this);

  ScheduledPostWebData copyWith({
    int? id,
    String? title,
    String? description,
    bool? isScheduled,
    String? scheduledAt,
    String? createdAt,
    List<String>? files,
    List<String>? thumbnailFiles,
    User? user,
    bool? isLiked,
    Map<String, bool>? platform,
  }) {
    return ScheduledPostWebData(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      isScheduled: isScheduled ?? this.isScheduled,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      createdAt: createdAt ?? this.createdAt,
      files: files ?? this.files,
      thumbnailFiles: thumbnailFiles ?? this.thumbnailFiles,
      user: user ?? this.user,
      isLiked: isLiked ?? this.isLiked,
      platform: platform ?? this.platform,
    );
  }
}

@JsonSerializable()
class User {
  @JsonKey(name: 'user_id')
  final int userId;
  final String username;
  final String name;
  @JsonKey(name: 'profile_image')
  final String profileImage;

  User({
    required this.userId,
    required this.username,
    required this.name,
    required this.profileImage,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    int? userId,
    String? username,
    String? name,
    String? profileImage,
  }) {
    return User(
      userId: userId ?? this.userId,
      username: username ?? this.username,
      name: name ?? this.name,
      profileImage: profileImage ?? this.profileImage,
    );
  }
}
