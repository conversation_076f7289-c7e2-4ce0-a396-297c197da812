// import 'dart:convert';

PlannerListPostModel deserializePlannerListPostModel(Map<String, dynamic> json) => PlannerListPostModel.fromJson(json);

class PlannerListPostModel {
  int count;
  String? next;
  String? previous;
  PlannerListPostResults results;

  PlannerListPostModel({
    required this.count,
    this.next,
    this.previous,
    required this.results,
  });

  factory PlannerListPostModel.fromJson(Map<String, dynamic> json) {
    return PlannerListPostModel(
      count: json['count'],
      next: json['next'],
      previous: json['previous'],
      results: PlannerListPostResults.fromJson(json['results']),
    );
  }

  Map<String, dynamic> toJson() => {
        'count': count,
        'next': next,
        'previous': previous,
        'results': results.toJson(),
      };

  PlannerListPostModel copyWith({
    int? count,
    String? next,
    String? previous,
    PlannerListPostResults? results,
  }) {
    return PlannerListPostModel(
      count: count ?? this.count,
      next: next ?? this.next,
      previous: previous ?? this.previous,
      results: results ?? this.results,
    );
  }
}

class PlannerListPostResults {
  bool status;
  String message;
  List<PlannerPostData> data;

  PlannerListPostResults({
    required this.status,
    required this.message,
    required this.data,
  });

  factory PlannerListPostResults.fromJson(Map<String, dynamic> json) {
    return PlannerListPostResults(
      status: json['status'],
      message: json['message'],
      data: (json['data'] as List).map((e) => PlannerPostData.fromJson(e)).toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'status': status,
        'message': message,
        'data': data.map((e) => e.toJson()).toList(),
      };

  PlannerListPostResults copyWith({
    bool? status,
    String? message,
    List<PlannerPostData>? data,
  }) {
    return PlannerListPostResults(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

class PlannerPostData {
  int id;
  String title;
  String description;
  bool isScheduled;
  String scheduledAt;
  DateTime createdAt;
  List<String> files;
  List<String> thumbnailFiles;
  User user;
  bool isLiked;
  String status;
  Map<String, bool> platform;
  int? width;
  int? height;

  PlannerPostData({
    required this.id,
    required this.title,
    required this.description,
    required this.isScheduled,
    required this.scheduledAt,
    required this.createdAt,
    required this.files,
    required this.thumbnailFiles,
    required this.user,
    required this.isLiked,
    required this.status,
    required this.platform,
    required this.width,
    required this.height,
  });

  factory PlannerPostData.fromJson(Map<String, dynamic> json) {
    return PlannerPostData(
      id: json['id'],
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      isScheduled: json['is_scheduled'],
      scheduledAt: json['scheduled_at'] ?? '',
      createdAt: DateTime.parse(json['created_at']),
      files: List<String>.from(json['files'] ?? []),
      thumbnailFiles: List<String>.from(json['thumbnail_files'] ?? []),
      user: User.fromJson(json['user']),
      isLiked: json['is_liked'],
      status: json['status'] ?? '',
      platform: Map<String, bool>.from(json['platform'] ?? {}),
      width: json['width'],
      height: json['height'],
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'title': title,
        'description': description,
        'is_scheduled': isScheduled,
        'scheduled_at': scheduledAt,
        'created_at': createdAt.toIso8601String(),
        'files': files,
        'thumbnail_files': thumbnailFiles,
        'user': user.toJson(),
        'is_liked': isLiked,
        'status': status,
        'platform': platform,
        'width': width,
        'height': height,
      };

  PlannerPostData copyWith({
    int? id,
    String? title,
    String? description,
    bool? isScheduled,
    String? scheduledAt,
    DateTime? createdAt,
    List<String>? files,
    List<String>? thumbnailFiles,
    User? user,
    bool? isLiked,
    String? status,
    Map<String, bool>? platform,
    int? width,
    int? height,
  }) {
    return PlannerPostData(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      isScheduled: isScheduled ?? this.isScheduled,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      createdAt: createdAt ?? this.createdAt,
      files: files ?? this.files,
      thumbnailFiles: thumbnailFiles ?? this.thumbnailFiles,
      user: user ?? this.user,
      isLiked: isLiked ?? this.isLiked,
      status: status ?? this.status,
      platform: platform ?? this.platform,
      width: width ?? this.width,
      height: height ?? this.height,
    );
  }
}

class User {
  int userId;
  String username;
  String name;
  String profileImage;

  User({
    required this.userId,
    required this.username,
    required this.name,
    required this.profileImage,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      userId: json['user_id'],
      username: json['username'] ?? '',
      name: json['name'] ?? '',
      profileImage: json['profile_image'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        'user_id': userId,
        'username': username,
        'name': name,
        'profile_image': profileImage,
      };

  User copyWith({
    int? userId,
    String? username,
    String? name,
    String? profileImage,
  }) {
    return User(
      userId: userId ?? this.userId,
      username: username ?? this.username,
      name: name ?? this.name,
      profileImage: profileImage ?? this.profileImage,
    );
  }
}
