import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/analytics/presentation/widget/post_container_widget.dart';
import 'package:flowkar/features/setting_screen/page/planner_calendar/presentation/planner_calendar.dart';
import 'package:flowkar/features/setting_screen/page/planner_calendar/presentation/planner_post_details_screen.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

class DayViewScreen extends StatelessWidget {
  final DateTime selectedDate;

  const DayViewScreen({super.key, required this.selectedDate});

  @override
  Widget build(BuildContext context) {
    final calendarController = CalendarController();
    calendarController.view = CalendarView.day;
    calendarController.displayDate = selectedDate;

    return Scaffold(
      appBar: CustomAppbar(
        hasLeadingIcon: true,
        height: 18.h,
        leading: [
          InkWell(
            onTap: () {
              FocusScope.of(context).unfocus();
              NavigatorService.goBack();
            },
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: CustomImageView(
                imagePath: Assets.images.svg.authentication.icBackArrow.path,
                height: 16.h,
              ),
            ),
          ),
          buildSizedBoxW(20.w),
          Text(
            // DateFormat('EEEE, MMM d, yyyy').format(selectedDate),
            "Planner",
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w700,
                  fontSize: 18.sp,
                ),
          ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: BlocBuilder<HomeFeedBloc, HomeFeedState>(
          builder: (context, state) {
            if (state.isSchedulePostWebLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            return SfCalendar(
              controller: calendarController,
              view: CalendarView.day,
              initialDisplayDate: selectedDate,
              dataSource: ScheduledPostDataSource(state.schedulePostWeb),
              timeSlotViewSettings: TimeSlotViewSettings(
                timeIntervalHeight: 60.h,
              ),
              headerStyle: CalendarHeaderStyle(
                textAlign: TextAlign.left,
                backgroundColor: Colors.transparent,
                textStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              selectionDecoration: BoxDecoration(
                // color: Theme.of(context).primaryColor.withOpacity(0.2),
                border: Border.all(
                  color: Theme.of(context).primaryColor,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(16.r),
              ),
              showNavigationArrow: true,
              onTap: (CalendarTapDetails details) {
                if (details.targetElement == CalendarElement.appointment) {
                  final appointment = details.appointments?.first;
                  if (appointment is ScheduledPostAppointment) {
                    PersistentNavBarNavigator.pushNewScreen(
                      context,
                      screen: PostDetailsScreen(appointment: appointment),
                    );
                  }
                }
              },
              appointmentBuilder: (context, calendarAppointmentDetails) {
                final appointment = calendarAppointmentDetails.appointments.first;
                if (appointment is ScheduledPostAppointment) {
                  return PostContainer(
                    imageUrl: appointment.scheduledPostData.files.isEmpty
                        ? Assets.images.pngs.flowkar.path
                        : appointment.scheduledPostData.files.first,
                    platforms: appointment.scheduledPostData.platform.entries
                        .where((entry) => entry.value)
                        .map((entry) => entry.key)
                        .toList(),
                    postText: appointment.scheduledPostData.title.isNotEmpty
                        ? '${appointment.scheduledPostData.title}\n${appointment.scheduledPostData.description}'
                        : appointment.scheduledPostData.description,
                    totalComment: 0,
                    totallike: 0,
                    totalShare: 0,
                    isPlanner: true,
                  );
                }
                return const SizedBox.shrink();
              },
            );
          },
        ),
      ),
    );
  }
}
