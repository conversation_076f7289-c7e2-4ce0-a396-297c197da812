import 'package:flowkar/core/utils/exports.dart';

class PlannerCalendarShimmer extends StatelessWidget {
  const PlannerCalendarShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: <PERSON><PERSON><PERSON>(
          children: [
            _buildCalendarHeaderShimmer(context),
            const SizedBox(height: 16),
            _buildWeekDaysHeaderShimmer(context),
            const SizedBox(height: 8),
            Expanded(child: _buildCalendarGridShimmer(context)),
          ],
        ),
      ),
    );
  }

  Widget _buildCalendarHeaderShimmer(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            height: 32,
            width: 120,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
            ),
          ),
          Row(
            children: [
              Container(
                height: 24,
                width: 24,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(width: 16),
              Container(
                height: 24,
                width: 24,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWeekDaysHeaderShimmer(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Row(
        children: List.generate(
          7,
          (index) => Expanded(
            child: Container(
              height: 20,
              margin: const EdgeInsets.symmetric(horizontal: 2),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCalendarGridShimmer(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Column(
        children: List.generate(
          6,
          (weekIndex) => Expanded(
            child: Row(
              children: List.generate(
                7,
                (dayIndex) => Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(color: Colors.grey[200]!, width: 0.4),
                        bottom: BorderSide(color: Colors.grey[200]!, width: 0.4),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Date number shimmer
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(4),
                          child: Center(
                            child: Container(
                              height: 20,
                              width: 20,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),

                        // Post items shimmer (random pattern)
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 2),
                            child: Column(
                              children: _buildRandomPostShimmers(weekIndex, dayIndex),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildRandomPostShimmers(int weekIndex, int dayIndex) {
    // Create a pseudo-random pattern based on week and day index
    final random = (weekIndex * 7 + dayIndex) % 4;

    List<Widget> shimmers = [];

    // Add random number of post shimmers (0-3)
    for (int i = 0; i < random; i++) {
      shimmers.add(
        Container(
          height: 12,
          width: double.infinity,
          margin: const EdgeInsets.only(bottom: 2),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      );
    }

    // Add a "+more" shimmer occasionally
    if (random >= 3) {
      shimmers.add(
        Container(
          height: 10,
          width: double.infinity,
          margin: const EdgeInsets.only(bottom: 2),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.8),
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      );
    }

    return shimmers;
  }
}

// Alternative simpler shimmer version
class PlannerCalendarShimmerSimple extends StatelessWidget {
  const PlannerCalendarShimmerSimple({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        title: Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            height: 24,
            width: 80,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
      ),
      body: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Month/Year header
              Container(
                height: 40,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              const SizedBox(height: 20),

              // Calendar grid
              Expanded(
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 7,
                    childAspectRatio: 0.8,
                    crossAxisSpacing: 2,
                    mainAxisSpacing: 2,
                  ),
                  itemCount: 42, // 6 weeks * 7 days
                  itemBuilder: (context, index) {
                    return Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color: Colors.grey[200]!,
                          width: 0.5,
                        ),
                      ),
                      child: Column(
                        children: [
                          // Date number
                          Container(
                            height: 20,
                            width: 20,
                            margin: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),

                          // Random post indicators
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 2),
                              child: Column(
                                children: List.generate(
                                  (index % 3), // Random number of posts
                                  (postIndex) => Container(
                                    height: 8,
                                    width: double.infinity,
                                    margin: const EdgeInsets.only(bottom: 2),
                                    decoration: BoxDecoration(
                                      color: Colors.grey[200],
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Usage in your existing code:
// Replace LoadingAnimationWidget with PlannerCalendarShimmer
class PlannerCalendarShimmerUsage extends StatelessWidget {
  const PlannerCalendarShimmerUsage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeFeedBloc, HomeFeedState>(
      builder: (context, state) {
        if (state.isSchedulePostWebLoading) {
          return const PlannerCalendarShimmer(); // Use this instead of LoadingAnimationWidget
        }

        // Your existing calendar widget code here
        return YourActualCalendarWidget();
      },
    );
  }
}

// Placeholder for your actual calendar widget
class YourActualCalendarWidget extends StatelessWidget {
  const YourActualCalendarWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(); // Your actual calendar implementation
  }
}
