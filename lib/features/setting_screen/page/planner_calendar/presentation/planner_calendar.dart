import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/setting_screen/page/planner_calendar/presentation/day_calendar.dart';
import 'package:flowkar/features/setting_screen/page/planner_calendar/presentation/planner_post_details_screen.dart';
import 'package:flowkar/features/setting_screen/page/planner_calendar/presentation/widget/planner_calendar_shimmer.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';
import 'package:flowkar/features/setting_screen/page/planner_calendar/model/scheduled_posts_web_model.dart';

class PlannerCalendar extends StatefulWidget {
  const PlannerCalendar({super.key});

  @override
  State<PlannerCalendar> createState() => _PlannerCalendarState();
}

class _PlannerCalendarState extends State<PlannerCalendar> {
  late CalendarController _calendarController;
  List<ScheduledPostWebData> _scheduledPosts = [];

  @override
  void initState() {
    super.initState();
    _calendarController = CalendarController();
    _calendarController.selectedDate = DateTime.now();
    final state = context.read<HomeFeedBloc>().state;
    if (state.schedulePostWeb.isNotEmpty) {
      state.schedulePostWeb.clear();
    }
    context.read<HomeFeedBloc>().add(GetScheduledPostWebEvent());
  }

  @override
  void dispose() {
    _calendarController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: BlocBuilder<HomeFeedBloc, HomeFeedState>(
        builder: (context, state) {
          if (state.isSchedulePostWebLoading) {
            return PlannerCalendarShimmer();
          }

          _scheduledPosts = state.schedulePostWeb;

          return Column(
            children: [
              Expanded(
                child: Container(
                  // margin: EdgeInsets.only(bottom: 100.h),
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: SfCalendar(
                    controller: _calendarController,
                    cellEndPadding: 2.w,
                    cellBorderColor: Colors.white,
                    view: CalendarView.month,
                    dataSource: ScheduledPostDataSource(_scheduledPosts),
                    showNavigationArrow: true,
                    monthViewSettings: MonthViewSettings(
                      numberOfWeeksInView: 6,
                      appointmentDisplayMode: MonthAppointmentDisplayMode.appointment,
                      // showAgenda: false,
                      showTrailingAndLeadingDates: true,
                      dayFormat: 'EEE',
                      navigationDirection: MonthNavigationDirection.vertical,
                      appointmentDisplayCount: 1, // 👈 4 appointments
                      monthCellStyle: MonthCellStyle(
                        /// 👉 Change trailing/leading date colors here
                        textStyle: Theme.of(context)
                            .textTheme
                            .bodyMedium
                            ?.copyWith(fontWeight: FontWeight.w600, color: Theme.of(context).primaryColor),
                        trailingDatesTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.red, // Or any color you want
                              fontWeight: FontWeight.w500,
                            ),
                        leadingDatesTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.red, // Or any color you want
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ),
                    viewHeaderStyle: ViewHeaderStyle(
                      dayTextStyle: TextStyle(
                          fontSize: 13.sp, color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold),
                    ),
                    appointmentTextStyle: TextStyle(
                      fontSize: 12.sp,
                      color: Theme.of(context).customColors.white,
                    ),
                    monthCellBuilder: (BuildContext context, MonthCellDetails details) {
                      // final eventsForDate = _getEventsForDate(details.date);
                      final isTrailingOrLeading =
                          details.date.month != details.visibleDates[details.visibleDates.length ~/ 2].month;

                      // Check if this is today's date
                      final isToday = DateUtils.isSameDay(details.date, DateTime.now());

                      // Check if this is selected date
                      final isSelected = _calendarController.selectedDate != null &&
                          DateUtils.isSameDay(details.date, _calendarController.selectedDate!);

                      return Container(
                        decoration: BoxDecoration(
                          border: isToday
                              ? Border.all(
                                  color: Theme.of(context).primaryColor,
                                  width: 2,
                                )
                              : isSelected
                                  ? Border.all(
                                      color: Theme.of(context).primaryColor.withOpacity(0.3),
                                      width: 2,
                                    )
                                  : Border(
                                      top: BorderSide(
                                        color: Theme.of(context).primaryColor.withOpacity(0.3), // Top border
                                        width: 0.4,
                                      ),
                                      bottom: BorderSide(
                                        color: Theme.of(context).primaryColor.withOpacity(0.3), // Bottom border
                                        width: 0.4,
                                      ),
                                    ),
                          borderRadius: isSelected || isToday ? BorderRadius.circular(8.r) : null,
                        ),
                        child: InkWell(
                          onTap: () {
                            setState(() {
                              _calendarController.selectedDate = details.date;
                            });
                            PersistentNavBarNavigator.pushNewScreen(context,
                                screen: DayViewScreen(selectedDate: details.date));
                          },
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Date number at top center
                              Container(
                                width: double.infinity,
                                padding: EdgeInsets.only(top: 3.h, bottom: 3.h),
                                child: Center(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: isToday ? Theme.of(context).primaryColor : null,
                                      shape: BoxShape.circle,
                                    ),
                                    width: isToday ? 26.w : null,
                                    height: isToday ? 26.w : null,
                                    child: Center(
                                      child: Text(
                                        details.date.day.toString(),
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                              fontWeight: FontWeight.w600,
                                              color: isToday
                                                  ? Theme.of(context).customColors.white
                                                  : isTrailingOrLeading
                                                      ? Colors.grey.shade400
                                                      : isSelected
                                                          ? Theme.of(context).primaryColor
                                                          : Theme.of(context).primaryColor,
                                            ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(child: Container()),
                            ],
                          ),
                        ),
                      );
                    },

                    // Handle tap on calendar dates
                    onTap: (CalendarTapDetails details) {
                      if (details.targetElement == CalendarElement.calendarCell) {
                        setState(() {
                          _calendarController.selectedDate = details.date;
                        });
                      }
                    },
                    onLongPress: (calendarLongPressDetails) {},
                    appointmentBuilder: (context, calendarAppointmentDetails) {
                      final appointments =
                          calendarAppointmentDetails.appointments.whereType<ScheduledPostAppointment>().toList();

                      if (appointments.isEmpty) return SizedBox.shrink();

                      // Determine how many to show
                      final visibleAppointments = appointments.take(3).toList();
                      final hiddenCount = appointments.length - visibleAppointments.length;

                      return Column(
                        children: [
                          // Show up to 4 appointments
                          ...visibleAppointments.map(
                            (appointment) => GestureDetector(
                              onTap: () {
                                PersistentNavBarNavigator.pushNewScreen(context,
                                    screen: PostDetailsScreen(appointment: appointment));
                              },
                              child: Container(
                                width: double.infinity,
                                padding: EdgeInsets.only(left: 2.5.w, bottom: 2.h),
                                margin: EdgeInsets.symmetric(vertical: 2.h / 4),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor,
                                  borderRadius: BorderRadius.circular(3.r),
                                ),
                                child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    appointment.scheduledPostData.title == "''"
                                        ? "post..."
                                        : appointment.scheduledPostData.title,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    textAlign: TextAlign.start,
                                    style: TextStyle(
                                      fontSize: 8.sp,
                                      color: Theme.of(context).customColors.white,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),

                          // If more exist, show count
                          if (hiddenCount > 0)
                            Container(
                              width: double.infinity,
                              padding: EdgeInsets.only(left: 2.w, right: 2.w, bottom: 1.h),
                              margin: EdgeInsets.symmetric(vertical: 2.h / 4),
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor.withOpacity(0.8),
                                borderRadius: BorderRadius.circular(3.r),
                              ),
                              child: Text(
                                ' +$hiddenCount more',
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.start,
                                style: TextStyle(
                                  fontSize: 8.sp,
                                  color: Theme.of(context).customColors.white,
                                ),
                              ),
                            ),
                        ],
                      );
                    },
                    headerStyle: CalendarHeaderStyle(
                      textAlign: TextAlign.left,
                      backgroundColor: Colors.transparent,
                      textStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                    // Today's date highlight color
                    todayHighlightColor: Theme.of(context).primaryColor,
                    headerHeight: 50.h,
                    selectionDecoration: BoxDecoration(
                      color: Colors.transparent, // Make transparent as we handle in monthCellBuilder
                    ),
                    // Enable selection mode
                    allowViewNavigation: true,
                    showDatePickerButton: false,
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          "Planner",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w700,
                fontSize: 18.sp,
              ),
        ),
      ],
    );
  }
}

// Data source class for calendar - Simplified
class ScheduledPostDataSource extends CalendarDataSource {
  ScheduledPostDataSource(List<ScheduledPostWebData> scheduledPosts) {
    appointments = scheduledPosts.map((post) {
      return ScheduledPostAppointment(
        id: post.id,
        title: post.title,
        description: post.description,
        startTime: _parseScheduledDate(post.scheduledAt),
        endTime: _parseScheduledDate(post.scheduledAt).add(const Duration(hours: 1)),
        postType: _determinePostType(post),
        scheduledPostData: post,
      );
    }).toList();
  }

  DateTime _parseScheduledDate(String? scheduledAt) {
    if (scheduledAt == null || scheduledAt.isEmpty) {
      return DateTime.now();
    }

    try {
      if (scheduledAt.contains('T')) {
        return DateTime.parse(scheduledAt);
      } else {
        return DateFormat('yyyy-MM-dd HH:mm:ss').parse(scheduledAt);
      }
    } catch (e) {
      Logger.lOG('Error parsing date: $scheduledAt, Error: $e');
      return DateTime.now();
    }
  }

  String _determinePostType(ScheduledPostWebData post) {
    // ignore: unnecessary_null_comparison
    if (post.files != null && post.files.isNotEmpty) {
      final mediaUrl = post.files.first;
      if (mediaUrl.contains('.mp4') || mediaUrl.contains('.mov') || mediaUrl.contains('video')) {
        return 'video';
      } else {
        return 'image';
      }
      // ignore: unnecessary_null_comparison
    } else if (post.title != null && post.title.isNotEmpty) {
      return 'text';
    }
    return 'post';
  }
}

// Custom appointment class for month view
class ScheduledPostAppointment extends Appointment {
  final int id;
  final String postType;
  final ScheduledPostWebData scheduledPostData;

  ScheduledPostAppointment({
    required this.id,
    required String title,
    required String description,
    required super.startTime,
    required super.endTime,
    required this.postType,
    required this.scheduledPostData,
  }) : super(
          subject: title,
          notes: description,
          color: _getColorForPostType(postType),
        );

  static Color _getColorForPostType(String postType) {
    return MyAppThemeHelper.primaryColor;
    // switch (postType.toLowerCase()) {
    //   case 'image':
    //     return Colors.blue;
    //   case 'video':
    //     return Colors.red;
    //   case 'text':
    //     return Colors.green;
    //   case 'story':
    //     return Colors.purple;
    //   default:
    //     return Colors.grey;
    // }
  }
}
