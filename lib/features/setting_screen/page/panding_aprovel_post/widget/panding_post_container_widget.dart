import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/setting_screen/page/panding_aprovel_post/bloc/panding_aprovel_post_bloc.dart';
import 'package:flowkar/features/setting_screen/page/panding_aprovel_post/bloc/panding_aprovel_post_event.dart';
import 'package:flowkar/features/setting_screen/page/panding_aprovel_post/bloc/panding_aprovel_post_state.dart';
import 'package:intl/intl.dart' as intl;

class PandingPostContainerWidget extends StatefulWidget {
  final int postId;
  final String username;
  final String handle;
  final String date;
  final String postDescription;
  final String imagePath;

  const PandingPostContainerWidget({
    super.key,
    required this.postId,
    required this.username,
    required this.handle,
    required this.date,
    required this.postDescription,
    required this.imagePath,
  });

  @override
  State<PandingPostContainerWidget> createState() => _PandingPostContainerWidgetState();
}

class _PandingPostContainerWidgetState extends State<PandingPostContainerWidget> {
  bool isExpanded = false;
  bool showSeeMore = false;

  @override
  void initState() {
    super.initState();
    // Check if content needs "See more" after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkIfContentNeedsSeeMore();
    });
  }

  void _checkIfContentNeedsSeeMore() {
    final textPainter = TextPainter(
      text: TextSpan(
        text: widget.postDescription,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 10.sp, height: 1.5),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout(maxWidth: MediaQuery.of(context).size.width - 120.w); // Approximate available width

    // Check if content exceeds 3 lines
    if (textPainter.computeLineMetrics().length > 3) {
      setState(() {
        showSeeMore = true;
      });
    }
  }

  String formatDate(String apiDate) {
    try {
      if (apiDate.isEmpty) {
        return "Date not available";
      }
      DateTime dateTime = DateTime.parse(apiDate).toLocal();
      final formatter = intl.DateFormat('MMM d, yyyy h:mm a');
      return formatter.format(dateTime);
    } catch (e) {
      return "Invalid date";
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h, left: 16.w, right: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ValueListenableBuilder<String>(
                      valueListenable: profileImageNotifier,
                      builder: (context, imagePath, child) {
                        return Container(
                          height: 36.0.h,
                          width: 36.0.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(100.0.r),
                            border: Border.all(
                              width: 1,
                              color: Theme.of(context).primaryColor.withOpacity(0.1),
                            ),
                          ),
                          clipBehavior: Clip.antiAlias,
                          child: CustomImageView(
                            radius: BorderRadius.circular(imagePath == AssetConstants.pngUser ? 0 : 100.0.r),
                            fit: imagePath == AssetConstants.pngUser ? BoxFit.contain : BoxFit.cover,
                            imagePath: imagePath,
                            alignment: Alignment.center,
                          ),
                        );
                      },
                    ),
                    buildSizedBoxW(8),
                    InkWell(
                      onTap: () {
                        // PersistentNavBarNavigator.pushNewScreen(context,
                        //     screen: UserProfileIdScreen(
                        //       userId: widget.userId,
                        //       stackonScreen: true,
                        //     ));
                      },
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.username,
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge!
                                .copyWith(fontSize: 14.sp, fontWeight: FontWeight.w700, color: Color(0xff292D32)),
                          ),
                          buildSizedBoxH(1),
                          Text(
                            widget.handle,
                            style: Theme.of(context)
                                .textTheme
                                .headlineSmall!
                                .copyWith(fontSize: 9.sp, fontWeight: FontWeight.w700, color: Color(0xff575353)),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 13.w,
                    vertical: 6.h,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(2.r),
                  ),
                  child: Text(
                    "Pending",
                    style:
                        Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 10.sp, fontWeight: FontWeight.w700),
                  ),
                ),
              ],
            ),
            Divider(
              thickness: 0.5,
              color: Theme.of(context).primaryColor.withOpacity(0.2),
            ),
            buildSizedBoxH(16),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 50.h,
                  width: 50.w,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(5.r),
                  ),
                  child: CustomImageView(
                    height: 50.h,
                    width: 50.w,
                    radius: BorderRadius.circular(5.r),
                    imagePath: widget.imagePath,
                    fit: BoxFit.cover,
                  ),
                ),
                buildSizedBoxW(8),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildPostDescription(),
                      if (showSeeMore) _buildSeeMoreButton(),
                    ],
                  ),
                ),
              ],
            ),
            buildSizedBoxH(12),
            Row(
              children: [
                CustomImageView(
                  imagePath: Assets.images.svg.uploadPost.svgScheduledCalender.path,
                  height: 15.h,
                  width: 15.w,
                ),
                buildSizedBoxW(8),
                Text(formatDate(widget.date),
                    // "Aug 9, 2024 2:32 pm",
                    style: Theme.of(context)
                        .textTheme
                        .bodySmall
                        ?.copyWith(fontSize: 14.sp, color: Theme.of(context).primaryColor.withOpacity(0.8))),
              ],
            ),
            buildSizedBoxH(18),
            BlocBuilder<PandingAprovelPostBloc, PandingAprovelPostState>(
              builder: (context, state) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: CustomElevatedButton(
                        height: 40.h,
                        brderRadius: 10.r,
                        onPressed: () {
                          context
                              .read<PandingAprovelPostBloc>()
                              .add(RejectPandingAprovelPostEvent(postId: widget.postId));
                        },
                        text: "Decline",
                        isLoading: state.rejectPostIds.contains(widget.postId),
                        isDisabled: state.rejectPostIds.isNotEmpty || state.approvePostIds.isNotEmpty,
                        buttonStyle: ButtonStyle(
                          padding: WidgetStateProperty.all(EdgeInsets.zero),
                          backgroundColor: WidgetStateProperty.all(Theme.of(context).customColors.white),
                          shape: WidgetStatePropertyAll(
                            RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10.r),
                              side: BorderSide(color: Theme.of(context).customColors.primaryColor),
                            ),
                          ),
                        ),
                        buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).customColors.primaryColor,
                            ),
                      ),
                    ),
                    buildSizedBoxW(10.0),
                    Expanded(
                      child: CustomElevatedButton(
                        height: 40.h,
                        brderRadius: 10.r,
                        onPressed: () {
                          context
                              .read<PandingAprovelPostBloc>()
                              .add(ApprovalPandingAprovelPostEvent(postId: widget.postId));
                        },
                        text: "Accept",
                        isLoading: state.approvePostIds.contains(widget.postId),
                        isDisabled: state.approvePostIds.isNotEmpty || state.rejectPostIds.isNotEmpty,
                        buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).customColors.white,
                            ),
                      ),
                    ),
                  ],
                );
              },
            ),
            buildSizedBoxH(5)
          ],
        ),
      ),
    );
  }

  Widget _buildPostDescription() {
    final textStyle = Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 11.5.sp, height: 1.5);

    if (!showSeeMore) {
      return Text(
        widget.postDescription,
        style: textStyle,
      );
    }

    if (isExpanded) {
      return Text(
        widget.postDescription,
        style: textStyle,
      );
    } else {
      return Text(
        widget.postDescription,
        style: textStyle,
        maxLines: 3,
        overflow: TextOverflow.ellipsis,
      );
    }
  }

  Widget _buildSeeMoreButton() {
    return GestureDetector(
      onTap: () {
        setState(() {
          isExpanded = !isExpanded;
        });
      },
      child: Padding(
        padding: EdgeInsets.only(top: 4.h),
        child: Text(
          isExpanded ? "See less" : "See more",
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontSize: 10.sp,
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w600,
              ),
        ),
      ),
    );
  }
}
