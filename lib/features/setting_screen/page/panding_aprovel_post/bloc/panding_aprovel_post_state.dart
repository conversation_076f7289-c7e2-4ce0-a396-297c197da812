import 'package:equatable/equatable.dart';
import 'package:flowkar/features/setting_screen/page/panding_aprovel_post/model/panding_aprovel_post_model.dart';

class PandingAprovelPostState extends Equatable {
  final bool isLoading;
  final bool isApproveLoading;
  final bool isRejectLoading;
  final PandingAprovelPostModel? pandingAprovelPostModel;
  final Set<int> approvePostIds;
  final Set<int> rejectPostIds;

  const PandingAprovelPostState({
    this.isLoading = false,
    this.isApproveLoading = false,
    this.isRejectLoading = false,
    this.pandingAprovelPostModel,
    this.approvePostIds = const {},
    this.rejectPostIds = const {},
  });

  PandingAprovelPostState copyWith({
    bool? isLoading,
    bool? isApproveLoading,
    bool? isRejectLoading,
    PandingAprovelPostModel? pandingAprovelPostModel,
    Set<int>? approvePostIds,
    Set<int>? rejectPostIds,
  }) {
    return PandingAprovelPostState(
      isLoading: isLoading ?? this.isLoading,
      isApproveLoading: isApproveLoading ?? this.isApproveLoading,
      isRejectLoading: isRejectLoading ?? this.isRejectLoading,
      pandingAprovelPostModel: pandingAprovelPostModel ?? this.pandingAprovelPostModel,
      approvePostIds: approvePostIds ?? this.approvePostIds,
      rejectPostIds: rejectPostIds ?? this.rejectPostIds,
    );
  }

  @override
  List<Object?> get props => [
        isLoading,
        isApproveLoading,
        isRejectLoading,
        pandingAprovelPostModel,
        approvePostIds,
        rejectPostIds,
      ];
}
