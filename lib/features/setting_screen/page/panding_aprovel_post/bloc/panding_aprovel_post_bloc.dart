import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/setting_screen/page/panding_aprovel_post/bloc/panding_aprovel_post_event.dart';
import 'package:flowkar/features/setting_screen/page/panding_aprovel_post/bloc/panding_aprovel_post_state.dart';

class PandingAprovelPostBloc extends Bloc<PandingAprovelPostEvent, PandingAprovelPostState> {
  final ApiClient apiClient = ApiClient(Dio());

  PandingAprovelPostBloc() : super(const PandingAprovelPostState()) {
    on<PandingApprovelPostEvent>(_onPandingAprovelPostEvent);
    on<ApprovalPandingAprovelPostEvent>(_onApprovalPandingAprovelPostEvent);
    on<RejectPandingAprovelPostEvent>(_onRejectPandingAprovelPostEvent);
  }

  Future<void> _onPandingAprovelPostEvent(PandingApprovelPostEvent event, Emitter<PandingAprovelPostState> emit) async {
    try {
      emit(state.copyWith(isLoading: true));

      final pendingPostApproval = await apiClient.pendingPostApproval();

      if (pendingPostApproval.status == true) {
        emit(state.copyWith(isLoading: false, pandingAprovelPostModel: pendingPostApproval));
      } else {
        emit(state.copyWith(isLoading: false));
      }
    } catch (e) {
      Logger.lOG("_onPandingAprovelPostEvent error: $e");
      emit(state.copyWith(isLoading: false));
    }
  }

  Future<void> _onApprovalPandingAprovelPostEvent(
      ApprovalPandingAprovelPostEvent event, Emitter<PandingAprovelPostState> emit) async {
    try {
      Set<int> newDeclining = Set<int>.from(state.approvePostIds)..add(event.postId);
      emit(state.copyWith(isApproveLoading: true, approvePostIds: newDeclining));

      final approvePendingPostUpload = await apiClient.approvePendingPostUpload(postId: event.postId);

      if (approvePendingPostUpload.status == true) {
        emit(state.copyWith(isApproveLoading: false));
        add(PandingApprovelPostEvent());
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            approvePendingPostUpload.message ?? "",
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
      } else {
        emit(state.copyWith(isApproveLoading: false));
      }
    } catch (e) {
      Logger.lOG("_onApprovalPandingAprovelPostEvent error: $e");
      emit(state.copyWith(isApproveLoading: false));
    } finally {
      Set<int> newDeclining = Set<int>.from(state.approvePostIds)..remove(event.postId);
      emit(state.copyWith(isApproveLoading: false, approvePostIds: newDeclining));
    }
  }

  Future<void> _onRejectPandingAprovelPostEvent(
      RejectPandingAprovelPostEvent event, Emitter<PandingAprovelPostState> emit) async {
    try {
      Set<int> newDeclining = Set<int>.from(state.rejectPostIds)..add(event.postId);
      emit(state.copyWith(isRejectLoading: true, rejectPostIds: newDeclining));

      final rejectPendingPostUpload = await apiClient.rejectPendingPostUpload(postId: event.postId);

      if (rejectPendingPostUpload.status == true) {
        emit(state.copyWith(isRejectLoading: false));

        add(PandingApprovelPostEvent());
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            rejectPendingPostUpload.message ?? "",
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
      } else {
        emit(state.copyWith(isRejectLoading: false));
      }
    } catch (e) {
      Logger.lOG("_onRejectPandingAprovelPostEvent error: $e");
      emit(state.copyWith(isRejectLoading: false));
    } finally {
      Set<int> newDeclining = Set<int>.from(state.rejectPostIds)..remove(event.postId);
      emit(state.copyWith(isRejectLoading: false, rejectPostIds: newDeclining));
    }
  }
}
