import 'package:flowkar/core/utils/exports.dart';

abstract class PandingAprovelPostEvent extends Equatable {
  const PandingAprovelPostEvent();
}

class PandingApprovelPostEvent extends PandingAprovelPostEvent {
  final bool isRefresh;

  const PandingApprovelPostEvent({this.isRefresh = false});

  @override
  List<Object?> get props => [isRefresh];
}

class ApprovalPandingAprovelPostEvent extends PandingAprovelPostEvent {
  final int postId;

  const ApprovalPandingAprovelPostEvent({required this.postId});

  @override
  List<Object?> get props => [postId];
}

class RejectPandingAprovelPostEvent extends PandingAprovelPostEvent {
  final int postId;

  const RejectPandingAprovelPostEvent({required this.postId});

  @override
  List<Object?> get props => [postId];
}
