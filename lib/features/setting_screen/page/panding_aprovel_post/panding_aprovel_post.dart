import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/profile_screen/presentation/widget/scheduled_post_screen_shimmer.dart';
import 'package:flowkar/features/setting_screen/page/panding_aprovel_post/bloc/panding_aprovel_post_bloc.dart';
import 'package:flowkar/features/setting_screen/page/panding_aprovel_post/bloc/panding_aprovel_post_event.dart';
import 'package:flowkar/features/setting_screen/page/panding_aprovel_post/bloc/panding_aprovel_post_state.dart';
import 'package:flowkar/features/setting_screen/page/panding_aprovel_post/widget/panding_post_container_widget.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

class PandingAprovelPostScreen extends StatefulWidget {
  const PandingAprovelPostScreen({super.key});

  @override
  State<PandingAprovelPostScreen> createState() => _PandingAprovelPostScreenState();
}

class _PandingAprovelPostScreenState extends State<PandingAprovelPostScreen> {
  @override
  void initState() {
    super.initState();
    context.read<PandingAprovelPostBloc>().add(PandingApprovelPostEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildDraftPostAppBar(),
      body: BlocBuilder<PandingAprovelPostBloc, PandingAprovelPostState>(
        builder: (context, state) {
          return BlocBuilder<ConnectivityBloc, ConnectivityState>(
            builder: (context, connectivityState) {
              if (!connectivityState.isConnected && state.pandingAprovelPostModel!.data.isEmpty) {
                return ScheduledPostShimmer();
              } else if (state.isLoading && !state.isApproveLoading && !state.isRejectLoading) {
                return ScheduledPostShimmer();
              } else if (state.pandingAprovelPostModel?.data.isEmpty ?? false) {
                return ListView(
                  physics: AlwaysScrollableScrollPhysics(),
                  children: [
                    buildSizedBoxH(MediaQuery.of(context).size.height / 6),
                    ExceptionWidget(
                      showButton: false,
                      imagePath: Assets.images.pngs.pngNoPost.path,
                      title: "No Pending Post found",
                      subtitle: "",
                    ),
                  ],
                );
              } else {
                return AnimationConfiguration.staggeredList(
                  position: 1,
                  duration: const Duration(milliseconds: 500),
                  child: SlideAnimation(
                    verticalOffset: 50.0,
                    child: FadeInAnimation(
                      child: ListView.builder(
                        itemCount: state.pandingAprovelPostModel?.data.length,
                        itemBuilder: (context, index) {
                          final pandingAprovelPostData = state.pandingAprovelPostModel?.data[index];

                          String imagePath = AssetConstants.pngPlaceholder;
                          if (pandingAprovelPostData?.thumbnailFiles.isNotEmpty ?? false) {
                            imagePath = pandingAprovelPostData!.thumbnailFiles.first;
                          } else if (pandingAprovelPostData?.files.isNotEmpty ?? false) {
                            imagePath = pandingAprovelPostData!.files.first;
                          }

                          return PandingPostContainerWidget(
                            imagePath: imagePath,
                            postId: pandingAprovelPostData?.id ?? 0,
                            postDescription: pandingAprovelPostData?.description ?? "",
                            date: pandingAprovelPostData?.createdAt.toString() ?? DateTime.now().toIso8601String(),
                            username: pandingAprovelPostData?.user.name ?? "",
                            handle: pandingAprovelPostData?.user.username ?? "",
                          );
                        },
                      ),
                    ),
                  ),
                );
              }
            },
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildDraftPostAppBar() {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () => NavigatorService.goBack(),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          Lang.of(context).lbl_panding_approvel_posts,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }
}
