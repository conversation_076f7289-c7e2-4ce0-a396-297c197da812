PandingAprovelPostModel deserializePandingAprovelPostModel(Map<String, dynamic> json) =>
    PandingAprovelPostModel.fromJson(json);

class PandingAprovelPostModel {
  final bool status;
  final String message;
  final List<Post> data;

  PandingAprovelPostModel({
    required this.status,
    required this.message,
    required this.data,
  });

  PandingAprovelPostModel copyWith({
    bool? status,
    String? message,
    List<Post>? data,
  }) {
    return PandingAprovelPostModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  factory PandingAprovelPostModel.fromJson(Map<String, dynamic> json) {
    return PandingAprovelPostModel(
      status: json['status'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? List<Post>.from(json['data'].map((x) => Post.fromJson(x))) : [],
    );
  }

  Map<String, dynamic> toJ<PERSON>() => {
        'status': status,
        'message': message,
        'data': List<dynamic>.from(data.map((x) => x.toJson())),
      };
}

class Post {
  final int id;
  final String title;
  final String description;
  final bool isScheduled;
  final String scheduledAt;
  final DateTime createdAt;
  final List<String> files;
  final List<String> thumbnailFiles;
  final User user;
  final bool isLiked;

  Post({
    required this.id,
    required this.title,
    required this.description,
    required this.isScheduled,
    required this.scheduledAt,
    required this.createdAt,
    required this.files,
    required this.thumbnailFiles,
    required this.user,
    required this.isLiked,
  });

  Post copyWith({
    int? id,
    String? title,
    String? description,
    bool? isScheduled,
    String? scheduledAt,
    DateTime? createdAt,
    List<String>? files,
    List<String>? thumbnailFiles,
    User? user,
    bool? isLiked,
  }) {
    return Post(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      isScheduled: isScheduled ?? this.isScheduled,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      createdAt: createdAt ?? this.createdAt,
      files: files ?? this.files,
      thumbnailFiles: thumbnailFiles ?? this.thumbnailFiles,
      user: user ?? this.user,
      isLiked: isLiked ?? this.isLiked,
    );
  }

  factory Post.fromJson(Map<String, dynamic> json) {
    return Post(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      isScheduled: json['is_scheduled'] ?? false,
      scheduledAt: json['scheduled_at'] ?? '',
      createdAt: DateTime.parse(json['created_at'] ?? ''),
      files: json['files'] != null ? List<String>.from(json['files']) : [],
      thumbnailFiles: json['thumbnail_files'] != null ? List<String>.from(json['thumbnail_files']) : [],
      user: User.fromJson(json['user'] ?? {}),
      isLiked: json['is_liked'] ?? false,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'title': title,
        'description': description,
        'is_scheduled': isScheduled,
        'scheduled_at': scheduledAt,
        'created_at': createdAt.toIso8601String(),
        'files': files,
        'thumbnail_files': thumbnailFiles,
        'user': user.toJson(),
        'is_liked': isLiked,
      };
}

class User {
  final int userId;
  final String username;
  final String name;
  final String profileImage;

  User({
    required this.userId,
    required this.username,
    required this.name,
    required this.profileImage,
  });

  User copyWith({
    int? userId,
    String? username,
    String? name,
    String? profileImage,
  }) {
    return User(
      userId: userId ?? this.userId,
      username: username ?? this.username,
      name: name ?? this.name,
      profileImage: profileImage ?? this.profileImage,
    );
  }

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      userId: json['user_id'] ?? 0,
      username: json['username'] ?? '',
      name: json['name'] ?? '',
      profileImage: json['profile_image'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        'user_id': userId,
        'username': username,
        'name': name,
        'profile_image': profileImage,
      };
}
