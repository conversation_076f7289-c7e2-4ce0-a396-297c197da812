import 'dart:convert';

import 'package:flowkar/core/utils/exports.dart';
import 'package:flutter/cupertino.dart';
import 'package:typewritertext/typewritertext.dart';

class CommentBox extends StatefulWidget {
  const CommentBox({
    super.key,
    required this.textEditingController,
    required this.focusNode,
    required this.onSubmitted,
    required this.onAIButtonPressed,
    this.isReply = false,
    this.replyToUsername,
    this.replyToCommentText, // Add this parameter
  });

  final TextEditingController textEditingController;
  final FocusNode focusNode;
  final Function(String?) onSubmitted;
  final Function(String, String?) onAIButtonPressed; // Modified to include reply context
  final bool isReply;
  final String? replyToUsername;
  final String? replyToCommentText; // Add this field

  @override
  State<CommentBox> createState() => _CommentBoxWidgetState();
}

class _CommentBoxWidgetState extends State<CommentBox> {
  late TypeWriterController typeWriterController =
      TypeWriterController(text: '', duration: const Duration(milliseconds: 50));

  @override
  initState() {
    super.initState();
    // Clear AI response
    context.read<HomeFeedBloc>().add(ClearAIGeneratedCommentEvent());
    typeWriterController = TypeWriterController(text: '', duration: const Duration(milliseconds: 50));
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom + 16),
      child: BlocBuilder<HomeFeedBloc, HomeFeedState>(
        builder: (context, state) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Show AI generated response if available
              if (state.aiGenerateMassageOrCommentModel?.result != null &&
                  (state.aiGenerateMassageOrCommentModel?.result.isNotEmpty ?? false))
                _buildAIResponseSection(state),

              Divider(color: Color(0xffD9D9D9), thickness: 1, height: 1),
              buildSizedBoxH(8.0),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _emojiText('💗'),
                  _emojiText('💸'),
                  _emojiText('🔥'),
                  _emojiText('👏🏻'),
                  _emojiText('😢'),
                  _emojiText('😍'),
                  _emojiText('😮'),
                  _emojiText('😂'),
                ],
              ),
              Divider(color: Color(0xffD9D9D9), thickness: 1),
              buildSizedBoxH(8.0),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Padding(padding: EdgeInsets.all(6)),
                  Container(
                    height: 35.0.h,
                    width: 35.0.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100.r),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(100.r),
                      child: ValueListenableBuilder<String>(
                        valueListenable: profileImageNotifier,
                        builder: (context, imagePath, child) {
                          return Container(
                            height: 28.0.h,
                            width: 28.0.w,
                            decoration: BoxDecoration(
                              border: Border.all(color: Theme.of(context).primaryColor.withOpacity(0.2), width: 2),
                              borderRadius: BorderRadius.circular(100.r),
                            ),
                            padding: imagePath == AssetConstants.pngUser ? EdgeInsets.all(7) : EdgeInsets.all(1.5),
                            child: ClipRRect(
                              clipBehavior: Clip.antiAlias,
                              child: CustomImageView(
                                radius: imagePath == AssetConstants.pngUser ? null : BorderRadius.circular(100.0.r),
                                fit: (imagePath == AssetConstants.pngUser) ? BoxFit.contain : BoxFit.cover,
                                imagePath: imagePath,
                                alignment: ((imagePath == AssetConstants.pngUser) && imagePath.isEmpty)
                                    ? Alignment.center
                                    : null,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  buildSizedBoxW(15),
                  Expanded(
                    child: TextField(
                      controller: widget.textEditingController,
                      focusNode: widget.focusNode,
                      maxLines: 1,
                      style: Theme.of(context).textTheme.bodyMedium,
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.transparent)),
                        focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.transparent)),
                        hintText:
                            widget.isReply ? 'Reply to @${widget.replyToUsername}' : Lang.of(context).lbl_add_a_comment,
                        isDense: true,
                        contentPadding: EdgeInsets.only(top: 8.0.h, bottom: 8.0.h, right: 4.0.w),
                      ),
                    ),
                  ),
                  // AI Button
                  _AIButton(
                    textEditingController: widget.textEditingController,
                    onAIButtonPressed: widget.onAIButtonPressed,
                    isReply: widget.isReply,
                    replyToCommentText: widget.replyToCommentText,
                  ),
                  buildSizedBoxW(5),
                  _DoneButton(
                    textEditorFocusNode: widget.focusNode,
                    textEditingController: widget.textEditingController,
                    onSubmitted: widget.onSubmitted,
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  // Widget to show AI generated response
  Widget _buildAIResponseSection(HomeFeedState state) {
    typeWriterController = TypeWriterController(
        text: state.aiGenerateMassageOrCommentModel?.result ?? "", duration: const Duration(milliseconds: 50));
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Theme.of(context).primaryColor.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(Icons.auto_fix_high_rounded, color: Theme.of(context).primaryColor, size: 16),
                  buildSizedBoxW(8),
                  Text(
                    'AI Generated Comment',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).primaryColor,
                        ),
                  ),
                ],
              ),
              InkWell(
                onTap: () {
                  context.read<HomeFeedBloc>().add(ClearAIGeneratedCommentEvent());
                },
                child: Padding(
                  padding: EdgeInsets.only(left: 8.w, right: 8.w, bottom: 8.w, top: 4.w),
                  child: Icon(Icons.close_rounded, color: Theme.of(context).primaryColor, size: 16),
                ),
              )
            ],
          ),
          buildSizedBoxH(8),
          TypeWriter(
              controller: typeWriterController,
              builder: (context, value) {
                return Text(
                  value.text,
                  style: Theme.of(context).textTheme.bodyMedium,
                );
              }),
          buildSizedBoxH(8),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  // Clear AI response
                  context.read<HomeFeedBloc>().add(ClearAIGeneratedCommentEvent());

                  String textToSend = widget.textEditingController.text.trim();

                  // For replies, extract the actual message content
                  if (widget.isReply) {
                    final usernamePattern = RegExp(r'^@\w+\s*');
                    textToSend = textToSend.replaceFirst(usernamePattern, '').trim();
                  }

                  // Call AI API with context
                  widget.onAIButtonPressed(textToSend, widget.replyToCommentText);

                  // Also trigger the bloc event
                  context.read<HomeFeedBloc>().add(AIgenerateCommentEvent(
                      context: context,
                      massageText: (widget.replyToCommentText?.isEmpty ?? true) || widget.replyToCommentText == null
                          ? textToSend
                          : widget.replyToCommentText.toString()));
                },
                child: Text(
                  'Re-Generate',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).primaryColor,
                      ),
                ),
              ),
              buildSizedBoxW(8),
              ElevatedButton(
                onPressed: () {
                  // Use AI response
                  // final currentText = widget.textEditingController.text;
                  // final aiResponse = state.aiGenerateMassageOrCommentModel!.result!;

                  // // If replying, keep the @username part
                  // if (widget.isReply && widget.replyToUsername != null) {
                  //   final usernameTag = '@${widget.replyToUsername} ';
                  //   if (currentText.startsWith(usernameTag)) {
                  //     widget.textEditingController.text = usernameTag + aiResponse;
                  //   } else {
                  //     widget.textEditingController.text = aiResponse;
                  //   }
                  // } else {
                  //   widget.textEditingController.text = aiResponse;
                  // }
                  final currentText = widget.textEditingController.text;
                  final rawAiResponse = state.aiGenerateMassageOrCommentModel?.result ?? "";
                  String aiResponse = "";

                  if (rawAiResponse.isNotEmpty) {
                    try {
                      aiResponse = json.decode(rawAiResponse) as String;
                    } catch (e) {
                      // જો JSON decoding fail થાય, તો Raw text જ fallback
                      aiResponse = rawAiResponse;
                    }
                  }

                  // If replying, keep the @username part
                  if (widget.isReply && widget.replyToUsername != null) {
                    final usernameTag = '@${widget.replyToUsername} ';
                    if (currentText.startsWith(usernameTag)) {
                      widget.textEditingController.text = usernameTag + aiResponse;
                    } else {
                      widget.textEditingController.text = aiResponse;
                    }
                  } else {
                    widget.textEditingController.text = aiResponse;
                  }

                  // Clear AI response
                  context.read<HomeFeedBloc>().add(ClearAIGeneratedCommentEvent());
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text('Apply'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Widget to display emoji text
  Widget _emojiText(String emoji) {
    return GestureDetector(
      onTap: () {
        widget.textEditingController.text += emoji;
        widget.textEditingController.selection = TextSelection.fromPosition(
          TextPosition(offset: widget.textEditingController.text.length),
        );
      },
      child: Text(
        emoji,
        style: const TextStyle(fontSize: 20),
      ),
    );
  }
}

// Enhanced AI Button Widget
class _AIButton extends StatefulWidget {
  const _AIButton({
    required this.textEditingController,
    required this.onAIButtonPressed,
    required this.isReply,
    this.replyToCommentText,
  });

  final TextEditingController textEditingController;
  final Function(String, String?) onAIButtonPressed;
  final bool isReply;
  final String? replyToCommentText;

  @override
  State<_AIButton> createState() => _AIButtonState();
}

class _AIButtonState extends State<_AIButton> {
  bool _isEnabled = false;

  @override
  void initState() {
    super.initState();
    widget.textEditingController.addListener(_updateButtonState);
  }

  @override
  void dispose() {
    widget.textEditingController.removeListener(_updateButtonState);
    super.dispose();
  }

  void _updateButtonState() {
    final text = widget.textEditingController.text.trim();

    // For replies, check if there's content after the @username
    if (widget.isReply) {
      final usernamePattern = RegExp(r'^@\w+\s*');
      final textWithoutUsername = text.replaceFirst(usernamePattern, '').trim();

      // Enable if there's meaningful content after @username
      final isOnlyEmojis = _isOnlyEmojis(textWithoutUsername);
      setState(() {
        _isEnabled = textWithoutUsername.isNotEmpty && !isOnlyEmojis;
      });
    } else {
      // For regular comments
      final isTextEmpty = text.isEmpty;
      final isOnlyEmojis = _isOnlyEmojis(text);
      setState(() {
        _isEnabled = !isTextEmpty && !isOnlyEmojis;
      });
    }
  }

  bool _isOnlyEmojis(String text) {
    if (text.isEmpty) return false;

    // Remove common emojis and check if anything remains
    final cleanText = text.replaceAll(RegExp(r'[💗💸🔥👏🏻😢😍😮😂]'), '').replaceAll(RegExp(r'\s+'), '');

    return cleanText.isEmpty;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeFeedBloc, HomeFeedState>(
      builder: (context, state) {
        final isLoading = state.isAIGeneratingMassageloading;

        return GestureDetector(
          onTap: _isEnabled && !isLoading
              ? () {
                  // Clear AI response
                  context.read<HomeFeedBloc>().add(ClearAIGeneratedCommentEvent());

                  String textToSend = widget.textEditingController.text.trim();

                  // For replies, extract the actual message content
                  if (widget.isReply) {
                    final usernamePattern = RegExp(r'^@\w+\s*');
                    textToSend = textToSend.replaceFirst(usernamePattern, '').trim();
                  }

                  // Call AI API with context
                  widget.onAIButtonPressed(textToSend, widget.replyToCommentText);

                  // Also trigger the bloc event
                  context.read<HomeFeedBloc>().add(AIgenerateCommentEvent(
                      context: context,
                      massageText: (widget.replyToCommentText?.isEmpty ?? true) || widget.replyToCommentText == null
                          ? textToSend
                          : widget.replyToCommentText.toString()));
                }
              : null,
          child: CircleAvatar(
            radius: 16.r,
            backgroundColor: _isEnabled && !isLoading
                ? Theme.of(context).primaryColor
                : Theme.of(context).primaryColor.withOpacity(0.3),
            child: isLoading
                ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CupertinoActivityIndicator(),
                  )
                : Icon(Icons.auto_fix_high_rounded, color: Colors.white, size: 20.sp),
          ),
        );
      },
    );
  }
}

class _DoneButton extends StatefulWidget {
  const _DoneButton({
    required this.onSubmitted,
    required this.textEditorFocusNode,
    required this.textEditingController,
  });

  final Function(String?) onSubmitted;
  final FocusNode textEditorFocusNode;
  final TextEditingController textEditingController;

  @override
  State<_DoneButton> createState() => _DoneButtonState();
}

class _DoneButtonState extends State<_DoneButton> {
  @override
  void initState() {
    super.initState();
    widget.textEditingController.addListener(() {
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        widget.onSubmitted(widget.textEditingController.text.trim());
      },
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.0.w),
        child: Container(
          height: 35.h,
          color: Colors.transparent,
          padding: EdgeInsets.only(right: 6.0.w),
          child: CustomImageView(
            height: 20.0.h,
            width: 20.0.w,
            imagePath: Assets.images.icons.other.icSend.path,
          ),
        ),
      ),
    );
  }
}
