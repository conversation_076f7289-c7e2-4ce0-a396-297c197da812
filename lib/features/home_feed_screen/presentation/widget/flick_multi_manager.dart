import 'dart:io';
import 'package:flick_video_player/flick_video_player.dart';
import 'package:flowkar/core/utils/exports.dart';

bool isMute = true;

class FlickMultiManager {
  final List<FlickManager> _flickManagers = [];
  FlickManager? _activeManager;

  init(FlickManager flickManager) {
    _flickManagers.add(flickManager);
    if (isMute) {
      flickManager.flickControlManager?.mute();
    } else {
      flickManager.flickControlManager?.unmute();
    }
    if (_flickManagers.length == 1) {
      play(flickManager);
    }
  }

  remove(FlickManager flickManager) {
    if (_activeManager == flickManager) {
      _activeManager = null;
    }
    flickManager.dispose();
    _flickManagers.remove(flickManager);
  }

  togglePlay(FlickManager flickManager) {
    if (_activeManager?.flickVideoManager?.isPlaying == true && flickManager == _activeManager) {
      pause();
    } else {
      play(flickManager);
    }
  }

  pause() {
    _activeManager?.flickControlManager?.pause();
  }

  play([FlickManager? flickManager]) {
    if (flickManager != null) {
      _activeManager?.flickControlManager?.pause();
      _activeManager = flickManager;
    }

    if (isMute) {
      _activeManager?.flickControlManager?.mute();
    } else {
      _activeManager?.flickControlManager?.unmute();
    }

    // For iOS, add a small delay to ensure proper video initialization
    if (Platform.isIOS) {
      Logger.lOG('FlickMultiManager: Playing video on iOS with delay');
      Future.delayed(const Duration(milliseconds: 50), () {
        Logger.lOG('FlickMultiManager: Executing delayed play on iOS');
        _activeManager?.flickControlManager?.play();
      });
    } else {
      Logger.lOG('FlickMultiManager: Playing video on Android immediately');
      _activeManager?.flickControlManager?.play();
    }
  }

  toggleMute() {
    _activeManager?.flickControlManager?.toggleMute();
    isMute = _activeManager?.flickControlManager?.isMute ?? false;
    if (isMute) {
      _activeManager?.flickControlManager?.mute();
    } else {
      _activeManager?.flickControlManager?.unmute();
    }
  }
}
