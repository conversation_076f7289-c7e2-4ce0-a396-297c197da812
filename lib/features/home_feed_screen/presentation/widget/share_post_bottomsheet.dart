import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/sm_chat/bloc/sm_chat_bloc.dart';
import 'package:share_plus/share_plus.dart';

class ShareBottomSheet extends StatefulWidget {
  final String postId;
  final String shareType;

  const ShareBottomSheet({super.key, required this.postId, required this.shareType});

  @override
  State<ShareBottomSheet> createState() => _ShareBottomSheetState();
}

class _ShareBottomSheetState extends State<ShareBottomSheet> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  bool isExpanded = false;
  // int? selectedIndex;
  int toUserId = 0;

  List<int> selectedUserIds = [];

  final List<Map<String, dynamic>> _shareOptions = [
    {
      'name': Lang.current.lbl_share,
      'icon': Assets.images.svg.homeFeed.svgPostShareMore.path,
      'color': Colors.grey,
    },
  ];

  @override
  void initState() {
    context.read<HomeFeedBloc>().add(UserSearchQueryChanged(''));
    super.initState();
    context.read<HomeFeedBloc>().add(PostShareEvent(context, postId: widget.postId, shareType: 'fxs', toUserId: 0));
    context.read<SmChatBloc>().add(const GetChatListEvent(page: 1, isReload: false));
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels > 20 && !isExpanded) {
      setState(() => isExpanded = true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeFeedBloc, HomeFeedState>(
      builder: (context, homeFeedState) {
        return Stack(
          alignment: Alignment.bottomCenter,
          children: [
            _buildDraggableSheet(homeFeedState),
            _buildBottomContainer(homeFeedState),
          ],
        );
      },
    );
  }

  Widget _buildDraggableSheet(HomeFeedState homeFeedState) {
    return DraggableScrollableSheet(
      initialChildSize: 0.6,
      minChildSize: 0.4,
      maxChildSize: 0.8,
      expand: false,
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).customColors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
          ),
          child: ListView(
            controller: scrollController,
            padding: const EdgeInsets.symmetric(vertical: 20),
            children: [
              _buildDragHandle(),
              buildSizedBoxH(16),
              _buildTitle(),
              buildSizedBoxH(16),
              _buildSearchBar(context, homeFeedState),
              buildSizedBoxH(16),
              homeFeedState.searchQuery.isEmpty ? _buildRecentContactsRow() : _buildAccountsList(homeFeedState),
              buildSizedBoxH(selectedUserIds.isNotEmpty ? 186 : 116),
              buildSizedBoxH(MediaQuery.of(context).viewInsets.bottom),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDragHandle() {
    return Center(
      child: Container(
        width: 40.w,
        height: 5.h,
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(10.r),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Center(
      child: Text(
        'Share via',
        style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context, HomeFeedState homeFeedState) {
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: _searchController,
      builder: (context, value, child) {
        return Padding(
          padding: EdgeInsets.only(left: 16.0.w, right: 16.0.w),
          child: Row(
            children: [
              Expanded(
                child: FlowkarTextFormField(
                  hint: Lang.of(context).lbl_search,
                  context: context,
                  controller: _searchController,
                  prefixIcon: CustomImageView(
                    imagePath: AssetConstants.icSearch,
                    height: 10,
                    width: 10,
                    margin: EdgeInsets.all(15.0),
                  ),
                  textStyle: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.copyWith(fontSize: 16.sp, color: Theme.of(context).customColors.black),
                  hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontSize: 16.sp, color: Theme.of(context).customColors.greylite, fontWeight: FontWeight.w500),
                  onChanged: (value) {
                    context.read<HomeFeedBloc>().add(UserSearchQueryChanged(value));
                    context.read<HomeFeedBloc>().add(SharePostSearchUserListEvent(searchtext: value));
                  },
                  fillColor: ThemeData().customColors.fillcolor,
                  borderDecoration: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(10.r)),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
                  suffixIcon: value.text.isNotEmpty
                      ? CustomImageView(
                          imagePath: AssetConstants.icClose,
                          height: 10.0.h,
                          width: 10.0.w,
                          margin: EdgeInsets.all(16.5),
                          onTap: () {
                            FocusManager.instance.primaryFocus?.unfocus();
                            _searchController.clear();
                            context.read<HomeFeedBloc>().add(UserSearchQueryChanged(''));
                          },
                        )
                      : null,
                ),
              ),
              buildSizedBoxW(8),
              GestureDetector(
                onTap: () => _handleShareAction(homeFeedState),
                child: Column(
                  children: [
                    Container(
                      height: 52.0.h,
                      width: 52.0.w,
                      padding: EdgeInsets.all(14),
                      decoration: BoxDecoration(
                        color: _shareOptions[0]['color'].withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: CustomImageView(
                        imagePath: _shareOptions[0]['icon'],
                        alignment: Alignment.center,
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        );
      },
    );
  }

  Widget _buildBottomContainer(HomeFeedState homeFeedState) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.white,
        border: Border(top: BorderSide(color: Theme.of(context).customColors.greydivider, width: 1.w)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // _buildShareOptionsGrid(homeFeedState),
          if (selectedUserIds.isNotEmpty) _buildSendButton(homeFeedState),
        ],
      ),
    );
  }

  // Widget _buildShareOptionsGrid(HomeFeedState homeFeedState) {
  //   return GridView.builder(
  //     shrinkWrap: true,
  //     physics: NeverScrollableScrollPhysics(),
  //     padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
  //     gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
  //       crossAxisCount: 4,
  //       childAspectRatio: 0.9,
  //       crossAxisSpacing: 10,
  //       mainAxisSpacing: 15,
  //     ),
  //     itemCount: _shareOptions.length,
  //     itemBuilder: (context, index) {
  //       final option = _shareOptions[index];
  //       return GestureDetector(
  //         onTap: () => _handleShareAction(homeFeedState),
  //         child: Column(
  //           children: [
  //             Container(
  //               height: 52.0.h,
  //               width: 52.0.w,
  //               padding: EdgeInsets.all(14),
  //               decoration: BoxDecoration(
  //                 color: option['color'].withOpacity(0.1),
  //                 shape: BoxShape.circle,
  //               ),
  //               child: CustomImageView(
  //                 imagePath: option['icon'],
  //                 alignment: Alignment.center,
  //               ),
  //             ),
  //             buildSizedBoxH(8),
  //             Text(
  //               option['name'],
  //               style: Theme.of(context)
  //                   .textTheme
  //                   .bodyMedium
  //                   ?.copyWith(fontSize: 12.sp),
  //               textAlign: TextAlign.center,
  //             ),
  //           ],
  //         ),
  //       );
  //     },
  //   );
  // }

  Widget _buildRecentContactsRow() {
    return BlocBuilder<SmChatBloc, SmChatState>(
      builder: (context, smChatState) {
        if (smChatState.isloding) {
          return buildShimmerRecentContactsRow();
        }

        // Get the current state to access search results
        final homeFeedState = context.read<HomeFeedBloc>().state;
        final searchResults = homeFeedState.searchuserList ?? [];

        // Combine recent contacts with selected users from search
        final Set<dynamic> combinedUsers = {};

        // Add all recent contacts
        for (var user in smChatState.chatList) {
          combinedUsers.add(user);
        }

        // Add selected users from search results that aren't in recent contacts
        for (var user in searchResults) {
          if (selectedUserIds.contains(user.userId ?? 0)) {
            // Check if user is not already in recent contacts
            bool exists = smChatState.chatList.any((recentUser) => recentUser.userId == user.userId);
            if (!exists) {
              combinedUsers.add(user);
            }
          }
        }

        // Convert to list and sort to show selected users first
        final sortedList = combinedUsers.toList();
        sortedList.sort((a, b) {
          bool aSelected = selectedUserIds.contains(a.userId ?? 0);
          bool bSelected = selectedUserIds.contains(b.userId ?? 0);
          if (aSelected && !bSelected) return -1;
          if (!aSelected && bSelected) return 1;
          return 0;
        });

        return sortedList.isEmpty
            ? _buildNoDataFound()
            : Align(
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Wrap(
                    runSpacing: 20.h,
                    children: List.generate(sortedList.length, (index) {
                      final user = sortedList[index];
                      final isSelected = selectedUserIds.contains(user?.userId ?? 0);

                      return InkWell(
                        onTap: () {
                          setState(() {
                            if (isSelected) {
                              selectedUserIds.remove(user?.userId ?? 0);
                            } else {
                              selectedUserIds.add(user?.userId ?? 0);
                            }
                          });
                        },
                        child: Container(
                          width: 70.w,
                          margin: EdgeInsets.symmetric(horizontal: 8.h),
                          child: Column(
                            children: [
                              Stack(
                                alignment: Alignment.bottomRight,
                                children: [
                                  _buildUserAvatar(
                                      user?.profileImage == "" || user?.profileImage == null
                                          ? AssetConstants.pngUserReomve
                                          : "${APIConfig.mainbaseURL}${user?.profileImage ?? ''}",
                                      isSelected),
                                  if (isSelected)
                                    Padding(
                                      padding: EdgeInsets.symmetric(horizontal: 4.w),
                                      child: Icon(Icons.check_circle_rounded,
                                          color: Theme.of(context).primaryColor, size: 18.sp),
                                    ),
                                ],
                              ),
                              buildSizedBoxH(5),
                              Text(
                                user?.userName ?? "",
                                maxLines: 2,
                                textAlign: TextAlign.center,
                                overflow: TextOverflow.ellipsis,
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }),
                  ),
                ),
              );
      },
    );
  }

  Widget _buildNoDataFound() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        buildSizedBoxH(MediaQuery.of(context).size.height / 6),
        Text(
          "No Users Yet",
          style: TextStyle(fontSize: 13.sp, fontWeight: FontWeight.bold),
        ),
        buildSizedBoxH(8.0),
        Text(
          "Search the user and share the post.",
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w400,
              ),
        ),
      ],
    );
  }

  Widget _buildAccountsList(HomeFeedState state) {
    if (state.searchuserList == null || state.searchuserList!.isEmpty) {
      return Center(
          child: ExceptionWidget(
        imagePath: AssetConstants.pngNoResultFound,
        title: Lang.of(context).lbl_no_result_found,
        subtitle: Lang.of(context).lbl_no_result_found_subtitle,
        showButton: false,
      ));
    }

    return BlocBuilder<SmChatBloc, SmChatState>(
      builder: (context, smChatState) {
        if (smChatState.isloding) {
          return buildShimmerRecentContactsRow();
        }

        // Sort the list to show selected users first
        final sortedList = List.from(state.searchuserList ?? []);
        sortedList.sort((a, b) {
          bool aSelected = selectedUserIds.contains(a?.userId ?? 0);
          bool bSelected = selectedUserIds.contains(b?.userId ?? 0);
          if (aSelected && !bSelected) return -1;
          if (!aSelected && bSelected) return 1;
          return 0;
        });

        return Align(
          alignment: Alignment.centerLeft,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Wrap(
              runSpacing: 20.h,
              children: List.generate(sortedList.length, (index) {
                final user = sortedList[index];
                final isSelected = selectedUserIds.contains(user?.userId ?? 0);

                return InkWell(
                  onTap: () {
                    setState(() {
                      if (isSelected) {
                        selectedUserIds.remove(user?.userId ?? 0);
                      } else {
                        selectedUserIds.add(user?.userId ?? 0);
                      }
                    });
                  },
                  child: Container(
                    width: 70.w,
                    margin: EdgeInsets.symmetric(horizontal: 8.h),
                    child: Column(
                      children: [
                        Stack(
                          alignment: Alignment.bottomRight,
                          children: [
                            _buildUserAvatar(
                                user?.profileImage == ""
                                    ? AssetConstants.pngUserReomve
                                    : "${APIConfig.mainbaseURL}${user?.profileImage ?? ''}",
                                isSelected),
                            if (isSelected)
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: 4.w),
                                child: Icon(Icons.check_circle_rounded,
                                    color: Theme.of(context).primaryColor, size: 18.sp),
                              ),
                          ],
                        ),
                        buildSizedBoxH(5),
                        Text(
                          user?.userName ?? "",
                          maxLines: 2,
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                      ],
                    ),
                  ),
                );
              }),
            ),
          ),
        );
      },
    );
  }

  Widget buildShimmerRecentContactsRow() {
    return Align(
      alignment: Alignment.topCenter,
      child: Wrap(
        runSpacing: 20.h,
        children: List.generate(20, (index) {
          return Container(
            width: 70.w,
            margin: EdgeInsets.symmetric(horizontal: 8.h),
            child: Column(
              children: [
                Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    height: 52.0.h,
                    width: 52.0.w,
                    margin: EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
                buildSizedBoxH(5),
                Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    height: 12.h,
                    width: 50.w,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                  ),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget _buildUserAvatar(String? imagePath, bool isSelected) {
    final fullImagePath = (imagePath?.isNotEmpty ?? false)
        ? (imagePath!.startsWith('/media/') ? APIConfig.mainbaseURL + imagePath : imagePath)
        : AssetConstants.pngUserReomve;

    return Container(
      height: 52.0.h,
      width: 52.0.w,
      margin: EdgeInsets.all(4),
      padding: EdgeInsets.all(2),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(isSelected ? 1 : 0.2),
          width: 2.w,
        ),
      ),
      child: CustomImageView(
        radius: BorderRadius.circular(100.r),
        fit: BoxFit.cover,
        imagePath: fullImagePath,
      ),
    );
  }

  Widget _buildSendButton(HomeFeedState homeFeedState) {
    return Column(
      children: [
        CustomElevatedButton(
          width: double.infinity,
          text: "Send",
          isLoading: homeFeedState.isPostShareLoading,
          isDisabled: homeFeedState.isPostShareLoading,
          brderRadius: 10.r,
          margin: EdgeInsets.symmetric(horizontal: 20.w),
          onPressed: () {
            FocusManager.instance.primaryFocus?.unfocus();
            _searchController.clear();

            // Filter out any null or 0 values from selectedUserIds
            final validUserIds = selectedUserIds.where((id) => id != 0).toList();

            if (validUserIds.isEmpty) {
              // Show error message if no users are selected
              toastification.show(
                type: ToastificationType.error,
                showProgressBar: false,
                title: Text('Please select at least one user to share with'),
                autoCloseDuration: const Duration(seconds: 3),
              );
              return;
            }

            context.read<HomeFeedBloc>().add(SharePostMessageEvent(postId: widget.postId, toUserId: validUserIds));
            Navigator.pop(context);
            context.read<HomeFeedBloc>().add(UserSearchQueryChanged(''));
          },
        ),
        buildSizedBoxH(16),
      ],
    );
  }

  void _handleShareAction(HomeFeedState homeFeedState) {
    final url = homeFeedState.postShareModel?.shareUrl;
    if (url == null || url.isEmpty) return;

    Share.shareUri(Uri.parse(url));
    Navigator.of(context).pop();
  }
}
