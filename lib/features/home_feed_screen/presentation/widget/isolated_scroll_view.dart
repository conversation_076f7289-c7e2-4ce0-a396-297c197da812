// Custom widget to isolate scroll behavior
import 'package:flowkar/core/utils/exports.dart';

class IsolatedScrollView extends StatefulWidget {
  final Widget child;
  final ScrollPhysics? physics;
  final Function(bool isAtTop)? onScrollPositionChanged;

  const IsolatedScrollView({
    super.key,
    required this.child,
    this.physics,
    this.onScrollPositionChanged,
  });

  @override
  // ignore: library_private_types_in_public_api
  _IsolatedScrollViewState createState() => _IsolatedScrollViewState();
}

class _IsolatedScrollViewState extends State<IsolatedScrollView> {
  late ScrollController _scrollController;
  bool _isAtTop = true;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final isAtTop = _scrollController.offset <= 0;
    if (_isAtTop != isAtTop) {
      setState(() {
        _isAtTop = isAtTop;
      });
      widget.onScrollPositionChanged?.call(isAtTop);
    }
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification notification) {
        // Completely block scroll notifications from reaching parent widgets
        return true;
      },
      child: GestureDetector(
        onPanDown: (_) {}, // Consume touch events
        onPanUpdate: (_) {}, // Consume pan gestures
        onPanEnd: (_) {}, // Consume pan end events
        child: SingleChildScrollView(
          controller: _scrollController,
          physics: widget.physics ?? const ClampingScrollPhysics(),
          child: widget.child,
        ),
      ),
    );
  }
}
