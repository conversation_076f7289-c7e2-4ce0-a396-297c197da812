import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/home_feed_screen/model/get_comment_model.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/comment_box.dart';
import 'package:flowkar/features/widgets/common/get_user_profile_by_id/get_user_profile_by_id.dart';

class CommentsBottomSheet extends StatefulWidget {
  final int postId;
  const CommentsBottomSheet({super.key, required this.postId});

  static Widget builder(BuildContext context) {
    final List args = ModalRoute.of(context)?.settings.arguments as List;
    return CommentsBottomSheet(postId: args[0]);
  }

  @override
  State<CommentsBottomSheet> createState() => _CommentsBottomSheetState();
}

class _CommentsBottomSheetState extends State<CommentsBottomSheet> with WidgetsBindingObserver {
  final TextEditingController commentEditingController = TextEditingController();
  final FocusNode commentFocusNode = FocusNode();
  final ScrollController _scrollController = ScrollController();

  final DraggableScrollableController _draggableController = DraggableScrollableController();

  bool isReply = false;
  String? _replyToCommentId;
  Map<String, bool> expandedReplies = {};

  bool _isFullyExpanded = false;
  bool _isKeyboardVisible = false;
  String currentUserId = '';
  String? _replyToCommentText; // Add this variable
  String? _replyToUsername; // Add this variable

  @override
  void initState() {
    super.initState();
    _fetchComments();
    _setupScrollListener();
    _setupFocusListener();
    WidgetsBinding.instance.addObserver(this);
    currentUserId = Prefobj.preferences!.get(Prefkeys.USER_ID).toString();
    _listenToAIResponse();
  }

  void _listenToAIResponse() {
    // Listen to BLoC state changes for
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _draggableController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = bottomInset > 0;

    if (isKeyboardVisible != _isKeyboardVisible) {
      setState(() {
        _isKeyboardVisible = isKeyboardVisible;
      });

      if (isKeyboardVisible) {
        _expandBottomSheet();
      }
      // Do not collapse the bottom sheet when the keyboard is hidden
    }
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.offset > 50 && !_isFullyExpanded) {
        _expandBottomSheet();
      }
    });
  }

  void _setupFocusListener() {
    commentFocusNode.addListener(() {
      if (commentFocusNode.hasFocus) {
        Future.delayed(const Duration(milliseconds: 100), () {
          _expandBottomSheet();
        });
      }
    });
  }

  void _expandBottomSheet() {
    setState(() {
      _isFullyExpanded = true;
    });
    _draggableController.animateTo(
      0.95,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void collapseBottomSheet() {
    if (!_isKeyboardVisible && !commentFocusNode.hasFocus) {
      setState(() {
        _isFullyExpanded = false;
      });
      _draggableController.animateTo(
        0.625,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _fetchComments() async {
    context.read<HomeFeedBloc>().add(GetCommentApiEvent(postId: widget.postId));
  }

  // void _handleReply(String username, String commentId) {
  //   setState(() {
  //     _replyToCommentId = commentId;
  //     isReply = true;
  //     commentEditingController.text = '@$username ';
  //     commentFocusNode.requestFocus();
  //     expandedReplies[commentId] = true;
  //   });
  // }
  void _handleReply(String username, String commentId, String commentText) {
    setState(() {
      _replyToCommentId = commentId;
      _replyToCommentText = commentText; // Store the comment text for AI context
      isReply = true;
      commentEditingController.text = '@$username ';
      commentFocusNode.requestFocus();
      expandedReplies[commentId] = true;
    });
  }

  double _getNoDataSpacingHeight() {
    if (_isFullyExpanded && !_isKeyboardVisible) {
      return MediaQuery.of(context).size.height / 5;
    } else {
      return MediaQuery.of(context).size.height / 7;
    }
  }

  void _toggleReplies(String commentId) {
    setState(() {
      expandedReplies[commentId] = !(expandedReplies[commentId] ?? false);
    });
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      controller: _draggableController,
      initialChildSize: 0.6,
      minChildSize: 0.6,
      maxChildSize: 0.95,
      expand: false,
      snap: true,
      snapSizes: const [0.6, 0.95],
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r),
              topRight: Radius.circular(20.r),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.only(
              bottom: 0,
            ),
            child: BlocBuilder<ThemeBloc, ThemeState>(
              builder: (context, themeState) {
                return BlocBuilder<HomeFeedBloc, HomeFeedState>(
                  builder: (context, state) {
                    return Column(
                      children: [
                        _buildAppBar(),
                        Expanded(
                          child: state.isCommentloding
                              ? const LoadingAnimationWidget()
                              : state.getcommentModel?.comments == null || state.getcommentModel!.comments.isEmpty
                                  ? ListView(
                                      controller: scrollController,
                                      physics: const ClampingScrollPhysics(),
                                      children: [
                                        buildSizedBoxH(_getNoDataSpacingHeight()),
                                        _buildNoDataFound(),
                                      ],
                                    )
                                  : _buildCommentsList(scrollController, state),
                        ),
                        _buildCommentInputSection(),
                        buildSizedBoxH(8.0),
                      ],
                    );
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildCommentsList(ScrollController scrollController, HomeFeedState state) {
    return NotificationListener<ScrollNotification>(
      onNotification: (scrollNotification) {
        if (scrollNotification is ScrollStartNotification) {
        } else if (scrollNotification is ScrollUpdateNotification) {
          if (scrollNotification.scrollDelta! > 0 && !_isFullyExpanded) {
            _expandBottomSheet();
          }
        }
        return false;
      },
      child: ListView.builder(
        controller: scrollController,
        physics: const ClampingScrollPhysics(),
        itemCount: state.getcommentModel!.comments.length,
        itemBuilder: (ctx, index) {
          final reversedIndex = state.getcommentModel!.comments.length - 1 - index;
          if (reversedIndex < 0) {
            return const SizedBox.shrink();
          }
          final comment = state.getcommentModel!.comments[reversedIndex];
          if (index >= state.getcommentModel!.comments.length) {
            return const SizedBox.shrink();
          }

          final replyCount = comment.replies.length;
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0.w, vertical: 8.0.h),
            child: _buildCommentItem(comment, replyCount),
          );
        },
      ),
    );
  }

  Widget _buildCommentItem(CommentData comment, int replyCount) {
    return Dismissible(
      key: Key(comment.id.toString()),
      direction: comment.user.id.toString() != Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
          ? DismissDirection.none
          : DismissDirection.endToStart,
      background: _buildDismissBackground(),
      confirmDismiss: comment.user.id.toString() != Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
          ? null
          : (direction) => _showDeleteDialog(comment.id, widget.postId.toString(), false),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCommentRow(comment),
          buildSizedBoxH(5),
          if (replyCount > 0) _buildRepliesSection(comment),
        ],
      ),
    );
  }

  Widget _buildDismissBackground() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(4.0.r),
      ),
      child: Align(
        alignment: Alignment.centerRight,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomImageView(
                imagePath: Assets.images.svg.setting.svgDeleteAccount.path,
                height: 20.0.h,
              ),
              buildSizedBoxW(10),
              Text("Delete"),
            ],
          ),
        ),
      ),
    );
  }

  Future<bool?> _showDeleteDialog(int commentId, String postId, bool isReply) {
    return showDialog<bool>(
      context: context,
      builder: (ctx) {
        bool isLoading = false;
        return StatefulBuilder(
          builder: (ctx, setState) {
            return CustomAlertDialog(
              imagePath: Assets.images.pngs.pngNoPost.path,
              title: isReply ? Lang.of(context).lbl_delete_reply_comment : Lang.of(context).lbl_delete_comment,
              subtitle: "",
              onConfirmButtonPressed: () async {
                setState(() {
                  isLoading = true;
                });
                context.read<HomeFeedBloc>().add(
                      DeleteCommentApiEvent(
                        commentId: commentId,
                        postId: postId,
                      ),
                    );
                FocusScope.of(context).requestFocus(FocusNode());
              },
              confirmButtonText: Lang.of(ctx).lbl_delete,
              isLoading: isLoading,
            );
          },
        );
      },
    );
  }

  // Handle AI button press
  // void _handleAIButtonPress(String currentText) async {
  //   try {
  //     // Call AI API here
  //     // context.read<HomeFeedBloc>().add(
  //     //       AICommentGenerateEvent(
  //     //         inputText: currentText,
  //     //         isReply: isReply,
  //     //         replyToUsername: _replyToUsername,
  //     //       ),
  //     //     );
  //     Logger.lOG(currentText);
  //   } catch (e) {
  //     // Handle error
  //     print('AI API Error: $e');
  //   }
  // }
  void _handleAIButtonPress(String currentText, String? replyToCommentText) async {
    try {
      // Prepare the message for AI API
      String messageForAI = currentText;

      // If it's a reply, provide context to AI
      if (isReply && replyToCommentText != null) {
        messageForAI = "Reply to this comment: '$replyToCommentText'. My response: $currentText";
      }

      Logger.lOG("AI Input: $messageForAI");

      // The AI API call is already handled in the CommentBox widget
      // through the bloc event, so we don't need to call it again here
    } catch (e) {
      Logger.lOG('AI API Error: $e');
    }
  }

  // Widget _buildCommentInputSection() {
  //   return Align(
  //     alignment: Alignment.bottomCenter,
  //     child: CommentBox(
  //       textEditingController: commentEditingController,
  //       focusNode: commentFocusNode,
  //       onAIButtonPressed: _handleAIButtonPress,
  //       onSubmitted: (commenttext) {
  //         if (commenttext!.isNotEmpty) {
  //           if (isReply && _replyToCommentId != null) {
  //             context.read<HomeFeedBloc>().add(
  //                   ReplycommentSocketEvent(
  //                     commentId: _replyToCommentId!,
  //                     replyText: commenttext.trim(),
  //                   ),
  //                 );
  //           } else {
  //             context.read<HomeFeedBloc>().add(
  //                   CommentPostSocketEvent(
  //                     commentText: commenttext.trim(),
  //                     postId: widget.postId.toString(),
  //                   ),
  //                 );
  //           }
  //           setState(() {
  //             commentEditingController.clear();
  //             _replyToCommentId = null;
  //             isReply = false;
  //             if (_replyToCommentId != null) {
  //               expandedReplies[_replyToCommentId!] = true;
  //             }
  //           });
  //         }
  //         commentEditingController.clear();
  //       },
  //     ),
  //   );
  // }
  Widget _buildCommentInputSection() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: CommentBox(
        textEditingController: commentEditingController,
        focusNode: commentFocusNode,
        onAIButtonPressed: _handleAIButtonPress,
        isReply: isReply,
        replyToUsername: _replyToUsername,
        replyToCommentText: _replyToCommentText,
        onSubmitted: (commenttext) {
          if (commenttext!.isNotEmpty) {
            if (isReply && _replyToCommentId != null) {
              context.read<HomeFeedBloc>().add(
                    ReplycommentSocketEvent(
                      commentId: _replyToCommentId!,
                      replyText: commenttext.trim(),
                    ),
                  );
            } else {
              context.read<HomeFeedBloc>().add(
                    CommentPostSocketEvent(
                      commentText: commenttext.trim(),
                      postId: widget.postId.toString(),
                    ),
                  );
            }
            setState(() {
              commentEditingController.clear();
              _replyToCommentId = null;
              _replyToCommentText = null;
              _replyToUsername = null;
              isReply = false;
              if (_replyToCommentId != null) {
                expandedReplies[_replyToCommentId!] = true;
              }
            });
          }
          commentEditingController.clear();
        },
      ),
    );
  }

  Widget _buildAppBar() {
    return Column(
      children: [
        buildSizedBoxH(8),
        Container(
          width: 40.0.w,
          height: 4.0.h,
          decoration: BoxDecoration(
            color: Theme.of(context).customColors.bottomsheetHendalColor,
            borderRadius: BorderRadius.circular(100.r),
          ),
        ),
        buildSizedBoxH(17),
        Text(
          Lang.of(context).lbl_comments,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w700,
                color: Theme.of(context).customColors.black,
                fontSize: 18.sp,
              ),
        ),
        buildSizedBoxH(8),
      ],
    );
  }

  Widget _buildNoDataFound() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          Lang.of(context).lbl_no_comments_yet,
          style: TextStyle(fontSize: 13.sp, fontWeight: FontWeight.bold),
        ),
        buildSizedBoxH(8.0),
        Text(
          Lang.of(context).lbl_start_the_conversation,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w400,
              ),
        ),
      ],
    );
  }

  Widget _buildCommentRow(CommentData comment) {
    return InkWell(
      onTap: () {
        if (comment.user.id.toString() == currentUserId) {
          PersistentNavBarNavigator.pushNewScreen(context, screen: UserProfileScreen(), withNavBar: false);
          // NavigatorService.pushNamed(AppRoutes.userProfileScreen);
        } else {
          PersistentNavBarNavigator.pushNewScreen(context,
              screen: GetUserProfileById(userId: comment.user.id, stackonScreen: false), withNavBar: false);
        }
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildProfileImage(comment.user.profileImage ?? '', comment.user),
          buildSizedBoxW(8.0),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(
                    text: comment.user.username,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: 13.0.sp,
                          fontWeight: FontWeight.w700,
                        ),
                    children: [
                      TextSpan(
                        text: "   ${comment.commentText}",
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontSize: 11.0.sp,
                              fontWeight: FontWeight.w400,
                            ),
                      ),
                    ],
                  ),
                ),
                buildSizedBoxH(5),
                _buildCommentActions(comment),
              ],
            ),
          ),
          IconButton(
            icon: Icon(
              comment.isLiked ? Icons.favorite_rounded : Icons.favorite_border_rounded,
              size: 15.0.sp,
            ),
            color: comment.isLiked ? Theme.of(context).primaryColor : null,
            onPressed: () {
              context.read<HomeFeedBloc>().add(
                    LikeCommentSocketEvent(commentId: comment.id),
                  );
            },
          ),
        ],
      ),
    );
  }

  // Build the profile image for a comment
  Widget _buildProfileImage(String profileImageUrl, CommentUser user) {
    return Container(
      height: 40.0.h,
      width: 40.0.w,
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(100.r),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.2),
          width: 2.w,
        ),
      ),
      padding: (profileImageUrl == AssetConstants.pngUser || profileImageUrl.isEmpty || profileImageUrl == '') &&
              user.id.toString() == Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
          ? EdgeInsets.all(8)
          : EdgeInsets.all(1),
      child: ClipRRect(
        clipBehavior: Clip.antiAlias,
        borderRadius: (profileImageUrl == AssetConstants.pngUser || profileImageUrl.isEmpty || profileImageUrl == '') &&
                user.id.toString() == Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
            ? BorderRadius.zero
            : BorderRadius.circular(100.0),
        child: SizedBox(
          height: 30.0,
          width: 30.0,
          child: CustomImageView(
            radius: user.id.toString() == Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                ? null
                : BorderRadius.circular(100.0),
            fit: BoxFit.cover,
            imagePath: profileImageUrl == AssetConstants.pngUser || profileImageUrl.isEmpty || profileImageUrl == ''
                ? user.id.toString() == Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                    ? AssetConstants.pngUser
                    : AssetConstants.pngUserReomve
                : profileImageUrl,
            alignment: Alignment.center,
          ),
        ),
      ),
    );
  }

  // Build the actions for a single comment (likes, replies)
  Widget _buildCommentActions(CommentData comment) {
    return Row(
      children: [
        Text(
          DateTime.parse(comment.createdAt).createdTimeAgo(),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 11.0.sp),
        ),
        buildSizedBoxW(13),
        Text(
          '${comment.likesCount} ${Lang.of(context).lbl_like}',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 11.0.sp),
        ),
        buildSizedBoxW(15),
        InkWell(
          onTap: () {
            setState(() {
              _replyToUsername = comment.user.username;
            });
            _handleReply(comment.user.username, comment.id.toString(), comment.commentText);
          },
          child: Text(
            Lang.of(context).lbl_reply,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontSize: 10.0.sp,
                  fontWeight: FontWeight.w700,
                ),
          ),
        ),
      ],
    );
  }

  // Build the replies section for a comment
  Widget _buildRepliesSection(CommentData comment) {
    return Column(
      children: [
        Row(
          children: [
            Padding(padding: EdgeInsets.symmetric(horizontal: 25.0.w)),
            SizedBox(
              width: 20.w,
              child: Divider(
                color: Theme.of(context).customColors.dividerColor,
                thickness: 1,
              ),
            ),
            buildSizedBoxW(6),
            InkWell(
              onTap: () => _toggleReplies(comment.id.toString()),
              child: Text(
                expandedReplies[comment.id.toString()] == true
                    ? Lang.of(context).lbl_hide_replies
                    : '${Lang.of(context).lbl_view_replies} (${comment.replies.length})',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w700,
                      color: Theme.of(context).customColors.black,
                    ),
              ),
            ),
          ],
        ),
        if (expandedReplies[comment.id.toString()] ?? false)
          ...comment.replies.map(
            (reply) => Padding(
              padding: EdgeInsets.only(top: 4.0.h, bottom: 4.0.h, left: 16.0.w),
              child: _buildReplyItem(reply, comment),
            ),
          ),
      ],
    );
  }

  Widget _buildReplyItem(ReplyData reply, CommentData comment) {
    return Dismissible(
      key: Key(reply.id.toString()),
      direction: reply.user.id.toString() != Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
          ? DismissDirection.none
          : DismissDirection.endToStart,
      background: _buildDismissBackground(),
      confirmDismiss: reply.user.id.toString() != Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
          ? null
          : (direction) => _showDeleteDialog(reply.id, widget.postId.toString(), true),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildProfileImage(reply.user.profileImage ?? '', reply.user),
          buildSizedBoxW(8.0),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(
                    text: reply.user.username,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: 13.0.sp,
                          fontWeight: FontWeight.w700,
                        ),
                    children: [
                      TextSpan(
                        text: "   ${reply.replyText}",
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontSize: 11.0.sp,
                              fontWeight: FontWeight.w400,
                            ),
                      ),
                    ],
                  ),
                ),
                buildSizedBoxH(5),
                _buildReplyCommentActions(reply),
              ],
            ),
          ),
          IconButton(
            icon: Icon(
              reply.isLiked ? Icons.favorite_outlined : Icons.favorite_border_outlined,
              size: 15.0.sp,
            ),
            color: reply.isLiked ? Theme.of(context).primaryColor : null,
            onPressed: () {
              context.read<HomeFeedBloc>().add(
                    ReplayCommentLikeSocketEvent(commentId: reply.id),
                  );
            },
          ),
        ],
      ),
    );
  }

  // Build the actions for a reply comment (likes, replies)
  Widget _buildReplyCommentActions(ReplyData reply) {
    return Row(
      children: [
        Text(
          DateTime.parse(reply.createdAt).createdTimeAgo(),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 11.0.sp),
        ),
        buildSizedBoxW(13),
        Text(
          '${reply.likesCount} ${Lang.of(context).lbl_like}',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 11.0.sp),
        ),
      ],
    );
  }
}

extension TimeAgo on DateTime {
  String createdTimeAgo() {
    final now = DateTime.now();
    final difference = now.difference(this);

    if (difference.inSeconds < 60) {
      return '${difference.inSeconds} seconds ago';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hours ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      return '${(difference.inDays / 7).floor()} weeks ago';
    } else if (difference.inDays < 365) {
      return '${(difference.inDays / 30).floor()} months ago';
    } else {
      return '${(difference.inDays / 365).floor()} years ago';
    }
  }
}
