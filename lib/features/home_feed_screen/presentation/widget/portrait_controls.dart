import 'package:flick_video_player/flick_video_player.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_manager.dart';

class FeedPlayerPortraitControls extends StatelessWidget {
  const FeedPlayerPortraitControls({super.key, this.flickMultiManager, this.flickManager});

  final FlickMultiManager? flickMultiManager;
  final FlickManager? flickManager;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: <Widget>[
          FlickAutoHideChild(
            autoHide: true,
            showIfVideoNotInitialized: false,
            child: Align(
              alignment: Alignment.topRight,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                decoration: BoxDecoration(
                  color: Colors.black38,
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: const FlickLeftDuration(),
              ),
            ),
          ),
          const Expanded(child: SizedBox()),
          GestureDetector(
            onTap: () => flickMultiManager?.toggleMute(),
            child: Container(
              color: Colors.transparent,
              height: 30,
              width: 30,
              padding: const EdgeInsets.all(3.5),
              child: FlickAutoHideChild(
                autoHide: false,
                showIfVideoNotInitialized: false,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.black38,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: FlickSoundToggle(
                    size: 15.h,
                    toggleMute: () => flickMultiManager?.toggleMute(),
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
