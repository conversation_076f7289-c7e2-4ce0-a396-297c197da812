// import 'package:flowkar/core/utils/exports.dart';

// class CaptionPreviewWidget extends StatefulWidget {
//   final String caption;
//   final String comment;
//   final VoidCallback? commentonTap;

//   const CaptionPreviewWidget({
//     super.key,
//     required this.caption,
//     required this.comment,
//     this.commentonTap,
//   });

//   @override
//   State<CaptionPreviewWidget> createState() => _CaptionPreviewWidgetState();
// }

// class _CaptionPreviewWidgetState extends State<CaptionPreviewWidget> {
//   bool _isCaptionExpanded = false;
//   bool _hasCaptionOverflow = false;

//   bool _isCommentExpanded = false;
//   bool _hasCommentOverflow = false;

//   @override
//   Widget build(BuildContext context) {
//     return LayoutBuilder(
//       builder: (context, constraints) {
//         //Caption
//         final TextSpan captionTextSpan = TextSpan(
//           text: widget.caption,
//           style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontSize: 12.5.sp),
//         );

//         final TextPainter captionTextPainter = TextPainter(
//           text: captionTextSpan,
//           maxLines: 2,
//           textDirection: TextDirection.ltr,
//         )..layout(maxWidth: constraints.maxWidth);

//         _hasCaptionOverflow = captionTextPainter.didExceedMaxLines;

//         //Comment
//         final TextSpan commentTextSpan = TextSpan(
//           text: widget.comment,
//           style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontSize: 12.5.sp),
//         );

//         final TextPainter commentTextPainter = TextPainter(
//           text: commentTextSpan,
//           maxLines: 2,
//           textDirection: TextDirection.ltr,
//         )..layout(maxWidth: constraints.maxWidth);

//         _hasCommentOverflow = commentTextPainter.didExceedMaxLines;

//         return Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // Caption display
//             if (widget.caption.isNotEmpty)
//               Text(
//                 widget.caption,
//                 style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontSize: 12.5.sp),
//                 maxLines: _isCaptionExpanded ? null : 2,
//                 overflow: _isCaptionExpanded ? TextOverflow.visible : TextOverflow.ellipsis,
//               ),
//             // "See More / See Less" for caption
//             if (_hasCaptionOverflow && widget.caption.isNotEmpty)
//               GestureDetector(
//                 onTap: () {
//                   setState(() {
//                     _isCaptionExpanded = !_isCaptionExpanded;
//                   });
//                 },
//                 child: Padding(
//                   padding: const EdgeInsets.only(top: 2.0),
//                   child: Text(
//                     _isCaptionExpanded ? 'See Less' : 'See More',
//                     style: Theme.of(context).textTheme.labelSmall?.copyWith(
//                           fontWeight: FontWeight.w600,
//                           color: Theme.of(context).primaryColor,
//                         ),
//                   ),
//                 ),
//               ),

//             // Comment display
//             if (!_hasCaptionOverflow && widget.comment.isNotEmpty)
//               Padding(
//                 padding: const EdgeInsets.only(top: 5.0),
//                 child: InkWell(
//                   onTap: widget.commentonTap,
//                   child: Text(
//                     widget.comment,
//                     style: Theme.of(context).textTheme.headlineSmall?.copyWith(
//                           fontSize: 10.5.sp,
//                           fontWeight: FontWeight.w500,
//                         ),
//                     maxLines: _isCommentExpanded ? null : 2,
//                     overflow: _isCommentExpanded ? TextOverflow.visible : TextOverflow.ellipsis,
//                   ),
//                 ),
//               ),
//             // "See More / See Less" for Comment
//             if (!_hasCaptionOverflow && _hasCommentOverflow && widget.comment.isNotEmpty)
//               GestureDetector(
//                 onTap: () {
//                   setState(() {
//                     _isCommentExpanded = !_isCommentExpanded;
//                   });
//                 },
//                 child: Padding(
//                   padding: const EdgeInsets.only(top: 2.0),
//                   child: Text(
//                     _isCommentExpanded ? 'See Less' : 'See More',
//                     style: Theme.of(context).textTheme.labelSmall?.copyWith(
//                           fontWeight: FontWeight.w600,
//                           color: Theme.of(context).primaryColor,
//                         ),
//                   ),
//                 ),
//               ),
//           ],
//         );
//       },
//     );
//   }
// }
