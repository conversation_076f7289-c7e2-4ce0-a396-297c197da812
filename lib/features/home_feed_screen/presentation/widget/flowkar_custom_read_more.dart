// library social_media_caption_formatter;

// import 'package:detectable_text_field/widgets/detectable_text.dart';
// import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/widgets/common/get_user_profile_by_id/get_user_profile_by_id.dart';
import 'package:readmore/readmore.dart';

// ignore: must_be_immutable
class CustomReadMores extends StatefulWidget {
  final String text;
  TextAlign? textAlign;
  TextDirection? textDirection;
  Locale? locale;
  TextScaler? textScaler;
  ValueNotifier<bool>? isCollapsed;
  String? preDataText;
  String? postDataText;
  TextStyle? preDataTextStyle;
  TextStyle? postDataTextStyle;
  String trimExpandedText = 'more';
  String trimCollapsedText = 'Show less';
  Color? colorClickableText;
  int trimLength = 240;
  int trimLines = 2;
  TrimMode trimMode = TrimMode.Length;
  TextStyle? style;

  String? semanticsLabel;
  TextStyle? moreStyle;
  TextStyle? lessStyle;
  TextStyle? delimiterStyle;
  List<Annotation>? annotations;
  bool isExpandable = true;

  CustomReadMores({
    super.key,
    required this.text,
  });

  @override
  State<CustomReadMores> createState() => _CustomReadMoresState();
}

class _CustomReadMoresState extends State<CustomReadMores> {
  void _showMessage(String message) {
    toastification.show(
      context: context,
      title: Text(message),
      type: ToastificationType.info,
      alignment: Alignment.bottomCenter,
      autoCloseDuration: const Duration(seconds: 2),
    );
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTextStyle.merge(
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 12.0.sp),
      child: _buildContent(),
    );
  }

  Widget _buildContent() {
    return ReadMoreText(
      widget.text,
      trimLines: 2,
      trimMode: TrimMode.Line,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 11.0.sp),
      colorClickableText: Theme.of(context).colorScheme.primary,
      trimCollapsedText: 'more',
      trimExpandedText: "show less",
      textAlign: TextAlign.start,
      annotations: [
        // URL
        Annotation(
          regExp: RegExp(
            r'(?:(?:https?|ftp)://)?[\w/\-?=%.]+\.[\w/\-?=%.]+',
          ),
          spanBuilder: ({
            required String text,
            TextStyle? textStyle,
          }) {
            return TextSpan(
              text: text,
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.copyWith(decoration: TextDecoration.underline, color: Colors.blue, fontSize: 11.0.sp),
              recognizer: TapGestureRecognizer()..onTap = () => _showMessage(text),
            );
          },
        ),

        Annotation(
          regExp: RegExp(r'@(?:[a-zA-Z0-9_]+)'),
          spanBuilder: ({
            required String text,
            TextStyle? textStyle,
          }) {
            return TextSpan(
                text: text,
                style: (Theme.of(context).textTheme.bodyMedium)?.copyWith(color: Colors.blue, fontSize: 11.0.sp),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    // Remove '@' from the text if it exists
                    text.replaceFirst('@', '');
                    // Navigate to UserProfileIdScreen with the cleaned text
                    PersistentNavBarNavigator.pushNewScreen(context,
                        screen: GetUserProfileById(
                          // userId: cleanedText,
                          stackonScreen: true,
                        ));
                  });
          },
        ),
        // Hashtag
        Annotation(
          // Test: non capturing group should work also
          regExp: RegExp('#(?:[a-zA-Z0-9_]+)'),
          spanBuilder: ({
            required String text,
            TextStyle? textStyle,
          }) {
            return TextSpan(
              text: text,
              style: (Theme.of(context).textTheme.bodyMedium)?.copyWith(color: Colors.blue, fontSize: 11.0.sp),
              recognizer: TapGestureRecognizer()..onTap = () => _showMessage(text),
            );
          },
        ),
      ],
    );
  }
}
