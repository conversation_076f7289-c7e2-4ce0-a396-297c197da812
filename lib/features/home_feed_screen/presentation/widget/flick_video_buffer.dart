import 'package:flick_video_player/flick_video_player.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:provider/provider.dart';

/// Shows a widget when the video is buffering (and video is playing).
class FlickVideoBuffers extends StatelessWidget {
  const FlickVideoBuffers({
    super.key,
    this.bufferingChild = const LoadingAnimationWidget(),
    this.child,
  });

  /// Widget to be shown when the video is buffering.
  final Widget bufferingChild;

  /// Widget to be shown when the video is not buffering.
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    FlickVideoManager videoManager = Provider.of<FlickVideoManager>(context);

    return Container(
      child: (videoManager.isBuffering && videoManager.isPlaying) ? bufferingChild : child,
    );
  }
}
