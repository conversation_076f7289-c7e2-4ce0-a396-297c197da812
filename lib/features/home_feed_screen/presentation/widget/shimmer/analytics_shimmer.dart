// ignore_for_file: deprecated_member_use

import 'package:flowkar/core/utils/exports.dart';

class AnalyticsCardShimmer extends StatelessWidget {
  const AnalyticsCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Shimmer.fromColors(
          baseColor: themeState.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
          highlightColor: themeState.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.only(right: 16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: 200.w,
                      height: 16.h,
                      margin: EdgeInsets.only(left: 18.w),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                    ),
                    Container(
                      width: 50.w,
                      height: 16.h,
                      margin: EdgeInsets.only(left: 18.w),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                    ),
                  ],
                ),
              ),
              buildSizedBoxH(10),
              SizedBox(
                height: 186.h,
                child: ListView.builder(
                  itemCount: 3,
                  scrollDirection: Axis.horizontal,
                  itemBuilder: (context, index) {
                    return Container(
                      height: 186.h,
                      width: 170.w,
                      margin: EdgeInsets.only(left: 14.w),
                      decoration: BoxDecoration(
                        color: Colors.grey.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            margin: EdgeInsets.only(left: 18.w, top: 18.h, bottom: 10.h),
                            width: 51.w,
                            height: 51.h,
                            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12.r),
                              color: Colors.white,
                            ),
                          ),
                          Container(
                            width: 90.w,
                            height: 10.h,
                            margin: EdgeInsets.only(left: 18.w),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10.r),
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Container(
                                margin: EdgeInsets.only(top: 12.h),
                                width: 51.w,
                                height: 51.h,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.r),
                                  color: Colors.white,
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                  children: [
                                    CustomImageView(
                                      margin: EdgeInsets.only(top: 2.h),
                                      // width: 18.w,
                                      height: 17.h,
                                      alignment: Alignment.center,
                                      imagePath: Assets.images.icons.homeFeed.icLikeFill.path,
                                      fit: BoxFit.fill,
                                    ),
                                    Text(
                                      "Likes",
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                          fontWeight: FontWeight.w400,
                                          fontSize: 8.sp,
                                          color: Theme.of(context).primaryColor),
                                    ),
                                  ],
                                ),
                              ),
                              //Comment
                              Container(
                                margin: EdgeInsets.only(top: 12.h),
                                width: 51.w,
                                height: 51.h,
                                // padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.r),
                                  color: Colors.white,
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                  children: [
                                    CustomImageView(
                                      margin: EdgeInsets.only(top: 2.h),
                                      // width: 18.w,
                                      height: 18.h,
                                      alignment: Alignment.center,
                                      imagePath: Assets.images.icons.social.icComment.path,
                                      fit: BoxFit.fill,
                                    ),
                                    Text(
                                      "Comments",
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                          fontWeight: FontWeight.w400,
                                          fontSize: 8.sp,
                                          color: Theme.of(context).primaryColor),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          buildSizedBoxH(12),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
