import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';

class HomeFeedShimmer extends StatelessWidget {
  final bool? showStoryShimmer;
  const HomeFeedShimmer({super.key, this.showStoryShimmer = false});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themestate) {
        return Column(
          children: [
            buildSizedBoxH(16.0),
            Expanded(child: _buildShimmerFeed(themestate)),
          ],
        );
      },
    );
  }

  // Widget _buildShimmerStories(ThemeState themestate) {
  //   return Padding(
  //     padding: EdgeInsets.only(left: 12.0.w, top: 16.0.h),
  //     child: SizedBox(
  //       height: 100.h,
  //       child: ListView.builder(
  //         physics: const ClampingScrollPhysics(parent: BouncingScrollPhysics()),
  //         scrollDirection: Axis.horizontal,
  //         itemCount: 5,
  //         itemBuilder: (context, index) => _buildShimmerStoryItem(themestate),
  //       ),
  //     ),
  //   );
  // }

  Widget _buildShimmerFeed(ThemeState themestate) {
    return ListView.builder(
      padding: EdgeInsets.only(bottom: 100.h),
      itemCount: 1,
      itemBuilder: (context, index) => _buildShimmerFeedItem(themestate),
    );
  }

  // Widget _buildShimmerStoryItem(ThemeState themestate) {
  //   return Padding(
  //     padding: EdgeInsets.only(right: 13.0.w),
  //     child: Column(
  //       children: [
  //         Shimmer.fromColors(
  //           baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
  //           highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
  //           child: Container(
  //             height: 65.h,
  //             width: 65.w,
  //             decoration: BoxDecoration(
  //               borderRadius: BorderRadius.circular(21.r),
  //               color: Colors.white,
  //             ),
  //           ),
  //         ),
  //         buildSizedBoxH(4.0),
  //         Shimmer.fromColors(
  //           baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
  //           highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
  //           child: Container(
  //             decoration: BoxDecoration(
  //               borderRadius: BorderRadius.circular(16.r),
  //               color: Colors.white,
  //             ),
  //             width: 60.w,
  //             height: 10.h,
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget _buildShimmerFeedItem(ThemeState themestate) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showStoryShimmer == true) ...[
          buildShimmerHorizontalList(),
          buildSizedBoxH(8.0),
        ],
        ...List.generate(
          3,
          (index) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildShimmerFeedHeader(themestate),
                buildSizedBoxH(12.0),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8.w),
                  child: _buildShimmerFeedImage(themestate),
                ),
                buildSizedBoxH(8.0),
                _buildShimmerFeedDescription(themestate),
                buildSizedBoxH(20.0),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildShimmerFeedHeader(ThemeState themestate) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            buildSizedBoxW(20.w),
            Shimmer.fromColors(
              baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
              highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
              child: Container(
                  height: 45.h,
                  width: 45.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(100.r),
                    color: Colors.white,
                  )),
            ),
            buildSizedBoxW(10.0),
            _buildShimmerFeedHeaderText(themestate),
          ],
        ),
        Padding(
          padding: EdgeInsets.only(right: 10.w),
          child: Icon(
            Icons.more_horiz_sharp,
            color: themestate.isDarkThemeOn ? Colors.grey.shade700 : Colors.grey.shade300,
          ),
        ),
      ],
    );
  }

  Widget _buildShimmerFeedHeaderText(ThemeState themestate) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Shimmer.fromColors(
          baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
          highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
          child: Container(
            width: 84.w,
            height: 9.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              color: Colors.white,
            ),
          ),
        ),
        buildSizedBoxH(5.0),
        Shimmer.fromColors(
          baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
          highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
          child: Container(
            width: 60.w,
            height: 9.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildShimmerFeedImage(ThemeState themestate) {
    return Shimmer.fromColors(
      baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
      highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
      child: Container(
        width: double.infinity,
        height: 300.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
        ),
      ),
    );
  }

  Widget _buildShimmerFeedDescription(ThemeState themestate) {
    return Padding(
      padding: EdgeInsets.only(left: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Shimmer.fromColors(
            baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
            highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
            child: Container(
              width: 150.w,
              height: 16.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.r),
                color: Colors.white,
              ),
            ),
          ),
          buildSizedBoxH(4.0),
          Shimmer.fromColors(
            baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
            highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
            child: Container(
              width: 100.w,
              height: 14.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.r),
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Widget _buildShimmerStoryItem(ThemeState themestate) {
//   return Padding(
//     padding: EdgeInsets.only(right: 13.0.w),
//     child: Column(
//       children: [
//         Shimmer.fromColors(
//           baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
//           highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
//           child: Container(
//             height: 65.h,
//             width: 65.w,
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(21.r),
//               color: Colors.white,
//             ),
//           ),
//         ),
//         buildSizedBoxH(4.0),
//         Shimmer.fromColors(
//           baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
//           highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
//           child: Container(
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(16.r),
//               color: Colors.white,
//             ),
//             width: 60.w,
//             height: 10.h,
//           ),
//         ),
//       ],
//     ),
//   );
// }

// Widget _buildShimmerStories(ThemeState themestate) {
//   return Padding(
//     padding: EdgeInsets.only(left: 12.0.w, top: 16.0.h),
//     child: SizedBox(
//       height: 100.h,
//       child: ListView.builder(
//         physics: const ClampingScrollPhysics(parent: BouncingScrollPhysics()),
//         scrollDirection: Axis.horizontal,
//         itemCount: 5,
//         itemBuilder: (context, index) => _buildShimmerStoryItem(themestate),
//       ),
//     ),
//   );
// }
Widget buildShimmerHorizontalList() {
  return Container(
    height: 120.h,
    margin: EdgeInsets.only(bottom: 8.h),
    child: ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: 10,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      itemBuilder: (context, index) {
        return Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            width: 75.w,
            height: 90.h,
            margin: EdgeInsets.only(right: 8.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(14.r),
            ),
          ),
        );
      },
    ),
  );
}

Widget buildShimmerLoading() {
  return Stack(
    alignment: Alignment.center,
    children: [
      Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          buildSizedBoxH(380.h.h),
          // Row(
          //   children: [
          //     Shimmer.fromColors(
          //       baseColor: Colors.grey[300]!,
          //       highlightColor: Colors.grey[100]!,
          //       child: Container(
          //         height: 16.h,
          //         width: 16.h,
          //         decoration: BoxDecoration(
          //           color: Colors.grey[300],
          //           borderRadius: BorderRadius.circular(4.r),
          //         ),
          //       ),
          //     ),
          //     buildSizedBoxW(20.w),
          //     Shimmer.fromColors(
          //       baseColor: Colors.grey[300]!,
          //       highlightColor: Colors.grey[100]!,
          //       child: Container(
          //         height: 18.h,
          //         width: 200.w,
          //         decoration: BoxDecoration(
          //           color: Colors.grey[300],
          //           borderRadius: BorderRadius.circular(4.r),
          //         ),
          //       ),
          //     ),
          //   ],
          // ),
          // buildSizedBoxH(10.h),
          // Image shimmer
          // Shimmer.fromColors(
          //   baseColor: Colors.grey[300]!,
          //   highlightColor: Colors.grey[100]!,
          //   child: Container(
          //     height: 350.h,
          //     width: double.infinity,
          //     decoration: BoxDecoration(
          //       color: Colors.grey[300],
          //       borderRadius: BorderRadius.circular(10.r),
          //     ),
          //   ),
          // ),
          buildSizedBoxH(128.h),
          // Text field shimmer
          // Shimmer.fromColors(
          //   baseColor: Colors.grey[300]!,
          //   highlightColor: Colors.grey[100]!,
          //   child: Container(
          //     height: 120.h,
          //     width: double.infinity,
          //     decoration: BoxDecoration(
          //       color: Colors.grey[300],
          //       borderRadius: BorderRadius.circular(12.r),
          //     ),
          //   ),
          // ),
          buildSizedBoxH(50.h),
          // Button shimmer
          // Shimmer.fromColors(
          //   baseColor: Colors.grey[300]!,
          //   highlightColor: Colors.grey[100]!,
          //   child: Container(
          //     height: 48.h,
          //     width: double.infinity,
          //     decoration: BoxDecoration(
          //       color: Colors.grey[300],
          //       borderRadius: BorderRadius.circular(8.r),
          //     ),
          //   ),
          // ),
        ],
      ),
      Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          LoadingAnimationWidget(),
          const SizedBox(height: 20),
          Text(
            'Please wait, we are generating Post...',
            style: TextStyle(fontSize: 15.sp, fontWeight: FontWeight.w600),
          ),
        ],
      ),
    ],
  );
}
