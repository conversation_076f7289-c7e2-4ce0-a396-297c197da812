BlockUserModel deserializeBlockUserModel(Map<String, dynamic> json) => BlockUserModel.fromJson(json);

class BlockUserModel {
  bool? status;
  String? message;
  bool? isBlocked;

  BlockUserModel({this.status, this.message, this.isBlocked});

  BlockUserModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    isBlocked = json['is_blocked'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['is_blocked'] = isBlocked;
    return data;
  }
}
