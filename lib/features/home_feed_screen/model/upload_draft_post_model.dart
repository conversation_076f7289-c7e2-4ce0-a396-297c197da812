UploadDraftPostModel deserializeUploadDraftPostModel(Map<String, dynamic> json) => UploadDraftPostModel.fromJson(json);

class UploadDraftPostModel {
  bool? status;
  String? message;
  Data? data;

  UploadDraftPostModel({this.status, this.message, this.data});

  UploadDraftPostModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  int? postId;
  bool? isPosted;

  Data({this.postId, this.isPosted});

  Data.fromJson(Map<String, dynamic> json) {
    postId = json['post_id'];
    isPosted = json['is_posted'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['post_id'] = postId;
    data['is_posted'] = isPosted;
    return data;
  }
}
