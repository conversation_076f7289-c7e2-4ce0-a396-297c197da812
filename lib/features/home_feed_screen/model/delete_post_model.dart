import 'package:json_annotation/json_annotation.dart';

part 'delete_post_model.g.dart';

DeletePostModel deserializeDeletePostModel(Map<String, dynamic> json) =>
    DeletePostModel.fromJson(json);

@JsonSerializable()
class DeletePostModel {
  final bool status;
  final String message;

  DeletePostModel({
    required this.status,
    required this.message,
  });

  factory DeletePostModel.fromJson(Map<String, dynamic> json) =>
      _$DeletePostModelFromJson(json);

  Map<String, dynamic> toJson() => _$DeletePostModelToJson(this);

  DeletePostModel copyWith({
    bool? status,
    String? message,
  }) {
    return DeletePostModel(
      status: status ?? this.status,
      message: message ?? this.message,
    );
  }
}
