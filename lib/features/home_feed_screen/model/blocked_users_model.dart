BlockedUsersModel deserializeBlockedUsersModel(Map<String, dynamic> json) => BlockedUsersModel.fromJson(json);

class BlockedUsersModel {
  bool? status;
  String? message;
  List<BlockedUsersData>? blockedusersdata;

  BlockedUsersModel({this.status, this.message, this.blockedusersdata});

  BlockedUsersModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      blockedusersdata = <BlockedUsersData>[];
      json['data'].forEach((v) {
        blockedusersdata!.add(BlockedUsersData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (blockedusersdata != null) {
      data['data'] = blockedusersdata!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  BlockedUsersModel copyWith({
    bool? status,
    String? message,
    List<BlockedUsersData>? blockedusersdata,
  }) {
    return BlockedUsersModel(
      status: status ?? this.status,
      message: message ?? this.message,
      blockedusersdata: blockedusersdata ?? this.blockedusersdata,
    );
  }
}

class BlockedUsersData {
  int? id;
  String? username;
  String? name;
  bool? isBlocked;
  String? profilePicture;

  BlockedUsersData({this.id, this.username, this.name, this.isBlocked, this.profilePicture});

  BlockedUsersData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    username = json['username'];
    name = json['name'];
    isBlocked = json['is_blocked'];
    profilePicture = json['profile_picture'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['username'] = username;
    data['name'] = name;
    data['is_blocked'] = isBlocked;
    data['profile_picture'] = profilePicture;
    return data;
  }

  BlockedUsersData copyWith({
    int? id,
    String? username,
    String? name,
    bool? isBlocked,
    String? profilePicture,
  }) {
    return BlockedUsersData(
      id: id ?? this.id,
      username: username ?? this.username,
      name: name ?? this.name,
      isBlocked: isBlocked ?? this.isBlocked,
      profilePicture: profilePicture ?? this.profilePicture,
    );
  }
}
