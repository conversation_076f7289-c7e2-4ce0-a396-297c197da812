PostShareModel deserializePostShareModel(Map<String, dynamic> json) => PostShareModel.fromJson(json);

class PostShareModel {
  final bool status;
  final String message;
  final String shareUrl;
  final String shareType;

  PostShareModel({
    required this.status,
    required this.message,
    required this.shareUrl,
    required this.shareType,
  });

  factory PostShareModel.fromJson(Map<String, dynamic> json) {
    return PostShareModel(
      status: json['status'],
      message: json['message'],
      shareUrl: json['share_url'],
      shareType: json['share_type'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'share_url': shareUrl,
      'share_type': shareType,
    };
  }

  PostShareModel copyWith({
    bool? status,
    String? message,
    String? shareUrl,
    String? shareType,
  }) {
    return PostShareModel(
      status: status ?? this.status,
      message: message ?? this.message,
      shareUrl: shareUrl ?? this.shareUrl,
      shareType: shareType ?? this.shareType,
    );
  }
}

SharePostMessageModel deserializeSharePostMessageModel(Map<String, dynamic> json) =>
    SharePostMessageModel.fromJson(json);

class SharePostMessageModel {
  final bool status;
  final int id;
  final String message;
  final String type;
  final int postId;
  final DateTime createdAt;
  final int sentBy;

  SharePostMessageModel({
    required this.status,
    required this.id,
    required this.message,
    required this.type,
    required this.postId,
    required this.createdAt,
    required this.sentBy,
  });

  factory SharePostMessageModel.fromJson(Map<String, dynamic> json) {
    return SharePostMessageModel(
      status: json['status'],
      id: json['id'],
      message: json['message'],
      type: json['type'],
      postId: json['post_id'],
      createdAt: DateTime.parse(json['created_at']),
      sentBy: json['sent_by'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'id': id,
      'message': message,
      'type': type,
      'post_id': postId,
      'created_at': createdAt.toIso8601String(),
      'sent_by': sentBy,
    };
  }

  SharePostMessageModel copyWith({
    bool? status,
    int? id,
    String? message,
    String? type,
    int? postId,
    DateTime? createdAt,
    int? sentBy,
  }) {
    return SharePostMessageModel(
      status: status ?? this.status,
      id: id ?? this.id,
      message: message ?? this.message,
      type: type ?? this.type,
      postId: postId ?? this.postId,
      createdAt: createdAt ?? this.createdAt,
      sentBy: sentBy ?? this.sentBy,
    );
  }
}
