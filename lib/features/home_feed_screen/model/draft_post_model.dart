DraftPostModel deserializeDraftPostModel(Map<String, dynamic> json) => DraftPostModel.fromJson(json);

class DraftPostModel {
  int? count;
  String? next;
  String? previous;
  Results? results;

  DraftPostModel({this.count, this.next, this.previous, this.results});

  factory DraftPostModel.fromJson(Map<String, dynamic> json) {
    return DraftPostModel(
      count: json['count'],
      next: json['next'],
      previous: json['previous'],
      results: json['results'] != null ? Results.fromJson(json['results']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'count': count,
      'next': next,
      'previous': previous,
      'results': results?.toJson(),
    };
  }
}

class Results {
  bool? status;
  String? message;
  String? name;
  String? username;
  String? profilePicture;
  List<DraftPostData>? data;

  Results({
    this.status,
    this.message,
    this.name,
    this.username,
    this.profilePicture,
    this.data,
  });

  factory Results.from<PERSON><PERSON>(Map<String, dynamic> json) {
    return Results(
      status: json['status'],
      message: json['message'],
      name: json['name'],
      username: json['username'],
      profilePicture: json['profile_picture'],
      data: json['data'] != null ? List<DraftPostData>.from(json['data'].map((x) => DraftPostData.fromJson(x))) : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'name': name,
      'username': username,
      'profile_picture': profilePicture,
      'data': data?.map((x) => x.toJson()).toList(),
    };
  }
}

class DraftPostData {
  int? id;
  String? title;
  String? description;
  String? location;
  bool? isPrivate;
  bool? isDeleted;
  bool? isVideo;
  int? likes;
  int? dislikes;
  int? commentsCount;
  int? reportCount;
  bool? isBanned;
  bool? facebook;
  String? facebookId;
  bool? tiktok;
  String? tiktokId;
  bool? instagram;
  String? instagramId;
  bool? linkedin;
  String? linkedinId;
  bool? pinterest;
  String? pinterestId;
  bool? vimeo;
  String? vimeoId;
  bool? youtube;
  String? youtubeId;
  bool? dailymotion;
  String? dailymotionId;
  bool? twitter;
  String? twitterId;
  bool? x;
  String? xId;
  bool? mastadon;
  String? mastadonId;
  bool? tumblr;
  bool? reddit;
  String? tumblrId;
  List<int>? taggedIn;
  String? scheduledAt;
  bool? isScheduled;
  bool? isUnscheduled;
  bool? isPosted;
  String? createdAt;
  int? industry;
  List<String>? files;
  List<String>? thumbnailFiles;

  DraftPostData({
    this.id,
    this.title,
    this.description,
    this.location,
    this.isPrivate,
    this.isDeleted,
    this.isVideo,
    this.likes,
    this.dislikes,
    this.commentsCount,
    this.reportCount,
    this.isBanned,
    this.facebook,
    this.facebookId,
    this.tiktok,
    this.tiktokId,
    this.instagram,
    this.instagramId,
    this.linkedin,
    this.linkedinId,
    this.pinterest,
    this.pinterestId,
    this.vimeo,
    this.vimeoId,
    this.youtube,
    this.youtubeId,
    this.dailymotion,
    this.dailymotionId,
    this.twitter,
    this.twitterId,
    this.x,
    this.xId,
    this.mastadon,
    this.mastadonId,
    this.tumblr,
    this.reddit,
    this.tumblrId,
    this.taggedIn,
    this.scheduledAt,
    this.isScheduled,
    this.isUnscheduled,
    this.isPosted,
    this.createdAt,
    this.industry,
    this.files,
    this.thumbnailFiles,
  });

  DraftPostData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    description = json['description'];
    location = json['location'];
    isPrivate = json['is_private'];
    isDeleted = json['is_deleted'];
    isVideo = json['is_video'];
    likes = json['likes'];
    dislikes = json['dislikes'];
    commentsCount = json['comments_count'];
    reportCount = json['report_count'];
    isBanned = json['is_banned'];
    facebook = json['facebook'];
    facebookId = json['facebook_id'];
    tiktok = json['tiktok'];
    tiktokId = json['tiktok_id'];
    instagram = json['instagram'];
    instagramId = json['instagram_id'];
    linkedin = json['linkedin'];
    linkedinId = json['linkedin_id'];
    pinterest = json['pinterest'];
    pinterestId = json['pinterest_id'];
    vimeo = json['vimeo'];
    vimeoId = json['vimeo_id'];
    youtube = json['youtube'];
    youtubeId = json['youtube_id'];
    dailymotion = json['dailymotion'];
    dailymotionId = json['dailymotion_id'];
    twitter = json['twitter'];
    twitterId = json['twitter_id'];
    x = json['x'];
    xId = json['x_id'];
    mastadon = json['mastadon'];
    mastadonId = json['mastadon_id'];
    tumblr = json['tumblr'];
    reddit = json['reddit'];
    tumblrId = json['tumblr_id'];
    taggedIn = json['tagged_in']?.cast<int>();
    scheduledAt = json['scheduled_at'];
    isScheduled = json['is_scheduled'];
    isUnscheduled = json['is_unscheduled'];
    isPosted = json['is_posted'];
    createdAt = json['created_at'];
    industry = json['industry'];
    files = json['files']?.cast<String>();
    thumbnailFiles = json['thumbnail_files']?.cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['description'] = description;
    data['location'] = location;
    data['is_private'] = isPrivate;
    data['is_deleted'] = isDeleted;
    data['is_video'] = isVideo;
    data['likes'] = likes;
    data['dislikes'] = dislikes;
    data['comments_count'] = commentsCount;
    data['report_count'] = reportCount;
    data['is_banned'] = isBanned;
    data['facebook'] = facebook;
    data['facebook_id'] = facebookId;
    data['tiktok'] = tiktok;
    data['tiktok_id'] = tiktokId;
    data['instagram'] = instagram;
    data['instagram_id'] = instagramId;
    data['linkedin'] = linkedin;
    data['linkedin_id'] = linkedinId;
    data['pinterest'] = pinterest;
    data['pinterest_id'] = pinterestId;
    data['vimeo'] = vimeo;
    data['vimeo_id'] = vimeoId;
    data['youtube'] = youtube;
    data['youtube_id'] = youtubeId;
    data['dailymotion'] = dailymotion;
    data['dailymotion_id'] = dailymotionId;
    data['twitter'] = twitter;
    data['twitter_id'] = twitterId;
    data['x'] = x;
    data['x_id'] = xId;
    data['mastadon'] = mastadon;
    data['mastadon_id'] = mastadonId;
    data['tumblr'] = tumblr;
    data['reddit'] = reddit;
    data['tumblr_id'] = tumblrId;
    data['tagged_in'] = taggedIn;
    data['scheduled_at'] = scheduledAt;
    data['is_scheduled'] = isScheduled;
    data['is_unscheduled'] = isUnscheduled;
    data['is_posted'] = isPosted;
    data['created_at'] = createdAt;
    data['industry'] = industry;
    data['files'] = files;
    data['thumbnail_files'] = thumbnailFiles;
    return data;
  }

  DraftPostData copyWith({
    int? id,
    String? title,
    String? description,
    String? location,
    bool? isPrivate,
    bool? isDeleted,
    bool? isVideo,
    int? likes,
    int? dislikes,
    int? commentsCount,
    int? reportCount,
    bool? isBanned,
    bool? facebook,
    String? facebookId,
    bool? tiktok,
    String? tiktokId,
    bool? instagram,
    String? instagramId,
    bool? linkedin,
    String? linkedinId,
    bool? pinterest,
    String? pinterestId,
    bool? vimeo,
    String? vimeoId,
    bool? youtube,
    String? youtubeId,
    bool? dailymotion,
    String? dailymotionId,
    bool? twitter,
    String? twitterId,
    bool? x,
    String? xId,
    bool? mastadon,
    String? mastadonId,
    bool? tumblr,
    bool? reddit,
    String? tumblrId,
    List<int>? taggedIn,
    String? scheduledAt,
    bool? isScheduled,
    bool? isUnscheduled,
    bool? isPosted,
    String? createdAt,
    int? industry,
    List<String>? files,
    List<String>? thumbnailFiles,
  }) {
    return DraftPostData(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      isPrivate: isPrivate ?? this.isPrivate,
      isDeleted: isDeleted ?? this.isDeleted,
      isVideo: isVideo ?? this.isVideo,
      likes: likes ?? this.likes,
      dislikes: dislikes ?? this.dislikes,
      commentsCount: commentsCount ?? this.commentsCount,
      reportCount: reportCount ?? this.reportCount,
      isBanned: isBanned ?? this.isBanned,
      facebook: facebook ?? this.facebook,
      facebookId: facebookId ?? this.facebookId,
      tiktok: tiktok ?? this.tiktok,
      tiktokId: tiktokId ?? this.tiktokId,
      instagram: instagram ?? this.instagram,
      instagramId: instagramId ?? this.instagramId,
      linkedin: linkedin ?? this.linkedin,
      linkedinId: linkedinId ?? this.linkedinId,
      pinterest: pinterest ?? this.pinterest,
      pinterestId: pinterestId ?? this.pinterestId,
      vimeo: vimeo ?? this.vimeo,
      vimeoId: vimeoId ?? this.vimeoId,
      youtube: youtube ?? this.youtube,
      youtubeId: youtubeId ?? this.youtubeId,
      dailymotion: dailymotion ?? this.dailymotion,
      dailymotionId: dailymotionId ?? this.dailymotionId,
      twitter: twitter ?? this.twitter,
      twitterId: twitterId ?? this.twitterId,
      x: x ?? this.x,
      xId: xId ?? this.xId,
      mastadon: mastadon ?? this.mastadon,
      mastadonId: mastadonId ?? this.mastadonId,
      tumblr: tumblr ?? this.tumblr,
      reddit: reddit ?? this.reddit,
      tumblrId: tumblrId ?? this.tumblrId,
      taggedIn: taggedIn ?? this.taggedIn,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      isScheduled: isScheduled ?? this.isScheduled,
      isUnscheduled: isUnscheduled ?? this.isUnscheduled,
      isPosted: isPosted ?? this.isPosted,
      createdAt: createdAt ?? this.createdAt,
      industry: industry ?? this.industry,
      files: files ?? this.files,
      thumbnailFiles: thumbnailFiles ?? this.thumbnailFiles,
    );
  }
}
