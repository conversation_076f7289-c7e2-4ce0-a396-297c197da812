DeleteDraftPostModel deserializeDeleteDraftPostModel(Map<String, dynamic> json) => DeleteDraftPostModel.fromJson(json);

class DeleteDraftPostModel {
  final bool? status;
  final String? message;
  final int? deletedPostId;

  DeleteDraftPostModel({
    this.status,
    this.message,
    this.deletedPostId,
  });

  factory DeleteDraftPostModel.fromJson(Map<String, dynamic> json) {
    return DeleteDraftPostModel(
      status: json['status'],
      message: json['message'],
      deletedPostId: json['deleted_post_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'deleted_post_id': deletedPostId,
    };
  }
}
