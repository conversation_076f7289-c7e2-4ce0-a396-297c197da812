import 'package:json_annotation/json_annotation.dart';

part 'delete_comment_model.g.dart';

DeleteCommentModel deserializeDeleteCommentModel(Map<String, dynamic> json) => DeleteCommentModel.fromJson(json);

@JsonSerializable()
class DeleteCommentModel {
  final bool status;
  final String message;

  DeleteCommentModel({
    required this.status,
    required this.message,
  });

  factory DeleteCommentModel.fromJson(Map<String, dynamic> json) => _$DeleteCommentModelFromJson(json);

  Map<String, dynamic> toJson() => _$DeleteCommentModelToJson(this);

  DeleteCommentModel copyWith({
    bool? status,
    String? message,
  }) {
    return DeleteCommentModel(
      status: status ?? this.status,
      message: message ?? this.message,
    );
  }
}
