import 'package:json_annotation/json_annotation.dart';

part 'analytics_model.g.dart';

AnalyticsModel deserializeAnalyticsModel(Map<String, dynamic> json) => AnalyticsModel.fromJson(json);

@JsonSerializable()
class AnalyticsModel {
  bool? status;
  String? message;
  AnalyticsData? data;

  AnalyticsModel({this.status, this.message, this.data});

  factory AnalyticsModel.fromJson(Map<String, dynamic> json) => _$AnalyticsModelFromJson(json);

  Map<String, dynamic> toJson() => _$AnalyticsModelToJson(this);
}

@JsonSerializable()
class AnalyticsData {
  int? id;
  int? userId;
  int? likes;
  int? comments;
  List<String>? files;
  Anylitics? instagram;
  Anylitics? facebook;
  Anylitics? youtube;
  Anylitics? tiktok;
  Anylitics? linkedin;
  Anylitics? pinterest;
  Anylitics? vimeo;
  Anylitics? tumblr;
  Anylitics? reddit;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'Threads')
  Anylitics? thread;

  AnalyticsData(
      {this.id,
      this.userId,
      this.likes,
      this.comments,
      this.files,
      this.instagram,
      this.facebook,
      this.youtube,
      this.tiktok,
      this.linkedin,
      this.pinterest,
      this.vimeo,
      this.tumblr,
      this.reddit,
      this.thread});

  factory AnalyticsData.fromJson(Map<String, dynamic> json) => _$AnalyticsDataFromJson(json);

  Map<String, dynamic> toJson() => _$AnalyticsDataToJson(this);
}

@JsonSerializable()
class Anylitics {
  int? like;
  int? comments;

  Anylitics({this.like, this.comments});

  factory Anylitics.fromJson(Map<String, dynamic> json) => _$AnyliticsFromJson(json);

  Map<String, dynamic> toJson() => _$AnyliticsToJson(this);
}
