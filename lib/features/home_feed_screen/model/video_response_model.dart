import 'package:json_annotation/json_annotation.dart';

part 'video_response_model.g.dart';

VideoResponseModel deserializeVideoResponseModel(Map<String, dynamic> json) => VideoResponseModel.fromJson(json);

@JsonSerializable()
class VideoResponseModel {
  final int count;
  final String? next;
  final String? previous;
  final Results results;

  VideoResponseModel({
    required this.count,
    this.next,
    this.previous,
    required this.results,
  });

  factory VideoResponseModel.fromJson(Map<String, dynamic> json) => _$VideoResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$VideoResponseModelToJson(this);

  VideoResponseModel copyWith({
    int? count,
    String? next,
    String? previous,
    Results? results,
  }) {
    return VideoResponseModel(
      count: count ?? this.count,
      next: next ?? this.next,
      previous: previous ?? this.previous,
      results: results ?? this.results,
    );
  }
}

@JsonSerializable()
class Results {
  final bool status;
  final String message;
  @JsonKey(name: 'data')
  final List<VideoData> data;

  Results({
    required this.status,
    required this.message,
    required this.data,
  });

  factory Results.fromJson(Map<String, dynamic> json) => _$ResultsFromJson(json);

  Map<String, dynamic> toJson() => _$ResultsToJson(this);

  Results copyWith({
    bool? status,
    String? message,
    List<VideoData>? data,
  }) {
    return Results(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

@JsonSerializable()
class VideoData {
  final int id;
  final String title;
  final String description;
  final String location;
  final int likes;
  final int dislikes;
  @JsonKey(name: 'comments_count')
  final int commentsCount;
  @JsonKey(name: 'tagged_in')
  final dynamic taggedIn;
  @JsonKey(name: 'created_at')
  final String createdAt;
  final List<String> files;
  final int width;
  final int height;
  @JsonKey(name: 'thumbail_files')
  final List<String> thumbnailFiles;
  @JsonKey(name: 'latest_comment')
  final String latestComment;
  final User user;
  @JsonKey(name: 'is_liked')
  final bool isLiked;
  @JsonKey(name: 'is_saved')
  final bool isSaved;


  VideoData({
    required this.id,
    required this.title,
    required this.description,
    required this.location,
    required this.likes,
    required this.dislikes,
    required this.commentsCount,
    required this.taggedIn,
    required this.createdAt,
    required this.files,
    required this.thumbnailFiles,
    required this.latestComment,
    required this.user,
    required this.isLiked,
    required this.isSaved,
    required this.width,
    required this.height,
  });

  factory VideoData.fromJson(Map<String, dynamic> json) => _$VideoDataFromJson(json);

  Map<String, dynamic> toJson() => _$VideoDataToJson(this);

  VideoData copyWith({
    int? id,
    String? title,
    String? description,
    String? location,
    int? likes,
    int? dislikes,
    int? commentsCount,
    Map<String, dynamic>? taggedIn,
    String? createdAt,
    List<String>? files,
    List<String>? thumbnailFiles,
    String? latestComment,
    User? user,
    bool? isLiked,
    bool? isSaved,
    int? width,
    int? height,
    bool? isTextPost,
  }) {
    return VideoData(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      likes: likes ?? this.likes,
      dislikes: dislikes ?? this.dislikes,
      commentsCount: commentsCount ?? this.commentsCount,
      taggedIn: taggedIn ?? this.taggedIn,
      createdAt: createdAt ?? this.createdAt,
      files: files ?? this.files,
      thumbnailFiles: thumbnailFiles ?? this.thumbnailFiles,
      latestComment: latestComment ?? this.latestComment,
      user: user ?? this.user,
      isLiked: isLiked ?? this.isLiked,
      isSaved: isSaved ?? this.isSaved,
      width: width ?? this.width,
      height: height ?? this.height,
    );
  }
}

@JsonSerializable()
class User {
  @JsonKey(name: 'user_id')
  final int userId;
  final String username;
  final String name;
  @JsonKey(name: 'profile_image')
  final String profileImage;

  User({
    required this.userId,
    required this.username,
    required this.name,
    required this.profileImage,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    int? userId,
    String? username,
    String? name,
    String? profileImage,
  }) {
    return User(
      userId: userId ?? this.userId,
      username: username ?? this.username,
      name: name ?? this.name,
      profileImage: profileImage ?? this.profileImage,
    );
  }
}
