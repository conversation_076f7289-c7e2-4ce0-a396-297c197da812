import 'package:json_annotation/json_annotation.dart';

part 'get_comment_model.g.dart';

GetCommentModel deserializeGetCommentModel(Map<String, dynamic> json) => GetCommentModel.fromJson(json);

@JsonSerializable(explicitToJson: true)
class GetCommentModel {
  final bool status;

  @JsonKey(name: 'data')
  final List<CommentData> comments;

  GetCommentModel({
    required this.status,
    required this.comments,
  });

  factory GetCommentModel.fromJson(Map<String, dynamic> json) => _$GetCommentModelFromJson(json);

  Map<String, dynamic> toJson() => _$GetCommentModelToJson(this);

  GetCommentModel copyWith({
    bool? status,
    List<CommentData>? comments,
  }) {
    return GetCommentModel(
      status: status ?? this.status,
      comments: comments ?? this.comments,
    );
  }
}

@JsonSerializable(explicitToJson: true)
class CommentData {
  final int id;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'comment_text')
  final String commentText;

  @Json<PERSON>ey(name: 'created_at')
  final String createdAt;

  final CommentUser user;

  @Json<PERSON>ey(name: 'likes_count')
  final int likesCount;

  @JsonKey(name: 'replies')
  final List<ReplyData> replies;

  @JsonKey(name: 'is_Liked')
  final bool isLiked;

  CommentData({
    required this.id,
    required this.commentText,
    required this.createdAt,
    required this.user,
    required this.likesCount,
    required this.replies,
    required this.isLiked,
  });

  factory CommentData.fromJson(Map<String, dynamic> json) => _$CommentDataFromJson(json);

  Map<String, dynamic> toJson() => _$CommentDataToJson(this);

  CommentData copyWith({
    int? id,
    String? commentText,
    String? createdAt,
    CommentUser? user,
    int? likesCount,
    List<ReplyData>? replies,
    bool? isLiked,
  }) {
    return CommentData(
      id: id ?? this.id,
      commentText: commentText ?? this.commentText,
      createdAt: createdAt ?? this.createdAt,
      user: user ?? this.user,
      likesCount: likesCount ?? this.likesCount,
      replies: replies ?? this.replies,
      isLiked: isLiked ?? this.isLiked,
    );
  }
}

@JsonSerializable()
class ReplyData {
  final int id;

  @JsonKey(name: 'reply_text')
  final String replyText;

  @JsonKey(name: 'created_at')
  final String createdAt;

  final CommentUser user;

  @JsonKey(name: 'likes_count')
  final int likesCount;

  @JsonKey(name: 'is_Liked')
  final bool isLiked;

  ReplyData({
    required this.id,
    required this.replyText,
    required this.createdAt,
    required this.user,
    required this.likesCount,
    required this.isLiked,
  });

  factory ReplyData.fromJson(Map<String, dynamic> json) => _$ReplyDataFromJson(json);

  Map<String, dynamic> toJson() => _$ReplyDataToJson(this);

  ReplyData copyWith({
    int? id,
    String? replyText,
    String? createdAt,
    CommentUser? user,
    int? likesCount,
    bool? isLiked,
  }) {
    return ReplyData(
      id: id ?? this.id,
      replyText: replyText ?? this.replyText,
      createdAt: createdAt ?? this.createdAt,
      user: user ?? this.user,
      likesCount: likesCount ?? this.likesCount,
      isLiked: isLiked ?? this.isLiked,
    );
  }
}

@JsonSerializable()
class CommentUser {
  final int id;
  final String username;

  @JsonKey(name: 'profile_image')
  final String? profileImage;

  CommentUser({
    required this.id,
    required this.username,
    this.profileImage,
  });

  factory CommentUser.fromJson(Map<String, dynamic> json) => _$CommentUserFromJson(json);

  Map<String, dynamic> toJson() => _$CommentUserToJson(this);

  CommentUser copyWith({
    int? id,
    String? username,
    String? profileImage,
  }) {
    return CommentUser(
      id: id ?? this.id,
      username: username ?? this.username,
      profileImage: profileImage ?? this.profileImage,
    );
  }
}
