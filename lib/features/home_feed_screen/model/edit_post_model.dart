import 'package:json_annotation/json_annotation.dart';

part 'edit_post_model.g.dart';

EditPostModel deserializeEditPostModel(Map<String, dynamic> json) => EditPostModel.fromJson(json);

@JsonSerializable()
class EditPostModel {
  final bool? status;
  final String? message;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'data')
  final EditPost? editpostdata;

  EditPostModel({this.status, this.message, this.editpostdata});

  factory EditPostModel.fromJson(Map<String, dynamic> json) => _$EditPostModelFromJson(json);

  Map<String, dynamic> toJson() => _$EditPostModelToJson(this);
}

@JsonSerializable()
class EditPost {
  final int? id;
  final String? title;
  final String? description;

  EditPost({this.id, this.title, this.description});

  factory EditPost.fromJson(Map<String, dynamic> json) => _$EditPostFromJson(json);

  Map<String, dynamic> toJson() => _$EditPostToJson(this);
}
