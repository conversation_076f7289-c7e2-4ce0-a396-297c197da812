// part of 'connectivity_bloc.dart';

// class ConnectivityState extends Equatable {
//   final bool isConnected;

//   const ConnectivityState({this.isConnected = true});

//   ConnectivityState copyWith({bool? isConnected}) {
//     return ConnectivityState(isConnected: isConnected ?? this.isConnected);
//   }

//   @override
//   List<Object?> get props => [isConnected];
// }

part of 'connectivity_bloc.dart';

class ConnectivityState extends Equatable {
  final bool isConnected;
  final bool isReconnected;

  const ConnectivityState({this.isConnected = true, this.isReconnected = false});

  ConnectivityState copyWith({bool? isConnected, bool? isReconnected}) {
    return ConnectivityState(
        isConnected: isConnected ?? this.isConnected, isReconnected: isReconnected ?? this.isReconnected);
  }

  @override
  List<Object?> get props => [isConnected, isReconnected];
}
