import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';

class ConnectivityHandler extends StatelessWidget {
  final Widget shimmerWidget;
  final Widget Function(BuildContext context, ConnectivityState state) builder;
  final VoidCallback? onReconnect;

  const ConnectivityHandler({
    super.key,
    required this.shimmerWidget,
    required this.builder,
    this.onReconnect,
  });

  @override
  Widget build(BuildContext context) {
    return BlocListener<ConnectivityBloc, ConnectivityState>(
      listener: (context, state) {
        if (state.isReconnected) {
          if (onReconnect != null) onReconnect!();
        }
      },
      child: BlocBuilder<ConnectivityBloc, ConnectivityState>(
        builder: (context, connectivityState) {
          return builder(context, connectivityState);
        },
      ),
    );
  }
}
