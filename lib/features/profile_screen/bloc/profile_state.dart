part of 'profile_bloc.dart';

// ignore: must_be_immutable
class UserProfileState extends Equatable {
  final bool loading;
  final bool editloading;
  final String? error;
  UserProfileDetailModel? userProfile;
  final QrResponseModel? qrResponseModel;

  final String selectedGender;
  String profile;
  final TextEditingController? nameController;
  final TextEditingController? userNameController;
  final TextEditingController? emailController;
  final TextEditingController? bioController;
  final TextEditingController? dobController;
  final TextEditingController? mobileNumberController;
  final TextEditingController? countryController;
  final TextEditingController? stateController;
  final TextEditingController? cityController;

  final FocusNode? nameFocusNode;
  final FocusNode? userNameFocusNode;
  final FocusNode? emailFocusNode;
  final FocusNode bioFocusNode;
  final FocusNode? dateOfBirthFocusNode;
  final FocusNode? mobileNumberFocusNode;
  final NewStoryModel? highlightStoryModel;
  final List<NewStory> highlightStoryData;
  final bool ishilightloading;
  final List<FolllowData> followList;
  final List<FolllowData> followingList;
  final bool isLoadingMore;
  final bool hasMoreData;
  final bool isloding;
  final int page;
  final TextEditingController? searchController;

  final List<DemographyItem> countries;
  final List<DemographyItem> states;
  final List<DemographyItem> cities;
  final DemographyItem? selectedCountry;
  final DemographyItem? selectedState;
  final DemographyItem? selectedCity;
  final bool isLoadingStates;
  final bool isLoadingCities;
  UserProfileState({
    required this.loading,
    required this.editloading,
    this.error,
    this.userProfile,
    this.qrResponseModel,
    this.selectedGender = '',
    this.profile = '',
    this.nameController,
    required this.userNameController,
    required this.emailController,
    required this.bioController,
    required this.dobController,
    required this.mobileNumberController,
    required this.countryController,
    required this.stateController,
    required this.cityController,
    required this.nameFocusNode,
    required this.userNameFocusNode,
    required this.emailFocusNode,
    required this.bioFocusNode,
    required this.dateOfBirthFocusNode,
    required this.mobileNumberFocusNode,
    this.highlightStoryModel,
    this.highlightStoryData = const [],
    this.ishilightloading = false,
    this.followList = const [],
    this.followingList = const [],
    this.isLoadingMore = false,
    this.isloding = false,
    this.page = 1,
    this.searchController,
    this.countries = const [],
    this.states = const [],
    this.cities = const [],
    this.selectedCountry,
    this.selectedState,
    this.selectedCity,
    this.isLoadingStates = false,
    this.isLoadingCities = false,
    this.hasMoreData = false,
  });

  factory UserProfileState.initial() {
    return UserProfileState(
      loading: false,
      editloading: false,
      error: null,
      userProfile: null,
      qrResponseModel: null,
      selectedGender: '',
      profile: '',
      nameController: TextEditingController(),
      userNameController: TextEditingController(),
      emailController: TextEditingController(),
      bioController: TextEditingController(),
      dobController: TextEditingController(),
      mobileNumberController: TextEditingController(),
      countryController: TextEditingController(),
      stateController: TextEditingController(),
      cityController: TextEditingController(),
      nameFocusNode: FocusNode(),
      userNameFocusNode: FocusNode(),
      emailFocusNode: FocusNode(),
      bioFocusNode: FocusNode(),
      dateOfBirthFocusNode: FocusNode(),
      mobileNumberFocusNode: FocusNode(),
      ishilightloading: false,
      countries: [],
      states: [],
      cities: [],
      selectedCountry: null,
      selectedState: null,
      selectedCity: null,
      isLoadingStates: false,
      isLoadingCities: false,
      hasMoreData: false,
    );
  }

  UserProfileState copyWith({
    bool? loading,
    bool? editloading,
    String? error,
    UserProfileDetailModel? userProfile,
    QrResponseModel? qrResponseModel,
    String? selectedGender,
    String? profile,
    TextEditingController? nameController,
    TextEditingController? userNameController,
    TextEditingController? emailController,
    TextEditingController? bioController,
    TextEditingController? dobController,
    TextEditingController? mobileNumberController,
    TextEditingController? countryController,
    TextEditingController? stateController,
    TextEditingController? cityController,
    FocusNode? nameFocusNode,
    FocusNode? userNameFocusNode,
    FocusNode? emailFocusNode,
    FocusNode? bioFocusNode,
    FocusNode? dateOfBirthFocusNode,
    FocusNode? mobileNumberFocusNode,
    NewStoryModel? highlightStoryModel,
    List<NewStory>? highlightStoryData,
    bool? ishilightloading,
    List<FolllowData>? followList,
    List<FolllowData>? followingList,
    bool? isLoadingMore,
    bool? isloding,
    int? page,
    TextEditingController? searchController,
    List<DemographyItem>? countries,
    List<DemographyItem>? states,
    List<DemographyItem>? cities,
    DemographyItem? selectedCountry,
    DemographyItem? selectedState,
    DemographyItem? selectedCity,
    bool? isLoadingStates,
    bool? isLoadingCities,
    bool? hasMoreData,
  }) {
    return UserProfileState(
      loading: loading ?? this.loading,
      editloading: editloading ?? this.editloading,
      error: error ?? this.error,
      userProfile: userProfile ?? this.userProfile,
      qrResponseModel: qrResponseModel ?? this.qrResponseModel,
      selectedGender: selectedGender ?? this.selectedGender,
      profile: profile ?? this.profile,
      nameController: nameController ?? this.nameController,
      userNameController: userNameController ?? this.userNameController,
      emailController: emailController ?? this.emailController,
      bioController: bioController ?? this.bioController,
      dobController: dobController ?? this.dobController,
      mobileNumberController: mobileNumberController ?? this.mobileNumberController,
      countryController: countryController ?? this.countryController,
      stateController: stateController ?? this.stateController,
      cityController: cityController ?? this.cityController,
      nameFocusNode: nameFocusNode ?? this.nameFocusNode,
      userNameFocusNode: userNameFocusNode ?? this.userNameFocusNode,
      emailFocusNode: emailFocusNode ?? this.emailFocusNode,
      bioFocusNode: bioFocusNode ?? this.bioFocusNode,
      dateOfBirthFocusNode: dateOfBirthFocusNode ?? this.dateOfBirthFocusNode,
      mobileNumberFocusNode: mobileNumberFocusNode ?? this.mobileNumberFocusNode,
      highlightStoryModel: highlightStoryModel ?? this.highlightStoryModel,
      highlightStoryData: highlightStoryData ?? this.highlightStoryData,
      ishilightloading: ishilightloading ?? this.ishilightloading,
      followList: followList ?? this.followList,
      followingList: followingList ?? this.followingList,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      isloding: isloding ?? this.isloding,
      page: page ?? this.page,
      searchController: searchController ?? this.searchController,
      countries: countries ?? this.countries,
      states: states ?? this.states,
      cities: cities ?? this.cities,
      selectedCountry: selectedCountry ?? this.selectedCountry,
      selectedState: selectedState ?? this.selectedState,
      selectedCity: selectedCity ?? this.selectedCity,
      isLoadingStates: isLoadingStates ?? this.isLoadingStates,
      isLoadingCities: isLoadingCities ?? this.isLoadingCities,
      hasMoreData: hasMoreData ?? this.hasMoreData,
    );
  }

  @override
  List<Object?> get props => [
        loading,
        error,
        userProfile,
        qrResponseModel,
        selectedGender,
        profile,
        nameController,
        userNameController,
        emailController,
        bioController,
        dobController,
        mobileNumberController,
        nameFocusNode,
        userNameFocusNode,
        emailFocusNode,
        bioFocusNode,
        dateOfBirthFocusNode,
        mobileNumberFocusNode,
        highlightStoryModel,
        highlightStoryData,
        ishilightloading,
        followList,
        followingList,
        isLoadingMore,
        isloding,
        page,
        searchController,
        countries,
        states,
        cities,
        selectedCountry,
        selectedState,
        selectedCity,
        isLoadingStates,
        isLoadingCities,
        hasMoreData,
      ];
}
