part of 'profile_bloc.dart';

sealed class UserPro<PERSON>leEvent extends Equatable {}

class UserProfileInitial extends UserPro<PERSON>leEvent {
  @override
  List<Object?> get props => [];
}

class FetchUserProfileEvent extends UserProfileEvent {
  final bool onEdit;
  FetchUserProfileEvent({required this.onEdit});

  @override
  List<Object?> get props => [onEdit];
}

class FetchShareProfileQrEvent extends UserProfileEvent {
  FetchShareProfileQrEvent();

  @override
  List<Object?> get props => [];
}

class UpdateGenderEvent extends UserProfileEvent {
  final String gender;

  UpdateGenderEvent({required this.gender});

  @override
  List<Object?> get props => [gender];
}

class UpdateProfilePhotoEvent extends UserProfileEvent {
  final File selectedProfilePhoto;

  UpdateProfilePhotoEvent({required this.selectedProfilePhoto});

  @override
  List<Object?> get props => [selectedProfilePhoto];
}

class EditUserProfileEvent extends UserProfileEvent {
  final int userId;
  final dynamic profileImage;
  final String name;
  final String username;
  final String email;
  final String bio;
  final String dob;
  final String mobile;
  final String gender;
  final String country;
  final String state;
  final String city;

  EditUserProfileEvent({
    required this.userId,
    required this.profileImage,
    required this.name,
    required this.username,
    required this.bio,
    required this.mobile,
    required this.email,
    required this.dob,
    required this.gender,
    required this.country,
    required this.state,
    required this.city,
  });

  @override
  List<Object?> get props => [
        userId,
        profileImage,
        name,
        username,
        bio,
        mobile,
        email,
        dob,
        gender,
        country,
        state,
        city,
      ];
}

class GetHighlightStoryApiEvent extends UserProfileEvent {
  @override
  List<Object?> get props => [];
}

class GetFollowerListApiEvent extends UserProfileEvent {
  final int page;

  GetFollowerListApiEvent({required this.page});

  @override
  List<Object?> get props => [page];
}

class FollowUserProfilebyIdSocketEvent extends UserProfileEvent {
  final int userId;
  final String type;
  FollowUserProfilebyIdSocketEvent({
    required this.userId,
    required this.type,
  });
  @override
  List<Object?> get props => [userId, type];
}

class UpdateFollowUserProfilebyIdEvent extends UserProfileEvent {
  final bool isFollowing;
  final int userId;
  final String? type;
  final int? followingcount;

  UpdateFollowUserProfilebyIdEvent({required this.isFollowing, required this.userId, this.type, this.followingcount});
  @override
  List<Object?> get props => [isFollowing, userId, type, followingcount];
}

class FollowUserProfileSocketEvent extends UserProfileEvent {
  final int userId;
  final String type;
  FollowUserProfileSocketEvent({
    required this.userId,
    required this.type,
  });
  @override
  List<Object?> get props => [userId, type];
}

class UpdateFollowUserProfileEvent extends UserProfileEvent {
  final bool isFollowing;
  final int userId;
  final String? type;
  final int? followingcount;

  UpdateFollowUserProfileEvent({required this.isFollowing, required this.userId, this.type, this.followingcount});
  @override
  List<Object?> get props => [isFollowing, userId, type, followingcount];
}

class GetFollowerListbyIdApiEvent extends UserProfileEvent {
  final int page;
  final int userId;

  GetFollowerListbyIdApiEvent({required this.page, required this.userId});

  @override
  List<Object?> get props => [page, userId];
}

class GetFollowingListApiEvent extends UserProfileEvent {
  final int page;

  GetFollowingListApiEvent({required this.page});

  @override
  List<Object?> get props => [page];
}

class GetFollowingListbyIdApiEvent extends UserProfileEvent {
  final int page;
  final int userId;

  GetFollowingListbyIdApiEvent({required this.page, required this.userId});

  @override
  List<Object?> get props => [page, userId];
}

class LoadCountriesEvent extends UserProfileEvent {
  @override
  List<Object?> get props => [];
}

class LoadStatesEvent extends UserProfileEvent {
  final int countryId;
  LoadStatesEvent(this.countryId);
  @override
  List<Object?> get props => [countryId];
}

class LoadCitiesEvent extends UserProfileEvent {
  final int stateId;
  LoadCitiesEvent(this.stateId);
  @override
  List<Object?> get props => [stateId];
}

class SelectCountryEvent extends UserProfileEvent {
  final DemographyItem? country;
  SelectCountryEvent(this.country);
  @override
  List<Object?> get props => [country];
}

class SelectStateEvent extends UserProfileEvent {
  final DemographyItem? state;
  SelectStateEvent(this.state);
  @override
  List<Object?> get props => [state];
}

class RemoveImageFromProfile extends UserProfileEvent {
  final String image;

  RemoveImageFromProfile({required this.image});

  @override
  List<Object?> get props => [image];
}

class SelectCityEvent extends UserProfileEvent {
  final DemographyItem? city;
  SelectCityEvent(this.city);
  @override
  List<Object?> get props => [city];
}

class DeleteHighLightStoryEvent extends UserProfileEvent {
  final int storyId;
  DeleteHighLightStoryEvent({required this.storyId});
  @override
  List<Object?> get props => [storyId];
}

// class FollowUserSocketEvent extends UserProfileEvent {
//   final int userId;
//   FollowUserSocketEvent({required this.userId});
//   @override
//   List<Object?> get props => [userId];
// }

// class UpdateFollowUserEvent extends UserProfileEvent {
//   final bool isFollowing;
//   final int userId;
//   UpdateFollowUserEvent({required this.isFollowing, required this.userId});
//   @override
//   List<Object?> get props => [isFollowing, userId];
// }
