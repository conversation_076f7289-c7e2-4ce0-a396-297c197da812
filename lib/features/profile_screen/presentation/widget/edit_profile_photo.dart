// // ignore_for_file: use_build_context_synchronously

// import 'dart:io';

// import 'package:flowkar/core/utils/exports.dart';
// import 'package:flowkar/core/utils/loading_animation_widget.dart';
// import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_manager.dart';
// import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_player.dart';
// import 'package:flowkar/features/upload_post/presentation/widget/filter_post_app_bar.dart';
// import 'package:flowkar/features/upload_post/presentation/widget/video_editor_widget.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:pro_image_editor/core/models/editor_callbacks/pro_image_editor_callbacks.dart';
// import 'package:pro_image_editor/features/main_editor/main_editor.dart';

// import 'package:uuid/uuid.dart';

// class ProfileImageEditorScreen extends StatefulWidget {
//   final List<String> imagePaths;

//   const ProfileImageEditorScreen({super.key, required this.imagePaths});

//   static Widget builder(BuildContext context) {
//     var imagePaths = ModalRoute.of(context)?.settings.arguments as List<String>;
//     return ProfileImageEditorScreen(imagePaths: imagePaths);
//   }

//   @override
//   State<ProfileImageEditorScreen> createState() => _ProfileImageEditorScreenState();
// }

// class _ProfileImageEditorScreenState extends State<ProfileImageEditorScreen> {
//   late FlickMultiManager flickMultiManager;
//   late List<String> _imagePaths;
//   bool isStart = true;
//   final List<String> _mediapaths = [];
//   final GlobalKey<AnimatedListState> _listKey = GlobalKey<AnimatedListState>();

//   @override
//   void initState() {
//     flickMultiManager = FlickMultiManager();
//     _imagePaths = List.from(widget.imagePaths);
//     super.initState();
//   }

//   void _removeItem(int index) async {
//     // Remove item from _mediapaths and _imagePaths
//     final removedPath = _mediapaths[index];
//     _mediapaths.removeAt(index);
//     _imagePaths.removeAt(index);

//     // Remove the item from AnimatedList
//     _listKey.currentState?.removeItem(
//       index,
//       (context, animation) {
//         return SizeTransition(
//           axis: Axis.horizontal,
//           sizeFactor: animation,
//           axisAlignment: 1.0,
//           child: _buildItem(removedPath, animation, index),
//         );
//       },
//       duration: const Duration(milliseconds: 300),
//     );

//     // Check if list is empty and pop if necessary
//     if (_imagePaths.isEmpty) {
//       NavigatorService.goBack(result: true);
//     }
//   }

//   Future<void> _editImage(String imagepath, int index) async {
//     final String tempPath = (await getTemporaryDirectory()).path;
//     const uuid = Uuid(); // Create a Uuid instance
//     final String uniqueId = uuid.v4(); // Generate a unique UUID
//     final String editedImageFilePath = '$tempPath/edited_image_$uniqueId.png'; // Use UUID for the file path

//     Navigator.push(
//       context,
//       MaterialPageRoute(
//         builder: (context) => ProImageEditor.file(
//           File(imagepath),
//           callbacks: ProImageEditorCallbacks(
//             onImageEditingComplete: (Uint8List bytes) async {
//               final editedImageFile = File(editedImageFilePath);
//               await editedImageFile.writeAsBytes(bytes); // Save the edited image

//               Logger.lOG("PATHH$editedImageFilePath");

//               Navigator.pop(context, editedImageFilePath);

//               setState(() {
//                 _mediapaths[index] = editedImageFilePath;
//               });

//               Logger.lOG("IMAGELIST$_mediapaths");

//               _listKey.currentState?.setState(() {
//                 _listKey.currentState?.insertItem(index);
//               });
//             },
//           ),
//         ),
//       ),
//     );
//   }

//   Future<void> _editVideo(String videopath, int index) async {
//     // Push to the VideoEditor screen and wait for result
//     final result = await Navigator.push(
//       context,
//       MaterialPageRoute(builder: (context) {
//         return VideoEditor(file: File(videopath));
//       }),
//     );

//     if (result != null) {
//       setState(() {
//         _mediapaths[index] = result;
//       });

//       _listKey.currentState?.setState(() {
//         _listKey.currentState?.insertItem(index);
//       });
//     }

//     Logger.lOG("Edited video result: $result");
//   }

//   @override
//   Widget build(BuildContext context) {
//     // ignore: deprecated_member_use
//     return WillPopScope(
//       onWillPop: () async {
//         NavigatorService.goBack(result: true);
//         return true;
//       },
//       child: Scaffold(
//         backgroundColor: Colors.white,
//         body: FutureBuilder<List<File?>>(
//           future: Future.wait(_imagePaths.map((path) async {
//             return File(path);
//           })),
//           builder: (context, snapshot) {
//             if (snapshot.connectionState == ConnectionState.waiting) {
//               return Center(child: LoadingAnimationWidget());
//             } else if (snapshot.hasError) {
//               return Center(child: Text('Error: ${snapshot.error}'));
//             } else if (snapshot.hasData) {
//               final files = snapshot.data;

//               if (files != null && files.isNotEmpty) {
//                 if (isStart) {
//                   _mediapaths.clear();
//                   for (var i = 0; i < files.length; i++) {
//                     _mediapaths.add(files[i]!.path);
//                   }
//                   isStart = false;
//                 }
//               }

//               return SafeArea(
//                 child: Column(
//                   children: [
//                     FilterPostAppbar(nextButton: () {}),
//                     Expanded(
//                       child: Column(
//                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                         children: [_buildItemView(files), const Spacer(), _buildthumbnail(files), buildSizedBoxH(12.0)],
//                       ),
//                     ),
//                   ],
//                 ),
//               );
//             }
//             return const SizedBox.shrink();
//           },
//         ),
//       ),
//     );
//   }

//   Widget _buildItemView(List<File?>? files) {
//     return Padding(
//       padding: EdgeInsets.only(top: 50.0.h),
//       child: SizedBox(
//         height: MediaQuery.of(context).size.height / 2,
//         child: AnimatedList(
//           shrinkWrap: true,
//           key: _listKey,
//           scrollDirection: Axis.horizontal,
//           initialItemCount: files!.length,
//           itemBuilder: (context, index, animation) {
//             return _buildItem(_mediapaths[index], animation, index);
//           },
//         ),
//       ),
//     );
//   }

//   _buildthumbnail(List<File?>? files) {
//     return FutureBuilder<File?>(
//         future: isVideo(files![0]!.path) ? getVideoThumbnail(files[0]!.path) : Future.value(File(files[0]!.path)),
//         builder: (BuildContext context, AsyncSnapshot<File?> snapshot) {
//           if (snapshot.connectionState == ConnectionState.waiting) {
//             return Padding(
//               padding: EdgeInsets.symmetric(horizontal: 16.0.w),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   InkWell(
//                     onTap: () => NavigatorService.goBack(result: true),
//                     child: Container(
//                       clipBehavior: Clip.antiAlias,
//                       decoration: BoxDecoration(boxShadow: [
//                         BoxShadow(
//                           color: Theme.of(context).customColors.black.withOpacity(0.2),
//                           spreadRadius: 1, // Spread radius
//                           blurRadius: 2, // Blur radius
//                           offset: const Offset(0, 3), // Shadow position
//                         ),
//                       ], borderRadius: BorderRadius.circular(16.0.r)),
//                       height: 50.0.h,
//                       width: 50.0.w,
//                       child: CustomImageView(
//                         imagePath: Assets.images.pngs.other.pngPlaceholder.path,
//                         fit: BoxFit.cover,
//                       ),
//                     ),
//                   ),
//                   CustomElevatedButton(
//                     width: 100.w,
//                     height: 40.h,
//                     onPressed: () {
//                       NavigatorService.pushNamed(AppRoutes.uploadPostScrteen, arguments: [
//                         _mediapaths,
//                         [false]
//                       ]);
//                     },
//                     text: "Next",
//                     // rightIconwidth: buildSizedBoxW(16.0),
//                     rightIcon: Icon(
//                       Icons.arrow_forward_ios_outlined,
//                       color: Theme.of(context).customColors.white,
//                       size: 16.0.sp,
//                     ),
//                   )
//                 ],
//               ),
//             );
//           }
//           if (snapshot.hasError) {
//             return Text('Error: ${snapshot.error}');
//           }
//           if (!snapshot.hasData) {
//             return const SizedBox.shrink();
//           }

//           final File imageFile = snapshot.data!;

//           return Padding(
//             padding: EdgeInsets.symmetric(horizontal: 16.0.w),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 InkWell(
//                   onTap: () => NavigatorService.goBack(result: true),
//                   child: Container(
//                     clipBehavior: Clip.antiAlias,
//                     decoration: BoxDecoration(boxShadow: [
//                       BoxShadow(
//                         color: Theme.of(context).customColors.black.withOpacity(0.2),
//                         spreadRadius: 1, // Spread radius
//                         blurRadius: 2, // Blur radius
//                         offset: const Offset(0, 3), // Shadow position
//                       ),
//                     ], borderRadius: BorderRadius.circular(16.0.r)),
//                     height: 50.0.h,
//                     width: 50.0.w,
//                     child: Image.file(
//                       imageFile,
//                       fit: BoxFit.cover,
//                     ),
//                   ),
//                 ),
//                 CustomElevatedButton(
//                   width: 100.w,
//                   height: 40.h,
//                   onPressed: () {
//                     NavigatorService.pushNamed(AppRoutes.uploadPostScrteen, arguments: [_mediapaths, false]);
//                   },
//                   text: 'Next',
//                   // rightIconwidth: buildSizedBoxW(16.0),
//                   rightIcon: Icon(
//                     Icons.arrow_forward_ios_outlined,
//                     color: Theme.of(context).customColors.white,
//                     size: 16.0.sp,
//                   ),
//                 )
//               ],
//             ),
//           );
//         });
//   }

//   Widget _buildItem(String path, Animation<double> animation, int index) {
//     final mediaUrl = path;
//     return SizeTransition(
//       axis: Axis.horizontal,
//       sizeFactor: animation,
//       axisAlignment: 1.0,
//       child: Column(
//         children: [
//           Padding(
//             padding: EdgeInsets.symmetric(horizontal: 8.0.w),
//             child: Stack(
//               children: [
//                 Container(
//                   clipBehavior: Clip.antiAlias,
//                   decoration: BoxDecoration(borderRadius: BorderRadius.circular(20.0.r)),
//                   height: MediaQuery.of(context).size.height / 2,
//                   width: 300.0.w,
//                   child: isVideo(mediaUrl)
//                       ? FlickMultiPlayer(
//                           file: File(path),
//                           flickMultiManager: flickMultiManager,
//                           image: Assets.images.pngs.other.pngLogo.path,
//                         )
//                       : Image.file(
//                           File(path),
//                           fit: BoxFit.cover,
//                           width: double.infinity,
//                         ),
//                 ),
//                 Positioned(
//                   left: 10,
//                   top: 10,
//                   child: GestureDetector(
//                     child: Container(
//                       decoration: BoxDecoration(
//                         borderRadius: BorderRadius.circular(25.r),
//                         color: Colors.black54,
//                       ),
//                       padding: EdgeInsets.symmetric(horizontal: 10.0.w, vertical: 3.0.h),
//                       child: InkWell(
//                         onTap: () {
//                           if (isVideo(mediaUrl)) {
//                             _editVideo(path, index);
//                           } else {
//                             _editImage(path, index);
//                           }
//                         },
//                         child: Row(
//                           mainAxisSize: MainAxisSize.min,
//                           children: [
//                             Icon(
//                               isVideo(mediaUrl) ? Icons.videocam : Icons.image,
//                               size: 16.sp,
//                               color: Theme.of(context).customColors.white,
//                             ),
//                             buildSizedBoxW(3),
//                             Text(
//                               Lang.of(context).lbl_edit,
//                               style: Theme.of(context)
//                                   .textTheme
//                                   .bodyMedium
//                                   ?.copyWith(color: Theme.of(context).customColors.white),
//                             )
//                           ],
//                         ),
//                       ),
//                     ),
//                   ),
//                 ),
//                 Positioned(
//                   right: 10,
//                   top: 10,
//                   child: GestureDetector(
//                     onTap: () => _removeItem(index),
//                     child: Container(
//                       decoration: BoxDecoration(
//                         borderRadius: BorderRadius.circular(25.r),
//                         color: Colors.black54,
//                       ),
//                       padding: const EdgeInsets.all(4.0),
//                       child: Icon(
//                         Icons.close,
//                         size: 16.sp,
//                         color: Theme.of(context).customColors.white,
//                       ),
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Future<File?> getFileFromPath(String path) async {
//     // Your logic to get a File from a path
//     return File(path); // Replace with your actual logic
//   }
// }
