import 'package:flowkar/core/utils/exports.dart';

class ScheduledPostShimmer extends StatelessWidget {
  const ScheduledPostShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
      itemCount: 4,
      itemBuilder: (context, index) {
        return Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            margin: EdgeInsets.only(bottom: 16.h),
            padding: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Profile row
                Row(
                  children: [
                    Container(
                      height: 36.h,
                      width: 36.w,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        shape: BoxShape.circle,
                      ),
                    ),
                    buildSizedBoxW(10.w),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: 10.h,
                          width: 80.w,
                          color: Colors.grey[300],
                        ),
                        buildSizedBoxH(5.h),
                        Container(
                          height: 8.h,
                          width: 60.w,
                          color: Colors.grey[300],
                        ),
                      ],
                    ),
                  ],
                ),
                buildSizedBoxH(16.h),
                Container(
                  height: 10.h,
                  width: double.infinity,
                  color: Colors.grey[300],
                ),
                buildSizedBoxH(10.h),
                Container(
                  height: 10.h,
                  width: double.infinity,
                  color: Colors.grey[300],
                ),
                buildSizedBoxH(16.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      height: 14.h,
                      width: 100.w,
                      color: Colors.grey[300],
                    ),
                    Container(
                      height: 14.h,
                      width: 60.w,
                      color: Colors.grey[300],
                    ),
                  ],
                ),
                buildSizedBoxH(16.h),
                Container(
                  height: 40.h,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
