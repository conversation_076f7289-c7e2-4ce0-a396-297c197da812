import 'package:flowkar/core/utils/exports.dart';

class ProfileShareScreenShimmer extends StatelessWidget {
  const ProfileShareScreenShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      period: const Duration(milliseconds: 1500),
      child: Column(
        children: [
          buildSizedBoxH(8),
          Container(
            height: 85.h,
            width: 85.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.grey.shade300,
            ),
          ),
          buildSizedBoxH(16.h),
          Container(
            height: 18.h,
            width: 120.w,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          buildSizedBoxH(8.h),
          Container(
            height: 14.h,
            width: 180.w,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          buildSizedBoxH(20),
          ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child: Container(
              width: 160.w,
              height: 160.h,
              color: Theme.of(context).customColors.white,
            ),
          ),
          buildSizedBoxH(60),
          Container(
            height: 55.h,
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            decoration: BoxDecoration(
              color: Theme.of(context).customColors.white,
              borderRadius: BorderRadius.circular(20.r),
              boxShadow: [
                BoxShadow(
                  blurRadius: 8,
                  color: Colors.black12,
                  offset: Offset(0, 0),
                ),
              ],
            ),
          ),
          buildSizedBoxH(60),
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                height: 60.h,
                child: CustomElevatedButton(
                  margin: EdgeInsets.only(bottom: 10.h),
                  color: Theme.of(context).primaryColor,
                  decoration: const BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        blurRadius: 8,
                        color: Colors.black12,
                        offset: Offset(0, 0),
                      ),
                    ],
                  ),
                  text: Lang.of(context).lbl_share_profile,
                  buttonTextStyle: Theme.of(context).textTheme.labelLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                        fontSize: 16.sp,
                        color: Theme.of(context).customColors.white,
                      ),
                  height: 40.h,
                  onPressed: () {},
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
