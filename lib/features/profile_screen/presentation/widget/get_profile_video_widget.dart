import 'dart:async';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/home_feed_screen/model/video_response_model.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/comment_bottom_sheet_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class GetProfileVideoWidget extends StatefulWidget {
  final List<VideoData>? profilevideo;
  final int? initialIndex;
  final int? userId;

  const GetProfileVideoWidget({super.key, this.profilevideo, this.initialIndex, this.userId});

  static Widget builder(BuildContext context) {
    return const GetProfileVideoWidget();
  }

  @override
  State<GetProfileVideoWidget> createState() => _GetProfileVideoWidgetState();
}

class _GetProfileVideoWidgetState extends State<GetProfileVideoWidget> with SingleTickerProviderStateMixin {
  late ItemScrollController _itemScrollController;
  late ItemPositionsListener _itemPositionsListener;
  int _initialIndex = 0;
  // final int _threshold = 5;
  final Map<int, Widget> _postCache = {};
  bool isLoadingMore = false;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _itemScrollController = ItemScrollController();
    _itemPositionsListener = ItemPositionsListener.create();
    _initialIndex = widget.initialIndex ?? 0;

    // context.read<HomeFeedBloc>().add(FetchUserProfileVideoEvent(page: 1));

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_initialIndex >= 0 && _initialIndex < (widget.profilevideo?.length ?? 0)) {
        _scrollToInitialIndex();
      }
    });
    // _itemPositionsListener.itemPositions.addListener(_scrollListener);
  }

  void _scrollToInitialIndex() {
    _itemScrollController.jumpTo(
      index: _initialIndex,
    );
  }

  Future<void> _refreshFeed() async {
    if (!mounted) return;

    final state = context.read<HomeFeedBloc>().state;
    if (state.getProfilevideo.isNotEmpty) {
      state.getProfilevideo.clear();
    }
    _postCache.clear();
    context.read<HomeFeedBloc>().add(FetchUserProfileVideoEvent(page: 1));
  }

  // void _scrollListener() {
  //   if (_debounceTimer?.isActive ?? false) return;

  //   _debounceTimer = Timer(const Duration(milliseconds: 200), () {
  //     if (!mounted) return;

  //     final visibleItems = _itemPositionsListener.itemPositions.value;
  //     if (visibleItems.isEmpty) return;

  //     final lastVisibleIndex = visibleItems.last.index;
  //     if (lastVisibleIndex >= (widget.profilevideo?.length ?? 0) - _threshold) {
  //       final state = context.read<HomeFeedBloc>().state;
  //       if (state.getProfilevideoResponseModel?.next == null || _isLoadingMore) {
  //         return;
  //       }

  //       _isLoadingMore = true;
  //       context.read<HomeFeedBloc>().add(FetchUserProfileVideoEvent(page: state.getProfilevideoPage + 1));
  //     }
  //   });
  // }

  Widget _buildPostItem(VideoData post, int index) {
    if (_postCache.containsKey(post.id)) {
      return _postCache[post.id]!;
    }

    final widget = RepaintBoundary(
      child: PostWidget(
        key: ValueKey('video_${post.id}'),
        width: post.width,
        height: post.height,
        taggedIn: post.taggedIn,
        userVideo: true,
        userByIDpost: false,
        userByIDvideo: false,
        userpost: false,
        state: context.read<HomeFeedBloc>().state,
        latestcomments: post.latestComment.toString(),
        index: index,
        userId: post.user.userId,
        postId: post.id,
        profileImage: post.user.profileImage,
        name: post.user.name,
        username: post.user.username,
        postMedia: post.files,
        thumbnailImage: post.thumbnailFiles.isNotEmpty ? post.thumbnailFiles : [],
        title: post.title == "''" || post.title.isEmpty ? '' : post.title,
        caption:
            "${post.title == "''" || post.title.isEmpty ? '' : post.title}${post.description == '' || post.description.isEmpty ? '' : post.title == "''" || post.title.isEmpty ? post.description : "\n${post.description}"}",
        likes: post.likes.toString(),
        comments: post.latestComment.toString(),
        postTime: post.createdAt,
        isLiked: post.isLiked,
        isSaved: post.isSaved,
        screenType: "User Profile",
        isprofilrpostDelete: true,
        doubleTap: () {
          if (post.isLiked == false) {
            context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: post.id));
          }
        },
        likeonTap: () {
          context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: post.id));
        },
        commentonTap: () {
          showModalBottomSheet(
            context: context,
            useRootNavigator: true,
            isScrollControlled: true,
            builder: (context) => CommentsBottomSheet(postId: post.id),
          );
        },
        shareonTap: () {},
        saveonTap: () {},
      ),
    );

    _postCache[post.id] = widget;
    return widget;
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    // _itemPositionsListener.itemPositions.removeListener(_scrollListener);
    _postCache.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ConnectivityBloc, ConnectivityState>(
      listener: (context, state) {
        if (state.isReconnected) {
          if (context.read<HomeFeedBloc>().state.getProfilevideo.isEmpty) {
            _refreshFeed();
          }
        }
      },
      child: Scaffold(
        appBar: CustomAppbar(
          hasLeadingIcon: true,
          height: 18.h,
          leading: [
            InkWell(
              onTap: () {
                PersistentNavBarNavigator.pop(context);
              },
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: CustomImageView(
                  imagePath: Assets.images.svg.authentication.icBackArrow.path,
                  height: 16.h,
                ),
              ),
            ),
            buildSizedBoxW(20.w),
            Text(
              Lang.of(context).lbl_post,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
            ),
          ],
        ),
        body: BlocBuilder<ThemeBloc, ThemeState>(
          builder: (context, themestate) {
            return BlocBuilder<HomeFeedBloc, HomeFeedState>(
              builder: (context, state) {
                return Scaffold(
                  body: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Expanded(child: _buildprofilevideo(state, themestate)),
                      BlocBuilder<ConnectivityBloc, ConnectivityState>(
                        builder: (context, connectivityState) {
                          return Visibility(
                            visible: state.getProfileisVideoLoadingMore && connectivityState.isConnected,
                            child: SizedBox(
                              height: 50.h,
                              child: Center(
                                  child: CupertinoActivityIndicator(color: Theme.of(context).colorScheme.primary)),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildprofilevideo(HomeFeedState state, ThemeState themestate) {
    return LiquidPullToRefresh(
      color: Theme.of(context).primaryColor.withOpacity(0.5),
      showChildOpacityTransition: false,
      backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      onRefresh: _refreshFeed,
      child: BlocBuilder<ConnectivityBloc, ConnectivityState>(
        builder: (context, connectivityState) {
          if (!connectivityState.isConnected && state.getProfilevideo.isEmpty) {
            return HomeFeedShimmer();
          } else if (state.getProfileVideoLoading) {
            return HomeFeedShimmer();
          } else if (state.getProfilevideo.isEmpty) {
            return ListView(
              physics: AlwaysScrollableScrollPhysics(),
              children: [
                buildSizedBoxH(MediaQuery.of(context).size.height / 6),
                ExceptionWidget(
                  imagePath: Assets.images.svg.exception.svgNodatafound.path,
                  showButton: false,
                  title: Lang.of(context).lbl_no_data_found,
                  subtitle: Lang.of(context).lbl_no_post,
                ),
              ],
            );
          } else {
            return NotificationListener<ScrollUpdateNotification>(
              onNotification: (notification) {
                if (notification.metrics.pixels == notification.metrics.maxScrollExtent) {
                  // _handlePagination();
                  // Handle video pagination
                  if (state.getProfilevideoResponseModel?.next != null && !state.getProfileisVideoLoadingMore) {
                    isLoadingMore = true;
                    context.read<HomeFeedBloc>().add(FetchUserProfileVideoEvent(page: state.getProfilevideoPage + 1));
                  }
                }
                return true;
              },
              child: ScrollablePositionedList.builder(
                padding: EdgeInsets.zero,
                itemCount: state.getProfilevideo.length,
                itemScrollController: _itemScrollController,
                itemPositionsListener: _itemPositionsListener,
                physics: const ClampingScrollPhysics(),
                itemBuilder: (context, index) {
                  final post = state.getProfilevideo[index];
                  return Padding(
                    padding: EdgeInsets.only(
                        bottom: state.getProfilevideo[index] == state.getProfilevideo.last ? 30.h : 0,
                        top: index == 0 ? 8.h : 0),
                    child: _buildPostItem(post, index),
                  );
                },
              ),
            );
          }
        },
      ),
    );
  }
}
