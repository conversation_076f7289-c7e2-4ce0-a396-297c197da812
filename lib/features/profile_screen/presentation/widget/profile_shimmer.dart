import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/profile_screen/bloc/profile_bloc.dart';
import 'package:flowkar/features/setting_screen/page/setting_screen.dart';
import 'package:flowkar/features/survey_form/bloc/survey_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class ProfileShimmer extends StatelessWidget {
  final bool isCurrentUser;
  final bool isSwitchUser;
  const ProfileShimmer({super.key, required this.isCurrentUser, this.isSwitchUser = false});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themestate) {
        return Scaffold(
          appBar: _buildAppBar(context),
          body: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.only(right: 16.0.w, left: 16.0.w, bottom: 8.0),
              child: Column(
                children: [
                  _buildShimmerProfileHeader(themestate),
                  buildSizedBoxH(16.0),
                  if (isCurrentUser) _buildShimmerHorizontalList(themestate),
                  if (isCurrentUser) buildSizedBoxH(20),
                  _buildShimmerTabBar(themestate),
                  buildSizedBoxH(10.0),
                  _buildShimmerGrid(themestate),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // _buildAppBar() {
  //   return Row(
  //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //     children: [
  //       Shimmer.fromColors(
  //         baseColor: Colors.grey[300]!,
  //         highlightColor: Colors.grey[100]!,
  //         child: Container(
  //           width: 100.w,
  //           height: 14.h,
  //           decoration: BoxDecoration(
  //             color: Colors.white,
  //             borderRadius: BorderRadius.circular(100.r),
  //           ),
  //         ),
  //       ),
  //       Shimmer.fromColors(
  //         baseColor: Colors.grey[300]!,
  //         highlightColor: Colors.grey[100]!,
  //         child: Container(
  //           width: 24.w,
  //           height: 24.h,
  //           decoration: BoxDecoration(
  //             color: Colors.white,
  //             borderRadius: BorderRadius.circular(6.r),
  //           ),
  //         ),
  //       ),
  //     ],
  //   );
  // }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    bool
        // isPostPermission = false;
        isPostPermission =
        Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) == Prefobj.preferences?.get(Prefkeys.USER_ID);
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        isSwitchUser
            ? Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: 16.w,
                  height: 16.h,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
              )
            : InkWell(
                onTap: () {
                  context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
                  FocusScope.of(context).unfocus();
                  NavigatorService.goBack();
                },
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: CustomImageView(
                    imagePath: Assets.images.svg.authentication.icBackArrow.path,
                    height: 16.h,
                  ),
                ),
              ),
        buildSizedBoxW(10.w),
        Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            width: 100.w,
            height: 14.h,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(100.r),
            ),
          ),
        ),
      ],
      actions: [
        isSwitchUser
            ? Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: 16.w,
                  height: 16.h,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
              )
            : BlocBuilder<UserProfileBloc, UserProfileState>(
                builder: (context, state) {
                  return Row(
                    children: [
                      buildSizedBoxW(11.w),
                      isCurrentUser
                          ? FutureBuilder<bool>(
                              future: Future.delayed(Duration(seconds: 2), () => isPostPermission),
                              builder: (context, snapshot) {
                                if (snapshot.connectionState == ConnectionState.waiting) {
                                  return SizedBox.shrink(); // Delay time ma kai nathi dakhadvu
                                }

                                if (snapshot.hasData && snapshot.data == true) {
                                  return InkWell(
                                    onTap: () => buildMoreOptionBottomSheet(context),
                                    child: Container(
                                      height: 36.h,
                                      width: 36.w,
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).customColors.white,
                                        shape: BoxShape.circle,
                                        boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 5)],
                                      ),
                                      child: Center(
                                        child: Icon(
                                          Icons.add_rounded,
                                          color: Theme.of(context).primaryColor,
                                        ),
                                      ),
                                    ),
                                  );
                                }

                                return SizedBox.shrink(); // Permission false hoy to pan kai nathi dakhadvu
                              },
                            )
                          // isPostPermission == true
                          //     ? InkWell(
                          //         onTap: () => buildMoreOptionBottomSheet(context),
                          //         child: Container(
                          //           height: 36.h,
                          //           width: 36.w,
                          //           decoration: BoxDecoration(
                          //               color: Theme.of(context).customColors.white,
                          //               shape: BoxShape.circle,
                          //               boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 5)]),
                          //           child: Center(
                          //             child: Icon(
                          //               Icons.add_rounded,
                          //               color: Theme.of(context).primaryColor,
                          //             ),
                          //           ),
                          //         ),
                          //       )
                          //     : SizedBox.shrink()
                          : SizedBox.shrink(),
                      buildSizedBoxW(11.w),
                      isCurrentUser
                          ? InkWell(
                              onTap: () => PersistentNavBarNavigator.pushNewScreen(context,
                                  screen: SettingScreen(refrelCode: state.userProfile?.data.refferenceCode ?? "")),
                              child: Container(
                                height: 36.h,
                                width: 36.w,
                                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                                decoration: BoxDecoration(
                                    color: Theme.of(context).customColors.white,
                                    shape: BoxShape.circle,
                                    boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 5)]),
                                child: CustomImageView(imagePath: Assets.images.icons.other.icMoreV.path),
                              ),
                            )
                          : SizedBox.shrink()
                    ],
                  );
                },
              )
      ],
    );
  }

  Widget _buildShimmerProfileHeader(ThemeState themestate) {
    return Column(
      children: [
        Row(
          children: [
            Shimmer.fromColors(
              baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
              highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
              child: Container(
                height: 89.h,
                width: 89.w,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
              ),
            ),
            buildSizedBoxW(8.h),
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Shimmer.fromColors(
                    baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
                    highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
                    child: Container(
                      width: 100.w,
                      height: 12.h,
                      color: Colors.white,
                    ),
                  ),
                  buildSizedBoxH(8.0),
                  _buildShimmerProfileInfo(themestate),
                ],
              ),
            ),
          ],
        ),
        buildSizedBoxH(10.h),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                height: 12.h,
                margin: EdgeInsets.only(right: 64.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
            ),
            buildSizedBoxH(4.0),
            Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                height: 12.h,
                margin: EdgeInsets.only(right: 120.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildShimmerProfileInfo(ThemeState themestate) {
    return Container(
      height: 60.h,
      decoration: BoxDecoration(
        color: themestate.isDarkThemeOn ? Colors.grey[850]! : Colors.grey[200]!,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Shimmer.fromColors(
            baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
            highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
            child: Container(
              width: 50.w,
              height: 18.h,
              color: Colors.white,
            ),
          ),
          Shimmer.fromColors(
            baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
            highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
            child: Container(
              width: 50.w,
              height: 18.h,
              color: Colors.white,
            ),
          ),
          Shimmer.fromColors(
            baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
            highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
            child: Container(
              width: 50.w,
              height: 18.h,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  // ignore: unused_element
  Widget _buildShimmerHorizontalList(ThemeState themestate) {
    return Container(
      height: 100.h,
      margin: EdgeInsets.only(bottom: 8.h),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 10,
        itemBuilder: (context, index) {
          return Shimmer.fromColors(
            baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
            highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
            child: Container(
              width: 75.w,
              height: 90.h,
              margin: EdgeInsets.only(right: 8.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(14.r),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildShimmerTabBar(ThemeState themestate) {
    return Container(
      height: 50.h,
      padding: EdgeInsets.symmetric(horizontal: 8.w),
      decoration: BoxDecoration(
        color: themestate.isDarkThemeOn ? Colors.grey[850]! : Colors.grey[200]!,
        borderRadius: BorderRadius.circular(25.r),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Shimmer.fromColors(
            baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
            highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
            child: Container(
              width: 60.w,
              height: 20.h,
              color: Colors.white,
            ),
          ),
          Shimmer.fromColors(
            baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
            highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
            child: Container(
              width: 60.w,
              height: 20.h,
              color: Colors.white,
            ),
          ),
          Shimmer.fromColors(
            baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
            highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
            child: Container(
              width: 60.w,
              height: 20.h,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerGrid(ThemeState themestate) {
    return StaggeredGridView.countBuilder(
      padding: EdgeInsets.only(bottom: 20.h, top: 0),
      shrinkWrap: true,
      crossAxisCount: 2,
      mainAxisSpacing: 2.0,
      crossAxisSpacing: 2.0,
      staggeredTileBuilder: (int index) {
        return StaggeredTile.count(1, index.isEven ? 1.3 : 1);
      },
      itemCount: 4,
      itemBuilder: (context, index) {
        return Shimmer.fromColors(
          baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
          highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(14.r),
            ),
          ),
        );
      },
    );
  }
}

Widget buildShimmerGrid() {
  return StaggeredGridView.countBuilder(
    padding: EdgeInsets.only(bottom: 20.h, top: 0, left: 16.w, right: 16.w),
    shrinkWrap: true,
    crossAxisCount: 2,
    mainAxisSpacing: 2.0,
    crossAxisSpacing: 2.0,
    staggeredTileBuilder: (int index) {
      return StaggeredTile.count(1, index.isEven ? 1.3 : 1);
    },
    itemCount: 4,
    itemBuilder: (context, index) {
      return Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(14.r),
          ),
        ),
      );
    },
  );
}
