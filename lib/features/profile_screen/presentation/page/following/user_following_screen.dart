import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/profile_screen/bloc/profile_bloc.dart';
import 'package:flowkar/features/profile_screen/presentation/page/follow/user_list_widget.dart';
import 'package:flowkar/features/profile_screen/presentation/widget/user_follow_screen_shimmer.dart';
import 'package:flutter/cupertino.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';

class UserFollowingScreen extends StatefulWidget {
  const UserFollowingScreen({super.key});

  @override
  State<UserFollowingScreen> createState() => _UserFollowingScreenState();
}

class _UserFollowingScreenState extends State<UserFollowingScreen> {
  late ScrollController followingscrollController;
  @override
  void initState() {
    super.initState();
    followingscrollController = ScrollController()..addListener(_scrollListener);
    final state = context.read<UserProfileBloc>().state;
    if (state.followingList.isNotEmpty) {
      state.followingList.clear();
    }
    context.read<UserProfileBloc>().add(GetFollowingListApiEvent(page: 1));
  }

  @override
  void dispose() {
    followingscrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (followingscrollController.position.pixels == followingscrollController.position.maxScrollExtent) {
      final state = context.read<UserProfileBloc>().state;
      if (!state.isLoadingMore) {
        context.read<UserProfileBloc>().add(GetFollowingListApiEvent(page: state.page + 1));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ConnectivityBloc, ConnectivityState>(
      listener: (context, connectivityState) {
        if (connectivityState.isReconnected) {
          context.read<UserProfileBloc>().add(GetFollowingListApiEvent(page: 1));
        }
      },
      child: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themestate) {
          return BlocBuilder<UserProfileBloc, UserProfileState>(
            builder: (context, state) {
              return Scaffold(
                appBar: _buildBackButton(),
                // resizeToAvoidBottomInset: false,
                body: Stack(
                  alignment: Alignment.bottomCenter,
                  children: [
                    Column(
                      children: [
                        // _buildBackButton(),
                        Expanded(
                          child: BlocBuilder<ConnectivityBloc, ConnectivityState>(
                            builder: (context, connectivityState) {
                              if (!connectivityState.isConnected && state.followingList.isEmpty) {
                                return UserFollowScreenShimmer();
                              } else if (state.isloding) {
                                return UserFollowScreenShimmer();
                              } else if (state.followingList.isEmpty) {
                                return ExceptionWidget(
                                  imagePath: Assets.images.svg.exception.svgNofollowPeople.path,
                                  showButton: false,
                                  title: "No following yet",
                                  subtitle: "You’re not following anyone yet.",
                                );
                              } else {
                                return _buildFollowerList(state);
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                    BlocBuilder<ConnectivityBloc, ConnectivityState>(
                      builder: (context, connectivityState) {
                        return Visibility(
                          visible: state.isLoadingMore && connectivityState.isConnected,
                          child: Container(
                            color: Colors.transparent,
                            height: 50.h,
                            child:
                                Center(child: CupertinoActivityIndicator(color: Theme.of(context).colorScheme.primary)),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  _buildBackButton() {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          Lang.of(context).lbl_following,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  void _initializeData() {
    final state = context.read<UserProfileBloc>().state;
    if (state.followingList.isNotEmpty) {
      state.followingList.clear();
    }
    context.read<UserProfileBloc>().add(GetFollowingListApiEvent(page: 1));
  }

  Widget _buildFollowerList(UserProfileState state) {
    return LiquidPullToRefresh(
      color: Theme.of(context).primaryColor.withOpacity(0.5),
      showChildOpacityTransition: false,
      backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      onRefresh: () async {
        _initializeData();
      },
      child: UserListWidget(
        searchController: state.searchController ?? TextEditingController(),
        scrollController: followingscrollController,
        followList: state.followingList,
        showSearchField: false,
        type: Lang.of(context).lbl_following,
      ),
    );
  }
}
