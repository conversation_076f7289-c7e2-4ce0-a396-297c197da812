// import 'package:flowkar/core/utils/exports.dart';
// import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
// import 'package:flowkar/features/profile_screen/bloc/profile_bloc.dart';
// import 'package:flowkar/features/profile_screen/presentation/page/follow/user_list_widget.dart';
// import 'package:flowkar/features/profile_screen/presentation/widget/user_follow_screen_shimmer.dart';
// import 'package:flutter/cupertino.dart';

// class UserFollowScreen extends StatefulWidget {
//   const UserFollowScreen({super.key});

//   @override
//   State<UserFollowScreen> createState() => _UserFollowScreenState();
// }

// class _UserFollowScreenState extends State<UserFollowScreen> {
//   late ScrollController followscrollController;
//   @override
//   void initState() {
//     super.initState();
//     followscrollController = ScrollController()..addListener(_scrollListener);
//     final state = context.read<UserProfileBloc>().state;
//     if (state.followList.isNotEmpty) {
//       state.followList.clear();
//     }
//     context.read<UserProfileBloc>().add(GetFollowerListApiEvent(page: 1));
//   }

//   @override
//   void dispose() {
//     followscrollController.dispose();
//     super.dispose();
//   }

//   void _scrollListener() {
//     if (followscrollController.position.pixels == followscrollController.position.maxScrollExtent) {
//       final state = context.read<UserProfileBloc>().state;
//       if (!state.isLoadingMore) {
//         context.read<UserProfileBloc>().add(GetFollowerListApiEvent(page: state.page + 1));
//       }
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return BlocListener<ConnectivityBloc, ConnectivityState>(
//       listener: (context, connectivityState) {
//         if (connectivityState.isReconnected) {
//           context.read<UserProfileBloc>().add(GetFollowerListApiEvent(page: 1));
//         }
//       },
//       child: BlocBuilder<ThemeBloc, ThemeState>(
//         builder: (context, themestate) {
//           return BlocBuilder<UserProfileBloc, UserProfileState>(
//             builder: (context, state) {
//               return Scaffold(
//                 resizeToAvoidBottomInset: false,
//                 // appBar: _buildBackButton(),
//                 body: Column(
//                   children: [
//                     _buildBackButton(),
//                     Expanded(
//                       child: BlocBuilder<ConnectivityBloc, ConnectivityState>(
//                         builder: (context, connectivityState) {
//                           if (!connectivityState.isConnected && state.followList.isEmpty) {
//                             return UserFollowScreenShimmer();
//                           } else if (state.isloding) {
//                             return UserFollowScreenShimmer();
//                           } else if (state.followList.isEmpty) {
//                             return ExceptionWidget(
//                               imagePath: Assets.images.svg.exception.svgNofollowPeople.path,
//                               showButton: false,
//                               title: "No followers yet",
//                               subtitle: "No followers yet be the first to follow!",
//                             );
//                           } else {
//                             return _buildFollowerList(state);
//                           }
//                         },
//                       ),
//                     ),
//                     BlocBuilder<ConnectivityBloc, ConnectivityState>(
//                       builder: (context, connectivityState) {
//                         return Visibility(
//                           visible: state.isLoadingMore && connectivityState.isConnected,
//                           child: SizedBox(
//                             height: 50.h,
//                             child:
//                                 Center(child: CupertinoActivityIndicator(color: Theme.of(context).colorScheme.primary)),
//                           ),
//                         );
//                       },
//                     ),
//                   ],
//                 ),
//               );
//             },
//           );
//         },
//       ),
//     );
//   }

//   _buildBackButton() {
//     return CustomAppbar(
//       hasLeadingIcon: true,
//       height: 18.h,
//       leading: [
//         InkWell(
//           onTap: () {
//             FocusScope.of(context).unfocus();
//             NavigatorService.goBack();
//           },
//           child: Padding(
//             padding: const EdgeInsets.all(8.0),
//             child: CustomImageView(
//               imagePath: Assets.images.svg.authentication.icBackArrow.path,
//               height: 16.h,
//             ),
//           ),
//         ),
//         buildSizedBoxW(20.w),
//         Text(
//           "Followers",
//           style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
//         ),
//       ],
//     );
//   }

//   Widget _buildFollowerList(UserProfileState state) {
//     return UserListWidget(
//       searchController: state.searchController ?? TextEditingController(),
//       scrollController: followscrollController,
//       followList: state.followList,
//       showSearchField: false,
//       type: Lang.of(context).lbl_follow,
//     );
//   }
// }

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/profile_screen/bloc/profile_bloc.dart';
import 'package:flowkar/features/profile_screen/presentation/page/follow/user_list_widget.dart';
import 'package:flowkar/features/profile_screen/presentation/widget/user_follow_screen_shimmer.dart';
import 'package:flutter/cupertino.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';

class UserFollowScreen extends StatefulWidget {
  const UserFollowScreen({super.key});

  @override
  State<UserFollowScreen> createState() => _UserFollowScreenState();
}

class _UserFollowScreenState extends State<UserFollowScreen> {
  late ScrollController followscrollController;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    followscrollController = ScrollController()..addListener(_scrollListener);
    _initializeData();
  }

  void _initializeData() {
    final state = context.read<UserProfileBloc>().state;
    if (state.followList.isNotEmpty) {
      state.followList.clear();
    }
    context.read<UserProfileBloc>().add(GetFollowerListApiEvent(page: 1));
  }

  @override
  void dispose() {
    followscrollController.removeListener(_scrollListener);
    followscrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (followscrollController.position.pixels >= followscrollController.position.maxScrollExtent - 200) {
      // Load before reaching bottom

      final state = context.read<UserProfileBloc>().state;

      // Check if we can load more data
      if (!_isLoadingMore && !state.isloding && state.followList.isNotEmpty) {
        // Add hasMoreData check in your bloc state

        setState(() {
          _isLoadingMore = true;
        });

        context.read<UserProfileBloc>().add(GetFollowerListApiEvent(page: state.page + 1));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ConnectivityBloc, ConnectivityState>(
      listener: (context, connectivityState) {
        if (connectivityState.isReconnected) {
          _initializeData();
        }
      },
      child: BlocListener<UserProfileBloc, UserProfileState>(
        listener: (context, state) {
          // Reset loading state when API call completes
          if (_isLoadingMore && !state.isloding) {
            setState(() {
              _isLoadingMore = false;
            });
          }
        },
        child: BlocBuilder<ThemeBloc, ThemeState>(
          builder: (context, themestate) {
            return BlocBuilder<UserProfileBloc, UserProfileState>(
              builder: (context, state) {
                return Scaffold(
                  resizeToAvoidBottomInset: false,
                  appBar: _buildBackButton(),
                  body: Stack(
                    alignment: Alignment.bottomCenter,
                    children: [
                      Column(
                        children: [
                          // _buildBackButton(),
                          Expanded(
                            child: BlocBuilder<ConnectivityBloc, ConnectivityState>(
                              builder: (context, connectivityState) {
                                if (!connectivityState.isConnected && state.followList.isEmpty) {
                                  return UserFollowScreenShimmer();
                                } else if (state.isloding && state.followList.isEmpty) {
                                  return UserFollowScreenShimmer();
                                } else if (state.followList.isEmpty && !state.isloding) {
                                  return ExceptionWidget(
                                    imagePath: Assets.images.svg.exception.svgNofollowPeople.path,
                                    showButton: false,
                                    title: "No followers yet",
                                    subtitle: "No followers yet be the first to follow!",
                                  );
                                } else {
                                  return _buildFollowerList(state);
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                      BlocBuilder<ConnectivityBloc, ConnectivityState>(
                        builder: (context, connectivityState) {
                          return Visibility(
                            visible: _isLoadingMore && connectivityState.isConnected,
                            child: Container(
                              color: Colors.transparent,
                              height: 50.h,
                              child: Center(
                                  child: CupertinoActivityIndicator(color: Theme.of(context).colorScheme.primary)),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  _buildBackButton() {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          "Followers",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  Widget _buildFollowerList(UserProfileState state) {
    return LiquidPullToRefresh(
      color: Theme.of(context).primaryColor.withOpacity(0.5),
      showChildOpacityTransition: false,
      backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      onRefresh: () async {
        _initializeData();
      },
      child: UserListWidget(
        searchController: state.searchController ?? TextEditingController(),
        scrollController: followscrollController,
        followList: state.followList,
        showSearchField: false,
        type: Lang.of(context).lbl_follow,
      ),
    );
  }
}
