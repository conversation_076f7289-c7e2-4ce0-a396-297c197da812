import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/profile_screen/bloc/profile_bloc.dart';
import 'package:flowkar/features/profile_screen/presentation/page/follow/user_list_widget.dart';
import 'package:flowkar/features/profile_screen/presentation/widget/user_follow_screen_shimmer.dart';
import 'package:flutter/cupertino.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';

class UserFollowbyIdScreen extends StatefulWidget {
  final int? userId;
  const UserFollowbyIdScreen({super.key, required this.userId});

  @override
  State<UserFollowbyIdScreen> createState() => _UserFollowbyIdScreenState();
}

class _UserFollowbyIdScreenState extends State<UserFollowbyIdScreen> {
  late ScrollController followscrollController;
  @override
  void initState() {
    super.initState();
    followscrollController = ScrollController()..addListener(_scrollListener);
    final state = context.read<UserProfileBloc>().state;
    if (state.followList.isNotEmpty) {
      state.followList.clear();
    }
    context.read<UserProfileBloc>().add(GetFollowerListbyIdApiEvent(page: 1, userId: widget.userId ?? 0));
  }

  void _initializeData() {
    final state = context.read<UserProfileBloc>().state;
    if (state.followList.isNotEmpty) {
      state.followList.clear();
    }
    context.read<UserProfileBloc>().add(GetFollowerListbyIdApiEvent(page: 1, userId: widget.userId ?? 0));
  }

  @override
  void dispose() {
    followscrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (followscrollController.position.pixels == followscrollController.position.maxScrollExtent) {
      final state = context.read<UserProfileBloc>().state;
      if (!state.isLoadingMore) {
        context
            .read<UserProfileBloc>()
            .add(GetFollowerListbyIdApiEvent(page: state.page + 1, userId: widget.userId ?? 0));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ConnectivityBloc, ConnectivityState>(
      listener: (context, connectivityState) {
        if (connectivityState.isReconnected) {
          context.read<UserProfileBloc>().add(GetFollowerListbyIdApiEvent(page: 1, userId: widget.userId ?? 0));
        }
      },
      child: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themestate) {
          return BlocBuilder<UserProfileBloc, UserProfileState>(
            builder: (context, state) {
              return Scaffold(
                resizeToAvoidBottomInset: false,
                appBar: _buildBackButton(),
                body: Stack(
                  alignment: Alignment.bottomCenter,
                  children: [
                    Column(
                      children: [
                        // _buildBackButton(),
                        Expanded(
                          child: BlocBuilder<ConnectivityBloc, ConnectivityState>(
                            builder: (context, connectivityState) {
                              if (!connectivityState.isConnected && state.followList.isEmpty) {
                                return UserFollowScreenShimmer();
                              } else if (state.isloding) {
                                return UserFollowScreenShimmer();
                              } else if (state.followList.isEmpty) {
                                return ExceptionWidget(
                                  imagePath: Assets.images.svg.exception.svgNofollowPeople.path,
                                  showButton: false,
                                  title: "No followers yet",
                                  subtitle: Lang.of(context).lbl_looks_like_this_user_doesnt,
                                );
                              } else {
                                return _buildFollowerList(state);
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                    BlocBuilder<ConnectivityBloc, ConnectivityState>(
                      builder: (context, connectivityState) {
                        return Visibility(
                          visible: state.isLoadingMore && connectivityState.isConnected,
                          child: Container(
                            color: Colors.transparent,
                            height: 50.h,
                            child:
                                Center(child: CupertinoActivityIndicator(color: Theme.of(context).colorScheme.primary)),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  _buildBackButton() {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            Navigator.pop(context);
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          "Followers",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  Widget _buildFollowerList(UserProfileState state) {
    return LiquidPullToRefresh(
      color: Theme.of(context).primaryColor.withOpacity(0.5),
      showChildOpacityTransition: false,
      backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      onRefresh: () async {
        _initializeData();
      },
      child: UserListWidget(
          searchController: state.searchController ?? TextEditingController(),
          scrollController: followscrollController,
          followList: state.followList,
          showSearchField: false,
          type: Lang.of(context).lbl_follow,
          // userId: widget.userId,
          isVisitor: true),
    );
  }
}
