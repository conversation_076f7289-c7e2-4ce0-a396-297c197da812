// ignore_for_file: deprecated_member_use, unused_element, use_build_context_synchronously, invalid_use_of_visible_for_testing_member

import 'dart:async';
import 'dart:io';

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/authentication/bloc/auth_bloc.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/home_feed_screen/model/post_response_model.dart';
import 'package:flowkar/features/profile_screen/bloc/profile_bloc.dart';
import 'package:flowkar/features/profile_screen/presentation/page/edit_profile_screen.dart';
import 'package:flowkar/features/profile_screen/presentation/page/follow/user_follow_screen.dart';
import 'package:flowkar/features/profile_screen/presentation/page/following/user_following_screen.dart';
import 'package:flowkar/features/profile_screen/presentation/widget/get_profile_post_widget.dart';
import 'package:flowkar/features/profile_screen/presentation/widget/get_profile_tag_post_widget.dart';
import 'package:flowkar/features/profile_screen/presentation/widget/get_profile_video_widget.dart';
import 'package:flowkar/features/profile_screen/presentation/widget/profile_shimmer.dart';
import 'package:flowkar/features/reels_screen/presentation/pages/video_page.dart';
import 'package:flowkar/features/reels_screen/service/reel_service.dart';
import 'package:flowkar/features/setting_screen/page/setting_screen.dart';
import 'package:flowkar/features/story_feature_demo/Story_view_demo/story_view/story_screen.dart';
// import 'package:flowkar/features/story_module/New_story_code/new_story_code.dart';
import 'package:flowkar/features/survey_form/bloc/survey_bloc.dart';
import 'package:flowkar/features/upload_post/presentation/widget/image_picker.dart';
import 'package:flowkar/features/user_management/bloc/user_management_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_linkify/flutter_linkify.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:permission_asker/permission_asker.dart';
import 'package:story_editor/story_editor.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/comment_bottom_sheet_widget.dart';
import 'package:flowkar/features/authentication/model/stored_user_account_model.dart';
import 'package:flowkar/features/authentication/presentation/widgets/multi_account_bottom_sheet.dart';
import 'package:flowkar/core/services/multi_account_manager.dart';
import 'package:url_launcher/url_launcher.dart';

class UserProfileScreen extends StatefulWidget {
  const UserProfileScreen({super.key});

  static Widget builder(BuildContext context) {
    return BlocProvider(
      create: (_) => UserProfileBloc()..add(UserProfileInitial()),
      child: const UserProfileScreen(),
    );
  }

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  late ScrollController _scrollController;
  int brandId = 0;
  String? brandName;
  int selectedIndex = 0;
  bool isOptin = false;
  String? userType;

  // Add caching for posts and videos
  final Map<int, Widget> _postCache = {};
  final Map<int, Widget> _videoCache = {};
  final Map<int, Widget> _tagPostCache = {};
  bool _isLoadingMore = false;
  bool _isInitialLoadDone = false;
  bool _allowBackNavigation = true;
  Timer? _backNavigationTimer;

  // Offline message state
  Timer? _offlineMessageTimer;
  bool _showOfflineMessage = false;
  bool _isRefreshAttemptedOffline = false;
  late bool isPostPermission;
  late bool isLoginUser;

  @override
  void initState() {
    userType = Prefobj.preferences?.get(Prefkeys.USERTYPE);
    isOptin = Prefobj.preferences?.get(Prefkeys.ISOPTIN) ?? false;
    brandId = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;

    super.initState();

    // Initialize controllers
    _tabController = TabController(length: 4, vsync: this);
    _scrollController = ScrollController()..addListener(_scrollListener);

    // Load initial data
    _loadInitialData();
  }

  void _loadInitialData() {
    if (!_isInitialLoadDone) {
      // context.read<UserManagementBloc>().add(GetBrandsAPI());
      final state = context.read<HomeFeedBloc>().state;
      final userProfilestate = context.read<UserProfileBloc>().state;

      // Clear existing data
      userProfilestate.userProfile = null;
      state.isuserProfileposts.clear();
      state.getProfilevideo.clear();
      state.getProfilePost.clear();
      state.getUserTextPostData.clear();
      if (userProfilestate.highlightStoryData.isNotEmpty) {
        userProfilestate.highlightStoryData.clear();
      }
      // Load data in parallel
      Future.wait([
        Future(() => context.read<UserProfileBloc>().add(FetchUserProfileEvent(onEdit: false))),
        Future(() => context.read<HomeFeedBloc>().add(FetchUserProfilePostEvent(page: 1))),
        Future(() => context.read<HomeFeedBloc>().add(FetchUserProfileVideoEvent(page: 1))),
        Future(() => context.read<HomeFeedBloc>().add(GetUserTextPostEvent(page: 1))),
        Future(() => context.read<HomeFeedBloc>().add(GetTagPostApi(tagpage: 1))),
        Future(() => context.read<UserProfileBloc>().add(GetHighlightStoryApiEvent())),
      ]).then((_) {
        if (mounted) {
          setState(() {
            _isInitialLoadDone = true;
          });
        }
      });
    }
  }

  void _scrollListener() {
    if (!mounted || _isLoadingMore) return;

    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8) {
      final state = context.read<HomeFeedBloc>().state;

      switch (_tabController.index) {
        case 0: // Posts
          if (state.getProfilePostmodel?.nextPage != null && !state.getprofilPosteLoadingmore) {
            _isLoadingMore = true;
            context.read<HomeFeedBloc>().add(FetchUserProfilePostEvent(page: state.getProfilepostpage + 1));
          }
          break;
        case 1: // Videos
          if (state.getProfilevideoResponseModel?.next != null && !state.getProfileisVideoLoadingMore) {
            _isLoadingMore = true;
            context.read<HomeFeedBloc>().add(FetchUserProfileVideoEvent(page: state.getProfilevideoPage + 1));
          }
          break;
        case 2: // Tagged
          if (state.tagPostmodel?.next != null && !state.istagLoadingMore) {
            _isLoadingMore = true;
            context.read<HomeFeedBloc>().add(GetTagPostApi(tagpage: state.tagpage + 1));
          }
          break;
        case 3: // Text Posts
          if (state.getUserTextPostModel?.next != null && !state.getUserTextPostLoadingMore) {
            _isLoadingMore = true;
            context.read<HomeFeedBloc>().add(GetUserTextPostEvent(page: state.getUserTextPostPage + 1));
          }
          break;
      }
    }
  }

  Future<void> _refreshFeed() async {
    if (!mounted) return;

    final state = context.read<HomeFeedBloc>().state;
    final userProfilestate = context.read<UserProfileBloc>().state;
    final connectivityState = context.read<ConnectivityBloc>().state;

    if (!connectivityState.isConnected) {
      // User attempted to refresh while offline
      _isRefreshAttemptedOffline = true;
      _offlineMessageTimer?.cancel();
      _offlineMessageTimer = Timer(const Duration(seconds: 3), () {
        if (mounted && _isRefreshAttemptedOffline) {
          setState(() {
            _showOfflineMessage = true;
          });
          Timer(const Duration(seconds: 3), () {
            if (mounted) {
              setState(() {
                _showOfflineMessage = false;
                _isRefreshAttemptedOffline = false;
              });
            }
          });
        }
      });
      return;
    }

    // Clear existing data
    userProfilestate.userProfile = null;
    state.getProfilevideo.clear();
    state.tagPostData.clear();
    state.isuserProfileposts.clear();
    state.getUserTextPostData.clear();
    state.getProfilePost.clear();

    // Clear caches
    _postCache.clear();
    _videoCache.clear();
    _tagPostCache.clear();
    _isLoadingMore = false;
    _isInitialLoadDone = false;
    userProfilestate.highlightStoryData.clear();

    // Load fresh data in parallel
    await Future.wait([
      Future(() => context.read<UserProfileBloc>().add(FetchUserProfileEvent(onEdit: false))),
      Future(() => context.read<HomeFeedBloc>().add(FetchUserProfilePostEvent(page: 1))),
      Future(() => context.read<HomeFeedBloc>().add(FetchUserProfileVideoEvent(page: 1))),
      Future(() => context.read<HomeFeedBloc>().add(GetTagPostApi(tagpage: 1))),
      Future(() => context.read<HomeFeedBloc>().add(GetUserTextPostEvent(page: 1))),
      Future(() => context.read<UserProfileBloc>().add(GetHighlightStoryApiEvent())),
    ]);

    if (mounted) {
      setState(() {
        _isInitialLoadDone = true;
      });
    }
  }

  /// Clears all BLoC states for account switching
  /// This ensures that when switching accounts, all previous user data is cleared
  /// and the UI will be refreshed with the new user's data
  Future<void> _clearAllBlocStates() async {
    try {
      Logger.lOG("Clearing all BLoC states for account switch...");

      // Use DataCleanupService to clear user data properly
      await DataCleanupService.clearUserDataForAccountSwitch();

      // Clear local caches
      _postCache.clear();
      _videoCache.clear();
      _tagPostCache.clear();
      _isLoadingMore = false;
      _isInitialLoadDone = false;

      Logger.lOG("All BLoC states cleared for account switch");
    } catch (error) {
      Logger.lOG("Error clearing BLoC states for account switch: $error");
    }
  }

  /// Refreshes all data after account switch completion
  /// This method triggers all necessary API calls to load the new user's data
  Future<void> _refreshAllDataAfterAccountSwitch() async {
    try {
      Logger.lOG("Starting data refresh after account switch...");

      // Reset loading states
      _isLoadingMore = false;
      _isInitialLoadDone = false;
      _postCache.clear();
      _videoCache.clear();
      _tagPostCache.clear();
      _isLoadingMore = false;
      _isInitialLoadDone = false;
      final homeFeedBloc = context.read<HomeFeedBloc>();
      final userProfileBloc = context.read<UserProfileBloc>();
      homeFeedBloc.emit(homeFeedBloc.state.copyWith(
        posts: [],
        video: [],
        savedposts: [],
        isDiscoverposts: [],
        isuserProfileposts: [],
        getProfilePost: [],
        getProfilevideo: [],
        tagPostData: [],
        getUserTextPostData: [],
        newStoryData: [],
        reels: [],
      ));

      userProfileBloc.emit(userProfileBloc.state.copyWith(
        userProfile: null,
        highlightStoryData: [],
        followList: [],
        followingList: [],
      ));
      final state = context.read<HomeFeedBloc>().state;

      // Clear existing data
      // userProfilestate.userProfile = null;
      state.getProfilePost.clear();
      state.getProfilevideo.clear();
      state.tagPostData.clear();
      state.isuserProfileposts.clear();
      state.getUserTextPostData.clear();

      // Trigger all necessary API calls for the new user in parallel
      await Future.wait([
        // User profile data
        Future(() => context.read<UserProfileBloc>().add(FetchUserProfileEvent(onEdit: false))),

        // User posts and content
        Future(() => context.read<HomeFeedBloc>().add(FetchUserProfilePostEvent(page: 1))),
        Future(() => context.read<HomeFeedBloc>().add(GetTagPostApi(tagpage: 1))),
        Future(() => context.read<HomeFeedBloc>().add(GetUserTextPostEvent(page: 1))),
        Future(() => context.read<HomeFeedBloc>().add(FetchUserProfileVideoEvent(page: 1))),

        // Stories and highlights
        Future(() => context.read<UserProfileBloc>().add(GetHighlightStoryApiEvent())),

        // Home feed data (for main feed refresh)
        Future(() => context.read<HomeFeedBloc>().add(GetAllPostApiEvent(page: 1))),

        Future(() => context.read<HomeFeedBloc>().add(GetAllVideoApiEvent(page: 1))),
        Future(() => context.read<HomeFeedBloc>().add(GetNewStoryApiEvent())),

        // User management data (brands list)
        // Future(() => context.read<UserManagementBloc>().add(GetBrandsAPI())),
      ]);

      if (mounted) {
        setState(() {
          _isInitialLoadDone = true;
        });
      }

      Logger.lOG("Data refresh completed after account switch");
    } catch (error) {
      Logger.lOG("Error refreshing data after account switch: $error");
    }
  }

  /// Switch to a selected account
  Future<void> _switchToAccount(StoredUserAccount selectedAccount) async {
    try {
      // Store current user before switching (if not already stored)
      await _storeCurrentUserBeforeSwitch();

      // Get the stored accounts list to find the index
      final accountsList = await MultiAccountManager.getStoredAccounts();
      if (accountsList != null) {
        final accountIndex = accountsList.accounts.indexWhere(
          (account) => account.brandId == selectedAccount.brandId && account.userId == selectedAccount.userId,
        );

        if (accountIndex != -1) {
          // Clear all data before switching
          // await _clearAllBlocStates();

          // Trigger account switch
          context.read<AuthBloc>().add(SwitchAccountEvent(
                brandId: selectedAccount.brandId,
                accountIndex: accountIndex,
                brandName: "",
                context: context,
              ));
        }
      }
    } catch (error) {
      Logger.lOG("Error switching to account: $error");
    }
  }

  /// Store current user before switching to another account
  Future<void> _storeCurrentUserBeforeSwitch() async {
    try {
      // Get current user info from preferences
      final currentName = Prefobj.preferences?.get(Prefkeys.NAME) ?? '';
      final currentUsername = Prefobj.preferences?.get(Prefkeys.USERNAME) ?? '';
      final currentProfileImage = Prefobj.preferences?.get(Prefkeys.PROFILE) ?? '';
      final currentBrandId = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;
      final currentUserId = int.tryParse(Prefobj.preferences?.get(Prefkeys.USER_ID)?.toString() ?? '0') ?? 0;
      final currentToken = Prefobj.preferences?.get(Prefkeys.AUTHTOKEN) ?? '';

      // Check if current user is already in stored accounts
      final storedAccounts = await MultiAccountManager.getStoredAccounts();
      bool currentUserExists = false;

      if (storedAccounts != null) {
        currentUserExists = storedAccounts.accounts
            .any((account) => account.userId == currentUserId && account.brandId == currentBrandId);
      }

      // If current user is not in stored accounts, add them
      if (!currentUserExists && currentUserId > 0) {
        final currentUserAccount = StoredUserAccount(
          brandId: currentBrandId,
          name: currentName.isNotEmpty ? currentName : 'User',
          profileImage: currentProfileImage,
          username: currentUsername.isNotEmpty ? currentUsername : 'user',
          userId: currentUserId,
          token: currentToken,
        );

        await MultiAccountManager.addAccount(currentUserAccount);
        Logger.lOG("Current user stored before account switch: ${currentUserAccount.name}");
      }
    } catch (error) {
      Logger.lOG("Error storing current user before switch: $error");
    }
  }

  /// Navigate to login screen for adding a new account
  void _navigateToAddAccount() {
    // Store current user before adding new account
    _storeCurrentUserBeforeSwitch();

    PersistentNavBarNavigator.pushNewScreen(
      context,
      screen: SigninPage(
        isAddingAccount: true,
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _postCache.clear();
    _videoCache.clear();
    _tagPostCache.clear();
    _backNavigationTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    userType = Prefobj.preferences?.get(Prefkeys.USERTYPE);
    isOptin = Prefobj.preferences?.get(Prefkeys.ISOPTIN) ?? false;
    brandId = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;
    isPostPermission = Prefobj.preferences?.get(Prefkeys.PRM_POST) ?? false;
    isLoginUser = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) == Prefobj.preferences?.get(Prefkeys.USER_ID);

    return Stack(
      alignment: Alignment.center,
      children: [
        Padding(
          padding: EdgeInsets.only(right: 3.w, left: 3.w),
          child: Scaffold(
            body: BlocListener<AuthBloc, AuthState>(
              listener: (context, authState) {
                // Listen for account switch completion
                if (authState.isSwitchUserAccountLoading && authState.loginResponseModel != null) {
                  // Account switch completed successfully, refresh all data for new user
                  Logger.lOG("Account switch detected, refreshing all data for new user");
                  // _refreshAllDataAfterAccountSwitch();
                }
                if (!authState.isSwitchUserAccountLoading && !_allowBackNavigation) {
                  _backNavigationTimer?.cancel(); // Previous timer cancel કરો
                  _allowBackNavigation = false;

                  _backNavigationTimer = Timer(Duration(seconds: 2), () {
                    if (mounted) {
                      setState(() {
                        _allowBackNavigation = true;
                      });
                    }
                  });
                } else if (authState.isSwitchUserAccountLoading) {
                  // જ્યારે loading start થાય ત્યારે back navigation disable કરો
                  _allowBackNavigation = false;
                }
              },
              child: BlocBuilder<AuthBloc, AuthState>(
                builder: (context, authState) {
                  return PopScope(
                    canPop: !authState.isSwitchUserAccountLoading && _allowBackNavigation,
                    onPopInvoked: (didPop) {
                      if (didPop) return;
                      NavigatorService.goBack();
                    },
                    child: authState.isSwitchUserAccountLoading
                        ? ProfileShimmer(isCurrentUser: true, isSwitchUser: true)
                        : BlocListener<UserManagementBloc, UserManagementState>(
                            listener: (context, userManagementState) {
                              brandId = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;
                              if (userManagementState.getBrandsModel != null) {
                                final brands = userManagementState.getBrandsModel?.data ?? [];
                                for (int i = 0; i < brands.length; i++) {
                                  if (brands[i].id == brandId) {
                                    selectedIndex = i;
                                    brandName = brands[i].name;
                                    break;
                                  }
                                }
                              }
                            },
                            child: BlocListener<ConnectivityBloc, ConnectivityState>(
                              listener: (context, state) {
                                if (state.isReconnected) {
                                  if (context.read<HomeFeedBloc>().state.getProfilePost.isEmpty) {
                                    _refreshFeed();
                                  }
                                }
                              },
                              child: BlocListener<HomeFeedBloc, HomeFeedState>(
                                listener: (context, homeFeedState) {
                                  if (_isLoadingMore) {
                                    // If _isLoadingMore is true, it means a pagination request was sent.
                                    // We should reset it when the corresponding loading process in HomeFeedBloc finishes.
                                    bool anyTabIsStillLoadingMore = homeFeedState.getprofilPosteLoadingmore ||
                                        homeFeedState.getProfileisVideoLoadingMore ||
                                        homeFeedState.istagLoadingMore ||
                                        homeFeedState.getUserTextPostLoadingMore;

                                    if (!anyTabIsStillLoadingMore) {
                                      // If _isLoadingMore was true, and now none of the tabs are in a "loading more" state,
                                      // it's safe to reset _isLoadingMore.
                                      if (mounted) {
                                        setState(() {
                                          _isLoadingMore = false;
                                        });
                                      }
                                    }
                                  }
                                },
                                child: BlocBuilder<HomeFeedBloc, HomeFeedState>(
                                  builder: (context, state) {
                                    return BlocBuilder<UserProfileBloc, UserProfileState>(
                                      builder: (context, userProfileState) {
                                        bool isCurrentUser = true;
                                        return BlocBuilder<ConnectivityBloc, ConnectivityState>(
                                          builder: (context, connectivityState) {
                                            if (!connectivityState.isConnected &&
                                                userProfileState.userProfile == null) {
                                              return ProfileShimmer(isCurrentUser: isCurrentUser);
                                            } else if (userProfileState.loading) {
                                              return ProfileShimmer(isCurrentUser: isCurrentUser);
                                            } else {
                                              return Scaffold(
                                                // backgroundColor: Colors.transparent,
                                                appBar: _buildAppBar(context, isCurrentUser, state, userProfileState),
                                                body: LiquidPullToRefresh(
                                                  color: Theme.of(context).primaryColor.withOpacity(0.5),
                                                  showChildOpacityTransition: false,
                                                  backgroundColor:
                                                      Theme.of(context).colorScheme.primary.withOpacity(0.5),
                                                  onRefresh: _refreshFeed,
                                                  child: DefaultTabController(
                                                    length: 4,
                                                    child: SafeArea(
                                                      bottom: false,
                                                      child: Column(
                                                        children: [
                                                          Expanded(
                                                            child: NestedScrollView(
                                                              controller: _scrollController, // Added controller
                                                              headerSliverBuilder: (context, _) {
                                                                return [
                                                                  SliverList(
                                                                    delegate: SliverChildListDelegate(
                                                                      [
                                                                        Column(
                                                                          children: [
                                                                            _buildProfileInfo(
                                                                                state, userProfileState, isCurrentUser),
                                                                            buildSizedBoxH(20),
                                                                            _buildhighlightStory(
                                                                                state: userProfileState),
                                                                            buildSizedBoxH(4),
                                                                          ],
                                                                        ),
                                                                        // _buildhighlightStory(state: state),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ];
                                                              },
                                                              body: Stack(
                                                                children: [
                                                                  Column(
                                                                    children: <Widget>[
                                                                      Material(
                                                                          color: Colors.transparent,
                                                                          child: Padding(
                                                                            padding:
                                                                                EdgeInsets.symmetric(horizontal: 0.0.w),
                                                                            child: _buildTabBar(),
                                                                          )),
                                                                      Expanded(
                                                                        child: TabBarView(
                                                                          // physics:
                                                                          //     const NeverScrollableScrollPhysics(),
                                                                          controller: _tabController,
                                                                          children: [
                                                                            _buildPostContentGrid(
                                                                                state, userProfileState),
                                                                            _buildVideoContent(state, userProfileState),
                                                                            _buildTagPeopleContent(state),
                                                                            _buildTextPostContent(state),
                                                                          ],
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                  if (state.getUserTextPostLoadingMore ||
                                                                      state.getProfileisVideoLoadingMore ||
                                                                      state.getprofilPosteLoadingmore ||
                                                                      state.istagLoadingMore)
                                                                    Align(
                                                                      alignment: Alignment.bottomCenter,
                                                                      child: CupertinoActivityIndicator(
                                                                        color: Theme.of(context).colorScheme.primary,
                                                                      ),
                                                                    ),
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              );
                                            }
                                          },
                                        );
                                      },
                                    );
                                  },
                                ),
                              ),
                            ),
                          ),
                  );
                },
              ),
            ),
          ),
        ),
        _buildOfflineMessage(_showOfflineMessage),
      ],
    );
  }

  PreferredSizeWidget _buildAppBar(
      BuildContext context, bool isCurrentUser, HomeFeedState state, UserProfileState userProfileState) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(10.w),
        Flexible(
          child: InkWell(
            onTap: () {
              // Load stored accounts first
              context.read<AuthBloc>().add(LoadStoredAccountsEvent());
              // VibrationHelper.singleShortBuzz();

              showModalBottomSheet(
                  useRootNavigator: true,
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  builder: (context) {
                    return MultiAccountBottomSheet(onAccountSelected: (selectedAccount) {
                      // Switch to the selected account
                      _switchToAccount(selectedAccount);
                    }, onAddAccountTapped: () {
                      // Navigate to login screen for adding new account
                      _navigateToAddAccount();
                    });
                  });
            },
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                buildSizedBoxW(10),
                Flexible(
                  child: Text(
                    userProfileState.userProfile?.data.username.isEmpty ?? true
                        ? "@${Prefobj.preferences?.get(Prefkeys.USERNAME)}"
                        : "@${userProfileState.userProfile?.data.username}",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style:
                        Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
                  ),
                ),
                // if (isCurrentUser && userType != "User")
                //   Padding(
                //     padding: const EdgeInsets.all(8.0),
                //     child: CustomImageView(imagePath: Assets.images.svg.profile.svgSwap.path),
                //   ),
              ],
            ),
          ),
        ),
        buildSizedBoxW(90),
      ],
      actions: [
        if (isCurrentUser)
          Row(
            children: [
              buildSizedBoxW(11.w),
              if (isPostPermissionNotifier.value ?? false)
                InkWell(
                  onTap: () => buildMoreOptionBottomSheet(context),
                  child: Container(
                    height: 36.h,
                    width: 36.w,
                    decoration: BoxDecoration(
                        color: Theme.of(context).customColors.white,
                        shape: BoxShape.circle,
                        boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 5)]),
                    child: Center(
                      child: Icon(
                        Icons.add_rounded,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                ),
              buildSizedBoxW(11.w),
              InkWell(
                onTap: () => PersistentNavBarNavigator.pushNewScreen(context,
                    screen: SettingScreen(
                      refrelCode: userProfileState.userProfile?.data.refferenceCode ?? "",
                    )),
                child: Container(
                  height: 36.h,
                  width: 36.w,
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                  decoration: BoxDecoration(
                      color: Theme.of(context).customColors.white,
                      shape: BoxShape.circle,
                      boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 5)]),
                  child: CustomImageView(imagePath: Assets.images.icons.other.icMoreV.path),
                ),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildProfileInfo(HomeFeedState state, UserProfileState userProfileState, bool isCurrentUser) {
    isOptin = Prefobj.preferences?.get(Prefkeys.ISOPTIN) ?? false;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Stack(
                alignment: Alignment.bottomRight,
                children: [
                  Container(
                    height: 89.h,
                    width: 89.w,
                    padding: EdgeInsets.all(userProfileState.userProfile?.data.profileImage == '' ? 16 : 0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100.r),
                      border: Border.all(
                        color: userProfileState.userProfile?.data.profileImage == ''
                            ? Theme.of(context).primaryColor.withOpacity(0.2)
                            : Theme.of(context).primaryColor.withOpacity(0.05),
                        width: 2.w,
                      ),
                      // color: Theme.of(context).primaryColor.withOpacity(0.5),
                    ),
                    child: CustomImageView(
                      imagePath: userProfileState.userProfile?.data.profileImage == ''
                          ? AssetConstants.pngUser
                          : userProfileState.userProfile?.data.profileImage,
                      radius: BorderRadius.circular(userProfileState.userProfile?.data.profileImage == '' ? 0 : 100.r),
                      fit: userProfileState.userProfile?.data.profileImage == '' ? BoxFit.contain : BoxFit.cover,
                      fallbackImage: AssetConstants.pngUserReomve,
                    ),
                  ),
                  if (isCurrentUser)
                    if (isLoginUser)
                      InkWell(
                        onTap: () async {
                          // await NavigatorService.pushNamed(AppRoutes.editProfileScreen);
                          _loadInitialData();
                          PersistentNavBarNavigator.pushNewScreen(context, screen: EditProfileScreen());
                        },
                        child: Container(
                          height: 27.h,
                          width: 27.w,
                          decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Theme.of(context).customColors.white,
                              border: Border.all(color: Colors.black26, width: 0.8),
                              boxShadow: [
                                // CustomImageView(imagePath: Assets.images.svg.profile.svgEdit.path,height: 5.h,width:5.w,fit:BoxFit.cover),
                                BoxShadow(
                                  color: Theme.of(context).customColors.black.withOpacity(0.15),
                                  blurRadius: 6,
                                  offset: Offset(0, 2),
                                  spreadRadius: 0,
                                ),
                              ]),
                          child: Padding(
                            padding: const EdgeInsets.all(6),
                            child: CustomImageView(
                              imagePath: Assets.images.svg.profile.svgEdit.path,
                            ),
                          ),
                        ),
                      ),

                  // BlocBuilder<SurveyBloc, SurveyState>(
                  //   builder: (context, surveyState) {
                  //     return surveyState.userHomeDataModel?.isOptIn == true
                  //         ? Container(
                  //             height: 27.h,
                  //             width: 27.w,
                  //             padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 7.h),
                  //             decoration: BoxDecoration(
                  //               color: Theme.of(context).customColors.white,
                  //               shape: BoxShape.circle,
                  //               border: Border.all(
                  //                 color: Theme.of(context).primaryColor,
                  //                 width: 0.6.w,
                  //               ),
                  //               boxShadow: [
                  //                 BoxShadow(
                  //                   offset: const Offset(0, 2),
                  //                   color: Theme.of(context).primaryColor,
                  //                   blurRadius: 5,
                  //                   spreadRadius: -2,
                  //                 ),
                  //               ],
                  //             ),
                  //             child: CustomImageView(imagePath: Assets.images.svg.profile.svgPremiumBadge.path),
                  //           )
                  //         : SizedBox.shrink();
                  //   },
                  // )
                ],
              ),
              buildSizedBoxW(16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(userProfileState.userProfile?.data.name ?? "",
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                        style: Theme.of(context)
                            .textTheme
                            .titleLarge
                            ?.copyWith(fontSize: 16.sp, color: Color(0xff22240F))),
                    buildSizedBoxH(6.h),
                    Container(
                      height: 60.h,
                      decoration: BoxDecoration(
                        color: Theme.of(context).customColors.greyContainerBg.withOpacity(0.9),
                        border: Border.all(color: Color.fromARGB(84, 99, 97, 84), width: 0.5.w),
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            userStatItem(
                              value: userProfileState.userProfile?.data.numberOfPost ?? 0,
                              label: Lang.of(context).lbl_post,
                            ),
                            verticalDivider(),
                            userStatItem(
                              value: userProfileState.userProfile?.data.numberOfFollowers ?? 0,
                              label: Lang.of(context).lbl_followers,
                              onTap: () {
                                PersistentNavBarNavigator.pushNewScreen(context, screen: UserFollowScreen());
                              },
                            ),
                            verticalDivider(),
                            userStatItem(
                              value: userProfileState.userProfile?.data.numberOfFollowing ?? 0,
                              label: Lang.of(context).lbl_following,
                              onTap: () {
                                PersistentNavBarNavigator.pushNewScreen(context, screen: UserFollowingScreen());
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (userProfileState.userProfile?.data.bio.isNotEmpty ?? false)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildSizedBoxH(10.h),
                BioTextWidget(text: userProfileState.userProfile?.data.bio ?? ""),
                // Text(userProfileState.userProfile?.data.bio ?? "",
                //     style: Theme.of(context).textTheme.titleLarge?.copyWith(fontSize: 16.sp)),
              ],
            ),
        ],
      ),
    );
  }

  Widget userStatItem({required int value, required String label, VoidCallback? onTap}) {
    return InkWell(
      onTap: onTap,
      child: SizedBox(
        width: label == Lang.of(context).lbl_post ? 40.w : 55.w,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              abbreviateNumber(value),
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontSize: 15.sp, fontWeight: FontWeight.bold, color: Color(0xff563D39)),
            ),
            buildSizedBoxH(4.h),
            Text(
              label,
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontSize: 12.sp, fontWeight: FontWeight.w500, color: Color(0xff8D8480)),
            ),
          ],
        ),
      ),
    );
  }

  Widget verticalDivider() {
    return Container(
      height: 40,
      width: 0.8,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).primaryColor.withOpacity(0),
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return AnimatedBuilder(
      animation: _tabController.animation!,
      builder: (context, _) {
        double animationValue = _tabController.animation!.value;

        Color resolveColor(int tabIndex) {
          double difference = (tabIndex - animationValue).abs();
          double t = difference.clamp(0.0, 1.0); // transition factor
          return Color.lerp(
            Theme.of(context).customColors.white,
            Theme.of(context).customColors.black,
            t,
          )!;
        }

        return Container(
          height: 48.h,
          margin: EdgeInsets.symmetric(vertical: 4.h, horizontal: 13.w),
          padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: Theme.of(context).customColors.greyContainerBg,
            borderRadius: BorderRadius.circular(25.r),
          ),
          child: TabBar(
            controller: _tabController,
            labelColor: Theme.of(context).customColors.white,
            unselectedLabelColor: Theme.of(context).customColors.black,
            dividerColor: Theme.of(context).customColors.transparent,
            indicatorSize: TabBarIndicatorSize.tab,
            overlayColor: WidgetStateColor.transparent,
            indicator: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: BorderRadius.circular(25.r),
            ),
            padding: EdgeInsets.zero,
            labelStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700, fontSize: 14.sp),
            unselectedLabelStyle:
                Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700, fontSize: 14.sp),
            textScaler: TextScaler.linear(0.93),
            tabs: [
              Tab(
                child: CustomImageView(
                  imagePath: Assets.images.svg.profile.svgPost.path,
                  color: resolveColor(0),
                ),
              ),
              Tab(
                child: CustomImageView(
                  imagePath: Assets.images.svg.profile.svgVideo.path,
                  color: resolveColor(1),
                ),
              ),
              Tab(
                child: CustomImageView(
                  imagePath: Assets.images.svg.profile.svgTagged.path,
                  color: resolveColor(2),
                ),
              ),
              Tab(
                child: CustomImageView(
                  imagePath: Assets.images.svg.profile.svgTextPost.path,
                  height: 20.h,
                  color: resolveColor(3),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPostContentGrid(HomeFeedState state, UserProfileState userProfileState) {
    // if (state.getProfilePost.isEmpty) {
    //   return Column(
    //     children: [
    //       buildSizedBoxH(20.0),
    //       ExceptionWidget(
    //         imagePath: Assets.images.pngs.pngNoPost.path,
    //         title: Lang.of(context).lbl_no_post_found,
    //         subtitle: Lang.of(context).lbl_share_your_first_post,
    //         showButton: false,
    //       ),
    //     ],
    //   );
    // }

    return state.getprofilrPosteLoading
        ? buildShimmerGrid()
        : state.getProfilePost.isEmpty
            ? Column(
                children: [
                  buildSizedBoxH(20.0),
                  ExceptionWidget(
                    imagePath: Assets.images.pngs.pngNoPost.path,
                    title: Lang.of(context).lbl_no_post_found,
                    subtitle: Lang.of(context).lbl_share_your_first_post,
                    showButton: false,
                  ),
                ],
              )
            : StaggeredGridView.countBuilder(
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.only(top: 10.h, bottom: 55.h, left: 16.0.w, right: 16.0.w),
                crossAxisCount: 2,
                mainAxisSpacing: 2.0,
                crossAxisSpacing: 2.0,
                staggeredTileBuilder: (int index) {
                  return StaggeredTile.count(1, index.isEven ? 1.3 : 1);
                },
                itemCount: state.getProfilePost.length,
                itemBuilder: (context, index) {
                  var post = state.getProfilePost[index];
                  var ispost = state.getProfilePost[index].files.isNotEmpty
                      ? post.files.first
                      : Assets.images.pngs.other.pngPlaceholder.path;
                  return Container(
                    clipBehavior: Clip.antiAlias,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(18.0.r),
                        border: Border.all(color: Theme.of(context).primaryColor.withOpacity(0.1), width: 2)),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16.0.r),
                      child: isVideo(ispost)
                          ? post.thumbnailFiles!.isNotEmpty
                              ? InkWell(
                                  onTap: () {
                                    setState(() {
                                      FocusScope.of(context).unfocus();
                                      PersistentNavBarNavigator.pushNewScreen(
                                        context,
                                        screen: GetProfilePostWidget(
                                          initialIndex: index,
                                          profilepost: state.getProfilePost,
                                          userId: userProfileState.userProfile?.data.id,
                                        ),
                                      );
                                    });
                                  },

                                  // onTap: () {
                                  //   Logger.lOG(
                                  //       "Click User Profile ${state.getProfilePost[index].thumbnailFiles},    $index , ${state.getProfilePost[index].id}");
                                  //   FocusManager.instance.primaryFocus?.unfocus();
                                  //   PersistentNavBarNavigator.pushNewScreen(context,
                                  //       screen: VideoReelPage(
                                  //         index: 0,
                                  //         reelService: ReelService(),
                                  //         screen: 'Profile',
                                  //         postdata: PostData.fromJson(state.getProfilePost[index].toJson()),
                                  //       ),
                                  //       withNavBar: false);
                                  //   Logger.lOG("widget.postdata Before ${state.getProfilePost[index].id}");
                                  // },
                                  child: Stack(
                                    fit: StackFit.expand,
                                    children: [
                                      CustomImageView(
                                        width: double.infinity,
                                        imagePath: post.thumbnailFiles?.first,
                                        fit: BoxFit.cover,
                                        // radius: BorderRadius.circular(16.0.r),
                                      ),
                                      Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          CustomImageView(
                                            margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                                            imagePath: Assets.images.svg.other.svgPlayIconWhite.path,
                                            color: Colors.white70,
                                            height: 30.h,
                                            width: 30.w,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                )
                              : Container(
                                  decoration: BoxDecoration(
                                    color: Colors.grey.shade300,
                                    image: DecorationImage(
                                      image: AssetImage(Assets.images.pngs.other.pngPlaceholder.path),
                                    ),
                                    // borderRadius: BorderRadius.circular(16.0.r),
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Padding(
                                        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                                        child: CustomImageView(
                                          // margin: EdgeInsets.symmetric(horizontal: 50.w, vertical: 50.h),
                                          height: 30.h,
                                          width: 30.w,
                                          imagePath: Assets.images.svg.other.icPlayVideo.path,
                                          // fit: BoxFit.contain,
                                          // radius: BorderRadius.circular(16.0.r),
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                          : GestureDetector(
                              onTap: () {
                                setState(() {
                                  FocusScope.of(context).unfocus();
                                  PersistentNavBarNavigator.pushNewScreen(
                                    context,
                                    screen: GetProfilePostWidget(
                                      initialIndex: index,
                                      profilepost: state.getProfilePost,
                                      userId: userProfileState.userProfile?.data.id,
                                    ),
                                  );
                                });
                              },
                              child: CustomImageView(
                                imagePath: post.files.first,
                                fit: BoxFit.cover,
                              ),
                            ),
                    ),
                  );
                },
              );
  }

  Widget _buildVideoContent(HomeFeedState state, UserProfileState userProfileState) {
    // if (state.getProfilevideo.isEmpty) {
    //   return Column(
    //     children: [
    //       buildSizedBoxH(20.0),
    //       ExceptionWidget(
    //         imagePath: Assets.images.pngs.pngNoPost.path,
    //         title: Lang.of(context).lbl_no_video_found,
    //         subtitle: Lang.of(context).lbl_share_your_first_video,
    //         showButton: false,
    //       ),
    //     ],
    //   );
    // }

    return state.getProfileVideoLoading
        ? buildShimmerGrid()
        : state.getProfilevideo.isEmpty
            ? Column(
                children: [
                  buildSizedBoxH(20.0),
                  ExceptionWidget(
                    imagePath: Assets.images.pngs.pngNoPost.path,
                    title: Lang.of(context).lbl_no_text_post_found,
                    subtitle: Lang.of(context).lbl_share_your_first_text_post,
                    showButton: false,
                  ),
                ],
              )
            : StaggeredGridView.countBuilder(
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.only(top: 10.h, bottom: 55.h, left: 16.0.w, right: 16.0.w),
                crossAxisCount: 2,
                mainAxisSpacing: 8.0,
                crossAxisSpacing: 8.0,
                staggeredTileBuilder: (int index) {
                  return StaggeredTile.count(1, index.isEven ? 1.3 : 1);
                },
                itemCount: state.getProfilevideo.length,
                itemBuilder: (BuildContext context, int index) {
                  final filePath = state.getProfilevideo[index].files.first;
                  return Container(
                    clipBehavior: Clip.antiAlias,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(18.0.r),
                        border: Border.all(color: Theme.of(context).primaryColor.withOpacity(0.1), width: 2)),
                    child: isVideo(filePath) || filePath == ''
                        ? state.getProfilevideo[index].thumbnailFiles.isNotEmpty
                            ? GestureDetector(
                                onTap: () {
                                  try {
                                    List<PostData> convertedPosts = state.getProfilevideo
                                        .map((video) => PostData(
                                              id: video.id,
                                              title: video.title,
                                              description: video.description,
                                              location: video.location,
                                              likes: video.likes,
                                              dislikes: video.dislikes,
                                              commentsCount: video.commentsCount,
                                              createdAt: video.createdAt,
                                              scheduledAt: "",
                                              files: video.files,
                                              thumbnailFiles: video.thumbnailFiles,
                                              latestComment: video.latestComment,
                                              user: User.fromJson(video.user.toJson()),
                                              isLiked: video.isLiked,
                                              isSaved: video.isSaved,
                                              width: video.width,
                                              height: video.height,
                                              isTextPost: false,
                                            ))
                                        .toList();

                                    final selectedPost = state.getProfilevideo[index];
                                    final matchedIndex =
                                        convertedPosts.indexWhere((post) => post.id == selectedPost.id);

                                    PersistentNavBarNavigator.pushNewScreen(
                                      context,
                                      screen: VideoReelPage(
                                        index: matchedIndex >= 0 ? matchedIndex : 0,
                                        reelService: ReelService(),
                                        screen: 'Profile',
                                        userId: int.tryParse(
                                                Prefobj.preferences?.get(Prefkeys.USER_ID)?.toString() ?? '0') ??
                                            0,
                                        postDataList: convertedPosts,
                                      ),
                                      withNavBar: false,
                                    );
                                  } catch (e) {
                                    Logger.lOG(" Error opening reel: $e");
                                  }
                                },
                                // onTap: () {
                                //   FocusScope.of(context).unfocus();
                                //   PersistentNavBarNavigator.pushNewScreen(
                                //     context,
                                //     screen: GetProfileVideoWidget(
                                //       initialIndex: index,
                                //       profilevideo: state.getProfilevideo,
                                //       userId: userProfileState.userProfile?.data.id,
                                //     ),
                                //   );
                                // },
                                child: Stack(
                                  fit: StackFit.expand,
                                  children: [
                                    CustomImageView(
                                      width: double.infinity,
                                      imagePath: state.getProfilevideo[index].thumbnailFiles.first,
                                      fit: BoxFit.cover,
                                      radius: BorderRadius.circular(16.0.r),
                                    ),
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        CustomImageView(
                                          margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                                          imagePath: Assets.images.svg.other.svgPlayIconWhite.path,
                                          color: Colors.white70,
                                          height: 30.h,
                                          width: 30.w,
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              )
                            : Container(
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade300,
                                  image: DecorationImage(
                                    image: AssetImage(Assets.images.pngs.other.pngPlaceholder.path),
                                  ),
                                  borderRadius: BorderRadius.circular(16.0.r),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                                      child: CustomImageView(
                                        height: 30.h,
                                        width: 30.w,
                                        imagePath: Assets.images.svg.other.icPlayVideo.path,
                                        radius: BorderRadius.circular(16.0.r),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                        : GestureDetector(
                            onTap: () {
                              FocusScope.of(context).unfocus();
                              PersistentNavBarNavigator.pushNewScreen(
                                context,
                                screen: GetProfileVideoWidget(
                                  initialIndex: index,
                                  profilevideo: state.getProfilevideo,
                                  userId: userProfileState.userProfile?.data.id,
                                ),
                              );
                            },
                            child: CustomImageView(
                              imagePath: filePath,
                              fit: BoxFit.cover,
                              radius: BorderRadius.circular(16.0.r),
                            ),
                          ),
                  );
                },
              );
  }

  Widget _buildTagPeopleContent(HomeFeedState state) {
    // if (state.tagPostData.isEmpty) {
    //   return Column(
    //     children: [
    //       buildSizedBoxH(20.0),
    //       ExceptionWidget(
    //         imagePath: Assets.images.pngs.pngNoPost.path,
    //         title: Lang.of(context).lbl_no_post_found,
    //         subtitle: Lang.of(context).lbl_no_tagged_post_massage,
    //         showButton: false,
    //       ),
    //     ],
    //   );
    // }

    return state.isloding
        ? buildShimmerGrid()
        : state.tagPostData.isEmpty
            ? Center(
                child: ExceptionWidget(
                  imagePath: Assets.images.pngs.pngNoPost.path,
                  title: Lang.of(context).lbl_no_post_found,
                  subtitle: Lang.of(context).lbl_no_tagged_post_massage,
                  showButton: false,
                ),
              )
            : StaggeredGridView.countBuilder(
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.only(top: 10.h, bottom: 55.h, left: 16.0.w, right: 16.0.w),
                crossAxisCount: 2,
                mainAxisSpacing: 8.0,
                crossAxisSpacing: 8.0,
                staggeredTileBuilder: (int index) {
                  return StaggeredTile.count(1, index.isEven ? 1.3 : 1);
                },
                itemCount: state.tagPostData.length,
                itemBuilder: (context, index) {
                  var post = state.tagPostData[index];
                  var ispost = state.tagPostData[index].files!.isNotEmpty
                      ? post.files?.first
                      : Assets.images.pngs.other.pngPlaceholder.path;
                  return Container(
                    clipBehavior: Clip.antiAlias,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(18.0.r),
                        border: Border.all(color: Theme.of(context).primaryColor.withOpacity(0.1), width: 2)),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8.r),
                      child: InkWell(
                        onTap: () {
                          FocusScope.of(context).unfocus();
                          PersistentNavBarNavigator.pushNewScreen(
                            context,
                            screen: GetProfiletagPostWidget(
                              initialIndex: index,
                              profilepost: state.tagPostData,
                              userId: state.tagPostData[index].user?.userId,
                            ),
                          );
                        },
                        child: isVideo(ispost ?? '')
                            ? Container(
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade300,
                                  image: DecorationImage(
                                    image: AssetImage(Assets.images.pngs.other.pngPlaceholder.path),
                                  ),
                                  borderRadius: BorderRadius.circular(16.0.r),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                                      child: CustomImageView(
                                        height: 30.h,
                                        width: 30.w,
                                        imagePath: Assets.images.svg.other.icPlayVideo.path,
                                        radius: BorderRadius.circular(16.0.r),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : CustomImageView(
                                imagePath: post.files?.first,
                                fit: BoxFit.cover,
                              ),
                      ),
                    ),
                  );
                },
              );
  }

  Widget _buildTextPostContent(HomeFeedState state) {
    if (state.getUserTextPostData.isEmpty) {
      return Column(
        children: [
          buildSizedBoxH(20.0),
          ExceptionWidget(
            imagePath: Assets.images.pngs.pngNoPost.path,
            title: Lang.of(context).lbl_no_post_found,
            subtitle: Lang.of(context).lbl_share_your_first_post,
            showButton: false,
          ),
        ],
      );
    }

    return state.getuserTextPostLoading
        ? Center(
            child: LoadingAnimationWidget(),
          )
        : ListView.builder(
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.only(top: 10.h, bottom: 55.h),
            itemCount: state.getUserTextPostData.length,
            itemBuilder: (context, index) {
              final textPost = state.getUserTextPostData[index];
              return RepaintBoundary(
                child: PostWidget(
                  key: ValueKey('text_post_${textPost.id}'),
                  width: 0,
                  height: 0,
                  userByIDpost: false,
                  userByIDvideo: false,
                  userVideo: false,
                  userpost: true,
                  isPost: true,
                  isTextPost: true,
                  state: state,
                  index: index,
                  userId: textPost.user?.userId ?? 0,
                  latestcomments: textPost.latestComment?.toString() ?? '',
                  postId: textPost.id ?? 0,
                  profileImage: textPost.user?.profileImage ?? '',
                  name: textPost.user?.name ?? '',
                  username: textPost.user?.username ?? '',
                  postMedia: [],
                  thumbnailImage: [],
                  title: textPost.title ?? '',
                  caption:
                      "${textPost.title == "''" || (textPost.title?.isEmpty ?? true) ? '' : textPost.title}${(textPost.description?.isEmpty ?? true) ? '' : textPost.title == "''" || (textPost.title?.isEmpty ?? true) ? textPost.description : "\n${textPost.description}"}",
                  likes: textPost.likes?.toString() ?? '0',
                  comments: textPost.commentsCount?.toString() ?? '0',
                  postTime: textPost.createdAt ?? '',
                  isLiked: textPost.isLiked ?? false,
                  isSaved: textPost.isSaved ?? false,
                  taggedIn: textPost.taggedIn,
                  doubleTap: () {
                    if (textPost.isLiked == false) {
                      context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: textPost.id ?? 0));
                    }
                  },
                  likeonTap: () {
                    context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: textPost.id ?? 0));
                  },
                  commentonTap: () {
                    showModalBottomSheet(
                      context: context,
                      useRootNavigator: true,
                      isScrollControlled: true,
                      builder: (context) => CommentsBottomSheet(postId: textPost.id ?? 0),
                    );
                  },
                  shareonTap: () {},
                  saveonTap: () {},
                ),
              );
            },
          );
  }

  Widget _buildFollowButton() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
      child: SizedBox(
        width: double.infinity,
        child: CustomElevatedButton(
          margin: EdgeInsets.only(bottom: 10.h),
          color: Theme.of(context).primaryColor,
          decoration: const BoxDecoration(
            boxShadow: [
              BoxShadow(
                blurRadius: 8,
                color: Colors.black12,
                offset: Offset(0, 0),
              ),
            ],
          ),
          text: Lang.of(context).lbl_follow,
          buttonTextStyle: Theme.of(context).textTheme.labelLarge?.copyWith(
                fontWeight: FontWeight.w700,
                fontSize: 16.0.sp,
                color: Theme.of(context).customColors.white,
              ),
          height: 40.h,
        ),
      ),
    );
  }

  Widget _buildPrivateAccInfoButton() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 70.h),
      child: Column(
        children: [
          CustomImageView(
            height: 55.h,
            imagePath: Assets.images.pngs.other.pngPrivateAccount.path,
          ),
          buildSizedBoxH(8.h),
          Text(
            Lang.of(context).lbl_this_account_is_private,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontSize: 14.sp, fontWeight: FontWeight.bold),
          ),
          buildSizedBoxH(16.h),
          Text(
            Lang.of(context).lbl_follow_this_account_to_see_their_photos_and_videos,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontSize: 12.sp, fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Widget _buildhighlightStory({required UserProfileState state}) {
    return state.ishilightloading
        ? buildShimmerHorizontalList()
        : Align(
            alignment: Alignment.centerLeft,
            child: SizedBox(
              height: 139.h,
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    GestureDetector(
                      onTap: () {
                        _buildAddStoryAlert(context);
                      },
                      child: _buildStoryItem(
                        'Highlight',
                        Assets.images.pngs.other.pngPlaceholder.path,
                        isSelect: true,
                      ),
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: List.generate(
                        state.highlightStoryData.length,
                        (index) {
                          var highlightData = state.highlightStoryData[index];
                          return GestureDetector(
                            onTap: () {
                              PersistentNavBarNavigator.pushNewScreen(
                                context,
                                screen: StoryScreen(
                                  users: state.highlightStoryData,
                                  index: index,
                                  isHilight: true,
                                ),
                                withNavBar: false,
                                customPageRoute: PageRouteBuilder(
                                  opaque: false,
                                  barrierColor: Colors.transparent,
                                  transitionDuration: Duration(milliseconds: 250),
                                  reverseTransitionDuration: Duration(milliseconds: 250),
                                  pageBuilder: (context, animation, secondaryAnimation) {
                                    return StoryScreen(
                                      users: state.highlightStoryData,
                                      index: index,
                                      isHilight: true,
                                    );
                                  },
                                  transitionsBuilder: (context, animation, secondaryAnimation, child) {
                                    final curvedAnimation = CurvedAnimation(
                                      parent: animation,
                                      curve: Curves.easeInOut,
                                    );

                                    return SlideTransition(
                                      position: Tween<Offset>(
                                        begin: Offset(0, 1),
                                        end: Offset.zero,
                                      ).animate(curvedAnimation),
                                      child: SlideTransition(
                                        position: Tween<Offset>(
                                          begin: Offset.zero,
                                          end: Offset(0, 1),
                                        ).animate(CurvedAnimation(
                                          parent: secondaryAnimation,
                                          curve: Curves.easeInOut,
                                        )),
                                        child: child,
                                      ),
                                    );
                                  },
                                ),
                              );
                            },
                            child: _buildStoryItem(
                              highlightData.stories![0].title.toString(),
                              highlightData.stories![0].storyfile!.isEmpty ||
                                      highlightData.stories![0].storyfile == null
                                  ? Assets.images.pngs.other.pngLogo.path
                                  : highlightData.stories![0].storyfile.toString(),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
  }

  Widget _buildStoryItem(String title, String imageUrl, {bool isSelect = false, String? url, Widget? child}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Container(
            height: 120.h,
            width: 85.w,
            margin: EdgeInsets.symmetric(horizontal: 4.w),
            decoration: BoxDecoration(
              color: Colors.black12,
              borderRadius: BorderRadius.circular(14.r),
              border: Border.all(color: isSelect ? Colors.transparent : Theme.of(context).primaryColor, width: 1.w),
              image: DecorationImage(image: NetworkImage(imageUrl.toString()), fit: BoxFit.fill),
            ),
            child: isSelect ? _buildhilight(title: title, imageUrl: imageUrl) : SizedBox.shrink()
            // Column(
            //   crossAxisAlignment: CrossAxisAlignment.start,
            //   mainAxisAlignment: isSelect ? MainAxisAlignment.start : MainAxisAlignment.start,
            //   mainAxisSize: MainAxisSize.min,
            //   children: [
            //     isSelect ? _buildhilight(title: title, imageUrl: imageUrl) : _buildAddHighlite(title: title, imageUrl: imageUrl),
            //   ],
            // ),
            ),
        _buildAddHighlite(title: title, imageUrl: imageUrl, isselect: isSelect)
      ],
    );
  }

  Widget _buildAddHighlite({String? imageUrl, String? title, bool? isselect}) {
    return Padding(
      padding: EdgeInsets.only(left: 0.0.w, top: 0.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 82.w,
            child: Text(
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              title ?? "",
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontSize: 12.sp,
                  color: isselect == true ? Colors.transparent : Theme.of(context).primaryColor,
                  fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildhilight({
    String? imageUrl,
    String? title,
  }) {
    return Padding(
      padding: EdgeInsets.only(top: 30.h),
      child: Column(
        children: [
          Stack(
            alignment: Alignment.bottomCenter,
            clipBehavior: Clip.none,
            children: [
              Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Theme.of(context).textTheme.titleSmall!.color!,
                      width: 1.w,
                    ),
                  ),
                  child: Align(
                    alignment: Alignment.topCenter,
                    child: ClipRRect(
                      clipBehavior: Clip.antiAlias,
                      child: ValueListenableBuilder<String>(
                        valueListenable: profileImageNotifier,
                        builder: (context, imagePath, child) {
                          return Container(
                            height: 40.h,
                            width: 40.w,
                            padding: imagePath == AssetConstants.pngUser
                                ? EdgeInsets.symmetric(horizontal: 5, vertical: 8)
                                : EdgeInsets.zero,
                            child: CustomImageView(
                              // alignment: Alignment.center,
                              // fit: BoxFit.contain,
                              fit: imagePath == AssetConstants.pngUser ? BoxFit.contain : BoxFit.cover,
                              radius: imagePath == AssetConstants.pngUser ? null : BorderRadius.circular(50.r),
                              imagePath: imagePath,
                              fallbackImage: AssetConstants.pngUserReomve,
                            ),
                          );
                        },
                      ),
                    ),
                  )),
              Positioned(
                  bottom: -10,
                  child: CustomImageView(height: 20.0.h, imagePath: Assets.images.svg.homeFeed.svgAddStory.path)),
            ],
          ),
          buildSizedBoxH(18.0),
          Text(
            title ?? '',
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: 12.sp),
          )
        ],
      ),
    );
  }

  _buildAddStoryAlert(BuildContext ctx) {
    return showDialog(
      context: ctx,
      builder: (context) {
        bool isLoading = false;

        return StatefulBuilder(
          builder: (context, setState) {
            return CustomAlertDialog(
              imagePath: Assets.images.pngs.exception.pngNoResultFound.path,
              title: 'Are you sure you want to add a new story or highlight?',
              subtitle: '',
              onConfirmButtonPressed: () async {
                bool isFacebook = await Prefobj.preferences?.get(Prefkeys.FACEBOOK);
                bool isInstagram = await Prefobj.preferences?.get(Prefkeys.INSTAGRAM);
                setState(() {
                  isLoading = true;
                });
                Navigator.pop(context);
                Navigator.push(
                  context,
                  PageRouteBuilder(
                    pageBuilder: (context, animation, secondaryAnimation) => flowkarStoryEditor(
                      centerText: "Start Your Design",
                      isfacebook: isFacebook,
                      isInstagram: isInstagram,
                      onDone: (media, instagram, facebook) {
                        ctx.read<HomeFeedBloc>().add(
                              UploadStoryApiEvent(
                                uploadFiles: File(media),
                                music: '',
                                title: '',
                                isFacebook: facebook,
                                isInstagram: instagram,
                              ),
                            );
                        Navigator.pop(context);
                      },
                      onDoneHighlight: (media, title) {
                        Logger.lOG(title);
                        ctx.read<HomeFeedBloc>().add(
                              UploadStoryApiEvent(
                                uploadFiles: File(media),
                                music: '',
                                title: title,
                                isFacebook: false,
                                isInstagram: false,
                              ),
                            );
                        Navigator.pop(context);
                      },
                    ),
                  ),
                );
              },
              confirmButtonText: "Add Highlight",
              isLoading: isLoading,
            );
          },
        );
      },
    );
  }

  Widget _buildOfflineMessage(bool showOfflineMessage) {
    return Positioned(
      bottom: 20.h,
      left: 0,
      right: 0,
      child: AnimatedSlide(
        offset: _showOfflineMessage ? Offset.zero : const Offset(0, 1),
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
        child: AnimatedOpacity(
          opacity: _showOfflineMessage ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 300),
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 10.w),
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.95),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.wifi_off,
                  color: Theme.of(context).customColors.white,
                  size: 18.sp,
                ),
                buildSizedBoxW(8),
                Text(
                  "Couldn't refresh feed",
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context).customColors.white,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class BioTextWidget extends StatefulWidget {
  final String text;

  const BioTextWidget({super.key, required this.text});

  @override
  State<BioTextWidget> createState() => _BioTextWidgetState();
}

class _BioTextWidgetState extends State<BioTextWidget> {
  bool _expanded = false;
  bool _hasOverflow = false;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      final textSpan = TextSpan(
        text: widget.text,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(fontSize: 16.sp),
      );

      final textPainter = TextPainter(
        text: textSpan,
        maxLines: 5,
        textDirection: TextDirection.ltr,
      )..layout(maxWidth: constraints.maxWidth);

      _hasOverflow = textPainter.didExceedMaxLines;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Linkify(
            text: widget.text,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontSize: 16.sp),
            maxLines: _expanded ? null : 5,
            overflow: _expanded ? TextOverflow.visible : TextOverflow.ellipsis,
            linkStyle: Theme.of(context).textTheme.titleLarge?.copyWith(fontSize: 16.sp, color: Colors.blue),
            onOpen: (link) {
              launchUrl(Uri.parse(link.url));
            },
          ),
          // Text(
          //   widget.text,
          //   style: Theme.of(context).textTheme.titleLarge?.copyWith(fontSize: 16.sp),
          //   maxLines: _expanded ? null : 5,
          //   overflow: _expanded ? TextOverflow.visible : TextOverflow.ellipsis,
          // ),
          if (_hasOverflow)
            GestureDetector(
              onTap: () {
                setState(() {
                  _expanded = !_expanded;
                });
              },
              child: Padding(
                padding: const EdgeInsets.only(top: 4.0),
                child: Text(
                  _expanded ? 'See less' : 'See more',
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).primaryColor,
                      ),
                ),
              ),
            ),
        ],
      );
    });
  }
}

buildMoreOptionBottomSheet(BuildContext context) {
  return showModalBottomSheet(
    useRootNavigator: true,
    context: context,
    backgroundColor: Colors.transparent,
    builder: (_) {
      return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(40.0.r)),
        ),
        padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 12.w),
        child: StatefulBuilder(
          builder: (context, setState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 3.h,
                width: 40.w,
                decoration: BoxDecoration(
                  color: Color(0xffE9EBEA),
                  borderRadius: BorderRadius.circular(100.r),
                ),
              ),
              buildSizedBoxH(8.h),
              buildMoreBottomsheetItem(
                context,
                imagePath: Assets.images.svg.profile.svgAddStory.path,
                label: 'Add Story',
                onTap: () async {
                  final status = await Permission.storage.request();
                  final camera = await Permission.camera.request();
                  final microphone = await Permission.microphone.request();
                  final manageExternalStorage = await Permission.manageExternalStorage.request();
                  bool isFacebook = await Prefobj.preferences?.get(Prefkeys.FACEBOOK);
                  bool isInstagram = await Prefobj.preferences?.get(Prefkeys.INSTAGRAM);
                  if (status.isGranted && camera.isGranted && microphone.isGranted && manageExternalStorage.isGranted) {
                    Logger.lOG('Storage permission granted');
                  } else {
                    Logger.lOG('Storage permission denied');
                  }
                  Navigator.pop(context);
                  Navigator.push(
                    //  use_build_context_synchronously
                    context,
                    PageRouteBuilder(
                      pageBuilder: (context, animation, secondaryAnimation) => flowkarStoryEditor(
                        centerText: "Start Your Design",
                        isfacebook: isFacebook,
                        isInstagram: isInstagram,
                        onDone: (media, instagram, facebook) {
                          context.read<HomeFeedBloc>().add(
                                UploadStoryApiEvent(
                                  uploadFiles: File(media),
                                  music: '',
                                  title: '',
                                  isFacebook: facebook,
                                  isInstagram: instagram,
                                ),
                              );
                          // Navigator.pop(context);
                        },
                        onDoneHighlight: (media, title) {
                          Logger.lOG(title);
                          context.read<HomeFeedBloc>().add(
                                UploadStoryApiEvent(
                                  uploadFiles: File(media),
                                  music: '',
                                  title: title,
                                  isFacebook: false,
                                  isInstagram: false,
                                ),
                              );
                          // Navigator.pop(context);
                        },
                      ),
                    ),
                  );
                  Logger.lOG('Add Story tapped');
                },
              ),
              buildMoreBottomsheetItem(
                context,
                imagePath: Assets.images.svg.profile.svgAddPost.path,
                label: 'Create Post',
                onTap: () async {
                  // NavigatorService.goBack();
                  // Navigator.pop(context);
                  context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
                  final status = await Permission.storage.request();
                  final camera = await Permission.camera.request();
                  final microphone = await Permission.microphone.request();
                  final manageExternalStorage = await Permission.manageExternalStorage.request();
                  if (status.isGranted && camera.isGranted && microphone.isGranted && manageExternalStorage.isGranted) {
                    Logger.lOG('Storage permission granted');
                  } else {
                    // openAppSettings();
                    Logger.lOG('Storage permission denied');
                  }
                  Navigator.pop(context);
                  PickMediaWidget().pickImages(context, 1, 'New Post');
                  Logger.lOG('Add Post tapped');
                },
              ),
              buildMoreBottomsheetItem(
                context,
                imagePath: Assets.images.svg.homeFeed.svgTextPost.path,
                iconSize: 23.h,
                label: 'Create Text Post',
                onTap: () async {
                  Navigator.pop(context);
                  context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
                  NavigatorService.pushNamed(AppRoutes.uploadPostScrteen, arguments: [[], true, false, ""]);
                  Logger.lOG('Add Text Post tapped');
                },
              ),
            ],
          ),
        ),
      );
    },
    isScrollControlled: true,
  );
}

Widget buildMoreBottomsheetItem(BuildContext context,
    {required String imagePath, required String label, required VoidCallback onTap, double? iconSize}) {
  return Container(
    margin: EdgeInsets.symmetric(vertical: 6.h),
    decoration: BoxDecoration(
      color: Theme.of(context).primaryColor.withOpacity(0.2),
      borderRadius: BorderRadius.circular(30.r),
    ),
    child: ListTile(
      minTileHeight: 60.h,
      leading: CustomImageView(
        height: iconSize ?? 20.h,
        width: iconSize ?? 20.w,
        imagePath: imagePath,
        fit: BoxFit.contain,
      ),
      title: Text(
        label,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 16.sp),
      ),
      onTap: onTap,
    ),
  );
}
