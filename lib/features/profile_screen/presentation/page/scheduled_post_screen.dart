// ignore_for_file: deprecated_member_use, non_constant_identifier_names

import 'package:flowkar/core/utils/date_time_utils.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/profile_screen/presentation/widget/scheduled_post_screen_shimmer.dart';
import 'package:flowkar/features/upload_post/bloc/post_bloc.dart';
import 'package:flowkar/features/widgets/custom/custom_conditional_scroll_physics.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';

class ScheduledPostScreen extends StatefulWidget {
  const ScheduledPostScreen({super.key});
  static Widget builder(BuildContext context) {
    return ScheduledPostScreen();
  }

  @override
  State<ScheduledPostScreen> createState() => _ScheduledPostScreenState();
}

class _ScheduledPostScreenState extends State<ScheduledPostScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    context.read<PostBloc>().add(SchedulePostApiEvent());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildScheduledPostAppBar(),
      body: LiquidPullToRefresh(
        color: Theme.of(context).primaryColor.withOpacity(0.5),
        showChildOpacityTransition: false,
        backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
        onRefresh: () {
          return Future.delayed(const Duration(milliseconds: 0), () {
            // ignore: use_build_context_synchronously
            context.read<PostBloc>().add(SchedulePostApiEvent());
          });
        },
        child: BlocListener<ConnectivityBloc, ConnectivityState>(
          listener: (context, connectivityState) {
            if (connectivityState.isReconnected) {
              context.read<PostBloc>().add(SchedulePostApiEvent());
            }
          },
          child: BlocBuilder<PostBloc, PostState>(
            builder: (context, state) {
              return BlocBuilder<ConnectivityBloc, ConnectivityState>(
                builder: (context, connectivityState) {
                  if (!connectivityState.isConnected && state.scheduleData.isEmpty) {
                    return ScheduledPostShimmer();
                  } else if (state.isScheduleloding) {
                    return ScheduledPostShimmer();
                  } else if (state.scheduleData.isEmpty) {
                    return ListView(
                      physics: AlwaysScrollableScrollPhysics(),
                      children: [
                        buildSizedBoxH(MediaQuery.of(context).size.height / 6),
                        ExceptionWidget(
                          showButton: false,
                          imagePath: Assets.images.pngs.pngNoPost.path,
                          title: "No Scheduled Post found",
                          subtitle: "",
                        ),
                      ],
                    );
                  } else {
                    return ListView.builder(
                      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                      controller: _scrollController,
                      physics: ConditionalAlwaysScrollPhysics(controller: _scrollController),
                      shrinkWrap: true,
                      itemCount: state.scheduleData.length,
                      itemBuilder: (context, index) {
                        final scPost = state.scheduleData[index];
                        final DateTime scheduledDateTime = DateTime.parse(scPost.scheduledAt);

                        return PostCard(
                          state: state,
                          onPressed: () {
                            _buildDeletePostAlert(context, index, scPost.id, state.isDeleteScheduledPostLoading);
                          },
                          username: scPost.user.name,
                          handle: '@${scPost.user.username}',
                          date: scheduledDateTime.formatWithDayMonth(),
                          time: scheduledDateTime.formatWithTimeOnly(),
                          imagePath: isVideo(scPost.files.first)
                              ? scPost.thumbnailFiles.isNotEmpty
                                  ? scPost.thumbnailFiles.first
                                  : Assets.images.pngs.other.pngPlaceholder.path
                              : scPost.files.first,
                          postDescription:
                              "${scPost.title == "''" || scPost.title.isEmpty ? '' : scPost.title}${scPost.description.isEmpty ? '' : scPost.title == "''" || scPost.title.isEmpty ? scPost.description : "\n${scPost.description}"}",
                        );
                      },
                    );
                  }
                },
              );
            },
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildScheduledPostAppBar() {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          Lang.of(context).lbl_scheduled_post,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  _buildDeletePostAlert(BuildContext ctx, int Index, int postId, bool isDeleteScheduledPostLoading) {
    return showDialog(
      context: ctx,
      builder: (ctx) {
        bool isLoading = false;

        return StatefulBuilder(
          builder: (ctx, setState) {
            return CustomAlertDialog(
              imageheight: 70.h,
              imagewidth: 70.w,
              imagePath: Assets.images.pngs.exception.pngDeletePost.path,
              title: 'Are you sure you want to delete your Scheduled Post ?',
              subtitle: '',
              onConfirmButtonPressed: () async {
                setState(() {
                  isLoading = true;
                });
                // if (widget.screenType == Lang.of(context).lbl_user_profile) {
                //   context.read<UserProfileBloc>().add(ProfileDeletePostApiEvent(
                //         postId: widget.postId,
                //         index: widget.index,
                //       ));
                // } else if (widget.screenType ==
                //     Lang.of(context).lbl_user_profile_id) {
                //   context
                //       .read<UserProfileIdBloc>()
                //       .add(ProfileDeletePostIdApiEvent(
                //         postId: widget.postId,
                //         index: widget.index,
                //       ));
                // } else {
                context.read<PostBloc>().add(DeleteSchedulePostApiEvent(index: Index, postId: postId));
                //}
              },
              confirmButtonText: Lang.of(ctx).lbl_delete,
              isLoading: isLoading,
            );
          },
        );
      },
    );
  }
}

class PostCard extends StatelessWidget {
  final String username;
  final String handle;
  final String date;
  final String time;
  final String postDescription;
  final String imagePath;
  final void Function()? onPressed;
  final PostState state;

  const PostCard(
      {super.key,
      required this.username,
      required this.handle,
      required this.date,
      required this.time,
      required this.postDescription,
      required this.imagePath,
      required this.onPressed,
      required this.state});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ValueListenableBuilder<String>(
                      valueListenable: profileImageNotifier,
                      builder: (context, imagePath, child) {
                        return Container(
                          height: 36.0.h,
                          width: 36.0.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(100.0.r),
                            border: Border.all(
                              width: 1,
                              color: imagePath == AssetConstants.pngUser
                                  ? Theme.of(context).primaryColor
                                  : Colors.transparent,
                            ),
                          ),
                          clipBehavior: Clip.antiAlias,
                          child: CustomImageView(
                            radius: BorderRadius.circular(imagePath == AssetConstants.pngUser ? 0 : 100.0.r),
                            fit: imagePath == AssetConstants.pngUser ? BoxFit.contain : BoxFit.cover,
                            imagePath: imagePath,
                            alignment: Alignment.center,
                          ),
                        );
                      },
                    ),
                    buildSizedBoxW(8),
                    InkWell(
                      onTap: () {
                        // PersistentNavBarNavigator.pushNewScreen(context,
                        //     screen: UserProfileIdScreen(
                        //       userId: widget.userId,
                        //       stackonScreen: true,
                        //     ));
                      },
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            username,
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge!
                                .copyWith(fontSize: 14.sp, fontWeight: FontWeight.w700, color: Color(0xff292D32)),
                          ),
                          buildSizedBoxH(1),
                          Text(
                            handle,
                            style: Theme.of(context)
                                .textTheme
                                .headlineSmall!
                                .copyWith(fontSize: 9.sp, fontWeight: FontWeight.w700, color: Color(0xff575353)),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 13.w,
                    vertical: 6.h,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(2.r),
                  ),
                  child: Text(
                    Lang.of(context).lbl_scheduled,
                    style:
                        Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 10.sp, fontWeight: FontWeight.w700),
                  ),
                ),
              ],
            ),
            Divider(
              thickness: 0.5,
              color: Theme.of(context).primaryColor.withOpacity(0.2),
            ),
            buildSizedBoxH(16),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 50.h,
                  width: 50.w,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(5.r),
                  ),
                  child: CustomImageView(
                    height: 50.h,
                    width: 50.w,
                    radius: BorderRadius.circular(5.r),
                    imagePath: imagePath,
                  ),
                ),
                buildSizedBoxW(8),
                Flexible(
                  child: Text(postDescription,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 10.sp, height: 1.5)),
                ),
              ],
            ),
            buildSizedBoxH(16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    CustomImageView(
                      imagePath: Assets.images.svg.uploadPost.svgScheduledCalender.path,
                      height: 15.h,
                      width: 15.w,
                    ),
                    buildSizedBoxW(8),
                    Text(date,
                        // "Aug 9, 2024 2:32 pm",
                        style: Theme.of(context)
                            .textTheme
                            .bodySmall
                            ?.copyWith(fontSize: 14.sp, color: Theme.of(context).primaryColor.withOpacity(0.8))),
                  ],
                ),
                buildSizedBoxW(16),
                Padding(
                  padding: EdgeInsets.only(right: 30.w),
                  child: Row(
                    children: [
                      Icon(
                        Icons.access_time_filled,
                        size: 20.sp,
                        color: Theme.of(context).primaryColor,
                      ),
                      buildSizedBoxW(8),
                      Text(
                        time,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.brown[700],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            buildSizedBoxH(16),
            Center(
              child: CustomElevatedButton(
                width: double.infinity,
                onPressed: onPressed,
                color: Theme.of(context).primaryColor,
                buttonStyle: ButtonStyle(
                  shape: WidgetStateProperty.all(RoundedRectangleBorder(borderRadius: BorderRadius.circular(50.r))),
                  backgroundColor: WidgetStatePropertyAll(Theme.of(context).primaryColor),
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.r),
                  boxShadow: [BoxShadow(blurRadius: 8, color: Colors.black12, offset: Offset(0, 0))],
                ),
                text: Lang.of(context).lbl_delete,
                buttonTextStyle: Theme.of(context).textTheme.labelLarge?.copyWith(
                      fontWeight: FontWeight.w500,
                      fontSize: 16.0.sp,
                      color: Theme.of(context).customColors.white,
                    ),
                height: 50.h,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
