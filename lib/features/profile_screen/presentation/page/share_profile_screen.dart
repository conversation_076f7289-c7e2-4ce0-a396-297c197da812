import 'package:ai_barcode_scanner/ai_barcode_scanner.dart';
import 'package:flowkar/core/helpers/vibration_helper.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/profile_screen/bloc/profile_bloc.dart';
import 'package:flowkar/features/profile_screen/presentation/widget/share_profile_screen_shimmer.dart';
import 'package:flowkar/features/user_management/bloc/user_management_bloc.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class ShareProfileScreen extends StatefulWidget {
  const ShareProfileScreen({
    super.key,
  });

  static Widget builder(BuildContext context) {
    return ShareProfileScreen();
  }

  @override
  State<ShareProfileScreen> createState() => _ShareProfileScreenState();
}

class _ShareProfileScreenState extends State<ShareProfileScreen> with TickerProviderStateMixin {
  late int brandId;
  @override
  void initState() {
    context.read<UserProfileBloc>().add(FetchUserProfileEvent(onEdit: false));
    // context.read<UserManagementBloc>().add(GetBrandsAPI());
    super.initState();
    context.read<UserProfileBloc>().add(FetchShareProfileQrEvent());
  }

  @override
  Widget build(BuildContext context) {
    brandId = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;
    return Scaffold(
      appBar: _buildAppBar(context),
      body: BlocListener<ConnectivityBloc, ConnectivityState>(
        listener: (context, connectivityState) {
          if (connectivityState.isReconnected) {
            context.read<UserProfileBloc>().add(FetchUserProfileEvent(onEdit: false));
            // context.read<UserManagementBloc>().add(GetBrandsAPI());
            context.read<UserProfileBloc>().add(FetchShareProfileQrEvent());
          }
        },
        child: BlocBuilder<UserProfileBloc, UserProfileState>(
          builder: (context, state) {
            return BlocBuilder<ConnectivityBloc, ConnectivityState>(
              builder: (context, connectivityState) {
                if (!connectivityState.isConnected) {
                  return ProfileShareScreenShimmer();
                } else if (state.loading) {
                  return ProfileShareScreenShimmer();
                } else {
                  return ListView(
                    children: [
                      buildSizedBoxH(8.h),
                      _buildProfileInfo(context, state),
                      buildSizedBoxH(20.h),
                      _buildQrCode(state),
                      buildSizedBoxH(60.h),
                      _buildShareLinkBUttons(context, state),
                      buildSizedBoxH(60.h),
                      _buildBUttons(context, state),
                    ],
                  );
                }
              },
            );
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          Lang.of(context).lbl_share_profile,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
      actions: [
        CustomImageView(
            imagePath: Assets.images.icons.other.icScanBarcode.path,
            onTap: () async {
              final scannedValue = await Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => AiBarcodeScanner(
                    hideSheetDragHandler: true,
                    hideSheetTitle: true,
                    onDispose: () {
                      debugPrint("Barcode scanner disposed!");
                    },
                    hideGalleryButton: true,
                    controller: MobileScannerController(
                      detectionSpeed: DetectionSpeed.noDuplicates,
                    ),
                    onDetect: (BarcodeCapture capture) {
                      final String? scannedValue = capture.barcodes.first.rawValue;
                      Logger.lOG("Barcode scanned: $scannedValue");
                      Navigator.pop(context, scannedValue);
                    },
                    validator: (value) {
                      if (value.barcodes.isEmpty) {
                        return false;
                      }
                      if (!(value.barcodes.first.rawValue?.contains('flutter.dev') ?? false)) {
                        return false;
                      }
                      return true;
                    },
                  ),
                ),
              );

              if (scannedValue != null) {
                Logger.lOG("Scanned Value: $scannedValue");
                // if (scannedValue.toString().contains(APIConfig.mainbaseURL)) {
                setState(() {
                  final Uri url = Uri.parse(scannedValue);
                  launchUrl(url, mode: LaunchMode.platformDefault);
                });
              } else {}
            }
            // },
            )
      ],
    );
  }

  Widget _buildProfileInfo(BuildContext context, UserProfileState state) {
    return BlocBuilder<UserManagementBloc, UserManagementState>(
      builder: (context, userState) {
        // final brands = userState.getBrandsModel?.data;

        // if (state.userProfile?.data.profileImage == null || state.userProfile?.data.profileImage.isEmpty == true) {
        //   return Shimmer.fromColors(
        //     baseColor: Colors.grey[300]!,
        //     highlightColor: Colors.grey[100]!,
        //     child: Column(
        //       children: [
        //         Container(
        //           height: 85.h,
        //           width: 85.w,
        //           decoration: BoxDecoration(
        //             shape: BoxShape.circle,
        //             color: Colors.grey.shade300,
        //           ),
        //         ),
        //         buildSizedBoxH(16),
        //         Container(
        //           height: 18.h,
        //           width: 120.w,
        //           decoration: BoxDecoration(
        //             color: Colors.grey.shade300,
        //             borderRadius: BorderRadius.circular(8.r),
        //           ),
        //         ),
        //         buildSizedBoxH(8),
        //         Container(
        //           height: 14.h,
        //           width: 180.w,
        //           decoration: BoxDecoration(
        //             color: Colors.grey.shade300,
        //             borderRadius: BorderRadius.circular(8.r),
        //           ),
        //         ),
        //       ],
        //     ),
        //   );
        // }

        // final brand = brands.firstWhere(
        //   (b) => b.id == brandId,
        //   orElse: () => BrandData(id: 0, name: '', email: '', logo: '', isInvited: false, domain: ''),
        // );

        // // ignore: unnecessary_null_comparison
        // if (brand == null) {
        //   return SizedBox.shrink();
        // }

        return Column(
          children: [
            ValueListenableBuilder<String>(
                valueListenable: profileImageNotifier,
                builder: (context, imagePath, child) {
                  // userType = Prefobj.preferences?.get(Prefkeys.USERTYPE);
                  return Container(
                    height: 85.0.h,
                    width: 85.0.w,
                    padding: (imagePath == AssetConstants.pngUser) ? EdgeInsets.all(16) : EdgeInsets.zero,
                    clipBehavior: Clip.antiAlias,
                    decoration: BoxDecoration(
                        color: Theme.of(context).customColors.white,
                        shape: BoxShape.circle,
                        boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 5)]),
                    child: CustomImageView(
                      fit: (imagePath == AssetConstants.pngUser) ? BoxFit.contain : BoxFit.cover,
                      imagePath: imagePath,
                      alignment: ((imagePath == AssetConstants.pngUser) && imagePath.isEmpty) ? Alignment.center : null,
                      fallbackImage: AssetConstants.pngUserReomve,
                    ),
                  );
                }),
            // Container(
            //   height: 85.h,
            //   width: 85.w,
            //   decoration: BoxDecoration(
            //     shape: BoxShape.circle,
            //     color: Theme.of(context).primaryColor.withOpacity(0.5),
            //   ),
            //   child: CustomImageView(
            //     imagePath: state.logo.isEmpty
            //         ? AssetConstants.pngUserReomve
            //         : brand.logo.isEmpty
            //             ? brand.logo
            //             : "${APIConfig.mainbaseURL}${brand.logo}",
            //     radius: BorderRadius.circular(100.r),
            //     fit: BoxFit.cover,
            //   ),
            // ),
            buildSizedBoxH(16.h),
            Text(
              "${state.userProfile?.data.name}",
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontSize: 19.sp, fontWeight: FontWeight.w700, color: Theme.of(context).customColors.black),
            ),
            buildSizedBoxH(4.h),
            Text(
              "${state.userProfile?.data.email} ",
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                  ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildQrCode(UserProfileState state) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(12.r),
          child: Container(
            width: 160.w,
            height: 160.h,
            decoration: BoxDecoration(
              border: Border.all(
                width: 5.w,
                color: Theme.of(context).customColors.black,
              ),
              color: Theme.of(context).primaryColor.withOpacity(0.5),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.r),
              child: CustomImageView(
                height: 140.h,
                imagePath: state.qrResponseModel?.qrCode == ''
                    ? Assets.images.svg.other.icPlayVideo.path
                    : state.qrResponseModel?.qrCode,
              ),
            ),
          ),
        )
      ],
    );
  }

  Widget _buildShareLinkBUttons(BuildContext context, UserProfileState state) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: InkResponse(
        onTap: () {
          Clipboard.setData(ClipboardData(text: '${state.qrResponseModel?.url}'));
          Logger.lOG("Copy Clip: ${state.qrResponseModel?.url}");
          toastification.show(
            type: ToastificationType.success,
            showProgressBar: false,
            title: Text(
              Lang.current.msg_link_copied_successfully,
              style: GoogleFonts.montserrat(
                fontSize: 12.0.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            autoCloseDuration: const Duration(seconds: 3),
          );
        },
        child: Container(
          height: 55.h,
          decoration: BoxDecoration(
            color: Theme.of(context).customColors.white,
            borderRadius: BorderRadius.circular(20.r),
            boxShadow: [
              BoxShadow(
                blurRadius: 8,
                color: Colors.black12,
                offset: Offset(0, 0),
              ),
            ],
          ),
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 18.r,
                    backgroundColor: Theme.of(context).primaryColor.withOpacity(0.14),
                    child: InkResponse(
                      child: CustomImageView(
                        imagePath: Assets.images.icons.other.icCopy.path,
                      ),
                    ),
                  ),
                  buildSizedBoxW(12.w),
                  Expanded(
                    child: Text(
                      state.qrResponseModel?.url ?? '',
                      overflow: TextOverflow.ellipsis,
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(fontWeight: FontWeight.w500, fontSize: 14.sp, color: Color(0xff292D32)),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBUttons(BuildContext context, UserProfileState state) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          height: 60.h,
          child: CustomElevatedButton(
            margin: EdgeInsets.only(bottom: 10.h),
            color: Theme.of(context).primaryColor,
            decoration: const BoxDecoration(
              boxShadow: [
                BoxShadow(
                  blurRadius: 8,
                  color: Colors.black12,
                  offset: Offset(0, 0),
                ),
              ],
            ),
            text: Lang.of(context).lbl_share_profile,
            buttonTextStyle: Theme.of(context).textTheme.labelLarge?.copyWith(
                  fontWeight: FontWeight.w700,
                  fontSize: 16.sp,
                  color: Theme.of(context).customColors.white,
                ),
            height: 40.h,
            onPressed: () {
              Share.share('${state.qrResponseModel?.url}');
              VibrationHelper.singleShortBuzz();
            },
          ),
        ),
      ],
    );
  }
}
