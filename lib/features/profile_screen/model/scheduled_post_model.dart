import 'package:json_annotation/json_annotation.dart';

part 'scheduled_post_model.g.dart';

ScheduledPostModel deserializeScheduledPostModel(Map<String, dynamic> json) => ScheduledPostModel.fromJson(json);

@JsonSerializable()
class ScheduledPostModel {
  final bool status;
  final String message;
  final int today;
  final int tomorrow;
  @Json<PERSON>ey(name: 'this_month')
  final int thisMonth;
  final List<ScheduledPostData> data;

  ScheduledPostModel({
    required this.status,
    required this.message,
    required this.today,
    required this.tomorrow,
    required this.thisMonth,
    required this.data,
  });

  factory ScheduledPostModel.fromJson(Map<String, dynamic> json) => _$ScheduledPostModelFromJson(json);

  Map<String, dynamic> toJson() => _$ScheduledPostModelToJson(this);
}

@JsonSerializable()
class ScheduledPostData {
  final int id;
  final String title;
  final String description;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_scheduled')
  final bool isScheduled;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'scheduled_at')
  final String scheduledAt;
  @J<PERSON><PERSON><PERSON>(name: 'created_at')
  final String createdAt;
  final List<String> files;
  @Json<PERSON>ey(name: 'thumbnail_files')
  final List<String> thumbnailFiles;
  final ScheduledPostUser user;
  @JsonKey(name: 'is_liked')
  final bool isLiked;

  ScheduledPostData({
    required this.id,
    required this.title,
    required this.description,
    required this.isScheduled,
    required this.scheduledAt,
    required this.createdAt,
    required this.files,
    required this.thumbnailFiles,
    required this.user,
    required this.isLiked,
  });

  factory ScheduledPostData.fromJson(Map<String, dynamic> json) => _$ScheduledPostDataFromJson(json);

  Map<String, dynamic> toJson() => _$ScheduledPostDataToJson(this);
}

@JsonSerializable()
class ScheduledPostUser {
  @JsonKey(name: 'user_id')
  final int userId;
  final String username;
  final String name;
  @JsonKey(name: 'profile_image')
  final String profileImage;

  ScheduledPostUser({
    required this.userId,
    required this.username,
    required this.name,
    required this.profileImage,
  });

  factory ScheduledPostUser.fromJson(Map<String, dynamic> json) => _$ScheduledPostUserFromJson(json);

  Map<String, dynamic> toJson() => _$ScheduledPostUserToJson(this);
}
