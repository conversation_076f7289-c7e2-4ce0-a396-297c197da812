import 'package:json_annotation/json_annotation.dart';

part 'edit_profile_response.g.dart';

EditProfileResponse deserializeEditProfileResponse(Map<String, dynamic> json) {
  return EditProfileResponse.fromJson(json);
}

@JsonSerializable()
class EditProfileResponse {
  final bool status;
  final String message;
  
  @JsonKey(name: 'image')
  final String? profileImage;

  EditProfileResponse({
    required this.status,
    required this.message,
    this.profileImage,
  });

  factory EditProfileResponse.fromJson(Map<String, dynamic> json) =>
      _$EditProfileResponseFromJson(json);

  Map<String, dynamic> toJson() => _$EditProfileResponseToJson(this);
}