GetUserTextPostModel deserializeGetUserTextPostModel(Map<String, dynamic> json) {
  return GetUserTextPostModel.fromJson(json);
}

class GetUserTextPostModel {
  int? count;
  String? next;
  String? previous;
  Results? results;

  GetUserTextPostModel({this.count, this.next, this.previous, this.results});

  factory GetUserTextPostModel.fromJson(Map<String, dynamic> json) {
    return GetUserTextPostModel(
      count: json['count'],
      next: json['next'],
      previous: json['previous'],
      results: json['results'] != null ? Results.fromJson(json['results']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'count': count,
      'next': next,
      'previous': previous,
      if (results != null) 'results': results!.toJson(),
    };
  }

  GetUserTextPostModel copyWith({
    int? count,
    String? next,
    String? previous,
    Results? results,
  }) {
    return GetUserTextPostModel(
      count: count ?? this.count,
      next: next ?? this.next,
      previous: previous ?? this.previous,
      results: results ?? this.results,
    );
  }
}

class Results {
  bool? status;
  String? message;
  List<UserTextPostData>? data;

  Results({this.status, this.message, this.data});

  factory Results.fromJson(Map<String, dynamic> json) {
    return Results(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null ? (json['data'] as List).map((v) => UserTextPostData.fromJson(v)).toList() : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      if (data != null) 'data': data!.map((v) => v.toJson()).toList(),
    };
  }

  Results copyWith({
    bool? status,
    String? message,
    List<UserTextPostData>? data,
  }) {
    return Results(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

class UserTextPostData {
  int? id;
  String? title;
  String? description;
  String? location;
  int? likes;
  int? dislikes;
  int? commentsCount;
  List<int>? taggedIn;
  String? createdAt;
  String? latestComment;
  User? user;
  bool? isLiked;
  bool? isSaved;
  bool? isTextPost;

  UserTextPostData({
    this.id,
    this.title,
    this.description,
    this.location,
    this.likes,
    this.dislikes,
    this.commentsCount,
    this.taggedIn,
    this.createdAt,
    this.latestComment,
    this.user,
    this.isLiked,
    this.isSaved,
    this.isTextPost,
  });

  factory UserTextPostData.fromJson(Map<String, dynamic> json) {
    return UserTextPostData(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      location: json['location'],
      likes: json['likes'],
      dislikes: json['dislikes'],
      commentsCount: json['comments_count'],
      taggedIn: json['tagged_in']?.cast<int>(),
      createdAt: json['created_at'],
      latestComment: json['latest_comment'],
      user: json['user'] != null ? User.fromJson(json['user']) : null,
      isLiked: json['is_liked'],
      isSaved: json['is_saved'],
      isTextPost: json['is_text_post'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'location': location,
      'likes': likes,
      'dislikes': dislikes,
      'comments_count': commentsCount,
      'tagged_in': taggedIn,
      'created_at': createdAt,
      'latest_comment': latestComment,
      if (user != null) 'user': user!.toJson(),
      'is_liked': isLiked,
      'is_saved': isSaved,
      'is_text_post': isTextPost,
    };
  }

  UserTextPostData copyWith({
    int? id,
    String? title,
    String? description,
    String? location,
    int? likes,
    int? dislikes,
    int? commentsCount,
    List<int>? taggedIn,
    String? createdAt,
    String? latestComment,
    User? user,
    bool? isLiked,
    bool? isSaved,
    bool? isTextPost,
  }) {
    return UserTextPostData(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      likes: likes ?? this.likes,
      dislikes: dislikes ?? this.dislikes,
      commentsCount: commentsCount ?? this.commentsCount,
      taggedIn: taggedIn ?? this.taggedIn,
      createdAt: createdAt ?? this.createdAt,
      latestComment: latestComment ?? this.latestComment,
      user: user ?? this.user,
      isLiked: isLiked ?? this.isLiked,
      isSaved: isSaved ?? this.isSaved,
      isTextPost: isTextPost ?? this.isTextPost,
    );
  }
}

class User {
  int? userId;
  String? username;
  String? name;
  String? profileImage;

  User({this.userId, this.username, this.name, this.profileImage});

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      userId: json['user_id'],
      username: json['username'],
      name: json['name'],
      profileImage: json['profile_image'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'username': username,
      'name': name,
      'profile_image': profileImage,
    };
  }

  User copyWith({
    int? userId,
    String? username,
    String? name,
    String? profileImage,
  }) {
    return User(
      userId: userId ?? this.userId,
      username: username ?? this.username,
      name: name ?? this.name,
      profileImage: profileImage ?? this.profileImage,
    );
  }
}
