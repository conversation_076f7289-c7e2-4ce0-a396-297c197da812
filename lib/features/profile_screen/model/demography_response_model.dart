// demography_response_model.dart
import 'package:json_annotation/json_annotation.dart';

part 'demography_response_model.g.dart';

DemographyResponseModel deserializeDemographyResponseModel(
    Map<String, dynamic> json) {
  return DemographyResponseModel.fromJson(json);
}

@JsonSerializable()
class DemographyResponseModel {
  final bool status;
  final String message;
  final List<DemographyItem> data;

  DemographyResponseModel({
    required this.status,
    required this.message,
    required this.data,
  });

  factory DemographyResponseModel.fromJson(Map<String, dynamic> json) =>
      _$DemographyResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$DemographyResponseModelToJson(this);

  DemographyResponseModel copyWith({
    bool? status,
    String? message,
    List<DemographyItem>? data,
  }) {
    return DemographyResponseModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

@JsonSerializable()
class DemographyItem {
  @JsonKey(name: 'request_type')
  final int requestType;

  @JsonKey(name: 'forwarding_id')
  final int forwardingId;

  final String name;

  DemographyItem({
    required this.requestType,
    required this.forwardingId,
    required this.name,
  });

  factory DemographyItem.fromJson(Map<String, dynamic> json) =>
      _$DemographyItemFromJson(json);

  Map<String, dynamic> toJson() => _$DemographyItemToJson(this);

  DemographyItem copyWith({
    int? requestType,
    int? forwardingId,
    String? name,
  }) {
    return DemographyItem(
      requestType: requestType ?? this.requestType,
      forwardingId: forwardingId ?? this.forwardingId,
      name: name ?? this.name,
    );
  }
}
