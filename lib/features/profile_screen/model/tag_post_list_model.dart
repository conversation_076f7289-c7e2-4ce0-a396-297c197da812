TagPostmodel deserializeTagPostmodel(Map<String, dynamic> json) => TagPostmodel.fromJson(json);

// class TagPostmodel {
//   int? count;
//   String? next;
//   String? previous;
//   Results? results;

//   TagPostmodel({this.count, this.next, this.previous, this.results});

//   TagPostmodel.fromJson(Map<String, dynamic> json) {
//     count = json['count'];
//     next = json['next'];
//     previous = json['previous'];
//     results = json['results'] != null ? Results.fromJson(json['results']) : null;
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['count'] = count;
//     data['next'] = next;
//     data['previous'] = previous;
//     if (results != null) {
//       data['results'] = results!.toJson();
//     }
//     return data;
//   }
// }

// class Results {
//   bool? status;
//   String? message;
//   List<TagPostData>? data;

//   Results({this.status, this.message, this.data});

//   Results.fromJson(Map<String, dynamic> json) {
//     status = json['status'];
//     message = json['message'];
//     if (json['data'] != null) {
//       data = <TagPostData>[];
//       json['data'].forEach((v) {
//         data!.add(TagPostData.fromJson(v));
//       });
//     }
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['status'] = status;
//     data['message'] = message;
//     if (this.data != null) {
//       data['data'] = this.data!.map((v) => v.toJson()).toList();
//     }
//     return data;
//   }
// }

// class TagPostData {
//   int? id;
//   String? title;
//   String? description;
//   String? location;
//   int? likes;
//   int? dislikes;
//   int? commentsCount;
//   // List<int>? taggedIn;
//   String? createdAt;
//   List<String>? files;
//   String? latestComment;
//   User? user;
//   bool? isLiked;
//   bool? isSaved;

//   TagPostData(
//       {this.id,
//       this.title,
//       this.description,
//       this.location,
//       this.likes,
//       this.dislikes,
//       this.commentsCount,
//       // this.taggedIn,
//       this.createdAt,
//       this.files,
//       this.latestComment,
//       this.user,
//       this.isLiked,
//       this.isSaved});

//   TagPostData.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     title = json['title'];
//     description = json['description'];
//     location = json['location'];
//     likes = json['likes'];
//     dislikes = json['dislikes'];
//     commentsCount = json['comments_count'];
//     // taggedIn = json['tagged_in'].cast<int>();
//     createdAt = json['created_at'];
//     files = json['files'].cast<String>();
//     latestComment = json['latest_comment'];
//     user = json['user'] != null ? User.fromJson(json['user']) : null;
//     isLiked = json['is_liked'];
//     isSaved = json['is_saved'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     data['title'] = title;
//     data['description'] = description;
//     data['location'] = location;
//     data['likes'] = likes;
//     data['dislikes'] = dislikes;
//     data['comments_count'] = commentsCount;
//     // data['tagged_in'] = taggedIn;
//     data['created_at'] = createdAt;
//     data['files'] = files;
//     data['latest_comment'] = latestComment;
//     if (user != null) {
//       data['user'] = user!.toJson();
//     }
//     data['is_liked'] = isLiked;
//     data['is_saved'] = isSaved;
//     return data;
//   }
// }

// class User {
//   int? userId;
//   String? username;
//   String? name;
//   String? profileImage;

//   User({this.userId, this.username, this.name, this.profileImage});

//   User.fromJson(Map<String, dynamic> json) {
//     userId = json['user_id'];
//     username = json['username'];
//     name = json['name'];
//     profileImage = json['profile_image'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['user_id'] = userId;
//     data['username'] = username;
//     data['name'] = name;
//     data['profile_image'] = profileImage;
//     return data;
//   }
// }
class TagPostmodel {
  int? count;
  String? next;
  String? previous;
  Results? results;

  TagPostmodel({this.count, this.next, this.previous, this.results});

  TagPostmodel.fromJson(Map<String, dynamic> json) {
    count = json['count'];
    next = json['next'];
    previous = json['previous'];
    results = json['results'] != null ? Results.fromJson(json['results']) : null;
  }

  Map<String, dynamic> toJson() {
    return {
      'count': count,
      'next': next,
      'previous': previous,
      'results': results?.toJson(),
    };
  }

  TagPostmodel copyWith({
    int? count,
    String? next,
    String? previous,
    Results? results,
  }) {
    return TagPostmodel(
      count: count ?? this.count,
      next: next ?? this.next,
      previous: previous ?? this.previous,
      results: results ?? this.results,
    );
  }
}

class Results {
  bool? status;
  String? message;
  List<TagPostData>? data;

  Results({this.status, this.message, this.data});

  Results.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    data = json['data']?.map<TagPostData>((v) => TagPostData.fromJson(v)).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.map((v) => v.toJson()).toList(),
    };
  }

  Results copyWith({
    bool? status,
    String? message,
    List<TagPostData>? data,
  }) {
    return Results(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

class TagPostData {
  int? id;
  String? title;
  String? description;
  String? location;
  int? likes;
  int? dislikes;
  int? commentsCount;
  String? createdAt;
  List<String>? files;
  int? width;
  int? height;
  String? latestComment;
  dynamic taggedIn;
  User? user;
  bool? isLiked;
  bool? isSaved;

  TagPostData({
    this.id,
    this.title,
    this.description,
    this.location,
    this.likes,
    this.dislikes,
    this.commentsCount,
    this.createdAt,
    this.files,
    this.width,
    this.height,
    this.latestComment,
    this.taggedIn,
    this.user,
    this.isLiked,
    this.isSaved,
  });

  TagPostData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    description = json['description'];
    location = json['location'];
    likes = json['likes'];
    dislikes = json['dislikes'];
    commentsCount = json['comments_count'];
    createdAt = json['created_at'];
    files = json['files']?.cast<String>();
    width = json['width'];
    height = json['height'];
    latestComment = json['latest_comment'];
    taggedIn = json['tagged_in'];
    user = json['user'] != null ? User.fromJson(json['user']) : null;
    isLiked = json['is_liked'];
    isSaved = json['is_saved'];
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'location': location,
      'likes': likes,
      'dislikes': dislikes,
      'comments_count': commentsCount,
      'created_at': createdAt,
      'files': files,
      'width': width,
      'height': height,
      'latest_comment': latestComment,
      'tagged_in': taggedIn,
      'user': user?.toJson(),
      'is_liked': isLiked,
      'is_saved': isSaved,
    };
  }

  TagPostData copyWith({
    int? id,
    String? title,
    String? description,
    String? location,
    int? likes,
    int? dislikes,
    int? commentsCount,
    String? createdAt,
    List<String>? files,
    int? width,
    int? height,
    String? latestComment,
    Map<String, dynamic>? taggedIn,
    User? user,
    bool? isLiked,
    bool? isSaved,
  }) {
    return TagPostData(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      likes: likes ?? this.likes,
      dislikes: dislikes ?? this.dislikes,
      commentsCount: commentsCount ?? this.commentsCount,
      createdAt: createdAt ?? this.createdAt,
      files: files ?? this.files,
      width: width ?? this.width,
      height: height ?? this.height,
      latestComment: latestComment ?? this.latestComment,
      taggedIn: taggedIn ?? this.taggedIn,
      user: user ?? this.user,
      isLiked: isLiked ?? this.isLiked,
      isSaved: isSaved ?? this.isSaved,
    );
  }
}

class User {
  int? userId;
  String? username;
  String? name;
  String? profileImage;

  User({this.userId, this.username, this.name, this.profileImage});

  User.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'];
    username = json['username'];
    name = json['name'];
    profileImage = json['profile_image'];
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'username': username,
      'name': name,
      'profile_image': profileImage,
    };
  }

  User copyWith({
    int? userId,
    String? username,
    String? name,
    String? profileImage,
  }) {
    return User(
      userId: userId ?? this.userId,
      username: username ?? this.username,
      name: name ?? this.name,
      profileImage: profileImage ?? this.profileImage,
    );
  }
}
