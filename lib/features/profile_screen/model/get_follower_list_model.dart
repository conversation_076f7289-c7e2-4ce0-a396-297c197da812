GetFollowerListModel deserializeGetFollowerListModel(Map<String, dynamic> json) => GetFollowerListModel.fromJson(json);

class GetFollowerListModel {
  int? count;
  String? next;
  String? previous;
  Results? results;

  GetFollowerListModel({this.count, this.next, this.previous, this.results});

  GetFollowerListModel.fromJson(Map<String, dynamic> json) {
    count = json['count'];
    next = json['next'];
    previous = json['previous'];
    results = json['results'] != null ? Results.fromJson(json['results']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['count'] = count;
    data['next'] = next;
    data['previous'] = previous;
    if (results != null) {
      data['results'] = results!.toJson();
    }
    return data;
  }

  GetFollowerListModel copyWith({
    int? count,
    String? next,
    String? previous,
    Results? results,
  }) {
    return GetFollowerListModel(
      count: count ?? this.count,
      next: next ?? this.next,
      previous: previous ?? this.previous,
      results: results ?? this.results,
    );
  }
}

class Results {
  bool? status;
  String? message;
  List<FolllowData>? data;

  Results({this.status, this.message, this.data});

  Results.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <FolllowData>[];
      json['data'].forEach((v) {
        data!.add(FolllowData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  Results copyWith({
    bool? status,
    String? message,
    List<FolllowData>? data,
  }) {
    return Results(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

class FolllowData {
  int? id;
  String? username;
  String? name;
  String? profilePicture;
  bool? isFollowing;

  FolllowData({this.id, this.username, this.name, this.profilePicture, this.isFollowing});

  FolllowData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    username = json['username'];
    name = json['name'];
    profilePicture = json['profile_picture'];
    isFollowing = json['is_following'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['username'] = username;
    data['name'] = name;
    data['profile_picture'] = profilePicture;
    data['is_following'] = isFollowing;
    return data;
  }

  FolllowData copyWith({
    int? id,
    String? username,
    String? name,
    String? profilePicture,
    bool? isFollowing,
  }) {
    return FolllowData(
      id: id ?? this.id,
      username: username ?? this.username,
      name: name ?? this.name,
      profilePicture: profilePicture ?? this.profilePicture,
      isFollowing: isFollowing ?? this.isFollowing,
    );
  }
}
