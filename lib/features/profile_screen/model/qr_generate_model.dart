import 'package:json_annotation/json_annotation.dart';

part 'qr_generate_model.g.dart';

QrResponseModel deserializeQrResponseModel(Map<String, dynamic> json) {
  return QrResponseModel.fromJson(json);
}


@JsonSerializable()
class QrResponseModel {
  @Json<PERSON>ey(name: 'status')
  final bool status;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'message')
  final String message;

  @JsonKey(name: 'QR')
  final String qrCode; 

  @Json<PERSON>ey(name: 'url')
  final String url;

  QrResponseModel({
    required this.status,
    required this.message,
    required this.qrCode,
    required this.url,
  });

  factory QrResponseModel.fromJson(Map<String, dynamic> json) =>
      _$QrResponseModelFromJson(json);

  /// Method to convert the instance to JSON
  Map<String, dynamic> toJson() => _$QrResponseModelToJson(this);
}
