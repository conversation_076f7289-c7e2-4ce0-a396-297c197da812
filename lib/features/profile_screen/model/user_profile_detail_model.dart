import 'package:json_annotation/json_annotation.dart';

part 'user_profile_detail_model.g.dart';

UserProfileDetailModel deserializeUserProfileDetailModel(Map<String, dynamic> json) {
  return UserProfileDetailModel.fromJson(json);
}

// @JsonSerializable()
// class UserProfileDetailModel {
//   final bool status;
//   final String message;
//   final Data data;

//   UserProfileDetailModel({
//     required this.status,
//     required this.message,
//     required this.data,
//     // required String mobile,
//     // required String profileImage,
//   });

//   factory UserProfileDetailModel.fromJson(Map<String, dynamic> json) => _$UserProfileDetailModelFromJson(json);

//   Map<String, dynamic> toJson() => _$UserProfileDetailModelToJson(this);
// }

// @JsonSerializable()
// class Data {
//   final int id;
//   final String name;
//   final String username;
//   final String email;
//   final String bio;
//   final String dob;
//   final String mobile;
//   final String gender;

//   @JsonKey(name: 'profile_image')
//   final String profileImage;

//   @JsonKey(name: 'is_blocked')
//   final bool isBlocked;

//   @JsonKey(name: 'number_of_post')
//   final int numberOfPost;

//   @JsonKey(name: 'number_of_followers')
//   final int numberOfFollowers;

//   @JsonKey(name: 'number_of_following')
//   final int numberOfFollowing;

//   @JsonKey(name: 'is_following')
//   final bool isFollowing;

//   final List<Post> posts;

//   Data({
//     required this.id,
//     required this.name,
//     required this.username,
//     required this.email,
//     required this.bio,
//     required this.dob,
//     required this.mobile,
//     required this.gender,
//     required this.profileImage,
//     required this.isBlocked,
//     required this.numberOfPost,
//     required this.numberOfFollowers,
//     required this.numberOfFollowing,
//     required this.isFollowing,
//     required this.posts,
//   });

//   factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

//   Map<String, dynamic> toJson() => _$DataToJson(this);
// }

// @JsonSerializable()
// class Post {
//   final int id;
//   final String title;
//   final String description;
//   final String location;
//   final int likes;
//   final int dislikes;

//   @JsonKey(name: 'comments_count')
//   final int commentsCount;

//   // final Map<String, dynamic> taggedIn;

//   @JsonKey(name: 'facebook')
//   final bool facebook;

//   @JsonKey(name: 'instagram')
//   final bool instagram;

//   @JsonKey(name: 'linkedin')
//   final bool linkedin;

//   @JsonKey(name: 'pinterest')
//   final bool pinterest;

//   @JsonKey(name: 'vimeo')
//   final bool vimeo;

//   @JsonKey(name: 'youtube')
//   final bool youtube;

//   @JsonKey(name: 'dailymotion')
//   final bool dailymotion;

//   @JsonKey(name: 'reddit')
//   final bool reddit;

//   @JsonKey(name: 'tumblr')
//   final bool tumblr;

//   @JsonKey(name: 'twitter')
//   final bool twitter;

//   @JsonKey(name: 'created_at')
//   final String createdAt;

//   final List<String> files;
//   final PostUser user;

//   Post({
//     required this.id,
//     required this.title,
//     required this.description,
//     required this.location,
//     required this.likes,
//     required this.dislikes,
//     required this.commentsCount,
//     // required this.taggedIn,
//     required this.facebook,
//     required this.instagram,
//     required this.linkedin,
//     required this.pinterest,
//     required this.vimeo,
//     required this.youtube,
//     required this.dailymotion,
//     required this.reddit,
//     required this.tumblr,
//     required this.twitter,
//     required this.createdAt,
//     required this.files,
//     required this.user,
//   });

//   factory Post.fromJson(Map<String, dynamic> json) => _$PostFromJson(json);

//   Map<String, dynamic> toJson() => _$PostToJson(this);
// }

// @JsonSerializable()
// class PostUser {
//   @JsonKey(name: 'user_id')
//   final int userId;

//   final String username;
//   final String name;

//   @JsonKey(name: 'profile_image')
//   final String profileImage;

//   PostUser({
//     required this.userId,
//     required this.username,
//     required this.name,
//     required this.profileImage,
//   });

//   factory PostUser.fromJson(Map<String, dynamic> json) => _$PostUserFromJson(json);

//   Map<String, dynamic> toJson() => _$PostUserToJson(this);
// }

@JsonSerializable()
class UserProfileDetailModel {
  final bool status;
  final String message;
  final Data data;

  UserProfileDetailModel({
    required this.status,
    required this.message,
    required this.data,
  });

  factory UserProfileDetailModel.fromJson(Map<String, dynamic> json) => _$UserProfileDetailModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserProfileDetailModelToJson(this);

  UserProfileDetailModel copyWith({
    bool? status,
    String? message,
    Data? data,
  }) {
    return UserProfileDetailModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

@JsonSerializable()
class Data {
  final int id;
  final String name;
  final String username;
  final String email;
  final String bio;
  final String country;
  final String state;
  final String city;

  final String dob;
  final String mobile;
  final String gender;

  @JsonKey(name: 'profile_image')
  final String profileImage;

  @JsonKey(name: 'is_blocked')
  final bool isBlocked;

  @JsonKey(name: 'number_of_post')
  final int numberOfPost;

  @JsonKey(name: 'number_of_followers')
  final int numberOfFollowers;

  @JsonKey(name: 'number_of_following')
  final int numberOfFollowing;

  @JsonKey(name: 'is_following')
  final bool isFollowing;
  @JsonKey(name: 'refference_code')
  final String refferenceCode;

  final List<Post> posts;

  Data({
    required this.id,
    required this.name,
    required this.username,
    required this.email,
    required this.bio,
    required this.dob,
    required this.country,
    required this.state,
    required this.city,
    required this.mobile,
    required this.gender,
    required this.profileImage,
    required this.isBlocked,
    required this.numberOfPost,
    required this.numberOfFollowers,
    required this.numberOfFollowing,
    required this.isFollowing,
    required this.refferenceCode,
    required this.posts,
  });

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

  Map<String, dynamic> toJson() => _$DataToJson(this);

  Data copyWith({
    int? id,
    String? name,
    String? username,
    String? email,
    String? bio,
    String? country,
    String? state,
    String? city,
    String? dob,
    String? mobile,
    String? gender,
    String? profileImage,
    bool? isBlocked,
    int? numberOfPost,
    int? numberOfFollowers,
    int? numberOfFollowing,
    bool? isFollowing,
    String? refferenceCode,
    String? refrelCode,
    List<Post>? posts,
  }) {
    return Data(
      id: id ?? this.id,
      name: name ?? this.name,
      username: username ?? this.username,
      email: email ?? this.email,
      bio: bio ?? this.bio,
      country: country ?? this.country,
      state: state ?? this.state,
      city: city ?? this.city,
      dob: dob ?? this.dob,
      mobile: mobile ?? this.mobile,
      gender: gender ?? this.gender,
      profileImage: profileImage ?? this.profileImage,
      isBlocked: isBlocked ?? this.isBlocked,
      numberOfPost: numberOfPost ?? this.numberOfPost,
      numberOfFollowers: numberOfFollowers ?? this.numberOfFollowers,
      numberOfFollowing: numberOfFollowing ?? this.numberOfFollowing,
      isFollowing: isFollowing ?? this.isFollowing,
      refferenceCode: refferenceCode ?? this.refferenceCode,
      posts: posts ?? this.posts,
    );
  }
}

@JsonSerializable()
class Post {
  final int id;
  final String title;
  final String description;
  final String location;
  final int likes;
  final int dislikes;

  @JsonKey(name: 'comments_count')
  final int commentsCount;

  @JsonKey(name: 'facebook')
  final bool facebook;

  @JsonKey(name: 'instagram')
  final bool instagram;

  @JsonKey(name: 'linkedin')
  final bool linkedin;

  @JsonKey(name: 'pinterest')
  final bool pinterest;

  @JsonKey(name: 'vimeo')
  final bool vimeo;

  @JsonKey(name: 'youtube')
  final bool youtube;

  @JsonKey(name: 'dailymotion')
  final bool dailymotion;

  @JsonKey(name: 'reddit')
  final bool reddit;

  @JsonKey(name: 'tumblr')
  final bool tumblr;

  @JsonKey(name: 'twitter')
  final bool twitter;

  @JsonKey(name: 'created_at')
  final String createdAt;

  final List<String> files;
  final PostUser user;

  Post({
    required this.id,
    required this.title,
    required this.description,
    required this.location,
    required this.likes,
    required this.dislikes,
    required this.commentsCount,
    required this.facebook,
    required this.instagram,
    required this.linkedin,
    required this.pinterest,
    required this.vimeo,
    required this.youtube,
    required this.dailymotion,
    required this.reddit,
    required this.tumblr,
    required this.twitter,
    required this.createdAt,
    required this.files,
    required this.user,
  });

  factory Post.fromJson(Map<String, dynamic> json) => _$PostFromJson(json);

  Map<String, dynamic> toJson() => _$PostToJson(this);

  Post copyWith({
    int? id,
    String? title,
    String? description,
    String? location,
    int? likes,
    int? dislikes,
    int? commentsCount,
    bool? facebook,
    bool? instagram,
    bool? linkedin,
    bool? pinterest,
    bool? vimeo,
    bool? youtube,
    bool? dailymotion,
    bool? reddit,
    bool? tumblr,
    bool? twitter,
    String? createdAt,
    List<String>? files,
    PostUser? user,
  }) {
    return Post(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      likes: likes ?? this.likes,
      dislikes: dislikes ?? this.dislikes,
      commentsCount: commentsCount ?? this.commentsCount,
      facebook: facebook ?? this.facebook,
      instagram: instagram ?? this.instagram,
      linkedin: linkedin ?? this.linkedin,
      pinterest: pinterest ?? this.pinterest,
      vimeo: vimeo ?? this.vimeo,
      youtube: youtube ?? this.youtube,
      dailymotion: dailymotion ?? this.dailymotion,
      reddit: reddit ?? this.reddit,
      tumblr: tumblr ?? this.tumblr,
      twitter: twitter ?? this.twitter,
      createdAt: createdAt ?? this.createdAt,
      files: files ?? this.files,
      user: user ?? this.user,
    );
  }
}

@JsonSerializable()
class PostUser {
  @JsonKey(name: 'user_id')
  final int userId;

  final String username;
  final String name;

  @JsonKey(name: 'profile_image')
  final String profileImage;

  PostUser({
    required this.userId,
    required this.username,
    required this.name,
    required this.profileImage,
  });

  factory PostUser.fromJson(Map<String, dynamic> json) => _$PostUserFromJson(json);

  Map<String, dynamic> toJson() => _$PostUserToJson(this);

  PostUser copyWith({
    int? userId,
    String? username,
    String? name,
    String? profileImage,
  }) {
    return PostUser(
      userId: userId ?? this.userId,
      username: username ?? this.username,
      name: name ?? this.name,
      profileImage: profileImage ?? this.profileImage,
    );
  }
}
