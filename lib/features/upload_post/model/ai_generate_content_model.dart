import 'dart:convert';
AIgenerateContentModel deserializeAIgenerateContentModel(Map<String, dynamic> json) => AIgenerateContentModel.fromJson(json);

class AIgenerateContentModel {
  final bool status;
  final String message;
  final String aiResponse;

  AIgenerateContentModel({
    required this.status,
    required this.message,
    required this.aiResponse,
  });

  // Factory constructor to create from JSON
  factory AIgenerateContentModel.fromJson(Map<String, dynamic> json) {
    return AIgenerateContentModel(
      status: json['status'] as bool,
      message: json['message'] as String,
      aiResponse: json['result'] as String,
    );
  }

  // Convert object to JSON
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'result': aiResponse,
    };
  }

  // CopyWith method
  AIgenerateContentModel copyWith({
    bool? status,
    String? message,
    String? aiResponse,
  }) {
    return AIgenerateContentModel(
      status: status ?? this.status,
      message: message ?? this.message,
      aiResponse: aiResponse ?? this.aiResponse,
    );
  }

  // Optional: fromRawJson / toRawJson for string handling
  factory AIgenerateContentModel.fromRawJson(String str) =>
      AIgenerateContentModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());
}