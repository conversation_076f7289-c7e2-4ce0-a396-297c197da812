// ignore_for_file: non_constant_identifier_names

import 'package:json_annotation/json_annotation.dart';

part 'upload_post_response_model.g.dart';

UploadPostResponseModel deserializeUploadPostResponseModel(
        Map<String, dynamic> json) =>
    UploadPostResponseModel.fromJson(json);

@JsonSerializable()
class UploadPostResponseModel {
  final bool? status;
  final String? message;
  final UploadPostData? data;
  final ThirdPartyData? third_party;

  UploadPostResponseModel({
    this.status,
    this.message,
    this.data,
    this.third_party,
  });

  factory UploadPostResponseModel.fromJson(Map<String, dynamic> json) =>
      _$UploadPostResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$UploadPostResponseModelToJson(this);
}

@JsonSerializable()
class UploadPostData {
  final int? id;
  final String? title;
  final String? description;
  final String? location;
  final bool? is_private;
  final bool? is_deleted;
  final bool? is_video;
  final int? likes;
  final int? dislikes;
  final int? comments_count;
  final int? report_count;
  final bool? is_banned;
  final bool? facebook;
  final String? facebook_id;
  final bool? tiktok;
  final String? tiktok_id;
  final bool? instagram;
  final String? instagram_id;
  final bool? linkedin;
  final String? linkedin_id;
  final bool? pinterest;
  final String? pinterest_id;
  final bool? vimeo;
  final String? vimeo_id;
  final bool? youtube;
  final String? youtube_id;
  final bool? dailymotion;
  final String? dailymotion_id;
  final bool? twitter;
  final String? twitter_id;
  final bool? tumblr;
  final bool? reddit;
  final String? tumblr_id;
  final dynamic tagged_in;
  final String? scheduled_at;
  final bool? is_scheduled;
  final bool? is_unscheduled;
  final bool? is_posted;
  final String? created_at;
  final List<String>? files;
  final List<String>? thumbnail_files;

  UploadPostData({
    this.id,
    this.title,
    this.description,
    this.location,
    this.is_private,
    this.is_deleted,
    this.is_video,
    this.likes,
    this.dislikes,
    this.comments_count,
    this.report_count,
    this.is_banned,
    this.facebook,
    this.facebook_id,
    this.tiktok,
    this.tiktok_id,
    this.instagram,
    this.instagram_id,
    this.linkedin,
    this.linkedin_id,
    this.pinterest,
    this.pinterest_id,
    this.vimeo,
    this.vimeo_id,
    this.youtube,
    this.youtube_id,
    this.dailymotion,
    this.dailymotion_id,
    this.twitter,
    this.twitter_id,
    this.tumblr,
    this.reddit,
    this.tumblr_id,
    this.tagged_in,
    this.scheduled_at,
    this.is_scheduled,
    this.is_unscheduled,
    this.is_posted,
    this.created_at,
    this.files,
    this.thumbnail_files,
  });

  factory UploadPostData.fromJson(Map<String, dynamic> json) =>
      _$UploadPostDataFromJson(json);

  Map<String, dynamic> toJson() => _$UploadPostDataToJson(this);
}

@JsonSerializable()
class ThirdPartyData {
  final String? message;

  ThirdPartyData({this.message});

  factory ThirdPartyData.fromJson(Map<String, dynamic> json) =>
      _$ThirdPartyDataFromJson(json);

  Map<String, dynamic> toJson() => _$ThirdPartyDataToJson(this);
}
