PostIndustryModel deserializePostIndustryModel(Map<String, dynamic> json) => PostIndustryModel.fromJson(json);

class PostIndustryModel {
  bool? status;
  String? message;
  List<PostIndustryData>? data;

  PostIndustryModel({this.status, this.message, this.data});

  PostIndustryModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <PostIndustryData>[];
      json['data'].forEach((v) {
        data!.add(PostIndustryData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  PostIndustryModel copyWith({
    bool? status,
    String? message,
    List<PostIndustryData>? data,
  }) {
    return PostIndustryModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

class PostIndustryData {
  int? id;
  String? name;

  PostIndustryData({this.id, this.name});

  PostIndustryData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['name'] = name;
    return data;
  }

  PostIndustryData copyWith({
    int? id,
    String? name,
  }) {
    return PostIndustryData(
      id: id ?? this.id,
      name: name ?? this.name,
    );
  }
}
