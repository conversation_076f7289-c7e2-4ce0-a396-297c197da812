SearchLocationModel deserializeSearchLocationModel(Map<String, dynamic> json) => SearchLocationModel.fromJson(json);

class SearchLocationModel {
  bool? status;
  String? message;
  List<SearchLocationDate>? data;

  SearchLocationModel({this.status, this.message, this.data});

  SearchLocationModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <SearchLocationDate>[];
      json['data'].forEach((v) {
        data!.add(SearchLocationDate.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  SearchLocationModel copyWith({
    bool? status,
    String? message,
    List<SearchLocationDate>? data,
  }) {
    return SearchLocationModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

class SearchLocationDate {
  String? suggestedPlace;
  double? lat;
  double? lng;

  SearchLocationDate({this.suggestedPlace, this.lat, this.lng});

  SearchLocationDate.fromJson(Map<String, dynamic> json) {
    suggestedPlace = json['suggested_place'];
    lat = _parseDouble(json['lat']);
    lng = _parseDouble(json['lng']);
  }

  double? _parseDouble(dynamic value) {
    if (value == null || value == '') return null;
    try {
      return double.parse(value.toString());
    } catch (e) {
      return null;
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['suggested_place'] = suggestedPlace;
    data['lat'] = lat;
    data['lng'] = lng;
    return data;
  }

  SearchLocationDate copyWith({
    String? suggestedPlace,
    double? lat,
    double? lng,
  }) {
    return SearchLocationDate(
      suggestedPlace: suggestedPlace ?? this.suggestedPlace,
      lat: lat ?? this.lat,
      lng: lng ?? this.lng,
    );
  }
}
