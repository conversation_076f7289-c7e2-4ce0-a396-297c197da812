GenerateDuplicatePostModel deserializeGenerateDuplicatePostModel(Map<String, dynamic> json) =>
    GenerateDuplicatePostModel.fromJson(json);

class GenerateDuplicatePostModel {
  final bool status;
  final String message;
  final String result;
  final String generatedImagePath;

  GenerateDuplicatePostModel({
    required this.status,
    required this.message,
    required this.result,
    required this.generatedImagePath,
  });

  factory GenerateDuplicatePostModel.fromJson(Map<String, dynamic> json) {
    return GenerateDuplicatePostModel(
      status: json['status'] as bool,
      message: json['message'] as String,
      result: json['result'] as String,
      generatedImagePath: json['generated_image_path'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'result': result,
      'generated_image_path': generatedImagePath,
    };
  }

  GenerateDuplicatePostModel copyWith({
    bool? status,
    String? message,
    String? result,
    String? generatedImagePath,
  }) {
    return GenerateDuplicatePostModel(
      status: status ?? this.status,
      message: message ?? this.message,
      result: result ?? this.result,
      generatedImagePath: generatedImagePath ?? this.generatedImagePath,
    );
  }
}
