// ignore_for_file: non_constant_identifier_names

import 'package:json_annotation/json_annotation.dart';

part 'user_profile_model.g.dart';

UserProfileModel deserializeUserProfileModel(Map<String, dynamic> json) {
  return UserProfileModel.fromJson(json);
}

@JsonSerializable()
class UserProfileModel {
  final bool status;
  final String message;
  final Data data;

  UserProfileModel({
    required this.status,
    required this.message,
    required this.data,
  });

  factory UserProfileModel.fromJson(Map<String, dynamic> json) => _$UserProfileModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserProfileModelToJson(this);
}

@JsonSerializable()
class Data {
  final int id;
  final String name;
  final String username;
  final String email;
  final String bio;
  final String dob;
  final String mobile;
  final String gender;
  final String profile_image;
  final bool is_blocked;
  final int number_of_post;
  final int number_of_followers;
  final int number_of_following;
  final bool is_following;
  final List<Post> posts;

  Data({
    required this.id,
    required this.name,
    required this.username,
    required this.email,
    required this.bio,
    required this.dob,
    required this.mobile,
    required this.gender,
    required this.profile_image,
    required this.is_blocked,
    required this.number_of_post,
    required this.number_of_followers,
    required this.number_of_following,
    required this.is_following,
    required this.posts,
  });

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

  Map<String, dynamic> toJson() => _$DataToJson(this);
}

@JsonSerializable()
class Post {
  final int id;
  final String title;
  final String description;
  final String location;
  final int likes;
  final int dislikes;
  final int comments_count;
  final dynamic tagged_in;
  final bool facebook;
  final bool instagram;
  final bool linkedin;
  final bool pinterest;
  final bool vimeo;
  final bool youtube;
  final bool dailymotion;
  final bool reddit;
  final bool tumblr;
  final bool twitter;
  final String created_at;
  final List<String> files;
  final int width;
  final int height;
  final PostUser user;

  Post({
    required this.id,
    required this.title,
    required this.description,
    required this.location,
    required this.likes,
    required this.dislikes,
    required this.comments_count,
    required this.tagged_in,
    required this.facebook,
    required this.instagram,
    required this.linkedin,
    required this.pinterest,
    required this.vimeo,
    required this.youtube,
    required this.dailymotion,
    required this.reddit,
    required this.tumblr,
    required this.twitter,
    required this.created_at,
    required this.files,
    required this.width,
    required this.height,
    required this.user,
  });

  factory Post.fromJson(Map<String, dynamic> json) => _$PostFromJson(json);

  Map<String, dynamic> toJson() => _$PostToJson(this);
}

@JsonSerializable()
class PostUser {
  final int user_id;
  final String username;
  final String name;
  final String profile_image;

  PostUser({
    required this.user_id,
    required this.username,
    required this.name,
    required this.profile_image,
  });

  factory PostUser.fromJson(Map<String, dynamic> json) => _$PostUserFromJson(json);

  Map<String, dynamic> toJson() => _$PostUserToJson(this);
}
