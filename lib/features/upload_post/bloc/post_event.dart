part of 'post_bloc.dart';

sealed class PostEvent extends Equatable {
  const PostEvent();

  @override
  List<Object> get props => [];
}

class UploadPostAPIEvent extends PostEvent {
  final BuildContext context;
  final List<File> images;
  final List<File>? videoThumbnail;
  final String description;
  final String title;
  final String location;
  final bool isLinkdedin;
  final bool isVimeo;
  final bool isPintrest;
  final bool isReddit;
  final bool isTumblr;
  final bool isInstagram;
  final bool isFacebook;
  final bool isTikTok;
  final bool isThread;
  final bool isYoutube;
  final bool isVideo;
  final bool isprivate;
  final List<int> taggedin;
  final List<String> hashtags;
  final String scheduledat;
  final int industry;
  final bool isPost;
  final bool isTextPost;
  final bool x;
  final bool mastadon;

  const UploadPostAPIEvent({
    required this.context,
    required this.images,
    this.videoThumbnail,
    required this.description,
    required this.title,
    required this.location,
    required this.isLinkdedin,
    required this.isVimeo,
    required this.isPintrest,
    required this.isReddit,
    required this.isTumblr,
    required this.isInstagram,
    required this.isFacebook,
    required this.isTikTok,
    required this.isThread,
    required this.isYoutube,
    required this.isVideo,
    required this.scheduledat,
    required this.isprivate,
    required this.taggedin,
    required this.hashtags,
    required this.industry,
    required this.isPost,
    required this.isTextPost,
    required this.x,
    required this.mastadon,
  });
  @override
  List<Object> get props => [
        images,
        description,
        isLinkdedin,
        isVimeo,
        isPintrest,
        isReddit,
        isTumblr,
        isInstagram,
        isFacebook,
        isTikTok,
        isThread,
        isYoutube,
        isVideo,
        title,
        location,
        scheduledat,
        isprivate,
        taggedin,
        hashtags,
        industry,
        isPost,
        isTextPost,
        x,
        mastadon,
      ];
}

class GetProfilePostEvent extends PostEvent {}

class DeleteAccountsEvent extends PostEvent {
  final BuildContext context;

  const DeleteAccountsEvent({required this.context});
  @override
  List<Object> get props => [context];
}

class SchedulePostApiEvent extends PostEvent {
  @override
  List<Object> get props => [];
}

class DeleteSchedulePostApiEvent extends PostEvent {
  final int postId;
  final int index;

  const DeleteSchedulePostApiEvent({required this.postId, required this.index});

  @override
  List<Object> get props => [postId, index];
}

class SearchmentionUserListEvent extends PostEvent {
  final String searchtext;
  const SearchmentionUserListEvent({required this.searchtext});
  @override
  List<Object> get props => [searchtext];
}

class SearchLocationEvent extends PostEvent {
  final String searchtext;
  final String lat;
  final String long;
  const SearchLocationEvent({required this.searchtext, required this.lat, required this.long});

  @override
  List<Object> get props => [searchtext, lat, long];
}

class GetPostIndustryDetailsEvent extends PostEvent {
  const GetPostIndustryDetailsEvent();
  @override
  List<Object> get props => [];
}

class ClearSearchUserListEvent extends PostEvent {}

class AIgenerateContentEvent extends PostEvent {
  final String prompt;
  final File media;
  const AIgenerateContentEvent({required this.prompt, required this.media});

  @override
  List<Object> get props => [prompt, media];
}

class ClearAIGeneratedContentEvent extends PostEvent {}

class AIgenerateDuplicatePostEvent extends PostEvent {
  final String prompt;
  final String media;
  final BuildContext context;
  const AIgenerateDuplicatePostEvent({required this.prompt, required this.media, required this.context});

  @override
  List<Object> get props => [prompt, media, context];
}
