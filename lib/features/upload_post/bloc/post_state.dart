part of 'post_bloc.dart';

class PostState extends Equatable {
  const PostState(
      {this.uploadPostResponseModel,
      this.isSearchLocationHasData = false,
      this.uploaderror,
      this.isUploading = false,
      // Get Post state
      this.userProfileModel,
      this.isGetPostLoading = false,
      this.getPostError,
      // Delete Account State
      this.isdeleteAccountLoading = false,
      this.deletemessage,
      this.deleteError,
      // Sc
      this.isScheduleloding = false,
      this.scheduledPostModel,
      this.scheduleData = const [],
      this.isDeleteScheduledPostLoading = false,
      this.searchuserList = const [],
      this.searchLocationloading = false,
      this.searchLocation = const [],
      // Get Post Industry Details
      this.isGetIndustryLoading = false,
      this.postIndustryModel,
      this.postIndustryData = const [],
      this.isAIGeneratingloading = false,
      this.isfirstTime = false,
      this.aIgenerateContentModel,
      this.isAIGeneratingDuplicatePostloading = false,
      this.generateDuplicatePostModel,
      this.duplicatePostFile});
  final UploadPostResponseModel? uploadPostResponseModel;
  final String? uploaderror;
  final bool isUploading;
  // Get PostState
  final UserProfileModel? userProfileModel;
  final bool isGetPostLoading;
  final String? getPostError;
  // Delete Account State
  final String? deletemessage;
  final bool isdeleteAccountLoading;
  final String? deleteError;
  // Schedule Post
  final bool isScheduleloding;
  final ScheduledPostModel? scheduledPostModel;
  final List<ScheduledPostData> scheduleData;
  final bool isDeleteScheduledPostLoading;

  final List<SearchUserData> searchuserList;
  final bool searchLocationloading;
  final List<SearchLocationDate> searchLocation;
  final bool isSearchLocationHasData;
// Get Post Industry details
  final bool isGetIndustryLoading;
  final PostIndustryModel? postIndustryModel;
  final List<PostIndustryData> postIndustryData;
  // AI Generate Content
  final bool isAIGeneratingloading;
  final bool isfirstTime;
  final AIgenerateContentModel? aIgenerateContentModel;
  final bool isAIGeneratingDuplicatePostloading;
  final GenerateDuplicatePostModel? generateDuplicatePostModel;
  final File? duplicatePostFile;

  @override
  List<Object?> get props => [
        uploadPostResponseModel,
        uploaderror,
        isUploading,
        isSearchLocationHasData,
        // Get PostState
        userProfileModel,
        isGetPostLoading,
        getPostError,
        // Delete Account State
        deletemessage,
        isdeleteAccountLoading,
        deleteError,
        // Schedule Post
        isScheduleloding,
        scheduledPostModel,
        scheduleData,
        isDeleteScheduledPostLoading,
        searchuserList,
        searchLocationloading,
        searchLocation,
        isGetIndustryLoading,
        postIndustryModel,
        postIndustryData,
        isAIGeneratingloading,
        isfirstTime,
        aIgenerateContentModel,
        isAIGeneratingDuplicatePostloading,
        generateDuplicatePostModel,
        duplicatePostFile,
      ];
  PostState copyWith({
    UploadPostResponseModel? uploadPostResponseModel,
    String? uploaderror,
    bool? isUploading,
    // Get PostState
    UserProfileModel? userProfileModel,
    bool? isSearchLocationHasData,
    bool? isGetPostLoading,
    String? getPostError,
    // Delete Account State
    String? deletemessage,
    bool? isdeleteAccountLoading,
    String? deleteError,
    // Schedule Post
    bool? isScheduleloding,
    ScheduledPostModel? scheduledPostModel,
    List<ScheduledPostData>? scheduleData,
    bool? isDeleteScheduledPostLoading,
    List<SearchUserData>? searchuserList,
    bool? searchLocationloading,
    List<SearchLocationDate>? searchLocation,
    DetectableTextEditingController? descriptionController,
    bool? isGetIndustryLoading,
    PostIndustryModel? postIndustryModel,
    List<PostIndustryData>? postIndustryData,
    bool? isAIGeneratingloading,
    bool? isfirstTime,
    AIgenerateContentModel? aIgenerateContentModel,
    bool? isAIGeneratingDuplicatePostloading,
    GenerateDuplicatePostModel? generateDuplicatePostModel,
    File? duplicatePostFile,
  }) {
    return PostState(
      uploadPostResponseModel: uploadPostResponseModel ?? this.uploadPostResponseModel,
      uploaderror: uploaderror ?? this.uploaderror,
      isUploading: isUploading ?? this.isUploading,
      // Get PostState
      userProfileModel: userProfileModel ?? this.userProfileModel,
      isGetPostLoading: isGetPostLoading ?? this.isGetPostLoading,
      getPostError: getPostError ?? this.getPostError,
      // Delete Account State
      deletemessage: deletemessage ?? this.deletemessage,
      isdeleteAccountLoading: isdeleteAccountLoading ?? this.isdeleteAccountLoading,
      deleteError: deleteError ?? this.deleteError,
      // Schedule Post
      isScheduleloding: isScheduleloding ?? this.isScheduleloding,
      scheduledPostModel: scheduledPostModel ?? this.scheduledPostModel,
      scheduleData: scheduleData ?? this.scheduleData,
      isDeleteScheduledPostLoading: isDeleteScheduledPostLoading ?? this.isDeleteScheduledPostLoading,
      searchuserList: searchuserList ?? this.searchuserList,
      searchLocationloading: searchLocationloading ?? this.searchLocationloading,
      searchLocation: searchLocation ?? this.searchLocation,
      // Get Post Industry Details
      isGetIndustryLoading: isGetIndustryLoading ?? this.isGetIndustryLoading,
      isSearchLocationHasData: isSearchLocationHasData ?? this.isSearchLocationHasData,
      postIndustryModel: postIndustryModel ?? this.postIndustryModel,
      postIndustryData: postIndustryData ?? this.postIndustryData,
      isAIGeneratingloading: isAIGeneratingloading ?? this.isAIGeneratingloading,
      isfirstTime: isfirstTime ?? this.isfirstTime,
      aIgenerateContentModel: aIgenerateContentModel ?? this.aIgenerateContentModel,
      isAIGeneratingDuplicatePostloading: isAIGeneratingDuplicatePostloading ?? this.isAIGeneratingDuplicatePostloading,
      generateDuplicatePostModel: generateDuplicatePostModel ?? this.generateDuplicatePostModel,
      duplicatePostFile: duplicatePostFile ?? this.duplicatePostFile,
    );
  }
}
