// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:detectable_text_field/widgets/detectable_text_editing_controller.dart';
import 'package:flowkar/core/socket/socket_service.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/discover/model/search_user_model.dart';
import 'package:flowkar/features/profile_screen/model/scheduled_post_model.dart';
import 'package:flowkar/features/upload_post/model/ai_generate_content_model.dart';
import 'package:flowkar/features/upload_post/model/generate_duplicate_post_model.dart';
import 'package:flowkar/features/upload_post/model/post_industry_model.dart';
import 'package:flowkar/features/upload_post/model/search_location_model.dart';
import 'package:flowkar/features/upload_post/model/upload_post_response_model.dart';
import 'package:flowkar/features/upload_post/model/user_profile_model.dart';
import 'package:flowkar/core/helpers/vibration_helper.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

part 'post_event.dart';
part 'post_state.dart';

class PostBloc extends Bloc<PostEvent, PostState> {
  ApiClient apiClient = ApiClient(Dio());
  PostBloc(super.initialState) {
    on<UploadPostAPIEvent>(_onUploadPostEvent);
    on<DeleteAccountsEvent>(_onDeleteProfile);
    on<SchedulePostApiEvent>(_schedulePostApi);
    on<DeleteSchedulePostApiEvent>(_onDeleteSchedulePostApi);
    on<SearchmentionUserListEvent>(_onSearchmentionUserListEvent);
    on<ClearSearchUserListEvent>(_onClearSearchUserListEvent);
    on<SearchLocationEvent>(_getSearchLocationAPI);
    on<GetPostIndustryDetailsEvent>(_getPostIndustryDetails);
    on<AIgenerateContentEvent>(_aIgenerateContentApi);
    on<ClearAIGeneratedContentEvent>(_clearAigeneratecontent);
    on<AIgenerateDuplicatePostEvent>(_aIgenerateDuplicatePostApi);
  }

  Future<void> _onUploadPostEvent(UploadPostAPIEvent event, Emitter<PostState> emit) async {
    emit(state.copyWith(isUploading: true));
    try {
      String taggedInJson = jsonEncode(event.taggedin);
      String hashtag = jsonEncode(event.hashtags);
      int brandId = await Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;

      // Simulate initial progress
      if (event.isPost == true) {
        await Future.delayed(const Duration(seconds: 1));
        progressNotifier.value = 0.3; // 30% progress

        // Simulate progress between 30% and 50%
        await Future.delayed(const Duration(seconds: 1));
        progressNotifier.value = 0.5; // 50% progress

        // Simulate progress between 50% and 70%
        await Future.delayed(const Duration(seconds: 1));
        progressNotifier.value = 0.7; // 70% progress

        // Simulate progress between 70% and 90%
        await Future.delayed(const Duration(seconds: 1));
        progressNotifier.value = 0.9;
      }

      String subscriptionId = Prefobj.preferences?.get(Prefkeys.SUBSCRIPTION) ?? "";
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      final result = event.industry == 0
          ? await apiClient.uploadPost(
              logInUserId: loginuserId,
              brandid: brandId.toString(),
              subscriptionId: subscriptionId.toString(),
              title: event.title.isEmpty ? "''" : event.title,
              description: event.description,
              location: event.location.isNotEmpty ? event.location : "",
              isPrivate: event.isprivate,
              isVideo: event.isVideo,
              facebook: event.isFacebook,
              linkedin: event.isLinkdedin,
              pinterest: event.isPintrest,
              instagram: event.isInstagram,
              dailymotion: event.isTikTok,
              twitter: event.isThread,
              vimeo: event.isVimeo,
              reddit: event.isReddit,
              tumblr: event.isTumblr,
              youtube: event.isYoutube,
              x: event.x,
              scheduledAt: event.scheduledat,
              taggedIn: taggedInJson,
              uploadFiles: event.images,
              videoThumbnail: event.isVideo ? event.videoThumbnail : [],
              hashtags: hashtag,
              isposted: event.isPost,
              isTextPost: event.isTextPost,
              mastadon: event.mastadon,
            )
          : await apiClient.uploadPost(
              logInUserId: loginuserId,
              brandid: brandId.toString(),
              subscriptionId: subscriptionId.toString(),
              title: event.title.isEmpty ? "''" : event.title,
              description: event.description,
              location: event.location.isNotEmpty ? event.location : "",
              isPrivate: event.isprivate,
              isVideo: event.isVideo,
              facebook: event.isFacebook,
              linkedin: event.isLinkdedin,
              pinterest: event.isPintrest,
              instagram: event.isInstagram,
              dailymotion: event.isTikTok,
              twitter: event.isThread,
              vimeo: event.isVimeo,
              reddit: event.isReddit,
              tumblr: event.isTumblr,
              youtube: event.isYoutube,
              x: event.x,
              scheduledAt: event.scheduledat,
              taggedIn: taggedInJson,
              uploadFiles: event.images,
              videoThumbnail: event.isVideo ? event.videoThumbnail : [],
              hashtags: hashtag,
              isposted: event.isPost,
              isTextPost: event.isTextPost,
              mastadon: event.mastadon,
              industry: event.industry);
      if (result.status == true) {
        emit(state.copyWith(isUploading: false));
        VibrationHelper.singleShortBuzz();
        if (event.isPost == true) {
          await Future.delayed(const Duration(seconds: 2));
          progressNotifier.value = 1.0; // 100% progress
          await Future.delayed(const Duration(milliseconds: 600));
          progressNotifier.value = 0.0;
        }
        // reset progress
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            result.message.toString(),
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        if (event.isPost == false) {
          NavigatorService.pushAndRemoveUntil(AppRoutes.bottomNavBar);
        }
        emit(state.copyWith(isUploading: false, uploadPostResponseModel: result));
      } else {
        emit(state.copyWith(isUploading: false));
        Logger.lOG(result.message ?? "Unknown error");
        if (event.isPost == true) {
          await Future.delayed(const Duration(milliseconds: 500));
          progressNotifier.value = 0.0;
        }
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            result.message.toString(),
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
      }
    } catch (error) {
      emit(state.copyWith(isUploading: false));
      if (event.isPost == true) {
        await Future.delayed(const Duration(milliseconds: 500));
        progressNotifier.value = 0.0;
      }
      emit(state.copyWith(isUploading: false, uploaderror: handleError(error)));
      handleError(error);
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
    }
  }

  Future<void> _onDeleteProfile(DeleteAccountsEvent event, Emitter<PostState> emit) async {
    {
      emit(state.copyWith(isdeleteAccountLoading: true));
      try {
        String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
        int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
        var result = await apiClient.deleteAccount(logInUserId: loginuserId, brandid: brandId.toString());

        if (result.status) {
          emit(state.copyWith(deletemessage: result.message, isdeleteAccountLoading: false));
          toastification.show(
            type: ToastificationType.success,
            showProgressBar: false,
            title: Text(
              Lang.of(event.context).lbl_your_account_is_successfully_deleted,
              style: Theme.of(event.context).textTheme.bodyMedium?.copyWith(fontSize: 12.0.sp),
            ),
            autoCloseDuration: const Duration(seconds: 3),
          );
          socialPlatformsStatus.value = {
            'VIMEO': false,
            'FACEBOOK': false,
            'INSTAGRAM': false,
            'THREAD': false,
            'LINKEDIN': false,
            'PINTEREST': false,
            'TUMBLR': false,
            'REDDIT': false,
            'YOUTUBE': false,
            'TIKTOK': false,
          };
          Prefobj.preferences?.delete(Prefkeys.AUTHTOKEN);
          Prefobj.preferences?.delete(Prefkeys.TMPAUTHTOKEN);
          Prefobj.preferences?.clear();
          SocketService.closeConnection();
          VibrationHelper.singleShortBuzz();
          NavigatorService.pushAndRemoveUntil(AppRoutes.signinPage);
        } else {
          emit(state.copyWith(isdeleteAccountLoading: false));
          Logger.lOG(result.message);
        }
      } catch (error) {
        emit(state.copyWith(isdeleteAccountLoading: false, deleteError: handleError(error)));
        Logger.lOG(error);
      }
    }
  }

  _schedulePostApi(SchedulePostApiEvent event, Emitter<PostState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      emit(state.copyWith(isScheduleloding: true));
      final results = await apiClient.getScheduledPosts(logInUserId: loginuserId, brandid: brandId.toString());
      if (results.status == true) {
        emit(state.copyWith(isScheduleloding: false, scheduleData: results.data, scheduledPostModel: results));
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isScheduleloding: false));
    }
  }

  _onDeleteSchedulePostApi(DeleteSchedulePostApiEvent event, Emitter<PostState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      emit(state.copyWith(isDeleteScheduledPostLoading: true));
      final results = await apiClient.deletescheduledPost(loginuserId, event.postId, brandId.toString());
      if (results.status == true) {
        NavigatorService.goBack();
        state.userProfileModel?.data.posts.removeAt(event.index);

        state.scheduleData.removeAt(event.index);
        emit(state.copyWith(isDeleteScheduledPostLoading: false));
      } else {
        emit(state.copyWith(isDeleteScheduledPostLoading: false));
        NavigatorService.goBack();
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isDeleteScheduledPostLoading: false));
      NavigatorService.goBack();
    }
  }

  _onSearchmentionUserListEvent(SearchmentionUserListEvent event, Emitter<PostState> emit) async {
    try {
      final completer = Completer<Map<String, dynamic>>();
      // Emitting the search request via Socket
      SocketService.emit(APIConfig.searchuser, {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'search_text': event.searchtext,
      });
      bool isCompleted = false;

      SocketService.response(
        APIConfig.searchuser,
        (response) {
          if (!isCompleted) {
            completer.complete(response);
            isCompleted = true;
          }
        },
      );

      // Await the response
      final response = await completer.future;
      if (response['data'] == null) {
        emit(state.copyWith(searchuserList: []));
      }
      final data = response['data'] as List<dynamic>;
      List<SearchUserData> allUsers = data.map((item) {
        if (item is Map<String, dynamic>) {
          return SearchUserData(
            userId: item['id'],
            name: item['name'],
            userName: item['username'],
            profileImage: item['profile'],
          );
        }
        throw Exception('Invalid data format');
      }).toList();
      final searchText = event.searchtext.toLowerCase();

      if (searchText.isEmpty) {
        // If the search text is empty, emit the full chat list
        emit(state.copyWith(searchuserList: []));
      } else {
        // Filter the users based on the search text
        // final filteredUsers = allUsers.where((user) {
        //   return user.userName?.toLowerCase().contains(searchText) ?? false;
        // }).toList();
        final filteredUsers = allUsers.where((user) {
          final name = user.name?.toLowerCase().trim() ?? '';
          final username = user.userName?.toLowerCase().trim() ?? '';
          return name.contains(searchText) || username.contains(searchText);
        }).toList();
        Logger.lOG(filteredUsers);
        // Emit the state with the filtered user list
        emit(state.copyWith(searchuserList: filteredUsers));
      }
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onClearSearchUserListEvent(ClearSearchUserListEvent event, Emitter<PostState> emit) async {
    try {
      emit(state.copyWith(searchuserList: []));
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(searchuserList: []));
    }
  }

  _getSearchLocationAPI(SearchLocationEvent event, Emitter<PostState> emit) async {
    emit(state.copyWith(searchLocationloading: true, isSearchLocationHasData: false));
    try {
      final results = event.searchtext.isNotEmpty
          ? await apiClient.searchLocation(search: event.searchtext)
          : await apiClient.searchLocation(lat: event.lat, long: event.long);
      if (results.status == true) {
        state.searchLocation.isNotEmpty ? state.searchLocation.clear() : null;
        emit(state.copyWith(
          isSearchLocationHasData: (results.data!.isEmpty) ? false : true,
          searchLocationloading: false,
          searchLocation: List.from(results.data ?? []),
        ));
        Logger.lOG(" _getSearchLocationAPI ${state.searchLocation}, ${state.isSearchLocationHasData}");
      } else {
        emit(state.copyWith(searchLocationloading: false, isSearchLocationHasData: false));
        Logger.lOG(" _getSearchLocationAPI No data found");
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(searchLocationloading: false, isSearchLocationHasData: false));
    }
  }

  _getPostIndustryDetails(GetPostIndustryDetailsEvent event, Emitter<PostState> emit) async {
    emit(state.copyWith(isGetIndustryLoading: true));
    try {
      final results = await apiClient.getPostIndustryDetails();
      if (results.status == true) {
        emit(state.copyWith(
            isGetIndustryLoading: false, postIndustryData: results.data ?? [], postIndustryModel: results));
        Logger.lOG(" _getPostIndustryDetails ${state.postIndustryData}");
      } else {
        emit(state.copyWith(isGetIndustryLoading: false));
        Logger.lOG(" _getPostIndustryDetails No data found");
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isGetIndustryLoading: false));
    }
  }

  _aIgenerateContentApi(AIgenerateContentEvent event, Emitter<PostState> emit) async {
    try {
      String loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? "";
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      emit(state.copyWith(isAIGeneratingloading: true));
      final results = state.isfirstTime == true
          ? await apiClient.generatePostContent(
              logInUserId: loginUserId, isSeekingGeneration: "0", media: event.media, brand: brandId.toString())
          : await apiClient.generatePostContent(
              logInUserId: loginUserId,
              prompt: event.prompt,
              isSeekingGeneration: event.prompt.isEmpty
                  ? "0"
                  : state.isfirstTime
                      ? "0"
                      : "1",
              media: event.media,
              brand: brandId.toString());
      if (results.status == true) {
        emit(state.copyWith(isAIGeneratingloading: false, aIgenerateContentModel: results, isfirstTime: true));
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isAIGeneratingloading: false));
    }
  }

  _clearAigeneratecontent(ClearAIGeneratedContentEvent event, Emitter<PostState> emit) {
    emit(state.copyWith(aIgenerateContentModel: null, isfirstTime: false));
  }

  // _aIgenerateDuplicatePostApi(AIgenerateDuplicatePostEvent event, Emitter<PostState> emit) async {
  //   try {
  //     String loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? "";
  //     int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

  //     emit(state.copyWith(isAIGeneratingDuplicatePostloading: true));
  //     final results = await apiClient.createDuplicatePost(
  //         logInUserId: loginUserId, prompt: event.prompt, media: event.media, brand: brandId.toString());
  //     if (results.status == true) {
  //       emit(state.copyWith(
  //         isAIGeneratingDuplicatePostloading: false,
  //         generateDuplicatePostModel: results,
  //       ));
  //     }
  //   } catch (e) {
  //     Logger.lOG(e.toString());
  //     emit(state.copyWith(isAIGeneratingDuplicatePostloading: false));
  //   }
  // }
  _aIgenerateDuplicatePostApi(AIgenerateDuplicatePostEvent event, Emitter<PostState> emit) async {
    try {
      String loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? "";
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      emit(state.copyWith(isAIGeneratingDuplicatePostloading: true));

      File? mediaFile;
      File? duplicatePostMedia;

      // URL string ne file ma convert karo
      if (event.media != "" && event.media.isNotEmpty) {
        mediaFile = await _downloadFileFromUrl(event.media);
      }

      final results = await apiClient.createDuplicatePost(
          logInUserId: loginUserId,
          prompt: event.prompt,
          media: mediaFile, // File object pass karo
          brand: brandId.toString());

      if (results.status == true) {
        // ignore: unnecessary_null_comparison
        if (results.generatedImagePath != null && results.generatedImagePath.isNotEmpty) {
          duplicatePostMedia = await _downloadFileFromUrl("${APIConfig.mainbaseURL}${results.generatedImagePath}");
          emit(state.copyWith(
            duplicatePostFile: duplicatePostMedia,
          ));
        }
        emit(state.copyWith(
          isAIGeneratingDuplicatePostloading: false,
          generateDuplicatePostModel: results,
        ));
      } else {
        NavigatorService.goBack();
        // Navigator.pop(event.context);
        toastification.show(
          type: ToastificationType.error,
          showProgressBar: false,
          title: Text(
            results.message,
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
      }

      // Temporary file delete karo (optional)
      if (mediaFile != null && await mediaFile.exists()) {
        await mediaFile.delete();
      }
    } catch (error) {
      NavigatorService.goBack();
      // Navigator.pop(event.context);
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
      // Logger.lOG(e.toString());
      emit(state.copyWith(isAIGeneratingDuplicatePostloading: false));
    }
  }

// URL thi file download karine convert karo
  Future<File?> _downloadFileFromUrl(String url) async {
    try {
      Logger.lOG('Downloading file from URL: $url');

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        // Temporary directory get karo
        Directory tempDir = await getTemporaryDirectory();
        String tempPath = tempDir.path;

        // File extension get karo URL thi
        String fileExtension = path.extension(url);
        if (fileExtension.isEmpty) {
          fileExtension = '.jpeg'; // Default extension
        }

        // Unique file name create karo
        String fileName = 'media_${DateTime.now().millisecondsSinceEpoch}$fileExtension';

        // File create karo
        File file = File('$tempPath/$fileName');
        await file.writeAsBytes(response.bodyBytes);

        Logger.lOG('File downloaded successfully: ${file.path}');
        return file;
      } else {
        Logger.lOG('Failed to download file. Status code: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      Logger.lOG('Error downloading file: $e');
      return null;
    }
  }
}
