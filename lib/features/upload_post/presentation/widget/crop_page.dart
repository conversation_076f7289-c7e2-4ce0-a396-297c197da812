// ignore_for_file: depend_on_referenced_packages
import 'package:flowkar/core/utils/exports.dart';
import 'package:fraction/fraction.dart';
import 'package:video_editor/video_editor.dart';

class CropPage extends StatelessWidget {
  const CropPage({super.key, required this.controller});

  final VideoEditorController controller;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.only(top: 55.h, left: 16.0.w, right: 16.0.w, bottom: 30.0.h),
        child: Column(children: [
          Row(children: [
            Expanded(
              child: IconButton(
                onPressed: () => controller.rotate90Degrees(RotateDirection.left),
                icon: const Icon(Icons.rotate_left),
              ),
            ),
            Expanded(
              child: IconButton(
                onPressed: () => controller.rotate90Degrees(RotateDirection.right),
                icon: const Icon(Icons.rotate_right),
              ),
            )
          ]),
          buildSizedBoxH(15),
          Expanded(
            child: CropGridViewer.edit(
              controller: controller,
              rotateCropArea: false,
              margin: const EdgeInsets.symmetric(horizontal: 20),
            ),
          ),
          buildSizedBoxH(15),
          Row(crossAxisAlignment: CrossAxisAlignment.end, children: [
            Expanded(
              child: AnimatedBuilder(
                animation: controller,
                builder: (_, __) => Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        IconButton(
                          onPressed: () => controller.preferredCropAspectRatio = controller.preferredCropAspectRatio?.toFraction().inverse().toDouble(),
                          icon: controller.preferredCropAspectRatio != null && controller.preferredCropAspectRatio! < 1
                              ? const Icon(Icons.panorama_vertical_select_rounded)
                              : const Icon(Icons.panorama_vertical_rounded),
                        ),
                        IconButton(
                          onPressed: () => controller.preferredCropAspectRatio = controller.preferredCropAspectRatio?.toFraction().inverse().toDouble(),
                          icon: controller.preferredCropAspectRatio != null && controller.preferredCropAspectRatio! > 1
                              ? const Icon(Icons.panorama_horizontal_select_rounded)
                              : const Icon(Icons.panorama_horizontal_rounded),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildCropButton(context, null),
                        _buildCropButton(context, 1.toFraction()),
                        _buildCropButton(context, Fraction.fromString("9/16")),
                        _buildCropButton(context, Fraction.fromString("3/4")),
                      ],
                    )
                  ],
                ),
              ),
            ),
          ]),
          buildSizedBoxH(15),
          _buildCancelAndDone(context)
        ]),
      ),
    );
  }

  Widget _buildCancelAndDone(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          onPressed: () => Navigator.pop(context),
          icon: Center(
            child: Text(Lang.of(context).lbl_Cancel, style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.primary)),
          ),
        ),
        IconButton(
          onPressed: () {
            // WAY 1: validate crop parameters set in the crop view
            controller.applyCacheCrop();
            // WAY 2: update manually with Offset values
            // controller.updateCrop(const Offset(0.2, 0.2), const Offset(0.8, 0.8));
            Navigator.pop(context);
          },
          icon: Center(
            child: Text("Done", style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.primary)),
          ),
        ),
      ],
    );
  }

  Widget _buildCropButton(BuildContext context, Fraction? f) {
    if (controller.preferredCropAspectRatio != null && controller.preferredCropAspectRatio! > 1) f = f?.inverse();

    return Flexible(
      child: TextButton(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor: controller.preferredCropAspectRatio == f?.toDouble() ? Colors.grey.shade800 : null,
          foregroundColor: controller.preferredCropAspectRatio == f?.toDouble() ? Colors.white : null,
          textStyle: Theme.of(context).textTheme.bodySmall,
        ),
        onPressed: () => controller.preferredCropAspectRatio = f?.toDouble(),
        child: Text(f == null ? 'free' : '${f.numerator}:${f.denominator}'),
      ),
    );
  }
}
