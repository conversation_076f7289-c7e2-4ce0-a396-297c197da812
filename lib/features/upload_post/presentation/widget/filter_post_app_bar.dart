import 'package:flowkar/core/utils/exports.dart';

class FilterPostAppbar extends StatelessWidget implements PreferredSizeWidget {
  final VoidCallback nextButton;
  const FilterPostAppbar({super.key, required this.nextButton});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themestate) {
        return AppBar(
          backgroundColor: Colors.transparent,
          elevation: 2,
          centerTitle: true,
          leading: Padding(
            padding: EdgeInsets.only(left: 16.0.w),
            child: InkWell(
              onTap: () => NavigatorService.goBack(result: true),
              child: Row(
                children: [
                  Icon(
                    Icons.close,
                    color: Theme.of(context).primaryColor,
                  )
                ],
              ),
            ),
          ),
          title: Text(
            "Filters",
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 16.0.sp, fontWeight: FontWeight.w700),
            textAlign: TextAlign.center,
          ),
          automaticallyImplyLeading: false,
          surfaceTintColor: Colors.transparent,
        );
      },
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
