import 'dart:io';

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';
import 'package:asset_picker/insta_assets_picker.dart' as asset;

class PickMediaWidget {
  bool _isNavigated = false;
  final List<String> _media = [];

  Future<void> pickImages(BuildContext context, int maxAssets, String title) async {
    await asset.FlowkarAssetPicker.pickAssets(context, useRootNavigator: true,
        onCompleted: (Stream<asset.InstaAssetsExportDetails> cropStream) {
      cropStream.listen((asset.InstaAssetsExportDetails details) async {
        // _media.clear();
        // for (var i = 0; i < details.data.length; i++) {
        //   if (details.data[i].croppedFile != null) {
        //     // If the cropped file exists, add its path
        //     _media.add(details.data[i].croppedFile!.path);
        //   } else {
        //     // Fetch the file asynchronously
        //     File? file = await details.data[i].selectedData.asset.file;
        //     Future.delayed(const Duration(seconds: 1));
        //     if (file != null) {
        //       _media.add(file.path);
        //       Logger.lOG("_media$i : $_media ");
        //     } else {
        //       Logger.lOG('Could not retrieve file for asset.');
        //     }
        //     // Check if the file is not null and add its path to the media list
        //   }
        // }

        _media.clear();
        for (var i = 0; i < details.data.length; i++) {
          String? filePath;

          if (details.data[i].croppedFile != null) {
            filePath = details.data[i].croppedFile!.path;
          } else {
            File? file = await details.data[i].selectedData.asset.file;
            if (file != null) {
              filePath = file.path;
            }
          }

          if (filePath != null && !_media.contains(filePath)) {
            _media.add(filePath);
            Logger.lOG("_media$i : $_media ");
          }
        }
      }).onDone(() async {
        Future.delayed(
          const Duration(milliseconds: 500),
          () async {
            if (!_isNavigated) {
              _isNavigated = true;
              final result = await NavigatorService.pushNamed(
                AppRoutes.videoandimageEditorScreen,
                arguments: _media,
              );
              if (result) {
                _isNavigated = false;
              }
            }
          },
        ).then(
          (value) {
            _media.clear();
          },
        );
      });
    },
        pickerConfig: asset.InstaAssetPickerConfig(
          pickerTheme: _buildPickerTheme(context),
          title: title,
          loadingIndicatorBuilder: (context, isAssetsEmpty) {
            return SizedBox(
              height: 60.h,
              width: 60.w,
              child: LoadingAnimationWidget(),
            );
          },
          actionsBuilder: (
            BuildContext context,
            ThemeData? pickerTheme,
            double height,
            VoidCallback unselectAll,
          ) =>
              [
            asset.InstaPickerCircleIconButton.unselectAll(
              onTap: unselectAll,
              theme: pickerTheme,
              size: height,
            ),
            buildSizedBoxW(8),
            asset.InstaPickerCircleIconButton(
              onTap: () async {
                Feedback.forTap(context);
                final AssetEntity? entity = await _pickFromWeChatCamera(context);
                if (entity == null) return;
                if (context.mounted) {
                  await asset.FlowkarAssetPicker.refreshAndSelectEntity(context, entity);
                }
              },
              theme: pickerTheme,
              icon: const Icon(Icons.camera_alt),
              size: height,
            ),
          ],
          specialItemBuilder: (context, _, __) {
            return ElevatedButton(
              onPressed: () async {
                Feedback.forTap(context);
                final AssetEntity? entity = await _pickFromWeChatCamera(context);
                if (entity == null) return;
                if (context.mounted) {
                  await asset.FlowkarAssetPicker.refreshAndSelectEntity(context, entity);
                }
              },
              style: ElevatedButton.styleFrom(
                shape: const RoundedRectangleBorder(),
                foregroundColor: Colors.white,
                backgroundColor: Colors.transparent,
              ),
              child: FittedBox(
                fit: BoxFit.cover,
                child: Text(
                  "Camera",
                  style: Theme.of(context).textTheme.bodySmall,
                  textAlign: TextAlign.center,
                ),
              ),
            );
          },
          specialItemPosition: asset.SpecialItemPosition.prepend,
        ),
        maxAssets: maxAssets,
        requestType: const asset.RequestType(3));
  }

  ThemeData _buildPickerTheme(
    BuildContext context,
  ) {
    return
        // themeState.isDarkThemeOn
        //     ? ThemeHelper.darkTheme.copyWith(
        //         typography: Typography.material2021(),
        //         brightness: Brightness.dark,
        //         canvasColor: AppColors.blackcolor,
        //         splashColor: Colors.black,
        //         colorScheme: Theme.of(context).colorScheme.copyWith(background: AppColors.blackcolor, secondary: AppColors.primaryColor),
        //         textTheme: _buildTextTheme(context, AppColors.whitecolor),
        //         elevatedButtonTheme: _buildElevatedButtonTheme(),
        //         appBarTheme: _buildAppBarTheme(AppColors.blackcolor, AppColors.whitecolor),
        //         textButtonTheme: _buildTextButtonTheme(
        //           AppColors.primaryColor,
        //         ),
        //       )
        //     :
        MyAppThemeHelper.lightTheme.copyWith(
      brightness: Brightness.light,
      canvasColor: Colors.white,
      splashColor: Colors.white,
      colorScheme:
          Theme.of(context).colorScheme.copyWith(background: Colors.black, secondary: Theme.of(context).primaryColor),
      textTheme: _buildTextTheme(context, Colors.white),
      elevatedButtonTheme: _buildElevatedButtonTheme(context),
      appBarTheme: _buildAppBarTheme(Colors.white, Colors.black),
      textButtonTheme: _buildTextButtonTheme(Theme.of(context).primaryColor),
    );
  }

  TextTheme _buildTextTheme(BuildContext context, Color color) {
    return Theme.of(context).textTheme.copyWith(
          bodySmall: GoogleFonts.montserrat(color: color, fontSize: 13.sp),
          bodyMedium: GoogleFonts.montserrat(color: color),
        );
  }

  ElevatedButtonThemeData _buildElevatedButtonTheme(BuildContext context) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        iconColor: Theme.of(context).customColors.white,
        backgroundColor: Theme.of(context).primaryColor,
      ),
    );
  }

  AppBarTheme _buildAppBarTheme(Color backgroundColor, Color iconColor) {
    return AppBarTheme(
      backgroundColor: backgroundColor,
      iconTheme: IconThemeData(color: iconColor),
      titleTextStyle: GoogleFonts.montserrat(
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
        color: iconColor,
      ),
    );
  }

  TextButtonThemeData _buildTextButtonTheme(Color color) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        textStyle: GoogleFonts.montserrat(
          fontSize: 13.sp,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  Future<AssetEntity?> _pickFromWeChatCamera(BuildContext context) => CameraPicker.pickFromCamera(context,
      locale: Localizations.maybeLocaleOf(context),
      pickerConfig: CameraPickerConfig(
        enableRecording: true,
        enableScaledPreview: true,
        theme: MyAppThemeHelper.lightTheme.copyWith(
          buttonTheme: ButtonThemeData(
            buttonColor: Theme.of(context).primaryColor,
            textTheme: ButtonTextTheme.normal,
          ),
        ),
      ));
}
