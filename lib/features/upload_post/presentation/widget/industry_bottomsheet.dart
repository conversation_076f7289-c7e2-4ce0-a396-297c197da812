import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/upload_post/bloc/post_bloc.dart';

class IndustrySearchBottomSheet extends StatefulWidget {
  final TextEditingController searchController;

  const IndustrySearchBottomSheet({super.key, required this.searchController});

  @override
  State<IndustrySearchBottomSheet> createState() => _IndustrySearchBottomSheetState();
}

class _IndustrySearchBottomSheetState extends State<IndustrySearchBottomSheet> {
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PostBloc, PostState>(
      builder: (context, state) {
        var filteredIndustries = widget.searchController.text.isEmpty
            ? state.postIndustryData
            : state.postIndustryData
                .where((industry) =>
                    industry.name.toString().toLowerCase().contains(widget.searchController.text.toLowerCase()))
                .toList();

        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20.0.r)),
          ),
          padding: EdgeInsets.only(top: 16.h, left: 12.w, right: 12.w),
          child: Column(
            children: [
              buildSizedBoxH(16.0),
              Row(
                children: [
                  buildSizedBoxW(2),
                  InkWell(
                    onTap: () {
                      _focusNode.unfocus();
                      Navigator.of(context).pop();
                    },
                    child: CustomImageView(
                      margin: EdgeInsets.all(5),
                      imagePath: AssetConstants.pngBack,
                      height: 18.0.h,
                    ),
                  ),
                  buildSizedBoxW(18.0),
                  Text(
                    'Industries',
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge
                        ?.copyWith(fontSize: 16.5.sp, fontWeight: FontWeight.w700),
                  ),
                  Spacer(),
                  CustomElevatedButton(
                    text: "Done",
                    height: 30.h,
                    width: 80.w,
                    onPressed: () {
                      _focusNode.unfocus();
                      if (widget.searchController.text.isNotEmpty) {
                        final selectedIndustry = state.postIndustryData.firstWhere(
                          (industry) => industry.name.toString() == widget.searchController.text,
                        );

                        Navigator.of(context).pop({
                          'name': selectedIndustry.name,
                          'id': selectedIndustry.id,
                        });
                      } else {
                        Navigator.of(context).pop();
                      }
                    },
                  ),
                ],
              ),
              buildSizedBoxH(20.0),
              TextFormField(
                controller: widget.searchController,
                focusNode: _focusNode,
                onChanged: (value) {
                  setState(() {});
                },
                decoration: InputDecoration(
                  hintText: "Search Industry",
                  fillColor: ThemeData().customColors.fillcolor,
                  filled: true,
                  contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(10.r)),
                    borderSide: BorderSide.none,
                  ),
                  prefixIcon: CustomImageView(
                    imagePath: AssetConstants.icSearch,
                    height: 10,
                    width: 10,
                    margin: EdgeInsets.all(15.0),
                  ),
                  suffixIcon: widget.searchController.text.isNotEmpty
                      ? IconButton(
                          icon: CustomImageView(
                            imagePath: AssetConstants.icClose,
                            height: 10.0.h,
                            width: 10.0.w,
                          ),
                          onPressed: () {
                            widget.searchController.clear();
                            setState(() {});
                            _focusNode.requestFocus();
                          },
                        )
                      : null,
                ),
              ),
              buildSizedBoxH(20.0),
              Expanded(
                child: _buildIndustryList(context, state, filteredIndustries, widget.searchController),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildIndustryList(
      BuildContext context, PostState state, List<dynamic> industries, TextEditingController controller) {
    if (state.isGetIndustryLoading) {
      return Center(child: LoadingAnimationWidget());
    }

    if (state.postIndustryData.isEmpty) {
      return ExceptionWidget(
        imagePath: AssetConstants.pngNoResultFound,
        title: "Please input valid industry",
        subtitle: "",
        showButton: false,
      );
    }

    if (industries.isEmpty) {
      return ExceptionWidget(
        imagePath: AssetConstants.pngNoResultFound,
        title: "No matching industries found",
        subtitle: "Try a different search term",
        showButton: false,
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      itemCount: industries.length,
      itemBuilder: (context, index) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            InkWell(
              onTap: () {
                controller.text = industries[index].name.toString();

                Navigator.of(context).pop({
                  'name': industries[index].name,
                  'id': industries[index].id,
                });
              },
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 8.h),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    industries[index].name.toString(),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: 15.sp,
                          color: Theme.of(context).customColors.black,
                        ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ),
            Divider(color: Colors.grey.shade300),
          ],
        );
      },
    );
  }
}

Future<Map<String, dynamic>?> showIndustryBottomSheet(
    BuildContext context, TextEditingController searchController) async {
  final result = await showModalBottomSheet<Map<String, dynamic>>(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => SizedBox(
      height: MediaQuery.of(context).size.height * 0.8,
      child: IndustrySearchBottomSheet(searchController: searchController),
    ),
  );
  return result;
}

Widget buildIndustryBottomSheet(TextEditingController searchIndustryController) {
  return IndustrySearchBottomSheet(searchController: searchIndustryController);
}
