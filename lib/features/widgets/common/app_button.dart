import 'package:flowkar/core/utils/exports.dart';
import 'package:flutter/cupertino.dart';

class CustomElevatedButton extends StatelessWidget {
  const CustomElevatedButton(
      {super.key,
      this.decoration,
      this.leftIcon,
      this.rightIcon,
      this.margin,
      this.onPressed,
      this.buttonStyle,
      this.alignment,
      this.buttonTextStyle,
      this.isDisabled = false,
      this.height,
      this.width,
      this.iconSpacing,
      this.isLoading = false,
      this.color,
      required this.text,
      this.fontSize,
      this.brderRadius,
      this.mainAxisAlignment,
      this.crossAxisAlignment});

  final BoxDecoration? decoration;
  final Widget? leftIcon;
  final Widget? rightIcon;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onPressed;
  final ButtonStyle? buttonStyle;
  final Alignment? alignment;
  final TextStyle? buttonTextStyle;
  final bool isDisabled;
  final double? height;
  final double? width;
  final double? iconSpacing;
  final bool isLoading;
  final String text;
  final Color? color;
  final double? fontSize;
  final double? brderRadius;
  final MainAxisAlignment? mainAxisAlignment;
  final CrossAxisAlignment? crossAxisAlignment;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 55.h,
      width: width ?? MediaQuery.of(context).size.width * 0.5,
      margin: margin,
      decoration: decoration,
      child: ElevatedButton(
        style: buttonStyle ??
            ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(brderRadius ?? 16.0.r),
              ),
              padding: EdgeInsets.zero,
              textStyle: Theme.of(context).textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w700),
            ),
        onPressed: isDisabled
            ? null
            : () {
                HapticFeedback.lightImpact();
                if (onPressed != null) onPressed!();
              },
        child: isLoading
            ? CupertinoActivityIndicator(color: color)
            : Row(
                mainAxisAlignment: mainAxisAlignment ?? MainAxisAlignment.center,
                crossAxisAlignment: crossAxisAlignment ?? CrossAxisAlignment.center,
                children: [
                  if (leftIcon != null) leftIcon!,
                  if (leftIcon != null && iconSpacing != null) SizedBox(width: iconSpacing),
                  Text(
                    text,
                    style: buttonTextStyle ??
                        Theme.of(context).textTheme.labelLarge?.copyWith(
                              fontWeight: FontWeight.w700,
                              fontSize: fontSize ?? 16.0.sp,
                              color: isDisabled
                                  ? Theme.of(context).disabledColor
                                  : Theme.of(context).colorScheme.onPrimary,
                            ),
                  ),
                  if (rightIcon != null && iconSpacing != null) SizedBox(width: iconSpacing),
                  if (rightIcon != null) rightIcon!,
                ],
              ),
      ),
    );
  }
}
