import 'package:flowkar/core/utils/exports.dart';

class BackgroundImage extends StatelessWidget {
  final Widget child;
  final String imagePath;

  const BackgroundImage(
      {super.key, required this.child, required this.imagePath});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return Stack(
          children: [
            CustomImageView(
              imagePath: imagePath,
              fit: BoxFit.cover,
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
            ),
            child,
          ],
        );
      },
    );
  }
}
