import 'package:flowkar/core/utils/exports.dart';

class ComingSoonScreen extends StatelessWidget {
  final String title;
  final String? subtitle;
  const ComingSoonScreen({
    super.key,
    required this.title,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildUploadPostAppBar(context),
      body: Center(
        child: ExceptionWidget(
          imagePath: Assets.images.pngs.exception.pngComingSoon.path,
          showButton: false,
          title: 'Exciting things are on the way!',
          subtitle: """Stay tuned for updates.""",
        ),
      ),
    );
  }

  PreferredSizeWidget _buildUploadPostAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            PersistentNavBarNavigator.pop(context);
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }
}
