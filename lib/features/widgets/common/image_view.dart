import 'dart:io';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';

class CustomImageView extends StatefulWidget {
  ///[imagePath] is required parameter for showing image

  final String? imagePath;
  final double? height;
  final double? width;
  final Color? color;
  final BoxFit? fit;
  final Alignment? alignment;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? radius;
  final BoxBorder? border;
  final BlendMode? colorBlendMode;
  final Function(BuildContext, String)? placeholder;
  final bool enableRetry;
  final VoidCallback? onRetry;
  final String? fallbackImage;

  const CustomImageView(
      {super.key,
      this.imagePath,
      this.height,
      this.width,
      this.color,
      this.fit,
      this.alignment,
      this.onTap,
      this.radius,
      this.margin,
      this.border,
      this.colorBlendMode,
      this.placeholder,
      this.enableRetry = true,
      this.onRetry,
      this.fallbackImage});

  @override
  State<CustomImageView> createState() => _CustomImageViewState();
}

class _CustomImageViewState extends State<CustomImageView> {
  String? _retryKey;
  bool _hasError = false;

  void _handleRetry() {
    setState(() {
      _retryKey = DateTime.now().millisecondsSinceEpoch.toString();
      _hasError = false;
    });
    if (widget.onRetry != null) {
      widget.onRetry!();
    }
  }

  void _onImageError() {
    // Use post-frame callback to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _hasError = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return widget.alignment != null
        ? Align(
            alignment: widget.alignment!,
            child: _buildWidget(context),
          )
        : _buildWidget(context);
  }

  Widget _buildWidget(BuildContext context) {
    return Padding(
      padding: widget.margin ?? EdgeInsets.zero,
      child: InkWell(
        onTap: widget.onTap,
        child: _buildCircleImage(),
      ),
    );
  }

  _buildCircleImage() {
    if (widget.radius != null) {
      return ClipRRect(
        borderRadius: widget.radius ?? BorderRadius.zero,
        child: _buildImageWithBorder(),
      );
    } else {
      return _buildImageWithBorder();
    }
  }

  _buildImageWithBorder() {
    if (widget.border != null) {
      return Container(
        decoration: BoxDecoration(
          border: widget.border,
          borderRadius: widget.radius,
        ),
        child: _buildImageView(),
      );
    } else {
      return _buildImageView();
    }
  }

  Widget _buildImageView() {
    if (widget.imagePath != null) {
      Widget imageWidget;

      switch (widget.imagePath!.imageType) {
        case ImageType.svg:
          imageWidget = SizedBox(
            height: widget.height,
            width: widget.width,
            child: SvgPicture.asset(
              widget.imagePath!,
              height: widget.height,
              width: widget.width,
              fit: widget.fit ?? BoxFit.contain,
              color: widget.color,
            ),
          );
          break;
        case ImageType.file:
          imageWidget = Image.file(
            File(widget.imagePath!),
            height: widget.height,
            width: widget.width,
            fit: widget.fit ?? BoxFit.cover,
            color: widget.color,
          );
          break;
        case ImageType.network:
          imageWidget = BlocListener<ConnectivityBloc, ConnectivityState>(
            listener: (context, connectivityState) {
              // Auto-retry when network is restored and image had error
              if (connectivityState.isReconnected && _hasError) {
                _handleRetry();
              }
            },
            child: CachedNetworkImage(
                key: _retryKey != null ? ValueKey(_retryKey) : null,
                height: widget.height,
                width: widget.width,
                fit: widget.fit,
                imageUrl: widget.imagePath!,
                filterQuality: FilterQuality.high,
                color: widget.color,
                repeat: ImageRepeat.noRepeat,
                placeholderFadeInDuration: const Duration(milliseconds: 300),
                fadeInDuration: const Duration(milliseconds: 300),
                fadeOutDuration: const Duration(milliseconds: 300),
                colorBlendMode: BlendMode.overlay,
                fadeOutCurve: Curves.bounceOut,
                placeholder: (context, url) => widget.placeholder != null
                    ? widget.placeholder!(context, url)
                    : Shimmer.fromColors(
                        baseColor: Colors.grey.shade200,
                        highlightColor: Colors.grey.shade50,
                        child: Container(
                          // height: 461.h,
                          width: double.infinity,
                          decoration: BoxDecoration(color: Colors.grey.shade200),
                        ),
                      ),
                cacheManager: CacheManager(
                  Config(
                    "flowkar",
                    stalePeriod: const Duration(days: 1),
                    maxNrOfCacheObjects: 1000,
                  ),
                ),
                imageBuilder: (context, imageProvider) {
                  // Reset error state when image loads successfully
                  if (_hasError) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        setState(() {
                          _hasError = false;
                        });
                      }
                    });
                  }
                  return Image(
                    image: imageProvider,
                    height: widget.height,
                    width: widget.width,
                    fit: widget.fit,
                    color: widget.color,
                  );
                },
                errorWidget: (context, url, error) {
                  _onImageError();
                  return _buildErrorWidget(context, url, error);
                }),
          );
          break;
        case ImageType.lottie:
          imageWidget = Lottie.asset(
            widget.imagePath!,
            height: widget.height,
            width: widget.width,
            fit: widget.fit ?? BoxFit.cover,
          );
          break;
        case ImageType.png:
        default:
          imageWidget = Image.asset(
            widget.imagePath!,
            height: widget.height,
            width: widget.width,
            fit: widget.fit ?? BoxFit.cover,
            color: widget.color,
            filterQuality: FilterQuality.high,
            gaplessPlayback: true,
            alignment: Alignment.center,
          );
          break;
      }

      // Check if imagePath is AssetConstants.pngUserReomve and wrap with Container
      // if (widget.imagePath == AssetConstants.pngUserReomve) {
      //   return Container(
      //     decoration: BoxDecoration(
      //       borderRadius: BorderRadius.circular(100.r),
      //       border: Border.all(
      //         color: Theme.of(context).primaryColor.withOpacity(0.05),
      //         width: 2.w,
      //       ),
      //     ),
      //     child: imageWidget,
      //   );
      // }

      return imageWidget;
    }
    return const SizedBox.shrink();
  }

  Widget _buildErrorWidget(BuildContext context, String url, dynamic error) {
    // If fallback image is provided, use it instead of retry UI
    if (widget.fallbackImage != null) {
      return CustomImageView(
        imagePath: widget.fallbackImage,
        height: widget.height,
        width: widget.width,
        fit: widget.fit,
        radius: widget.radius,
        enableRetry: false, // Prevent infinite recursion
      );
    }

    if (!widget.enableRetry) {
      return Assets.images.pngs.pngNotFound.image(
        height: widget.height,
        width: widget.width,
        fit: widget.fit ?? BoxFit.cover,
      );
    }

    return BlocBuilder<ConnectivityBloc, ConnectivityState>(
      builder: (context, connectivityState) {
        // Show loading indicator when network is restored and auto-retrying
        return GestureDetector(
          onTap: _handleRetry,
          child: Container(
            height: widget.height,
            width: widget.width,
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: widget.radius,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.refresh_rounded,
                  size: 32.sp,
                  color: Colors.grey.shade600,
                ),
                buildSizedBoxH(8.h),
                Text(
                  Lang.of(context).lbl_couldnt_load_image_tap_retry,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade600,
                        fontSize: 12.sp,
                      ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

extension ImageTypeExtension on String {
  ImageType get imageType {
    if (startsWith('http') || startsWith('https')) {
      return ImageType.network;
    } else if (endsWith('.svg')) {
      return ImageType.svg;
    } else if (endsWith('.json')) {
      return ImageType.lottie;
    } else if (startsWith('/data') || startsWith('/storage')) {
      return ImageType.file;
    } else {
      return ImageType.png;
    }
  }
}

enum ImageType { svg, png, network, file, lottie, unknown }
