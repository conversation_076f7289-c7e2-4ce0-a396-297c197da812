// ignore_for_file: deprecated_member_use

import 'dart:async';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/home_feed_screen/model/video_response_model.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/comment_bottom_sheet_widget.dart';
import 'package:flowkar/features/survey_form/bloc/survey_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class GetUserVideoByIdWidget extends StatefulWidget {
  final List<VideoData>? userByIdVideo;
  final int? initialIndex;
  final int? userId;

  const GetUserVideoByIdWidget({super.key, this.userByIdVideo, this.initialIndex, this.userId});

  static Widget builder(BuildContext context) {
    return const GetUserVideoByIdWidget();
  }

  @override
  State<GetUserVideoByIdWidget> createState() => _GetUserVideoByIdWidgetState();
}

class _GetUserVideoByIdWidgetState extends State<GetUserVideoByIdWidget> with SingleTickerProviderStateMixin {
  late ItemScrollController _itemScrollController;
  late ItemPositionsListener _itemPositionsListener;
  int _initialIndex = 0;
  final int _threshold = 5;
  Timer? _debounceTimer;
  bool _isLoadingMore = false;
  final Map<int, Widget> _videoCache = {};

  @override
  void initState() {
    super.initState();
    context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
    _itemScrollController = ItemScrollController();
    _itemPositionsListener = ItemPositionsListener.create();
    _initialIndex = widget.initialIndex ?? 0;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_initialIndex >= 0 && _initialIndex < (widget.userByIdVideo?.length ?? 0)) {
        _scrollToInitialIndex();
      }
    });
    _itemPositionsListener.itemPositions.addListener(_scrollListener);
  }

  void _scrollToInitialIndex() {
    _itemScrollController.jumpTo(
      index: _initialIndex,
    );
  }

  Future<void> _refreshFeed() async {
    if (!mounted) return;

    final state = context.read<HomeFeedBloc>().state;
    if (state.getProfileByIDvideo.isNotEmpty) {
      state.getProfileByIDvideo.clear();
    }
    _videoCache.clear();
    context.read<HomeFeedBloc>().add(GetUserVideoByIdApiEvent(page: 1, userId: widget.userId));
  }

  void _scrollListener() {
    if (_debounceTimer?.isActive ?? false) return;

    _debounceTimer = Timer(const Duration(milliseconds: 200), () {
      if (!mounted) return;

      final visibleItems = _itemPositionsListener.itemPositions.value;
      if (visibleItems.isNotEmpty) {
        final lastVisibleIndex = visibleItems.last.index;

        if (lastVisibleIndex >= (widget.userByIdVideo?.length ?? 0) - _threshold) {
          final state = context.read<HomeFeedBloc>().state;
          if (state.getProfilevideoByIDResponseModel?.next == null) {
            return;
          } else {
            if (!state.getProfileisVideoByIDLoadingMore && !_isLoadingMore) {
              _isLoadingMore = true;
              context
                  .read<HomeFeedBloc>()
                  .add(GetUserVideoByIdApiEvent(page: state.getProfilevideoByIDPage + 1, userId: widget.userId));
            }
          }
        }
      }
    });
  }

  Widget _buildVideoItem(VideoData video, int index) {
    if (_videoCache.containsKey(video.id)) {
      return _videoCache[video.id]!;
    }

    final widget = RepaintBoundary(
      child: PostWidget(
        key: ValueKey('video_${video.id}'),
        width: video.width,
        height: video.height,
        userByIDvideo: true,
        userByIDpost: false,
        userVideo: false,
        userpost: false,
        taggedIn: video.taggedIn,
        state: context.read<HomeFeedBloc>().state,
        latestcomments: video.latestComment.toString(),
        index: index,
        userId: video.user.userId,
        postId: video.id,
        profileImage: video.user.profileImage,
        name: video.user.name,
        username: video.user.username,
        postMedia: video.files,
        thumbnailImage: video.thumbnailFiles.isNotEmpty ? video.thumbnailFiles : [],
        title: video.title == "''" || video.title.isEmpty ? '' : video.title,
        caption:
            "${video.title == "''" || video.title.isEmpty ? '' : video.title}${video.description == '' || video.description.isEmpty ? '' : video.title == "''" || video.title.isEmpty ? video.description : "\n${video.description}"}",
        likes: video.likes.toString(),
        comments: video.commentsCount.toString(),
        postTime: video.createdAt,
        isLiked: video.isLiked,
        isSaved: video.isSaved,
        screenType: "User Profile",
        doubleTap: () {
          if (video.isLiked == false) {
            context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: video.id));
          }
        },
        likeonTap: () {
          context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: video.id));
        },
        commentonTap: () {
          showModalBottomSheet(
            context: context,
            useRootNavigator: true,
            isScrollControlled: true,
            builder: (context) => CommentsBottomSheet(postId: video.id),
          );
        },
        shareonTap: () {},
        saveonTap: () {},
      ),
    );

    _videoCache[video.id] = widget;
    return widget;
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _itemPositionsListener.itemPositions.removeListener(_scrollListener);
    _videoCache.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        hasLeadingIcon: true,
        height: 18.h,
        leading: [
          InkWell(
            onTap: () {
              context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
              FocusScope.of(context).unfocus();
              NavigatorService.goBack();
            },
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: CustomImageView(
                imagePath: Assets.images.svg.authentication.icBackArrow.path,
                height: 16.h,
              ),
            ),
          ),
          buildSizedBoxW(20.w),
          Text(
            Lang.of(context).lbl_post,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
          ),
        ],
      ),
      body: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themestate) {
          return BlocBuilder<HomeFeedBloc, HomeFeedState>(
            builder: (context, state) {
              if (state.getProfileVideoByIDLoading) {
                return const HomeFeedShimmer();
              }
              return Scaffold(
                body: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(child: _builduserByIdVideo(state, themestate)),
                    BlocBuilder<ConnectivityBloc, ConnectivityState>(
                      builder: (context, connectivityState) {
                        return Visibility(
                          visible: state.getProfileisVideoByIDLoadingMore && connectivityState.isConnected,
                          child: SizedBox(
                            height: 50.h,
                            child:
                                Center(child: CupertinoActivityIndicator(color: Theme.of(context).colorScheme.primary)),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _builduserByIdVideo(HomeFeedState state, ThemeState themestate) {
    return LiquidPullToRefresh(
      color: Theme.of(context).primaryColor.withOpacity(0.5),
      showChildOpacityTransition: false,
      backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      onRefresh: _refreshFeed,
      child: BlocBuilder<HomeFeedBloc, HomeFeedState>(
        builder: (context, homeFeedState) {
          return BlocBuilder<ConnectivityBloc, ConnectivityState>(
            builder: (context, connectivityState) {
              if (!connectivityState.isConnected && homeFeedState.isuserProfileposts.isEmpty) {
                return const HomeFeedShimmer();
              } else if (homeFeedState.profilPosteByIdLoading) {
                return const HomeFeedShimmer();
              } else if (homeFeedState.isuserProfileposts.isEmpty) {
                return ListView(
                  physics: AlwaysScrollableScrollPhysics(),
                  children: [
                    buildSizedBoxH(MediaQuery.of(context).size.height / 6),
                    ExceptionWidget(
                      imagePath: Assets.images.svg.exception.svgNodatafound.path,
                      showButton: false,
                      title: Lang.of(context).lbl_no_data_found,
                      subtitle: Lang.of(context).lbl_no_post,
                    ),
                  ],
                );
              } else {
                return ScrollablePositionedList.builder(
                  padding: EdgeInsets.zero,
                  itemCount: homeFeedState.getProfileByIDvideo.length,
                  itemScrollController: _itemScrollController,
                  itemPositionsListener: _itemPositionsListener,
                  physics: const AlwaysScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    final postIndex = index;
                    final post = homeFeedState.getProfileByIDvideo[postIndex];
                    return Padding(
                      padding: EdgeInsets.only(
                          bottom: homeFeedState.getProfileByIDvideo[postIndex] == homeFeedState.getProfileByIDvideo.last
                              ? 30.h
                              : 0),
                      child: _buildVideoItem(post, postIndex),
                    );
                  },
                );
              }
            },
          );
        },
      ),
    );
  }
}
