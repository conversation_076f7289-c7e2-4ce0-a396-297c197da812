import 'package:flowkar/core/utils/exports.dart';

class FlowkarTextForm<PERSON>ield extends StatelessWidget {
  const FlowkarTextFormField({
    super.key,
    required this.context,
    this.validator,
    this.width,
    this.alignment,
    this.textStyle,
    this.isObscure = false,
    this.isCapitalized = false,
    this.maxLines = 1,
    this.minLines,
    this.isLabelEnabled = true,
    this.readOnly = false,
    this.controller,
    this.inputAction,
    this.focusNode,
    this.label,
    this.hint = "",
    this.onTap,
    this.prefix,
    this.maxLength,
    this.suffixIcon,
    this.autofillHints,
    this.inputFormatters,
    this.prefixIcon,
    this.onFieldSubmitted,
    this.onChanged,
    this.textInputAction,
    this.textInputType,
    this.hintStyle,
    this.hintText,
    this.obscureText = false,
    this.suffix,
    this.fillColor,
    this.filled = false,
    this.borderDecoration,
    this.enabled = true,
    this.labelText,
    this.required = false,
    this.autovalidateMode,
    this.onSaved,
    this.lableStyle,
    this.initialValue,
    this.contentPadding,
    this.height,
    this.counter,
    this.buildCounter,
  });

  final BuildContext context;
  final Alignment? alignment;
  final double? width;
  final double? height;
  final TextInputAction? inputAction;
  final TextEditingController? controller;
  final String? label;
  final String hint;
  final bool isObscure;
  final bool isCapitalized;
  final TextStyle? textStyle;
  final int maxLines;
  final int? minLines;
  final int? maxLength;
  final ValueChanged<String>? onChanged;
  final bool obscureText;
  final bool isLabelEnabled;
  final String? Function(String?)? validator;
  final Function(String)? onFieldSubmitted;
  final bool readOnly;
  final Widget? suffixIcon;
  final String? hintText;
  final TextStyle? lableStyle;
  final TextStyle? hintStyle;
  final Widget? prefix;
  final Widget? prefixIcon;
  final void Function()? onTap;
  final Iterable<String>? autofillHints;
  final FocusNode? focusNode;
  final List<TextInputFormatter>? inputFormatters;
  final TextInputAction? textInputAction;
  final TextInputType? textInputType;
  final Widget? suffix;
  final Color? fillColor;
  final bool? filled;
  final InputBorder? borderDecoration;
  final bool? enabled;
  final String? labelText;
  final bool? required;
  final AutovalidateMode? autovalidateMode;
  final String? initialValue;
  final void Function(String?)? onSaved;
  final EdgeInsetsGeometry? contentPadding;
  final Widget? counter;
  final InputCounterWidgetBuilder? buildCounter;

  @override
  Widget build(BuildContext context) {
    return alignment != null
        ? Align(
            alignment: alignment ?? Alignment.center,
            child: textFormFieldWidget,
          )
        : textFormFieldWidget;
  }

  Widget get textFormFieldWidget => SizedBox(
      width: width ?? double.maxFinite,
      height: height,
      child: TextFormField(
          enabled: enabled,
          onSaved: onSaved,
          initialValue: initialValue,
          onChanged: onChanged,
          obscureText: obscureText,
          inputFormatters: inputFormatters,
          autofillHints: autofillHints,
          onTap: onTap,
          maxLines: maxLines,
          minLines: minLines,
          maxLength: maxLength,
          onFieldSubmitted: onFieldSubmitted,
          focusNode: focusNode,
          controller: controller,
          readOnly: readOnly,
          style: textStyle ??
              Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.copyWith(fontSize: 16.5.sp, color: Theme.of(context).customColors.black),
          textCapitalization: isCapitalized ? TextCapitalization.words : TextCapitalization.none,
          textInputAction: textInputAction,
          keyboardType: textInputType,
          decoration: decoration,
          validator: validator,
          enableSuggestions: true,
          cursorOpacityAnimates: true,
          autovalidateMode: autovalidateMode ?? AutovalidateMode.disabled,
          buildCounter: buildCounter ??
              (BuildContext context, {required int currentLength, required bool isFocused, required int? maxLength}) =>
                  counter ?? const Offstage()));

  InputDecoration get decoration => InputDecoration(
        labelText: labelText,
        labelStyle: lableStyle ??
            Theme.of(context)
                .textTheme
                .bodyMedium
                ?.copyWith(fontSize: 16.5.sp, color: Theme.of(context).customColors.black),
        alignLabelWithHint: true,
        contentPadding: contentPadding,
        hintText: hintText ?? hint,
        hintStyle: hintStyle ??
            Theme.of(context)
                .textTheme
                .bodyMedium
                ?.copyWith(fontSize: 16.5.sp, color: Theme.of(context).customColors.black),
        prefixIcon: prefixIcon,
        suffixIcon: suffixIcon,
        prefix: prefix,
        suffix: suffix,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        isDense: true,
        fillColor: fillColor ?? Theme.of(context).customColors.white,
        filled: filled ?? false,
        counter: counter ?? const Offstage(),
        border: borderDecoration,
        enabledBorder: borderDecoration,
        focusedBorder: borderDecoration,
      );
}
