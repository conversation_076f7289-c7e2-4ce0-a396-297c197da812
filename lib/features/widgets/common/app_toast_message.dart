import 'package:flutter/material.dart';

class CustomToastMessageWidget extends StatelessWidget {
  final String message;
  final Color backgroundColor;
  final Duration duration;
  final TextStyle textStyle;

  const CustomToastMessageWidget({
    super.key,
    required this.message,
    this.backgroundColor = Colors.black,
    this.duration = const Duration(seconds: 2),
    this.textStyle = const TextStyle(color: Colors.white, fontSize: 16),
  });

  @override
  Widget build(BuildContext context) {
    Future.delayed(duration, () {
      Navigator.of(context).pop();
    });

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              message,
              style: textStyle,
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }

  static void showToast(BuildContext context, String message,
      {Color backgroundColor = Colors.black,
      Duration duration = const Duration(seconds: 2),
      TextStyle textStyle =
          const TextStyle(color: Colors.white, fontSize: 16)}) {
    showDialog(
      barrierDismissible: true,
      context: context,
      builder: (BuildContext context) {
        return CustomToastMessageWidget(
          message: message,
          backgroundColor: backgroundColor,
          duration: duration,
          textStyle: textStyle,
        );
      },
    );
  }
}
