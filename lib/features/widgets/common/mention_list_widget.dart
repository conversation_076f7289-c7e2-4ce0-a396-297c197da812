import 'package:detectable_text_field/widgets/detectable_text_editing_controller.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/discover/model/search_user_model.dart';

class MentionUserListWidget extends StatefulWidget {
  final List<SearchUserData> userList;
  final DetectableTextEditingController? descriptionController;

  final Function(List<Map<String, String>> mentionedUsers) onMentionAdded;

  const MentionUserListWidget({
    super.key,
    this.descriptionController,
    this.userList = const [],
    required this.onMentionAdded,
  });

  @override
  State<MentionUserListWidget> createState() => _MentionUserListWidgetState();
}

class _MentionUserListWidgetState extends State<MentionUserListWidget> {
  List<Map<String, String>> mentionedUsers = [];

  @override
  void initState() {
    super.initState();
    widget.descriptionController?.addListener(_onTextChanged);
  }

  void _onTextChanged() {
    // Loop through added users and check if changes made in their names
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themestate) {
        return _buildUserList(themestate, widget.userList);
      },
    );
  }

  Widget _buildUserList(
    ThemeState themestate,
    List<SearchUserData> userList,
  ) {
    return ListView.builder(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      shrinkWrap: true,
      scrollDirection: Axis.vertical,
      physics: ScrollPhysics(),
      itemCount: userList.length,
      itemBuilder: (context, index) {
        return _buildUserDetail(context, themestate, index, userList);
      },
    );
  }

  Widget _buildUserDetail(BuildContext context, ThemeState themestate, int index, List<SearchUserData> userList) {
    String currentText = widget.descriptionController?.text ?? '';

    // Check if the username is already mentioned
    bool isMentioned = currentText.contains('@${userList[index].userName}');

    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Row(
        children: [
          Container(
            height: 48.0.h,
            width: 48.0.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(100.r),
              border: Border.all(
                color: isMentioned ? Colors.grey.shade200 : Theme.of(context).textTheme.titleSmall!.color!,
                width: 2.w,
              ),
            ),
            child: ClipRRect(
              clipBehavior: Clip.hardEdge,
              child: Padding(
                padding: const EdgeInsets.all(2.0),
                child: CustomImageView(
                  radius: BorderRadius.circular(100.r),
                  height: 48.0.h,
                  width: 48.0.w,
                  fit: BoxFit.cover,
                  imagePath: userList[index].profileImage == null || userList[index].profileImage == ""
                      ? Assets.images.pngs.other.pngPlaceholder.path
                      : "${APIConfig.mainbaseURL}${userList[index].profileImage}",
                  alignment: Alignment.center,
                ),
              ),
            ),
          ),
          buildSizedBoxW(8),
          Flexible(
            child: InkWell(
              onTap: isMentioned
                  ? () {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        toastification.show(
                          type: ToastificationType.warning,
                          showProgressBar: false,
                          title: Text(
                            'Looks like this user is already part of the post!',
                            style: GoogleFonts.montserrat(
                              fontSize: 12.0.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          autoCloseDuration: const Duration(seconds: 3),
                        );
                      });
                    }
                  : () {
                      // Update currentText with '@' prefix
                      String mentionedUsername = '@${userList[index].userName} ';
                      if (currentText.contains('@')) {
                        // Find the last '@' and replace with the selected username
                        int atIndex = currentText.lastIndexOf('@');
                        currentText = '${currentText.substring(0, atIndex)}$mentionedUsername';
                      } else {
                        currentText += mentionedUsername;
                      }

                      widget.descriptionController?.text = currentText.trimRight();

                      // Create a map for the mentioned user
                      Map<String, String> userMap = {
                        "user_name": '@${userList[index].userName}',
                        "user_id": userList[index].userId.toString(),
                      };

                      // Add the user map if not already in the list
                      if (!mentionedUsers.any((user) => user['user_id'] == userMap['user_id'])) {
                        mentionedUsers.add(userMap);
                      }

                      // Pass the updated mentioned users list
                      widget.onMentionAdded(mentionedUsers);
                    },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    userList[index].name ?? '',
                    style: Theme.of(context).textTheme.titleLarge!.copyWith(
                          fontSize: 13.sp,
                          fontWeight: FontWeight.w700,
                          color: isMentioned ? Colors.grey : Theme.of(context).textTheme.titleLarge!.color,
                        ),
                  ),
                  buildSizedBoxH(6.0),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Flexible(
                        child: Text(
                          userList[index].userName ?? '',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                                fontSize: 12.sp,
                                color: isMentioned ? Colors.grey : Theme.of(context).textTheme.headlineSmall!.color,
                              ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
