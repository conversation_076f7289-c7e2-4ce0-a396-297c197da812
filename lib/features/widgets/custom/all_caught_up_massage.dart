import 'dart:io';

import 'package:flowkar/core/utils/exports.dart';

class AllCaughtUpMassage extends StatelessWidget {
  final bool showAllCaughtUp;
  const AllCaughtUpMassage({super.key, required this.showAllCaughtUp});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: Platform.isAndroid ? 80.h : 60.h, // Better positioning
      left: 0,
      right: 0,
      child: AnimatedSlide(
        offset: showAllCaughtUp ? Offset.zero : const Offset(0, 1),
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
        child: AnimatedOpacity(
          opacity: showAllCaughtUp ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 300),
          child: Center(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.9),
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    color: Theme.of(context).customColors.white,
                    size: 18.sp,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    "All caught up!",
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).customColors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 14.sp,
                        ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
