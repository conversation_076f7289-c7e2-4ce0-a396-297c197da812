import 'package:flowkar/core/utils/exports.dart';

class CustomAppbar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final bool hasLeadingIcon;
  final List<Widget>? actions;
  final VoidCallback? onLeadingTap;
  final TextStyle? textStyle;
  final double? height;
  final double? width;
  final AlignmentGeometry? alignment;
  final String? leadingImagePath;
  final List<Widget>? leading;

  const CustomAppbar(
      {super.key,
      this.title,
      this.hasLeadingIcon = true,
      this.actions,
      this.onLeadingTap,
      this.textStyle,
      this.height,
      this.width,
      this.alignment,
      this.leadingImagePath,
      this.leading});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themestate) {
        return SafeArea(
          child: Padding(
            padding: EdgeInsets.only(
              // top: Platform.isIOS ? 55.0.h : 40.0.h,
              right: 16.0.w,
              left: 16.0.w,
              bottom: 8.0,
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                if (hasLeadingIcon)
                  Row(
                    children: leading != null
                        ? leading!
                        : [
                            InkWell(
                              onTap: onLeadingTap ??
                                  () {
                                    FocusScope.of(context).unfocus();
                                    NavigatorService.goBack();
                                  },
                              child: CustomImageView(
                                imagePath: leadingImagePath ?? AssetConstants.pngBack,
                                height: height ?? 18.0.h,
                                width: width,
                              ),
                            ),
                          ],
                  ),
                Align(
                  alignment: hasLeadingIcon ? Alignment.center : alignment ?? Alignment.centerLeft,
                  child: Text(
                    title ?? '',
                    style: textStyle ??
                        Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w700,
                              fontSize: 18.sp,
                            ),
                  ),
                ),
                if (actions != null)
                  Positioned(
                    right: 0,
                    child: Row(children: actions!),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
