import 'package:flowkar/core/utils/exports.dart';

class CustomDropdown extends StatefulWidget {
  final List<String> items;
  final String hintText;
  final Widget? prefixIcon;
  final String selectedValue;
  final String? validator;
  final ValueChanged<String?> onChange;
  final Map<String, String> itemNamesMap;
  final bool isValidGender;

  const CustomDropdown({
    super.key,
    required this.items,
    required this.hintText,
    required this.selectedValue,
    required this.onChange,
    required this.itemNamesMap,
    this.validator,
    this.prefixIcon,
    this.isValidGender = false,
  });

  @override
  _CustomDropdownState createState() => _CustomDropdownState();
}

class _CustomDropdownState extends State<CustomDropdown> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        InkWell(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          onTap: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          child: InputDecorator(
            decoration: InputDecoration(
              contentPadding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
              prefixIcon: widget.prefixIcon,
              labelText: widget.isValidGender ? Lang.of(context).lbl_gender : null,
              hintText: !widget.isValidGender ? Lang.of(context).lbl_gender : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: const BorderSide(
                  color: Color(0XFFE0E0E0),
                  width: 1,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: const BorderSide(
                  color: Color(0XFFE0E0E0),
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: const BorderSide(
                  color: Color(0XFFE0E0E0),
                  width: 1,
                ),
              ),
              // border: OutlineInputBorder(

              //   borderSide: BorderSide(
              //     color: Theme.of(context).primaryColor,
              //     width: 1.0,
              //   ),
              //   borderRadius: BorderRadius.only(
              //     topLeft: Radius.circular(8.r),
              //     topRight: Radius.circular(8.r),
              //     bottomLeft: Radius.circular(_isExpanded ? 0 : 8.r),
              //     bottomRight: Radius.circular(_isExpanded ? 0 : 8.r),
              //   ),
              // ),

              // enabledBorder: OutlineInputBorder(
              //   borderSide: BorderSide(
              //     color: Theme.of(context).primaryColor,
              //     width: 1.0,
              //   ),
              //   borderRadius: BorderRadius.only(
              //     topLeft: Radius.circular(8.r),
              //     topRight: Radius.circular(8.r),
              //     bottomLeft: Radius.circular(_isExpanded ? 0 : 8.r),
              //     bottomRight: Radius.circular(_isExpanded ? 0 : 8.r),
              //   ),
              // ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.selectedValue.isNotEmpty ? widget.itemNamesMap[widget.selectedValue]! : widget.hintText,
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontSize: 16.sp, color: Theme.of(context).customColors.black, fontWeight: FontWeight.w500),
                ),
                CustomImageView(
                  imagePath: Assets.images.icons.other.icDropdown.path,
                  height: 22.h,
                ),
              ],
            ),
          ),
        ),
        if (_isExpanded)
          Container(
            height: widget.items.length > 3 ? 300.h : (widget.items.length * 60.h).toDouble(),
            decoration: BoxDecoration(
              border: Border.all(
                color: Color(0XFFE0E0E0),
                width: 1.0,
              ),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(8.r),
                bottomRight: Radius.circular(8.r),
              ),
            ),
            child: ListView.separated(
              padding: EdgeInsets.zero,
              itemCount: widget.items.length,
              separatorBuilder: (context, index) => Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Divider(
                  color: Color(0XFFE0E0E0),
                  thickness: 1.0,
                  height: 1.h,
                ),
              ),
              itemBuilder: (context, index) {
                final id = widget.items[index];
                return ListTile(
                  contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
                  title: Text(
                    widget.itemNamesMap[id] ?? '',
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        fontSize: 16.sp, color: Theme.of(context).customColors.black, fontWeight: FontWeight.w500),
                  ),
                  onTap: () {
                    widget.onChange(id);
                    setState(() {
                      _isExpanded = false;
                    });
                  },
                );
              },
            ),
          ),
      ],
    );
  }
}
