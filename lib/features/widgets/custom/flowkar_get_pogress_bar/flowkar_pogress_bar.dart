import 'dart:async';

import 'package:flowkar/core/utils/exports.dart';

class FlowkarProgressIndicator extends StatefulWidget {
  final ValueNotifier<double?> progressNotifier;

  const FlowkarProgressIndicator({super.key, required this.progressNotifier});

  @override
  State<FlowkarProgressIndicator> createState() => _FlowkarProgressIndicatorState();
}

class _FlowkarProgressIndicatorState extends State<FlowkarProgressIndicator> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  double _currentProgress = 0.0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _animation = Tween<double>(begin: _currentProgress, end: _currentProgress).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    widget.progressNotifier.addListener(_onProgressChanged);
  }

  void _onProgressChanged() {
    _animation = Tween<double>(
      begin: _currentProgress,
      end: widget.progressNotifier.value,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward(from: 0.0);
    if (widget.progressNotifier.value == 1.0) {
      _refreshFeed();
    }
    _currentProgress = widget.progressNotifier.value!;
  }

  Future<void> _refreshFeed() async {
    final state = context.read<HomeFeedBloc>().state;
    if (state.posts.isNotEmpty) {
      state.posts.clear();
      // state.video.clear();
      state.newStoryData.clear();
    }
    if (state.video.isNotEmpty) {
      state.video.clear();
      state.newStoryData.clear();
    }
    context.read<HomeFeedBloc>().add(GetAllPostApiEvent(page: 1));
    context.read<HomeFeedBloc>().add(GetAllVideoApiEvent(page: 1));
    context.read<HomeFeedBloc>().add(GetNewStoryApiEvent());
  }

  @override
  void dispose() {
    _animationController.dispose();
    widget.progressNotifier.removeListener(_onProgressChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return LinearProgressIndicator(
          value: _animation.value,
          backgroundColor: Colors.transparent,
          valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).primaryColor),
          minHeight: 2.0,
        );
      },
    );
  }
}
