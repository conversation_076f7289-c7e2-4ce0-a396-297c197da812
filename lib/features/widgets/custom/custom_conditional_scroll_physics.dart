import 'package:flowkar/core/utils/exports.dart';

class ConditionalAlwaysScrollPhysics extends AlwaysScrollableScrollPhysics {
  final ScrollController controller;

  const ConditionalAlwaysScrollPhysics({required this.controller, super.parent});

  @override
  ConditionalAlwaysScrollPhysics applyTo(ScrollPhysics? ancestor) {
    return ConditionalAlwaysScrollPhysics(
      controller: controller,
      parent: buildParent(ancestor),
    );
  }

  @override
  double applyBoundaryConditions(ScrollMetrics position, double value) {
    final maxScrollExtent = position.maxScrollExtent;
    final minScrollExtent = position.minScrollExtent;

    // Prevent overscroll at top
    if (value < minScrollExtent && controller.offset <= minScrollExtent) {
      return value - minScrollExtent;
    }

    // Prevent overscroll at bottom
    if (value > maxScrollExtent && controller.offset >= maxScrollExtent) {
      return value - maxScrollExtent;
    }

    return super.applyBoundaryConditions(position, value);
  }
}

class ConditionalScrollPhysics extends ClampingScrollPhysics {
  final ScrollController controller;

  const ConditionalScrollPhysics({required this.controller, super.parent});

  @override
  ConditionalScrollPhysics applyTo(ScrollPhysics? ancestor) {
    return ConditionalScrollPhysics(
      controller: controller,
      parent: buildParent(ancestor),
    );
  }

  @override
  double applyBoundaryConditions(ScrollMetrics position, double value) {
    final maxScrollExtent = position.maxScrollExtent;
    final minScrollExtent = position.minScrollExtent;

    // Prevent overscroll at top
    if (value < minScrollExtent && controller.offset <= minScrollExtent) {
      return value - minScrollExtent;
    }

    // Prevent overscroll at bottom
    if (value > maxScrollExtent && controller.offset >= maxScrollExtent) {
      return value - maxScrollExtent;
    }

    return super.applyBoundaryConditions(position, value);
  }
}

class ConditionalNeverScrollPhysics extends NeverScrollableScrollPhysics {
  final ScrollController controller;

  const ConditionalNeverScrollPhysics({required this.controller, super.parent});

  @override
  ConditionalNeverScrollPhysics applyTo(ScrollPhysics? ancestor) {
    return ConditionalNeverScrollPhysics(
      controller: controller,
      parent: buildParent(ancestor),
    );
  }

  @override
  double applyBoundaryConditions(ScrollMetrics position, double value) {
    final maxScrollExtent = position.maxScrollExtent;
    final minScrollExtent = position.minScrollExtent;

    // Prevent overscroll at top
    if (value < minScrollExtent && controller.offset <= minScrollExtent) {
      return value - minScrollExtent;
    }

    // Prevent overscroll at bottom
    if (value > maxScrollExtent && controller.offset >= maxScrollExtent) {
      return value - maxScrollExtent;
    }

    return super.applyBoundaryConditions(position, value);
  }
}
