import 'package:flowkar/core/helpers/vibration_helper.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/home_feed_screen/model/blocked_users_model.dart';
import 'package:flowkar/features/widgets/common/get_user_profile_by_id/get_user_profile_by_id.dart';
import 'package:flowkar/features/widgets/custom/custom_transition.dart';

class BlockUserListWidget extends StatefulWidget {
  final TextEditingController searchController;
  final bool showSearchField;
  final ScrollController scrollController;
  final String? type;
  final bool? isVisitor;
  final bool? isLoading;
  final List<BlockedUsersData>? blockedUsersList;

  const BlockUserListWidget({
    super.key,
    required this.searchController,
    required this.showSearchField,
    required this.scrollController,
    this.type,
    this.isVisitor,
    this.isLoading,
    this.blockedUsersList,
  });

  @override
  State<BlockUserListWidget> createState() => _BlockUserListWidgetState();
}

class _BlockUserListWidgetState extends State<BlockUserListWidget> {
  bool showCloseButton = false;
  bool isBlocking = false;
  String? blockingUserId;

  @override
  void initState() {
    super.initState();
    widget.searchController.addListener(_updateCloseButtonVisibility);
  }

  void _updateCloseButtonVisibility() {
    setState(() {
      showCloseButton = widget.searchController.text.isNotEmpty;
    });
  }

  void resetBlocking() {
    if (mounted) {
      setState(() {
        isBlocking = false;
        blockingUserId = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AbsorbPointer(
      absorbing: isBlocking,
      child: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themestate) {
          return Column(
            children: [
              Expanded(
                child: Column(
                  children: [
                    if (widget.showSearchField) _buildSearchTextField(themestate),
                    if (widget.showSearchField) buildSizedBoxH(8.0),
                    Expanded(
                      child: _buildUserList(themestate, widget.blockedUsersList ?? []),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildUserList(ThemeState themestate, List<BlockedUsersData> blockedUsersList) {
    return ShowUpTransition(
      forward: true,
      delay: const Duration(milliseconds: 300),
      child: ListView.builder(
        physics: const AlwaysScrollableScrollPhysics(),
        controller: widget.scrollController,
        padding: EdgeInsets.zero,
        shrinkWrap: true,
        itemCount: widget.blockedUsersList?.length,
        itemBuilder: (context, index) {
          return _buildUserDetail(context, themestate, index);
        },
      ),
    );
  }

  Widget _buildUserDetail(BuildContext context, ThemeState themestate, int index) {
    final userId = widget.blockedUsersList?[index].id.toString(); // Convert to String
    final currentUserId = Prefobj.preferences?.get(Prefkeys.USER_ID)?.toString(); // Convert to String
    final isCurrentUser = userId == currentUserId;

    // Logger.lOG("isCurrentUser: $isCurrentUser, userId: $userId, currentUserId: $currentUserId");

    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 50.0.h,
                width: 50.0.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  // borderRadius: BorderRadius.circular(14.r),
                  border: Border.all(
                    color: Theme.of(context).textTheme.titleSmall?.color ?? Colors.black,
                    width: 2.w,
                  ),
                ),
                child: ClipRRect(
                  clipBehavior: Clip.hardEdge,
                  child: Padding(
                    padding: const EdgeInsets.all(2.0),
                    child: CustomImageView(
                      radius: BorderRadius.circular(100.r),
                      height: 50.0.h,
                      width: 50.0.w,
                      fit: BoxFit.cover,
                      imagePath: widget.blockedUsersList?[index].profilePicture == null ||
                              (widget.blockedUsersList?[index].profilePicture?.isEmpty ?? true)
                          ? Assets.images.pngs.other.pngPlaceholder.path
                          : "${APIConfig.mainbaseURL}${widget.blockedUsersList?[index].profilePicture}",
                      alignment: Alignment.center,
                      onTap: () {
                        PersistentNavBarNavigator.pushNewScreen(context,
                            screen:
                                GetUserProfileById(userId: widget.blockedUsersList?[index].id, stackonScreen: true));
                      },
                    ),
                  ),
                ),
              ),
              buildSizedBoxW(8),
              InkWell(
                onTap: () {
                  PersistentNavBarNavigator.pushNewScreen(context,
                      screen: GetUserProfileById(userId: widget.blockedUsersList?[index].id, stackonScreen: true));
                },
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.blockedUsersList?[index].name ?? '',
                      style: Theme.of(context).textTheme.titleLarge!.copyWith(
                          fontSize: 15.sp, fontWeight: FontWeight.w600, color: Theme.of(context).primaryColor),
                    ),
                    Text(
                      "@${widget.blockedUsersList?[index].username ?? ''}",
                      style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                          fontSize: 12.4.sp, fontWeight: FontWeight.w400, color: ThemeData().customColors.greylite),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (!isCurrentUser) // Only show the follow/following button if this is not the current user
            BlocBuilder<HomeFeedBloc, HomeFeedState>(
              builder: (context, state) {
                return CustomElevatedButton(
                  height: 40.0.h,
                  width: MediaQuery.of(context).size.width * 0.3,
                  text: Lang.of(context).lbl_unblock,
                  isLoading: isBlocking && blockingUserId == userId,
                  isDisabled: isBlocking,
                  brderRadius: 10.r,
                  buttonTextStyle: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.copyWith(fontSize: 14.sp, color: Theme.of(context).customColors.white),
                  onPressed: () {
                    setState(() {
                      isBlocking = true;
                      blockingUserId = userId;
                    });
                    context
                        .read<HomeFeedBloc>()
                        .add(BlockUserApiEvent(userId: userId.toString(), context: context, isblocked: true));
                    VibrationHelper.singleShortBuzz();
                    Future.delayed(
                      Duration(seconds: 4),
                      () {
                        setState(() {
                          isBlocking = false;
                        });
                      },
                    );
                  },
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildSearchTextField(ThemeState themestate) {
    return FlowkarTextFormField(
      height: 48.0.h,
      context: context,
      controller: widget.searchController,
      // fillColor: AppColors.searchtextfieldcolor,
      filled: true,
      isLabelEnabled: false,
      labelText: Lang.of(context).lbl_search,
      prefix: CustomImageView(
        color: Theme.of(context).customColors.white,
        imagePath: Assets.images.icons.other.icSearch.path,
        margin: const EdgeInsets.all(16.0),
        height: 16.0.h,
        width: 16.0.w,
      ),
      // onChanged: (value) {
      //   // context
      //   //     .read<UserProfileBloc>()
      //   //     .add(SearchFollowListEvent(searchtext: value));
      // },
      suffix: Visibility(
        visible: showCloseButton,
        child: IconButton(
          icon: CustomImageView(
            color: Theme.of(context).customColors.white,
            height: 16.0.h,
            width: 16.0.w,
            imagePath: Assets.images.icons.other.icClose.path,
          ),
          onPressed: () {
            widget.searchController.clear();
            FocusScope.of(context).unfocus();
          },
        ),
      ),
      inputAction: TextInputAction.done,
    );
  }
}
