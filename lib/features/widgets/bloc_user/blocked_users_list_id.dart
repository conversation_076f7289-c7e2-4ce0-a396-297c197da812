import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/profile_screen/presentation/widget/user_follow_screen_shimmer.dart';
import 'package:flowkar/features/widgets/bloc_user/blocked_user_list_widget.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';

class BlockedUserListScreen extends StatefulWidget {
  const BlockedUserListScreen({
    super.key,
  });

  @override
  State<BlockedUserListScreen> createState() => _BlockedUserListScreenState();
}

class _BlockedUserListScreenState extends State<BlockedUserListScreen> {
  late ScrollController blockedUserListscrollController;
  @override
  void initState() {
    super.initState();
    blockedUserListscrollController = ScrollController();
    //   ..addListener(_scrollListener);
    final state = context.read<HomeFeedBloc>().state;
    if (state.blockedUsersList.isNotEmpty) {
      state.blockedUsersList.clear();
    }
    context.read<HomeFeedBloc>().add(GetblockedusersListApiEvent());
  }

  void _initializeData() {
    final state = context.read<HomeFeedBloc>().state;
    if (state.blockedUsersList.isNotEmpty) {
      state.blockedUsersList.clear();
    }
    context.read<HomeFeedBloc>().add(GetblockedusersListApiEvent());
  }

  @override
  void dispose() {
    blockedUserListscrollController.dispose();
    super.dispose();
  }

  // void _scrollListener() {
  //   if (blockedUserListscrollController.position.pixels ==
  //       blockedUserListscrollController.position.maxScrollExtent) {
  //     final state = context.read<UserProfileIdBloc>().state;
  //     if (!state.isLoadingMore) {
  //       context
  //           .read<HomeFeedBloc>()
  //           .add(GetblockedusersListApiEvent(userId: widget.userId ?? ''));
  //     }
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ConnectivityBloc, ConnectivityState>(
      listener: (context, connectivityState) {
        if (connectivityState.isReconnected) {
          blockedUserListscrollController = ScrollController();
          //   ..addListener(_scrollListener);
          final state = context.read<HomeFeedBloc>().state;
          if (state.blockedUsersList.isNotEmpty) {
            state.blockedUsersList.clear();
          }
          context.read<HomeFeedBloc>().add(GetblockedusersListApiEvent());
        }
      },
      child: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themestate) {
          return BlocBuilder<HomeFeedBloc, HomeFeedState>(
            builder: (context, state) {
              return Scaffold(
                appBar: _buildBackButton(),
                body: Column(
                  children: [
                    Expanded(
                      child: BlocBuilder<ConnectivityBloc, ConnectivityState>(
                        builder: (context, connectivityState) {
                          if (!connectivityState.isConnected && state.blockedUsersList.isEmpty) {
                            return UserFollowScreenShimmer();
                          } else if (state.isloding) {
                            return UserFollowScreenShimmer();
                          } else if (state.blockedUsersList.isEmpty) {
                            return ExceptionWidget(
                              imagePath: Assets.images.svg.other.svgNodatafound.path,
                              showButton: false,
                              title: Lang.of(context).lbl_data_not_found,
                              subtitle: Lang.of(context).lbl_we_could_not,
                            );
                          } else {
                            return _buildblockuserList(state);
                          }
                        },
                      ),
                    ),
                    // Visibility(
                    //   visible: state.isLoadingMore,
                    //   child: SizedBox(
                    //     height: 50.h,
                    //     child: Center(
                    //         child: CupertinoActivityIndicator(
                    //             color: Theme.of(context).colorScheme.primary)),
                    //   ),
                    // ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildBackButton() {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          Lang.of(context).lbl_blocked_accounts,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  Widget _buildblockuserList(HomeFeedState state) {
    if (state.searchController != null) {
      return LiquidPullToRefresh(
        color: Theme.of(context).primaryColor.withOpacity(0.5),
        showChildOpacityTransition: false,
        backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
        onRefresh: () async {
          _initializeData();
        },
        child: BlockUserListWidget(
          searchController: state.searchController ?? TextEditingController(),
          scrollController: blockedUserListscrollController,
          blockedUsersList: state.blockedUsersList,
          showSearchField: false,
          isLoading: state.isBlockLoading,
          type: Lang.of(context).lbl_likes,
        ),
      );
    } else {
      return const Center(child: Text('Search controller is not available'));
    }
  }
}
