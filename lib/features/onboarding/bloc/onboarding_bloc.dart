import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
part 'onboarding_state.dart';
part 'onboarding_event.dart';

class OnboardingBloc extends Bloc<OnboardingEvent, OnboardingState> {
  OnboardingBloc() : super(OnboardingState(currentIndex: 0)) {
    on<OnboardingInitialEvent>(_onInitialize);
    on<PageChangedEvent>(_onPageChanged);
    on<NextPageEvent>(_onNextPage);
  }

  void _onInitialize(OnboardingInitialEvent event, Emitter<OnboardingState> emit) {}

  void _onPageChanged(PageChangedEvent event, Emitter<OnboardingState> emit) {
    emit(state.copyWith(currentIndex: event.index));
  }

  void _onNextPage(NextPageEvent event, Emitter<OnboardingState> emit) {
    int nextIndex = state.currentIndex + 1;
    emit(state.copyWith(currentIndex: nextIndex));
  }
}
