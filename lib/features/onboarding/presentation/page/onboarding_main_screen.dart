import 'package:flowkar/core/utils/exports.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import '../../bloc/onboarding_bloc.dart';

class OnboardingmainScreen extends StatelessWidget {
  const OnboardingmainScreen({super.key});

  static Widget builder(BuildContext context) {
    return BlocProvider(
      create: (context) => OnboardingBloc(),
      child: const OnboardingmainScreen(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.light.copyWith(
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarColor: Theme.of(context).primaryColor,
          systemNavigationBarIconBrightness: Brightness.light),
      child: Scaffold(
          body: Center(
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            CustomImageView(
              imagePath: AssetConstants.svgOnboarding,
              width: MediaQuery.of(context).size.width,
              fit: BoxFit.cover,
            ),
            Positioned(
              bottom: 10,
              left: 0,
              right: 0,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Column(
                    children: [
                      Text(
                        'One Stop Solution',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w700,
                              fontSize: 28.sp,
                              color: Theme.of(context).customColors.white,
                            ),
                      ),
                      SizedBox(height: 12.h),
                      Text(
                        '10+ Social Platforms',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w400,
                              fontSize: 10.sp,
                              color: Theme.of(context).customColors.white,
                            ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        'manage one maintain all.',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w400,
                              fontSize: 10.sp,
                              color: Theme.of(context).customColors.white,
                            ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          BlocBuilder<OnboardingBloc, OnboardingState>(
                            builder: (context, state) {
                              return AnimatedSmoothIndicator(
                                activeIndex: state.currentIndex,
                                count: 4,
                                effect: CustomizableEffect(
                                  activeDotDecoration: DotDecoration(
                                    width: 20,
                                    height: 6,
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(3),
                                  ),
                                  dotDecoration: DotDecoration(
                                    width: 6,
                                    height: 6,
                                    color: const Color(0xff887774),
                                    borderRadius: BorderRadius.circular(3),
                                  ),
                                  spacing: 6,
                                ),
                              );
                            },
                          ),
                          GestureDetector(
                            onTap: () {
                              NavigatorService.pushNamed(AppRoutes.onboardingPage);
                            },
                            child: CircleAvatar(
                              radius: 28,
                              backgroundColor: Theme.of(context).customColors.white,
                              child: CustomImageView(
                                margin: EdgeInsets.all(12.0),
                                imagePath: AssetConstants.pngOnbordingRightArrow,
                                fit: BoxFit.contain,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      )),
    );
  }
}
