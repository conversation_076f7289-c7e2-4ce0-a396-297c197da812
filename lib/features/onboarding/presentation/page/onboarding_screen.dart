import 'package:flowkar/core/utils/exports.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import '../../bloc/onboarding_bloc.dart';
import '../../model/item.dart';

class OnboardingScreen extends StatelessWidget {
  OnboardingScreen({super.key});

  static Widget builder(BuildContext context) {
    return BlocProvider(
      create: (context) => OnboardingBloc(),
      child: OnboardingScreen(),
    );
  }

  final PageController controller = PageController();

  @override
  Widget build(BuildContext context) {
    List<Item> itemList = [
      Item(
        title: '10+ Social Platforms',
        subTitle: 'We deliver what we promise more than 10 social platforms including Instagram, Facebook and Threads.',
        subTitle2: '',
        image: AssetConstants.pngOnboarding2,
      ),
      Item(
        title: 'One stop solution',
        subTitle: 'Wishing to manage all your social activities together in one place?',
        subTitle2: 'Yes you are here at Flowkar we do it all',
        image: AssetConstants.pngOnboarding3,
      ),
      Item(
        title: '<PERSON><PERSON> Grow Kar',
        subTitle: '<PERSON>kar is the bridge between your feed and audience\'s need',
        subTitle2: '',
        image: AssetConstants.pngOnboarding4,
      ),
    ];

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.dark.copyWith(
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: Theme.of(context).primaryColor,
          systemNavigationBarIconBrightness: Brightness.dark),
      child: Scaffold(
        body: Stack(
          children: [
            BlocBuilder<OnboardingBloc, OnboardingState>(
              builder: (context, state) {
                return PageView.builder(
                  controller: controller,
                  onPageChanged: (index) {
                    context.read<OnboardingBloc>().add(PageChangedEvent(index));
                  },
                  itemCount: itemList.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: EdgeInsets.only(bottom: MediaQuery.of(context).size.height * 0.25),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Flexible(
                              child: CustomImageView(
                                imagePath: itemList[index].image,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Padding(
                padding: const EdgeInsets.all(30.0),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.28,
                  width: double.infinity,
                  padding: EdgeInsets.all(20.0),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.9),
                    borderRadius: BorderRadius.circular(26),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 6,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: Center(
                    child: Stack(
                      children: [
                        BlocBuilder<OnboardingBloc, OnboardingState>(builder: (context, state) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  itemList[state.currentIndex].title,
                                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 22.sp,
                                        color: Theme.of(context).customColors.primaryColor,
                                      ),
                                ),
                                SizedBox(height: 12.h),
                                Text(
                                  itemList[state.currentIndex].subTitle,
                                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.w500,
                                        fontSize: 10.sp,
                                        color: Theme.of(context).customColors.primaryColor,
                                      ),
                                  textAlign: TextAlign.center,
                                ),
                                SizedBox(height: 4.h),
                                Text.rich(
                                  TextSpan(
                                    children: [
                                      if (itemList[state.currentIndex].subTitle2.contains("Flowkar we do it all")) ...[
                                        TextSpan(
                                          text: itemList[state.currentIndex]
                                              .subTitle2
                                              .replaceFirst("Flowkar we do it all", ""),
                                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                                fontWeight: FontWeight.w400,
                                                fontSize: 10.sp,
                                                color: Theme.of(context).customColors.primaryColor,
                                              ),
                                        ),
                                        TextSpan(
                                          text: "Flowkar we do it all",
                                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                                fontWeight: FontWeight.w400,
                                                fontSize: 10.sp,
                                                color: Theme.of(context).customColors.primaryColor,
                                              ),
                                        ),
                                      ] else ...[
                                        TextSpan(
                                          text: itemList[state.currentIndex].subTitle2,
                                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                                fontWeight: FontWeight.w400,
                                                fontSize: 10.sp,
                                                color: Theme.of(context).customColors.primaryColor,
                                              ),
                                        ),
                                      ]
                                    ],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                SizedBox(height: MediaQuery.of(context).size.height * 0.04.h),
                              ],
                            ),
                          );
                        }),
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: SizedBox(
                            width: MediaQuery.of(context).size.width.w,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                BlocBuilder<OnboardingBloc, OnboardingState>(
                                  builder: (context, state) {
                                    return Row(
                                      children: [
                                        Container(
                                          height: 6,
                                          width: 6,
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: Color(0xff887774),
                                          ),
                                        ),
                                        SizedBox(width: 3.w),
                                        AnimatedSmoothIndicator(
                                          activeIndex: state.currentIndex,
                                          count: itemList.length,
                                          effect: CustomizableEffect(
                                            activeDotDecoration: DotDecoration(
                                              width: 20.w,
                                              height: 6.h,
                                              color: Color(0xff563D39),
                                              borderRadius: BorderRadius.circular(100),
                                            ),
                                            dotDecoration: DotDecoration(
                                              width: 6.w,
                                              height: 6.h,
                                              color: const Color(0xff887774),
                                              borderRadius: BorderRadius.circular(3),
                                            ),
                                            spacing: 6,
                                          ),
                                        ),
                                      ],
                                    );
                                  },
                                ),
                                GestureDetector(
                                  onTap: () {
                                    final bloc = context.read<OnboardingBloc>();
                                    final newIndex = bloc.state.currentIndex + 1;

                                    if (newIndex < itemList.length) {
                                      controller.animateToPage(
                                        newIndex,
                                        duration: const Duration(milliseconds: 500),
                                        curve: Curves.linear,
                                      );
                                      bloc.add(PageChangedEvent(newIndex));
                                    } else {
                                      Prefobj.preferences?.put(Prefkeys.FIRSTTIME, 'true');
                                      NavigatorService.pushAndRemoveUntil(AppRoutes.signinPage);
                                    }
                                  },
                                  child: CircleAvatar(
                                    radius: 26.r,
                                    backgroundColor: Color(0xff563D39),
                                    child: CustomImageView(
                                      imagePath: AssetConstants.pngOnbordingRightArrow,
                                      color: Colors.white,
                                      margin: EdgeInsets.all(12.0),
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
