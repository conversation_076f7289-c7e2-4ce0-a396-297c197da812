import 'dart:math';

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/reward_leader/bloc/leader_event.dart';
import 'package:flowkar/features/reward_leader/bloc/leader_state.dart';
import 'package:flowkar/features/story_feature_demo/Story_view_demo/const.dart';

class LeaderboardBloc extends Bloc<LeaderboardEvent, LeaderboardState> {
  final ApiClient apiClient = ApiClient(Dio());

  LeaderboardBloc() : super(LeaderboardState.initial()) {
    on<GetLeaderboardDataEvent>(_onGetLeaderboardData);
    on<GetRewardDataEvent>(_ongetRewardDataGetApiEvent);
  }

  Future<void> _onGetLeaderboardData(GetLeaderboardDataEvent event, Emitter<LeaderboardState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      emit(state.copyWith(isLoading: true));

      final leaderboardModel = await apiClient.getLeaderboardData(
        logInUserId: loginuserId,
        brand: brandId.toString(),
      );

      if (leaderboardModel.status == true) {
        emit(state.copyWith(isLoading: false, leaderboardModel: leaderboardModel));
      } else {
        emit(state.copyWith(isLoading: false));
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isLoading: false));
    }
  }

  Future<void> _ongetRewardDataGetApiEvent(GetRewardDataEvent event, Emitter<LeaderboardState> emit) async {
    emit(state.copyWith(isRewardDataLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final getRewardDataModel = await apiClient.getRewardData(
        logInUserId: loginuserId,
        brand: brandId.toString(),
      );
      if (getRewardDataModel.status == true) {
        Logger.lOG("Get Reward section ${getRewardDataModel.data.persantage} ${getRewardDataModel.data}");

        showDialog(
          context: event.context,
          builder: (ctx) {
            return Scaffold(
              backgroundColor: Colors.transparent,
              body: Center(
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 40.h),
                      decoration: BoxDecoration(
                        color: Theme.of(event.context).customColors.white,
                        border: Border.all(color: Theme.of(event.context).primaryColor, width: 2.w),
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      child: state.isRewardDataLoading
                          ? Padding(
                              padding: EdgeInsets.symmetric(vertical: 40.h),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  LoadingAnimationWidget(),
                                ],
                              ),
                            )
                          : Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                InkWell(
                                  onTap: () => NavigatorService.goBack(),
                                  child: Padding(
                                    padding: EdgeInsets.only(right: 15.w, top: 10.h),
                                    child: Icon(Icons.close_rounded,
                                        color: Theme.of(event.context).primaryColor, size: 24.sp),
                                  ),
                                ),
                                buildSizedBoxH(10.h),
                                Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                                  child: Column(
                                    children: [
                                      _buildTaskItem(
                                        event.context,
                                        subtitle: Lang.current.lbl_like_posts_to_earn,
                                        title: Lang.current.lbl_like_post,
                                        points: getRewardDataModel.data.recurrringData.like,
                                        target: 20,
                                      ),
                                      buildSizedBoxH(10),
                                      _buildTaskItem(
                                        event.context,
                                        subtitle: Lang.current.lbl_leave_comment_to_earn,
                                        title: Lang.current.lbl_comment,
                                        points: getRewardDataModel.data.recurrringData.comment,
                                        target: 20,
                                      ),
                                      buildSizedBoxH(10),
                                      _buildTaskItem(
                                        event.context,
                                        title: Lang.current.lbl_share,
                                        subtitle: Lang.current.lbl_share_with_friends_to_earn,
                                        points: getRewardDataModel.data.recurrringData.share,
                                        target: 20,
                                      ),
                                      buildSizedBoxH(10),
                                      _buildTaskItem(
                                        event.context,
                                        title: Lang.current.lbl_follow_user,
                                        subtitle: Lang.current.lbl_follow_user_earn,
                                        points: getRewardDataModel.data.recurrringData.follow,
                                        target: 20,
                                      ),
                                      buildSizedBoxH(10),
                                      _buildTaskItem(
                                        event.context,
                                        title: Lang.current.lbl_7_days_login,
                                        subtitle: Lang.current.lbl_7Days_Login_to_earn,
                                        points: getRewardDataModel.data.user7DayStatus ? 7 : Random().nextInt(7),
                                        target: 7,
                                        showClaimButton: getRewardDataModel.data.user7DayStatus,
                                        isCompleted: getRewardDataModel.data.user7DayStatus,
                                      ),
                                      buildSizedBoxH(10),
                                      _buildTaskItem(
                                        event.context,
                                        title: Lang.current.lbl_profile_completed,
                                        subtitle: Lang.current.lbl_complete_profile_to_earn,
                                        points: getRewardDataModel.data.persantage,
                                        target: 100,
                                        isCompleted: getRewardDataModel.data.profileCompletionStatus,
                                      ),
                                    ],
                                  ),
                                ),
                                buildSizedBoxH(20),
                              ],
                            ),
                    ),

                    /// Floating Task Tag
                    Positioned(
                      top: 20.h,
                      left: 0,
                      right: 0,
                      child: Center(
                        child: Container(
                          width: 140.w,
                          height: 40.h,
                          padding: EdgeInsets.symmetric(horizontal: 27.w),
                          decoration: BoxDecoration(
                            color: Theme.of(event.context).primaryColor,
                            borderRadius: BorderRadius.circular(10.r),
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            Lang.current.lbl_tasks,
                            style: Theme.of(event.context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(event.context).customColors.white,
                                fontSize: 24.sp,
                                fontWeight: FontWeight.w700),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
        emit(state.copyWith(isRewardDataLoading: false, getRewardDataModel: getRewardDataModel));
      } else {
        emit(state.copyWith(isRewardDataLoading: false));
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isRewardDataLoading: false));
    }
  }

  Widget _buildTaskItem(BuildContext context,
      {String? title,
      int? points,
      int? target,
      bool isCompleted = false,
      bool showClaimButton = false,
      String? subtitle}) {
    bool isClaimed = false;
    return StatefulBuilder(
      builder: (context, setState) => Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          (isClaimed)
                              ? Icon(
                                  Icons.check_circle_rounded,
                                  color: Theme.of(context).primaryColor,
                                  size: 18.w,
                                )
                              : SizedBox.shrink(),
                          (isClaimed) ? buildSizedBoxW(2.w) : SizedBox.shrink(),
                          Text(
                            title ?? "",
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w500,
                                  color: Theme.of(context).customColors.black,
                                ),
                          ),
                        ],
                      ),
                      if (subtitle?.isNotEmpty ?? false)
                        Padding(
                          padding: EdgeInsets.only(top: 4.h),
                          child: Text(
                            subtitle!,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  fontSize: 10.sp,
                                  fontWeight: FontWeight.w400,
                                  color: Theme.of(context).customColors.black,
                                ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                          ),
                        ),
                    ],
                  ),
                ),

                // Right: Points or Claim Text
                SizedBox(width: 10.w),

                if (isCompleted || points == target) ...[
                  buildSizedBoxW(10),
                  CustomElevatedButton(
                    width: 67.w,
                    height: 30.h,
                    text: (isClaimed == true) ? Lang.current.lbl_claimed : Lang.current.lbl_claim,
                    fontSize: 12.sp,
                    buttonTextStyle: TextStyle(
                        color: (isClaimed == true) ? Theme.of(context).primaryColor : Colors.white,
                        fontSize: 12.sp,
                        fontWeight: (isClaimed == true) ? FontWeight.bold : FontWeight.w700),
                    decoration: BoxDecoration(boxShadow: [
                      BoxShadow(blurRadius: 1, offset: Offset(1, 1), color: cWhite, blurStyle: BlurStyle.inner),
                    ], shape: BoxShape.circle),
                    brderRadius: 30.r,
                    buttonStyle: ButtonStyle(
                      padding: WidgetStateProperty.all(EdgeInsets.zero),
                      side: WidgetStateProperty.all(BorderSide(color: Theme.of(context).primaryColor, width: 1.w)),
                      backgroundColor: (isClaimed == true)
                          ? WidgetStateProperty.all(const Color.fromARGB(255, 224, 219, 219))
                          : WidgetStateProperty.all(Theme.of(context).primaryColor),
                    ),
                    onPressed: () {
                      setState(
                        () {
                          isClaimed = true;
                        },
                      );
                      isClaimed = true;
                      Logger.lOG("is Claimed $isClaimed");
                    },
                  ),
                ] else ...[
                  CustomElevatedButton(
                    width: 67.w,
                    height: 30.h,
                    text: '+ $points pts',
                    fontSize: 12.sp,
                    isLoading: false,
                    brderRadius: 30.r,
                    onPressed: () {},
                  ),
                ]
              ],
            ),

            // Progress or Claim Button
            if (!showClaimButton) ...[
              buildSizedBoxH(12),
              LinearProgressIndicator(
                minHeight: 6.h,
                value: ((points ?? 0) / (target ?? 1)).clamp(0.0, 1.0),
                backgroundColor: Theme.of(context).primaryColor.withOpacity(0.20),
                valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).primaryColor),
                borderRadius: BorderRadius.circular(100.r),
              ),
            ],
            if (showClaimButton) ...[
              buildSizedBoxH(12),
              CustomElevatedButton(
                width: double.infinity,
                height: 45.h,
                text: Lang.current.lbl_claim,
                isLoading: false,
                brderRadius: 10.r,
                onPressed: () => NavigatorService.goBack(),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
