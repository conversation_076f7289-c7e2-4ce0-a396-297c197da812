import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/reward_leader/model/get_reward_data_model.dart';
import 'package:flowkar/features/reward_leader/model/leaderboard_model.dart';

class LeaderboardState extends Equatable {
  final bool isLoading;
  final LeaderBoardModel? leaderboardModel;
  final String errorMessage;

  //MARK:Get Reward Data
  final bool isRewardDataLoading;
  final GetRewardDataModel? getRewardDataModel;
  final RewardData? rewardData;

  const LeaderboardState({
    required this.isLoading,
    this.leaderboardModel,
    required this.errorMessage,
    this.isRewardDataLoading = false,
    this.getRewardDataModel,
    this.rewardData,
  });

  factory LeaderboardState.initial() {
    return const LeaderboardState(
      isLoading: false,
      leaderboardModel: null,
      errorMessage: '',
      isRewardDataLoading: false,
      getRewardDataModel: null,
      rewardData: null,
    );
  }

  LeaderboardState copyWith({
    bool? isLoading,
    LeaderBoardModel? leaderboardModel,
    String? errorMessage,
    bool? isRewardDataLoading,
    GetRewardDataModel? getRewardDataModel,
    RewardData? rewardData,
  }) {
    return LeaderboardState(
      isLoading: isLoading ?? this.isLoading,
      leaderboardModel: leaderboardModel ?? this.leaderboardModel,
      errorMessage: errorMessage ?? this.errorMessage,
      isRewardDataLoading: isRewardDataLoading ?? this.isRewardDataLoading,
      getRewardDataModel: getRewardDataModel ?? this.getRewardDataModel,
      rewardData: rewardData ?? this.rewardData,
    );
  }

  @override
  List<Object?> get props =>
      [isLoading, leaderboardModel, errorMessage, isRewardDataLoading, getRewardDataModel, rewardData];
}
