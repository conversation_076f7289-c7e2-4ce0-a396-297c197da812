import 'package:flowkar/core/utils/exports.dart';

abstract class LeaderboardEvent extends Equatable {
  const LeaderboardEvent();

  @override
  List<Object> get props => [];
}

class GetLeaderboardDataEvent extends LeaderboardEvent {}

class GetRewardDataEvent extends LeaderboardEvent {
  final BuildContext context;
  const GetRewardDataEvent({required this.context});

  @override
  List<Object> get props => [context];
}
