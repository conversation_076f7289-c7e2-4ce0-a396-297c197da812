GetRewardDataModel deserializeGetRewardDataModel(Map<String, dynamic> json) => GetRewardDataModel.fromJson(json);

class GetRewardDataModel {
  final bool status;
  final String message;
  final RewardData data;

  GetRewardDataModel({
    required this.status,
    required this.message,
    required this.data,
  });

  GetRewardDataModel copyWith({
    bool? status,
    String? message,
    RewardData? data,
  }) {
    return GetRewardDataModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  factory GetRewardDataModel.fromJson(Map<String, dynamic> json) {
    return GetRewardDataModel(
      status: json['status'],
      message: json['message'],
      data: RewardData.fromJson(json['data']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data.toJson(),
    };
  }
}

class RewardData {
  final bool user7DayStatus;
  final RecurringData recurrringData;
  final bool profileCompletionStatus;
  final int persantage;

  RewardData({
    required this.user7DayStatus,
    required this.recurrringData,
    required this.profileCompletionStatus,
    required this.persantage,
  });

  RewardData copyWith({
    bool? user7DayStatus,
    RecurringData? recurrringData,
    bool? profileCompletionStatus,
    int? persantage,
  }) {
    return RewardData(
      user7DayStatus: user7DayStatus ?? this.user7DayStatus,
      recurrringData: recurrringData ?? this.recurrringData,
      profileCompletionStatus: profileCompletionStatus ?? this.profileCompletionStatus,
      persantage: persantage ?? this.persantage,
    );
  }

  factory RewardData.fromJson(Map<String, dynamic> json) {
    return RewardData(
      user7DayStatus: json['user_7_day_status'],
      recurrringData: RecurringData.fromJson(json['recurrring_data']),
      profileCompletionStatus: json['profile_completion_status'],
      persantage: json['persantage'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_7_day_status': user7DayStatus,
      'recurrring_data': recurrringData.toJson(),
      'profile_completion_status': profileCompletionStatus,
      'persantage': persantage,
    };
  }
}

class RecurringData {
  final int like;
  final int comment;
  final int follow;
  final int share;

  RecurringData({
    required this.like,
    required this.comment,
    required this.follow,
    required this.share,
  });

  RecurringData copyWith({
    int? like,
    int? comment,
    int? follow,
    int? share,
  }) {
    return RecurringData(
      like: like ?? this.like,
      comment: comment ?? this.comment,
      follow: follow ?? this.follow,
      share: share ?? this.share,
    );
  }

  factory RecurringData.fromJson(Map<String, dynamic> json) {
    return RecurringData(
      like: json['like'],
      comment: json['comment'],
      follow: json['follow'],
      share: json['share'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'like': like,
      'comment': comment,
      'follow': follow,
      'share': share,
    };
  }
}
