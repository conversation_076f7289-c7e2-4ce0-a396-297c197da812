LeaderBoardModel deserializeLeaderBoardModel(Map<String, dynamic> json) => LeaderBoardModel.from<PERSON>son(json);

class LeaderBoardModel {
  final bool status;
  final String message;
  final List<LeaderBoardUser> data;

  LeaderBoardModel({
    required this.status,
    required this.message,
    required this.data,
  });

  factory LeaderBoardModel.fromJson(Map<String, dynamic> json) {
    return LeaderBoardModel(
      status: json['status'],
      message: json['message'],
      data: (json['data'] as List).map((e) => LeaderBoardUser.fromJson(e)).toList(),
    );
  }

  LeaderBoardModel copyWith({
    bool? status,
    String? message,
    List<LeaderBoardUser>? data,
  }) {
    return LeaderBoardModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

class LeaderBoardUser {
  final int rank;
  final int userId;
  final String name;
  final int points;
  final String profilePicture;

  LeaderBoardUser({
    required this.rank,
    required this.userId,
    required this.name,
    required this.points,
    required this.profilePicture,
  });

  factory LeaderBoardUser.fromJson(Map<String, dynamic> json) {
    return LeaderBoardUser(
      rank: json['rank'],
      userId: json['user_id'],
      name: json['name'],
      points: json['points'],
      profilePicture: json['profile_picture'] ?? '',
    );
  }

  LeaderBoardUser copyWith({
    int? rank,
    int? userId,
    String? name,
    int? points,
    String? profilePicture,
  }) {
    return LeaderBoardUser(
      rank: rank ?? this.rank,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      points: points ?? this.points,
      profilePicture: profilePicture ?? this.profilePicture,
    );
  }
}
