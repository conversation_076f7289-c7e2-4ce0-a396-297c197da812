import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/reward_leader/bloc/leader_bloc.dart';
import 'package:flowkar/features/reward_leader/bloc/leader_event.dart';
import 'package:flowkar/features/reward_leader/bloc/leader_state.dart';
import 'package:flowkar/features/reward_leader/model/leaderboard_model.dart';
import 'package:flowkar/features/reward_leader/presentation/widget/leader_shimmer_screen.dart';
import 'package:flowkar/features/widgets/custom/custom_conditional_scroll_physics.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';

class RewardLeaderScreen extends StatefulWidget {
  const RewardLeaderScreen({super.key});

  @override
  State<RewardLeaderScreen> createState() => _RewardLeaderScreenState();
}

class _RewardLeaderScreenState extends State<RewardLeaderScreen> {
  String? curruntUserId;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    curruntUserId = Prefobj.preferences?.get(Prefkeys.USER_ID).toString();
    context.read<LeaderboardBloc>().add(GetLeaderboardDataEvent());
  }

  Future<void> _refreshFeed() async {
    context.read<LeaderboardBloc>().add(GetLeaderboardDataEvent());
  }

  @override
  Widget build(BuildContext context) {
    curruntUserId = Prefobj.preferences!.get(Prefkeys.USER_ID).toString();

    return Scaffold(
      appBar: _buildAppBar(context),
      body: BlocBuilder<LeaderboardBloc, LeaderboardState>(
        builder: (context, state) {
          final leaderboardUsers = state.leaderboardModel?.data;
          final topUsers = leaderboardUsers?.take(3).toList();
          final remainingUsers = leaderboardUsers?.skip(3).toList();

          if (state.isLoading) {
            return RewardLeaderShimmerScreen();
          }

          return LiquidPullToRefresh(
            color: Theme.of(context).primaryColor.withOpacity(0.5),
            showChildOpacityTransition: false,
            backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
            onRefresh: _refreshFeed,
            child: ListView(
              controller: _scrollController,
              physics: ConditionalAlwaysScrollPhysics(controller: _scrollController),
              children: [
                _buildTop3Users(topUsers),
                buildSizedBoxH(26),
                Column(
                  children: List.generate(
                    remainingUsers?.length ?? 0,
                    (index) {
                      final user = remainingUsers?[index];
                      return _buildRegularUserItem(user, index + 4);
                    },
                  ),
                )
              ],
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          Lang.current.lbl_reward_leader,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  Widget _buildTop3Users(List<LeaderBoardUser>? topUsers) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTopUserCircle(topUsers?[1], topUsers?[1].rank ?? 2), // 2nd
          buildSizedBoxW(30),
          _buildTopUserCircle(topUsers?[0], topUsers?[0].rank ?? 1, isCenter: true), // 1st
          buildSizedBoxW(30),
          _buildTopUserCircle(topUsers?[2], topUsers?[2].rank ?? 3), // 3rd
        ],
      ),
    );
  }

  Widget _buildTopUserCircle(LeaderBoardUser? user, int rank, {bool isCenter = false}) {
    return SizedBox(
      width: 80,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          buildSizedBoxH(rank == 1 ? 0 : 30),
          Stack(
            clipBehavior: Clip.none,
            alignment: Alignment.bottomCenter,
            children: [
              Column(
                children: [
                  if (rank == 1)
                    CustomImageView(
                      height: 13.h,
                      width: 17.w,
                      imagePath: Assets.images.svg.profile.svgPremiumBadge.path,
                    ),
                  Container(
                    height: isCenter ? 72.r : 60.r,
                    width: isCenter ? 72.r : 60.r,
                    decoration: BoxDecoration(
                      border: Border.all(color: Theme.of(context).primaryColor, width: 3.w),
                      shape: BoxShape.circle,
                    ),
                    child: CustomImageView(
                        height: isCenter ? 72.r : 60.r,
                        width: isCenter ? 72.r : 60.r,
                        radius: BorderRadius.circular(100.r),
                        imagePath:
                            user?.profilePicture.isEmpty ?? true ? AssetConstants.pngUserReomve : user?.profilePicture,
                        fit: BoxFit.cover),
                  ),
                ],
              ),
              Positioned(
                bottom: -16.h,
                child: Container(
                  padding: EdgeInsets.all(8.r),
                  decoration: BoxDecoration(color: Theme.of(context).primaryColor, shape: BoxShape.circle),
                  child: Text(rank.toString(),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w700,
                            color: Theme.of(context).customColors.white,
                          )),
                ),
              ),
            ],
          ),
          buildSizedBoxH(8.h),
          Text(
            user?.name ?? '',
            maxLines: 2,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold, fontSize: 14.sp),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomImageView(
                height: 12.h,
                width: 12.w,
                imagePath: Assets.images.svg.rewardLeader.svgCup.path,
              ),
              buildSizedBoxW(2),
              Text(
                "${abbreviateNumber(user?.points ?? 0)} pts",
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 12.sp),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRegularUserItem(LeaderBoardUser? user, int position) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 4.h, horizontal: 16.w),
      decoration: BoxDecoration(
        color: user?.userId == int.parse(curruntUserId ?? "") ? Theme.of(context).primaryColor : Colors.transparent,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 8.h),
        child: Row(
          children: [
            SizedBox(
              width: 30.w,
              child: Text(
                // position.toString(),
                position < 10 ? '0${user?.rank}' : "${user?.rank}",
                textAlign: TextAlign.right,
                // textAlign: position >= 100 ? TextAlign.left : TextAlign.right,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: user?.userId == int.parse(curruntUserId ?? "")
                          ? Theme.of(context).customColors.white
                          : Theme.of(context).customColors.black,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ),
            buildSizedBoxW(12),
            Container(
              height: 36.h,
              width: 36.w,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100.r),
                  border: Border.all(width: 1.5.w, color: Theme.of(context).primaryColor)),
              child: CustomImageView(
                margin: EdgeInsets.all(1.8),
                radius: BorderRadius.circular(100.r),
                imagePath: user?.profilePicture.isEmpty ?? true ? AssetConstants.pngUserReomve : user?.profilePicture,
                fit: BoxFit.cover,
              ),
            ),
            buildSizedBoxW(12),
            Expanded(
              child: Text(
                user?.name ?? "",
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    overflow: TextOverflow.ellipsis,
                    color: user?.userId == int.parse(curruntUserId ?? "")
                        ? Theme.of(context).customColors.white
                        : Theme.of(context).customColors.black),
              ),
            ),
            buildSizedBoxW(12),
            Row(
              children: [
                CustomImageView(
                  height: 16.h,
                  width: 16.w,
                  imagePath: Assets.images.svg.rewardLeader.svgCup.path,
                  color: user?.userId == int.parse(curruntUserId ?? "")
                      ? Theme.of(context).customColors.white
                      : Theme.of(context).customColors.black,
                ),
                buildSizedBoxW(4),
                Text(
                  "${abbreviateNumber(user?.points ?? 0)} pts",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: user?.userId == int.parse(curruntUserId ?? "")
                            ? Theme.of(context).customColors.white
                            : Theme.of(context).customColors.black,
                      ),
                ),
                buildSizedBoxW(12),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
