import 'package:flowkar/core/utils/exports.dart';

class RewardLeaderShimmerScreen extends StatelessWidget {
  const RewardLeaderShimmerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: [
        _buildTop3UsersShimmer(context),
        buildSizedBoxH(26),
        Column(
          children: List.generate(
            10,
            (index) => _buildRegularUserShimmer(context, index + 4),
          ),
        )
      ],
    );
  }

  Widget _buildTop3UsersShimmer(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildTopUserCircleShimmer(context, 2), // 2nd place
          buildSizedBoxW(30),
          _buildTopUserCircleShimmer(context, 1, isCenter: true), // 1st place
          buildSizedBoxW(30),
          _buildTopUserCircleShimmer(context, 3), // 3rd place
        ],
      ),
    );
  }

  Widget _buildTopUserCircleShimmer(BuildContext context, int rank, {bool isCenter = false}) {
    return Column(
      children: [
        buildSizedBoxH(rank == 1 ? 0 : 30),
        Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            height: isCenter ? 72.r : 60.r,
            width: isCenter ? 72.r : 60.r,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.grey[300]!, width: 3.w),
            ),
          ),
        ),
        SizedBox(height: 8.h),
        // Name shimmer
        Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            height: 16.h,
            width: 60.w,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4.r),
            ),
          ),
        ),
        buildSizedBoxH(4),
        Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            height: 16.h,
            width: 50.w,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4.r),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRegularUserShimmer(BuildContext context, int position) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 5.h, horizontal: 20.w),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
        child: Row(
          children: [
            // Position number shimmer
            SizedBox(
              width: 20.w,
              child: Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  height: 16.h,
                  width: 16.w,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
              ),
            ),
            buildSizedBoxW(12),
            // Profile picture shimmer
            Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                height: 32.h,
                width: 32.w,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
              ),
            ),
            buildSizedBoxW(12),
            // Name shimmer
            Expanded(
              child: Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  height: 16.h,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
              ),
            ),
            buildSizedBoxW(12),
            // Points section shimmer
            Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                height: 14.h,
                width: 60.w,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
