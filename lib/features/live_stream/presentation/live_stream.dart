// // ignore_for_file: deprecated_member_use, use_build_context_synchronously

// // ignore_for_file: deprecated_member_use, use_build_context_synchronously

// import 'package:flowkar/core/utils/exports.dart';
// import 'package:flowkar/core/utils/loading_animation_widget.dart';
// import 'package:permission_handler/permission_handler.dart';
// import 'package:socket_io_client/socket_io_client.dart' as io;
// import 'package:flutter_webrtc/flutter_webrtc.dart';
// import 'package:wakelock_plus/wakelock_plus.dart';
// import 'dart:math';
// import 'dart:async';

// class LiveStreamPage extends StatefulWidget {
//   final dynamic args;

//   const LiveStreamPage({super.key, required this.args});
//   static Widget builder(BuildContext context) {
//     var args = ModalRoute.of(context)?.settings.arguments;
//     //args[0]-->liveid -->  liv-userid
//     //args[1]-->userId -->  userId

//     Logger.lOG("LiveStreamPage: ${(args as List?)?[0]}");
//     return LiveStreamPage(args: args);
//   }

//   @override
//   State<LiveStreamPage> createState() => _LiveStreamPageState();
// }

// class _LiveStreamPageState extends State<LiveStreamPage> with TickerProviderStateMixin, WidgetsBindingObserver {
//   // Controllers
//   final TextEditingController _commentController = TextEditingController();
//   final ValueNotifier<String> _inputText = ValueNotifier('');
//   bool _isCommentsVisible = true;

//   // WebRTC
//   final RTCVideoRenderer _localRenderer = RTCVideoRenderer();
//   final Map<String, RTCVideoRenderer> _remoteRenderers = {};
//   final Map<String, RTCPeerConnection> _peerConnections = {};
//   MediaStream? _localStream;
//   final Set<String> _remoteStreamIds = {};
//   String? _currentRoom;
//   bool _isMuted = false;
//   bool _isCameraOff = false;

//   // Room role
//   bool _isRoomCreator = false;
//   bool showVideoWhenJoining = false;

//   // Socket
//   late io.Socket socket;
//   String status = "Disconnected";
//   bool isStatusError = false;

//   // UI state
//   bool isConnected = false;
//   bool _isStreaming = false;
//   bool _isLoading = true;
//   String uerName = '';
//   String userProfileImage = '';
//   int _currentUserCount = 0;

//   // Comments
//   List<Map<String, dynamic>> _comments = [];

//   // Emoji animation
//   final List<Map<String, dynamic>> _activeEmojis = [];
//   Timer? _emojiTimer;

//   @override
//   void initState() {
//     super.initState();
//     WidgetsBinding.instance.addObserver(this);
//     _initializeRenderers();
//     _initializeSocket();
//     // Enable wakelock when stream starts
//     WakelockPlus.enable();

//     if (widget.args[1] != Prefobj.preferences?.get(Prefkeys.USER_ID)) {
//       uerName = widget.args[2].toString();
//       userProfileImage = widget.args[3].toString();
//       Logger.lOG("USER NAME: $uerName");
//       Logger.lOG("USER PROFILE IMAGE: $userProfileImage");
//       _joinRoom();
//       Logger.lOG("VIEWR JOIN");
//     } else {
//       _createRoom();
//       Logger.lOG("ROOM CREATE");
//     }
//     Future.delayed(Duration(seconds: 5), () {
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//         });
//       }
//     });
//   }

//   @override
//   void dispose() {
//     WidgetsBinding.instance.removeObserver(this);
//     // Disable wakelock when stream ends
//     WakelockPlus.disable();
//     // Cancel all animation controllers
//     for (var emoji in _activeEmojis) {
//       if (emoji['controller'] != null) {
//         emoji['controller'].dispose();
//       }
//     }
//     _emojiTimer?.cancel();
//     _commentController.dispose();
//     if (widget.args[1] == Prefobj.preferences?.get(Prefkeys.USER_ID)) {
//       disconnectroom();
//       _stopExistingStream();
//       _localRenderer.dispose();
//       socket.dispose();
//     }
//     super.dispose();
//   }

//   @override
//   void didChangeAppLifecycleState(AppLifecycleState state) async {
//     Logger.lOG("App lifecycle state changed to: $state");

//     if (state == AppLifecycleState.detached || state == AppLifecycleState.paused) {
//       Logger.lOG("App being terminated or going to background - initiating cleanup");

//       if (widget.args[1] == Prefobj.preferences?.get(Prefkeys.USER_ID)) {
//         // Stream creator cleanup
//         await disconnectroom();
//         _stopExistingStream();
//         await _localRenderer.dispose();
//         socket.dispose();
//         WakelockPlus.disable();
//         if (mounted) {
//           Navigator.pop(context, true);
//         }
//         if (mounted) {
//           context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
//         }
//       } else {
//         // Viewer cleanup
//         await cleanUser();
//         _stopExistingStream();
//         await _localRenderer.dispose();
//         socket.dispose();
//         WakelockPlus.disable();
//         if (mounted) {
//           Navigator.pop(context, true);
//         }
//         if (mounted) {
//           context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
//         }
//       }
//     }
//   }

//   Future<void> _initializeRenderers() async {
//     await _localRenderer.initialize();
//   }

//   void _initializeSocket() {
//     socket = io.io(
//       APIConfig.liveStreamUrl,
//       io.OptionBuilder().setTransports(['websocket']).setPath('/socket.io/').disableAutoConnect().build(),
//     );

//     socket.onConnect((_) {
//       Logger.lOG("Socket Connected Successfully");
//       if (mounted) {
//         setState(() {
//           isConnected = true;
//         });
//       }
//     });

//     socket.onDisconnect((_) {
//       Logger.lOG("Socket Disconnected");
//       if (mounted) {
//         setState(() {
//           isConnected = false;
//         });
//       }
//     });

//     socket.on('check_room', (data) {
//       if (data['valid']) {
//         setState(() {
//           _isRoomCreator = false;
//         });
//         _startStreaming(_currentRoom!);
//       } else {
//         _setStatus("Room does not exist! Please enter a valid room.", true);
//       }
//     });

//     socket.on('room_created', (data) {
//       _setStatus("Room '${data['room']}' created successfully!");
//       setState(() {
//         _isRoomCreator = true;
//       });
//       _startStreaming(data['room']);
//     });

//     socket.on('user_joined', (data) async {
//       if (data['room'] == _currentRoom) {
//         final pc = await _createPeerConnection(data['userId']);

//         try {
//           final offer = await pc.createOffer();
//           await pc.setLocalDescription(offer);
//           socket.emit('offer', {
//             'offer': {'type': offer.type, 'sdp': offer.sdp},
//             'userId': data['userId'],
//             'room': _currentRoom,
//           });
//         } catch (e) {
//           Logger.lOG("Error creating offer: $e");
//         }
//       }
//     });

//     socket.on('offer', (data) async {
//       if (data['room'] == _currentRoom) {
//         final pc = await _createPeerConnection(data['userId']);

//         try {
//           await pc.setRemoteDescription(
//             RTCSessionDescription(data['offer']['sdp'], data['offer']['type']),
//           );

//           final answer = await pc.createAnswer();
//           await pc.setLocalDescription(answer);

//           socket.emit('answer', {
//             'answer': {'type': answer.type, 'sdp': answer.sdp},
//             'userId': data['userId'],
//             'room': _currentRoom,
//           });
//         } catch (e) {
//           Logger.lOG("Error handling offer: $e");
//         }
//       }
//     });

//     socket.on('answer', (data) async {
//       if (data['room'] == _currentRoom) {
//         try {
//           final pc = _peerConnections[data['userId']];
//           if (pc != null) {
//             await pc.setRemoteDescription(
//               RTCSessionDescription(
//                 data['answer']['sdp'],
//                 data['answer']['type'],
//               ),
//             );
//           }
//         } catch (e) {
//           Logger.lOG("Error handling answer: $e");
//         }
//       }
//     });

//     socket.on('ice_candidate', (data) async {
//       if (data['room'] == _currentRoom) {
//         try {
//           final pc = _peerConnections[data['userId']];
//           if (pc != null) {
//             await pc.addCandidate(
//               RTCIceCandidate(
//                 data['candidate']['candidate'],
//                 data['candidate']['sdpMid'],
//                 data['candidate']['sdpMLineIndex'],
//               ),
//             );
//           }
//         } catch (e) {
//           Logger.lOG("Error adding ICE candidate: $e");
//         }
//       }
//     });

//     socket.on('user_left', (data) {
//       _cleanupUser(data['userId']);
//     });

//     socket.on('room_closed', (data) {
//       if (data['room'] == _currentRoom && !_isRoomCreator) {
//         _handleRoomClosed();
//       }
//     });

//     socket.on('current_users_number', (data) {
//       Logger.lOG("Current Users Count Response: $data");
//       if (data['room'] == _currentRoom) {
//         setState(() {
//           _currentUserCount = data['count'] ?? 0;
//         });
//       }
//     });

//     socket.on('comment_list', (data) {
//       Logger.lOG("Comment list received: $data");
//       if (mounted && data['room'] == _currentRoom) {
//         setState(() {
//           _comments.clear();
//           if (data['comments'] != null) {
//             _comments.addAll((data['comments'] as List)
//                 .map((comment) => {
//                       'userId': comment['userId'] ?? '',
//                       'username': comment['username'] ?? '',
//                       'comment': comment['comment'] ?? '',
//                       'timestamp': comment['timestamp'] ?? DateTime.now().millisecondsSinceEpoch,
//                       'is_animated': comment['is_animated'] ?? false,
//                     })
//                 .toList());
//           }
//           Logger.lOG("Updated comment list with ${_comments.length} comments");
//         });
//       }
//     });

//     socket.on('new_comment', (data) {
//       Logger.lOG("New comment received: $data");
//       if (mounted && data['room'] == _currentRoom) {
//         setState(() {
//           _comments.add({
//             'userId': data['userId'] ?? '',
//             'username': data['username'] ?? '',
//             'comment': data['comment'] ?? '',
//             'timestamp': DateTime.now().millisecondsSinceEpoch,
//             'is_animated': data['is_animated'] ?? false,
//           });
//           if (data['is_animated'] == true && data['comment'] is String && data['comment'].isNotEmpty) {
//             _showEmojiAnimation(data['comment']);
//           }
//         });
//       }
//     });

//     socket.connect();
//   }

//   void _handleRoomClosed() {
//     context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
//     toastification.show(
//       type: ToastificationType.warning,
//       showProgressBar: false,
//       title: Text(
//         'The live has ended by the host.',
//         style: GoogleFonts.montserrat(
//           fontSize: 12.0.sp,
//           fontWeight: FontWeight.w500,
//         ),
//       ),
//       autoCloseDuration: const Duration(seconds: 3),
//     );

//     _stopExistingStream();
//     _localRenderer.dispose();
//     socket.dispose();

//     Navigator.of(context).pop();
//   }

//   void _setStatus(String message, [bool isError = false]) {
//     setState(() {
//       status = message;
//       isStatusError = isError;
//     });
//   }

//   Future<RTCPeerConnection> _createPeerConnection(String userId) async {
//     if (_peerConnections.containsKey(userId)) {
//       Logger.lOG("Peer connection already exists for: $userId");
//       return _peerConnections[userId]!;
//     }

//     final config = {
//       'iceServers': [
//         {'urls': 'stun:stun.l.google.com:19302'},
//         {'urls': 'stun:stun1.l.google.com:19302'},
//       ],
//     };

//     final pc = await createPeerConnection(config);

//     pc.onIceCandidate = (candidate) {
//       socket.emit('ice_candidate', {
//         'candidate': {
//           'candidate': candidate.candidate,
//           'sdpMid': candidate.sdpMid,
//           'sdpMLineIndex': candidate.sdpMLineIndex,
//         },
//         'userId': userId,
//         'room': _currentRoom,
//       });
//     };

//     pc.onTrack = (RTCTrackEvent event) async {
//       if (event.streams.isNotEmpty) {
//         final streamId = event.streams[0].id;

//         if (!_remoteStreamIds.contains(streamId)) {
//           _remoteStreamIds.add(streamId);

//           final renderer = RTCVideoRenderer();
//           await renderer.initialize();
//           renderer.srcObject = event.streams[0];

//           setState(() {
//             _remoteRenderers[userId] = renderer;
//           });
//         }
//       }
//     };

//     if (_localStream != null) {
//       _localStream!.getTracks().forEach((track) {
//         pc.addTrack(track, _localStream!);
//       });
//     }

//     _peerConnections[userId] = pc;
//     return pc;
//   }

//   void _stopExistingStream() {
//     if (_localStream != null) {
//       _localStream!.getTracks().forEach((track) => track.stop());
//       _localStream = null;
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//         });
//       }
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//         });
//       }

//       setState(() {
//         _isStreaming = false;
//       });

//       for (var pc in _peerConnections.values) {
//         pc.close();
//       }
//       _peerConnections.clear();

//       for (var renderer in _remoteRenderers.values) {
//         renderer.srcObject = null;
//         renderer.dispose();
//       }
//       _remoteRenderers.clear();

//       // Clear tracked streams
//       _remoteStreamIds.clear();
//     }
//   }

//   void _cleanupUser(String userId) {
//     if (_remoteRenderers.containsKey(userId)) {
//       final renderer = _remoteRenderers[userId]!;
//       renderer.srcObject = null;
//       renderer.dispose();
//       _remoteRenderers.remove(userId);
//     }

//     if (_peerConnections.containsKey(userId)) {
//       _peerConnections[userId]!.close();
//       _peerConnections.remove(userId);
//     }

//     // Only update user count if we're not the room creator
//     if (!_isRoomCreator) {
//       setState(() {
//         _currentUserCount = (_currentUserCount - 1).clamp(0, _currentUserCount);
//       });
//     }
//   }

//   Future<void> _createRoom() async {
//     final room = widget.args[0].toString();
//     if (room.isEmpty) {
//       _setStatus("Please enter a room name.", true);
//       return;
//     }

//     _stopExistingStream();
//     _currentRoom = room;
//     socket.emit("create_room", {"room": room});
//     Logger.lOG("Room created: $room");

//     // Request user count after room creation
//     // socket.emit('current_users_number', {
//     //   'room': room,
//     // });

//     toastification.show(
//       type: ToastificationType.success,
//       showProgressBar: false,
//       title: Text(
//         'Successfully created the live video.',
//         style: GoogleFonts.montserrat(
//           fontSize: 12.0.sp,
//           fontWeight: FontWeight.w500,
//         ),
//       ),
//       autoCloseDuration: const Duration(seconds: 3),
//     );
//   }

//   Future<void> _joinRoom() async {
//     final room = widget.args[0].toString();
//     if (room.isEmpty) {
//       _setStatus("Please enter a room name.", true);
//       return;
//     }

//     _stopExistingStream();
//     _currentRoom = room;

//     setState(() {
//       showVideoWhenJoining = false;
//     });

//     try {
//       socket.emit("check_room", {"room": room});
//       socket.emit("join_room", {"room": room});

//       // Request comment list when joining
//       Logger.lOG("Requesting comment list for room: $room");
//       socket.emit('get_comment_list', {
//         'room': room,
//       });
//     } catch (e) {
//       Logger.lOG("Error joining room: $e");
//       _setStatus("Failed to join room. Please try again.", true);
//     }
//   }

//   Future<void> disconnectroom() async {
//     final room = widget.args[0].toString();
//     socket.emit("close_room", {"room": room});
//     Logger.lOG("Room closed: $room");
//     Logger.lOG("Room closed: $room");
//   }

//   Future<void> cleanUser() async {
//     final room = widget.args[0].toString();
//     socket.emit("leave_stream", {"room": room});
//     // Don't close the room, just leave
//   }

//   Future<void> _startStreaming(String room) async {
//     if (await _requestPermissions()) {
//       try {
//         // final constraints = {
//         //   'video': {
//         //     'width': {'ideal': 1280},
//         //     'height': {'ideal': 720},
//         //     'facingMode': 'user',
//         //   },
//         //   'audio': _isRoomCreator,
//         // };
//         final constraints = {
//           'video': {
//             'width': {'ideal': 640},
//             'height': {'ideal': 480},
//             'facingMode': 'user',
//             'mandatory': {
//               'minWidth': 320,
//               'minHeight': 240,
//               'maxWidth': 1280,
//               'maxHeight': 720,
//             },
//           },
//           'audio': _isRoomCreator,
//         };

//         _localStream = await navigator.mediaDevices.getUserMedia(constraints);
//         _localRenderer.srcObject = _localStream;

//         setState(() {
//           _isStreaming = true;
//         });

//         socket.emit("join_room", {"room": room});
//       } catch (e) {
//         Logger.lOG("Error accessing media devices: $e");
//         _setStatus("Error accessing camera and microphone!", true);
//       }
//     } else {
//       openAppSettings();
//     }
//   }

//   Future<bool> _requestPermissions() async {
//     Map<Permission, PermissionStatus> statuses = await [Permission.camera, Permission.microphone].request();

//     return statuses[Permission.camera]?.isGranted == true && statuses[Permission.microphone]?.isGranted == true;
//   }

//   void _toggleMute() {
//     if (_localStream != null) {
//       setState(() {
//         _isMuted = !_isMuted;
//       });
//       _localStream!.getAudioTracks().forEach((track) {
//         track.enabled = !_isMuted;
//       });
//     }
//   }

//   void _toggleCamera() {
//     if (_localStream != null) {
//       setState(() {
//         _isCameraOff = !_isCameraOff;
//       });
//       _localStream!.getVideoTracks().forEach((track) {
//         track.enabled = !_isCameraOff;
//       });
//     }
//   }

//   void _sendComment() {
//     if (_commentController.text.trim().isEmpty) return;

//     final comment = _commentController.text.trim();
//     Logger.lOG("Sending comment: $comment");

//     socket.emit('send_comment', {
//       'room': _currentRoom,
//       'comment': comment,
//       'userId': Prefobj.preferences?.get(Prefkeys.USER_ID),
//       'username': Prefobj.preferences?.get(Prefkeys.NAME),
//       'is_animated': false,
//     });

//     _commentController.clear();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return WillPopScope(
//       onWillPop: () async {
//         if (widget.args[1] == Prefobj.preferences?.get(Prefkeys.USER_ID)) {
//           final shouldPop = await showDialog<bool>(
//             context: context,
//             builder: (context) => CustomAlertDialog(
//               title: "End your live video?",
//               subtitle: "If you end your Live, it'll also end for all of your viewers.",
//               isLoading: false,
//               onConfirmButtonPressed: () async {
//                 await disconnectroom();
//                 toastification.show(
//                   type: ToastificationType.success,
//                   showProgressBar: false,
//                   title: Text(
//                     'Successfully closed the live video.',
//                     style: GoogleFonts.montserrat(
//                       fontSize: 12.0.sp,
//                       fontWeight: FontWeight.w500,
//                     ),
//                   ),
//                   autoCloseDuration: const Duration(seconds: 3),
//                 );
//                 context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
//                 _stopExistingStream();
//                 await _localRenderer.dispose();
//                 socket.dispose();
//                 Navigator.pop(context, true);
//               },
//               confirmButtonText: "End now",
//             ),
//           );
//           Logger.lOG(shouldPop);

//           if (shouldPop == true) {
//             context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
//             Navigator.pop(context, true);
//             return false;
//           }

//           return false;
//         } else {
//           final shouldPop = await showDialog<bool>(
//             context: context,
//             builder: (context) => CustomAlertDialog(
//               title: "Leave live video?",
//               subtitle: "Are you sure you want to leave the live video?",
//               isLoading: false,
//               onConfirmButtonPressed: () async {
//                 await cleanUser();
//                 context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
//                 _stopExistingStream();
//                 await _localRenderer.dispose();
//                 socket.dispose();
//                 Navigator.pop(context, true);
//               },
//               confirmButtonText: "Leave",
//             ),
//           );
//           Logger.lOG(shouldPop);

//           if (shouldPop == true) {
//             context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
//             Navigator.pop(context, true);
//             return false;
//           }

//           return false;
//         }
//       },
//       child: AnnotatedRegion<SystemUiOverlayStyle>(
//         value: SystemUiOverlayStyle.light.copyWith(
//           statusBarIconBrightness: Brightness.light,
//           systemNavigationBarIconBrightness: Brightness.light,
//         ),
//         child: SafeArea(
//           child: Scaffold(
//             extendBodyBehindAppBar: true,
//             backgroundColor: Theme.of(context).customColors.black,
//             appBar: _buildViewStreamAppBar(),
//             body: _isStreaming ? _buildVideoView() : LoadingAnimationWidget(),
//           ),
//         ),
//       ),
//     );
//   }

//   PreferredSizeWidget _buildViewStreamAppBar() {
//     return CustomAppbar(
//       hasLeadingIcon: true,
//       leading: [
//         Padding(
//           padding: EdgeInsets.only(top: 4.0.h),
//           child: Row(
//             children: [
//               InkWell(
//                 onTap: () async {
//                   final isOwner = widget.args[1] == Prefobj.preferences?.get(Prefkeys.USER_ID);
//                   final shouldPop = await showDialog<bool>(
//                     barrierDismissible: false,
//                     context: context,
//                     builder: (context) => CustomAlertDialog(
//                       title: isOwner ? "End your live video?" : "Leave live video?",
//                       subtitle: isOwner
//                           ? "If you end your Live, it'll also end for all of your viewers."
//                           : "Are you sure you want to leave the live video?",
//                       isLoading: false,
//                       confirmButtonText: isOwner ? "End now" : "Leave",
//                       onConfirmButtonPressed: () async {
//                         if (isOwner) {
//                           await disconnectroom();
//                           toastification.show(
//                             type: ToastificationType.success,
//                             showProgressBar: false,
//                             title: Text(
//                               'Successfully closed the live video.',
//                               style: GoogleFonts.montserrat(
//                                 fontSize: 12.0.sp,
//                                 fontWeight: FontWeight.w500,
//                               ),
//                             ),
//                             autoCloseDuration: const Duration(seconds: 3),
//                           );
//                           _stopExistingStream();
//                           await _localRenderer.dispose();
//                           socket.dispose();
//                         } else {
//                           await cleanUser();
//                           _stopExistingStream();
//                           await _localRenderer.dispose();
//                           socket.dispose();
//                         }
//                         context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
//                         Navigator.pop(context, true);
//                       },
//                     ),
//                   );
//                   Logger.lOG(shouldPop);

//                   if (shouldPop == true) {
//                     context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
//                     Navigator.pop(context, true);
//                   }
//                 },
//                 child: CustomImageView(
//                   imagePath: AssetConstants.pngBack,
//                   color: Theme.of(context).customColors.white,
//                   height: 16.0.h,
//                   margin: EdgeInsets.only(left: 10.0.w),
//                 ),
//               ),
//               buildSizedBoxW(10.w),
//               _buildUserProfileImage(),
//               buildSizedBoxW(8),
//               SizedBox(
//                 // color: Colors.red,
//                 width: MediaQuery.of(context).size.width - 242.w,
//                 child: Padding(
//                   padding: EdgeInsets.only(right: 10.w),
//                   child: Text(
//                     maxLines: 1,
//                     overflow: TextOverflow.ellipsis,
//                     uerName.isNotEmpty ? uerName : Prefobj.preferences?.get(Prefkeys.NAME) ?? '',
//                     style: Theme.of(context).textTheme.bodyMedium?.copyWith(
//                           color: Theme.of(context).customColors.white,
//                           fontWeight: FontWeight.w700,
//                           fontSize: 16.0.sp,
//                         ),
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ],
//       actions: [
//         SafeArea(
//           child: Padding(
//             padding: EdgeInsets.only(top: 4.0.h),
//             child: Row(
//               children: [
//                 _buildLiveTag(),
//                 buildSizedBoxW(10),
//                 _buildViewerCount(),
//               ],
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildUserProfileImage() {
//     if (uerName.isNotEmpty) {
//       return ClipRRect(
//         clipBehavior: Clip.hardEdge,
//         child: Padding(
//           padding: const EdgeInsets.all(4.0),
//           child: CustomImageView(
//             radius: BorderRadius.circular(25.r),
//             height: 36.0.h,
//             width: 36.0.w,
//             fit: BoxFit.cover,
//             imagePath:
//                 userProfileImage.isEmpty ? AssetConstants.pngUserReomve : "${APIConfig.mainbaseURL}$userProfileImage",
//             alignment: Alignment.center,
//           ),
//         ),
//       );
//     } else {
//       return Container(
//         height: 36.h,
//         width: 36.w,
//         decoration: BoxDecoration(
//           color: Theme.of(context).customColors.white,
//           shape: BoxShape.circle,
//           boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 5)],
//         ),
//         child: ValueListenableBuilder<String>(
//           valueListenable: profileImageNotifier,
//           builder: (context, imagePath, child) {
//             return Container(
//               height: 36.0.h,
//               width: 36.0.w,
//               padding: imagePath == AssetConstants.pngUser ? EdgeInsets.all(8) : EdgeInsets.zero,
//               child: CustomImageView(
//                 radius: imagePath == AssetConstants.pngUser ? null : BorderRadius.circular(100.0.r),
//                 fit: imagePath == AssetConstants.pngUser ? BoxFit.contain : BoxFit.cover,
//                 imagePath: imagePath,
//                 alignment: (imagePath == AssetConstants.pngUser && imagePath.isEmpty) ? Alignment.center : null,
//               ),
//             );
//           },
//         ),
//       );
//     }
//   }

//   Widget _buildLiveTag() {
//     return Container(
//       padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
//       decoration: BoxDecoration(
//         color: Colors.red,
//         borderRadius: BorderRadius.circular(10.r),
//       ),
//       child: Row(
//         children: [
//           Icon(Icons.circle, color: Colors.white, size: 12),
//           SizedBox(width: 4),
//           Text(
//             'LIVE',
//             style: Theme.of(context).textTheme.bodyMedium?.copyWith(
//                   color: Theme.of(context).customColors.white,
//                   fontWeight: FontWeight.bold,
//                   fontSize: 16.0.sp,
//                 ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildViewerCount() {
//     return Container(
//       padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
//       decoration: BoxDecoration(
//         color: Theme.of(context).primaryColor.withOpacity(0.5),
//         borderRadius: BorderRadius.circular(10.r),
//         border: Border.all(
//           color: Theme.of(context).primaryColor.withOpacity(0.5),
//         ),
//       ),
//       child: Row(
//         children: [
//           Icon(Icons.remove_red_eye_rounded, color: Theme.of(context).customColors.white, size: 16.sp),
//           SizedBox(width: 4),
//           Text(
//             '${_currentUserCount > 0 ? _currentUserCount - 2 : 0}',
//             style: TextStyle(
//               color: Colors.white,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildVideoView() {
//     if (_isRoomCreator) {
//       return Stack(
//         children: [
//           _isCameraOff ? _buildCameraOffView() : _buildLocalVideoView(),
//           _buildEmojiAnimation(),
//           _buildBottomCommentOverlay(),
//           _buildCreatorControls(),
//         ],
//       );
//     } else {
//       return _isLoading
//           ? LoadingAnimationWidget()
//           : Stack(
//               children: [
//                 _buildRemoteVideoView(),
//                 Positioned(
//                   top: 60.h,
//                   right: 16.w,
//                   child: _buildControlButton(
//                     icon: _isCommentsVisible ? Icons.visibility_off : Icons.visibility,
//                     isActive: !_isCommentsVisible,
//                     onTap: () {
//                       setState(() {
//                         _isCommentsVisible = !_isCommentsVisible;
//                       });
//                     },
//                   ),
//                 ),
//                 _buildEmojiAnimation(),
//                 _buildBottomCommentOverlay(padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 8.h)),
//               ],
//             );
//     }
//   }

//   Widget _buildCameraOffView() {
//     return SizedBox.expand(
//       child: Container(
//         color: Colors.black,
//         child: Center(
//           child: ValueListenableBuilder<String>(
//             valueListenable: profileImageNotifier,
//             builder: (context, imagePath, child) {
//               return Container(
//                 height: 200.0.h,
//                 width: 200.0.w,
//                 decoration: BoxDecoration(
//                   shape: BoxShape.circle,
//                   color: Theme.of(context).customColors.white,
//                 ),
//                 padding: imagePath == AssetConstants.pngUser ? EdgeInsets.all(40) : EdgeInsets.zero,
//                 child: CustomImageView(
//                   radius: imagePath == AssetConstants.pngUser ? null : BorderRadius.circular(100.0.r),
//                   fit: imagePath == AssetConstants.pngUser ? BoxFit.contain : BoxFit.cover,
//                   imagePath: imagePath,
//                   alignment: (imagePath == AssetConstants.pngUser && imagePath.isEmpty) ? Alignment.center : null,
//                 ),
//               );
//             },
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildLocalVideoView() {
//     return SizedBox.expand(
//       child: RTCVideoView(
//         _localRenderer,
//         objectFit: RTCVideoViewObjectFit.RTCVideoViewObjectFitCover,
//         mirror: true,
//         filterQuality: FilterQuality.medium,
//       ),
//     );
//   }

//   Widget _buildRemoteVideoView() {
//     // Find the broadcaster's stream (first user who joined)
//     final broadcasterRenderer = _remoteRenderers.values.firstWhere(
//       (renderer) => renderer.srcObject != null,
//       orElse: () => _remoteRenderers.values.first,
//     );

//     return SizedBox.expand(
//       child: RTCVideoView(
//         broadcasterRenderer,
//         objectFit: RTCVideoViewObjectFit.RTCVideoViewObjectFitCover,
//         mirror: true,
//         filterQuality: FilterQuality.medium,
//       ),
//     );
//   }

//   Widget _buildBottomCommentOverlay({EdgeInsets? padding}) {
//     return Positioned(
//       bottom: 0,
//       left: 0,
//       right: 0,
//       child: Container(
//         padding: padding,
//         decoration: BoxDecoration(
//           gradient: LinearGradient(
//             begin: Alignment.bottomCenter,
//             end: Alignment.topCenter,
//             colors: [
//               Colors.black.withOpacity(_isCommentsVisible ? 0.6 : 0.3), // Reduced opacity when comments are hidden
//               Colors.transparent,
//             ],
//           ),
//         ),
//         child: _buildCommentSection(),
//       ),
//     );
//   }

//   Widget _buildCreatorControls() {
//     return Positioned(
//       top: 60.h,
//       right: 16.w,
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.start,
//         children: [
//           if (_isRoomCreator) ...[
//             _buildControlButton(
//               icon: _isMuted ? Icons.mic_off_rounded : Icons.mic_rounded,
//               isActive: _isMuted,
//               onTap: _toggleMute,
//             ),
//             buildSizedBoxH(10.0),
//             _buildControlButton(
//               icon: _isCameraOff ? Icons.videocam_off_rounded : Icons.videocam_rounded,
//               isActive: _isCameraOff,
//               onTap: _toggleCamera,
//             ),
//             buildSizedBoxH(10.0),
//             // Comment visibility toggle button
//             _buildControlButton(
//               icon: _isCommentsVisible ? Icons.visibility_off : Icons.visibility,
//               isActive: !_isCommentsVisible,
//               onTap: () {
//                 setState(() {
//                   _isCommentsVisible = !_isCommentsVisible;
//                 });
//               },
//             ),
//           ],
//         ],
//       ),
//     );
//   }

//   Widget _buildControlButton({
//     required IconData icon,
//     required bool isActive,
//     required VoidCallback onTap,
//   }) {
//     return InkWell(
//       onTap: onTap,
//       child: Container(
//         padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
//         decoration: BoxDecoration(
//           color: Theme.of(context).primaryColor.withOpacity(isActive ? 0.8 : 0.5),
//           borderRadius: BorderRadius.circular(10.r),
//           border: Border.all(color: Theme.of(context).primaryColor),
//         ),
//         child: Icon(
//           icon,
//           color: Colors.white,
//           size: 22.sp,
//         ),
//       ),
//     );
//   }

//   Widget _buildCommentItem(Map<String, dynamic> comment) {
//     return Padding(
//       padding: EdgeInsets.symmetric(vertical: 4.h),
//       child: Row(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             '${comment['username']}: ',
//             style: TextStyle(
//               color: Colors.white,
//               fontWeight: FontWeight.bold,
//               fontSize: 12.sp,
//             ),
//           ),
//           Expanded(
//             child: Text(
//               comment['comment'],
//               style: TextStyle(
//                 color: Colors.white,
//                 fontSize: 12.sp,
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildCommentSection() {
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         // Comments list and input field - only shown when _isCommentsVisible is true

//         // Comments list
//         //  if (_isCommentsVisible)
//         if (_comments.isNotEmpty)
//           Container(
//             padding: EdgeInsets.only(top: 50.h),
//             height: MediaQuery.of(context).size.height / 2.5,
//             child: ListView.builder(
//               padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 8.0.h),
//               reverse: true,
//               itemCount: _comments.length,
//               itemBuilder: (context, index) => _buildCommentItem(_comments[_comments.length - 1 - index]),
//             ),
//           ),
//         Container(
//           height: 40.h,
//           width: double.infinity,
//           margin: EdgeInsets.only(bottom: 8.h),
//           child: SingleChildScrollView(
//             scrollDirection: Axis.horizontal,
//             padding: EdgeInsets.only(left: 16.w, right: 16.w),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//               children: [
//                 _emojiText('💗'),
//                 _emojiText('💸'),
//                 _emojiText('🔥'),
//                 _emojiText('👏🏻'),
//                 _emojiText('😢'),
//                 _emojiText('😍'),
//                 _emojiText('😮'),
//                 _emojiText('😂'),
//                 _emojiText('🎉'),
//                 _emojiText('✨'),
//                 _emojiText('🌟'),
//                 _emojiText('💫'),
//                 _emojiText('💯'),
//                 _emojiText('🙌'),
//                 _emojiText('🤩'),
//                 _emojiText('🥳'),
//                 _emojiText('🎊'),
//                 _emojiText('🏆'),
//                 _emojiText('💪'),
//                 _emojiText('👍'),
//                 _emojiText('❤️'),
//                 _emojiText('💖'),
//                 _emojiText('💝'),
//                 _emojiText('💕'),
//                 _emojiText('💓'),
//                 _emojiText('💞'),
//                 _emojiText('💘'),
//                 _emojiText('💗'),
//                 _emojiText('💓'),
//                 _emojiText('💖'),
//               ],
//             ),
//           ),
//         ),
//         // Comment input
//         if (_isCommentsVisible)
//           Container(
//             margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
//             child: Row(
//               children: [
//                 Expanded(
//                   child: FlowkarTextFormField(
//                     context: context,
//                     hintText: 'Add a comment...',
//                     controller: _commentController,
//                     onChanged: (value) {
//                       _inputText.value = value;
//                     },
//                     onFieldSubmitted: (value) {
//                       _sendComment();
//                     },
//                     textInputAction: TextInputAction.send,
//                     fillColor: Theme.of(context).primaryColor.withOpacity(0.2),
//                     borderDecoration: OutlineInputBorder(
//                       borderSide: BorderSide(color: Theme.of(context).primaryColor),
//                       borderRadius: BorderRadius.circular(10.r),
//                     ),
//                     filled: true,
//                     textStyle: Theme.of(context)
//                         .textTheme
//                         .bodyMedium
//                         ?.copyWith(fontSize: 16.5.sp, color: Theme.of(context).customColors.white),
//                     hintStyle: Theme.of(context)
//                         .textTheme
//                         .bodyMedium
//                         ?.copyWith(fontSize: 16.5.sp, color: Theme.of(context).customColors.white),
//                     suffix: ValueListenableBuilder<String>(
//                       valueListenable: _inputText,
//                       builder: (_, inputTextValue, __) {
//                         if (inputTextValue.isNotEmpty) {
//                           return Padding(
//                             padding: EdgeInsets.only(right: 0.0.w),
//                             child: InkWell(
//                                 onTap: () {
//                                   _sendComment();
//                                 },
//                                 child: Icon(
//                                   Icons.send_rounded,
//                                   color: Theme.of(context).customColors.white,
//                                 )),
//                           );
//                         } else {
//                           return SizedBox.shrink();
//                         }
//                       },
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//       ],
//       // Emoji row - always visible
//     );
//   }

//   void _showEmojiAnimation(String emoji) {
//     // Number of emojis to shoot
//     final int emojiCount = Random().nextInt(2) + 8; // Random number between 3-4

//     // Create multiple emojis with slight variations
//     for (int i = 0; i < emojiCount; i++) {
//       // Add slight delay between each emoji for a more dynamic effect
//       Future.delayed(Duration(milliseconds: i * 100), () {
//         if (!mounted) return;

//         setState(() {
//           // Calculate random starting position with some spread
//           final screenWidth = MediaQuery.of(context).size.width;
//           final baseX = Random().nextDouble() * (screenWidth - 100);
//           final spreadX = (Random().nextDouble() - 0.5) * 100; // Spread of ±50 pixels
//           final startX = (baseX + spreadX).clamp(0.0, screenWidth - 100);

//           _activeEmojis.add({
//             'emoji': emoji,
//             'position': Offset(
//               startX,
//               MediaQuery.of(context).size.height, // Start from bottom
//             ),
//             'opacity': 1.0,
//             'animation': null,
//             'controller': AnimationController(
//               vsync: this,
//               duration: Duration(milliseconds: 1800), // Slightly varied duration
//             ),
//           });
//         });

//         // Start animation with slight delay
//         Future.delayed(Duration(milliseconds: 20), () {
//           if (mounted) {
//             setState(() {
//               final lastEmoji = _activeEmojis.last;
//               final controller = lastEmoji['controller'] as AnimationController;

//               // Add slight random variation to the end position
//               final randomOffset = (Random().nextDouble() - 0.5) * 40; // ±20 pixels variation

//               lastEmoji['animation'] = Tween<double>(
//                 begin: MediaQuery.of(context).size.height,
//                 end: -100.0 + randomOffset, // Slightly varied end position
//               ).animate(CurvedAnimation(
//                 parent: controller,
//                 curve: Curves.easeOut,
//               ));

//               // Add listener to remove emoji when animation completes
//               controller.addStatusListener((status) {
//                 if (status == AnimationStatus.completed) {
//                   if (mounted && _activeEmojis.isNotEmpty) {
//                     setState(() {
//                       final emojiToRemove = _activeEmojis.removeAt(0);
//                       emojiToRemove['controller'].dispose();
//                     });
//                   }
//                 }
//               });

//               controller.forward();
//             });
//           }
//         });
//       });
//     }
//   }

//   Widget _buildEmojiAnimation() {
//     return Stack(
//       children: _activeEmojis.map((emojiData) {
//         final animation = emojiData['animation'] as Animation<double>?;
//         if (animation == null) return SizedBox.shrink();

//         return AnimatedBuilder(
//           animation: animation,
//           builder: (context, child) {
//             // Calculate opacity based on position
//             final progress = animation.value / MediaQuery.of(context).size.height;
//             final opacity = 1.0 - (progress * 1.5).clamp(0.0, 1.0); // Fade out faster near top

//             return Positioned(
//               left: emojiData['position'].dx,
//               top: animation.value,
//               child: Opacity(
//                 opacity: opacity,
//                 child: Text(
//                   emojiData['emoji'],
//                   style: TextStyle(fontSize: 50.sp),
//                 ),
//               ),
//             );
//           },
//         );
//       }).toList(),
//     );
//   }

//   Widget _emojiText(String emoji) {
//     return InkWell(
//       onTap: () {
//         socket.emit('send_comment', {
//           'room': _currentRoom,
//           'comment': emoji,
//           'userId': Prefobj.preferences?.get(Prefkeys.USER_ID),
//           'username': Prefobj.preferences?.get(Prefkeys.NAME),
//           'is_animated': true,
//         });
//         _showEmojiAnimation(emoji);
//       },
//       child: Container(
//         margin: EdgeInsets.only(right: 4.w),
//         padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
//         decoration: BoxDecoration(
//           color: Theme.of(context).primaryColor.withOpacity(0.5),
//           // color: Colors.black,
//           borderRadius: BorderRadius.circular(10.r),
//           border: Border.all(color: Theme.of(context).primaryColor),
//         ),
//         child: Text(
//           emoji,
//           style: TextStyle(fontSize: 20.sp),
//         ),
//       ),
//     );
//   }
// }

import 'package:flowkar/core/services/pip_service.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';

import 'package:permission_handler/permission_handler.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'dart:math';
import 'dart:async';

class LiveStreamPage extends StatefulWidget {
  final dynamic args;

  const LiveStreamPage({super.key, required this.args});
  static Widget builder(BuildContext context) {
    var args = ModalRoute.of(context)?.settings.arguments;
    //args[0]-->liveid -->  liv-userid
    //args[1]-->userId -->  userId

    Logger.lOG("LiveStreamPage: ${(args as List?)?[0]}");
    return LiveStreamPage(args: args);
  }

  @override
  State<LiveStreamPage> createState() => _LiveStreamPageState();
}

class _LiveStreamPageState extends State<LiveStreamPage> with TickerProviderStateMixin, WidgetsBindingObserver {
  // Controllers
  final TextEditingController _commentController = TextEditingController();
  final ValueNotifier<String> _inputText = ValueNotifier('');
  bool _isCommentsVisible = true;

  // WebRTC
  final RTCVideoRenderer _localRenderer = RTCVideoRenderer();
  final Map<String, RTCVideoRenderer> _remoteRenderers = {};
  final Map<String, RTCPeerConnection> _peerConnections = {};
  MediaStream? _localStream;
  final Set<String> _remoteStreamIds = {};
  String? _currentRoom;
  bool _isMuted = false;
  bool _isCameraOff = false;
  bool _isFrontCamera = true; // Track camera facing mode

  // Room role
  bool _isRoomCreator = false;
  bool showVideoWhenJoining = false;

  // Socket
  late io.Socket socket;
  String status = "Disconnected";
  bool isStatusError = false;

  // UI state
  bool isConnected = false;
  bool _isStreaming = false;
  bool _isLoading = true;
  String uerName = '';
  String userProfileImage = '';
  int _currentUserCount = 0;

  // Comments
  List<Map<String, dynamic>> _comments = [];

  // Emoji animation
  final List<Map<String, dynamic>> _activeEmojis = [];
  Timer? _emojiTimer;
  StreamSubscription<ConnectivityState>? _connectivitySubscription;
  Timer? _networkTimer;

  // PIP functionality
  final PipService _pipService = PipService();
  bool _isPipSupported = false;
  bool _isPipActive = false;
  bool _pipPermissionGranted = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeRenderers();
    _initializeSocket();

    _initializePip();
    _requestPipPermission();

    WakelockPlus.enable();

    if (widget.args[1] != Prefobj.preferences?.get(Prefkeys.USER_ID)) {
      uerName = widget.args[2].toString();
      userProfileImage = widget.args[3].toString();
      Logger.lOG("USER NAME: $uerName");
      Logger.lOG("USER PROFILE IMAGE: $userProfileImage");
      _joinRoom();
      Logger.lOG("VIEWR JOIN");
    } else {
      _createRoom();
      Logger.lOG("ROOM CREATE");
    }
    Future.delayed(Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
    // _connectivitySubscription = context.read<ConnectivityBloc>().stream.listen(_onConnectivityChanged);
  }

  void _onConnectivityChanged(ConnectivityState state) {
    if (!state.isConnected) {
      // Start 30s timer if not already running
      _networkTimer ??= Timer(Duration(seconds: 15), _handleNetworkTimeout);
    } else if (state.isConnected) {
      // Cancel timer if network is back
      _networkTimer?.cancel();
      _networkTimer = null;
    }
  }

  void _handleNetworkTimeout() async {
    toastification.show(
      type: ToastificationType.error,
      showProgressBar: false,
      description: Text(
        maxLines: 3,
        'Live stream ended due to network issues.',
        style: GoogleFonts.montserrat(
          fontSize: 12.0.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
      autoCloseDuration: const Duration(seconds: 3),
    );
    if (widget.args[1] == Prefobj.preferences?.get(Prefkeys.USER_ID)) {
      // Stream creator: close stream
      await disconnectroom();
      _stopExistingStream();
      await _localRenderer.dispose();
      socket.dispose();
      if (mounted) Navigator.pop(context, true);
    } else {
      // Viewer: leave stream
      await cleanUser();
      _stopExistingStream();
      await _localRenderer.dispose();
      socket.dispose();
      if (mounted) Navigator.pop(context, true);
    }
  }

  // @override
  // void dispose() {
  //   WidgetsBinding.instance.removeObserver(this);
  //   // Disable wakelock when stream ends
  //   WakelockPlus.disable();
  //   // Cancel all animation controllers
  //   for (var emoji in _activeEmojis) {
  //     if (emoji['controller'] != null) {
  //       emoji['controller'].dispose();
  //     }
  //   }
  //   _emojiTimer?.cancel();
  //   _commentController.dispose();
  //   if (widget.args[1] == Prefobj.preferences?.get(Prefkeys.USER_ID)) {
  //     disconnectroom();
  //   } else {
  //     cleanUser();
  //   }
  //   _stopExistingStream();
  //   _localRenderer.dispose();
  //   socket.dispose();
  //   super.dispose();
  // }
  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    _networkTimer?.cancel();
    WidgetsBinding.instance.removeObserver(this);

    // Disable wakelock when stream ends
    WakelockPlus.disable();

    // Cancel all animation controllers
    for (var emoji in _activeEmojis) {
      if (emoji['controller'] != null) {
        emoji['controller'].dispose();
      }
    }
    _emojiTimer?.cancel();
    _commentController.dispose();

    // Force stop all audio/video before cleanup
    _forceStopAllMedia();

    // Cleanup based on user role
    if (widget.args[1] == Prefobj.preferences?.get(Prefkeys.USER_ID)) {
      // Stream creator cleanup
      disconnectroom();
    } else {
      // Viewer cleanup - this is crucial for stopping audio
      cleanUser();
    }

    _stopExistingStream();
    _localRenderer.dispose();
    socket.dispose();

    super.dispose();
  }

// New method to force stop all media
  void _forceStopAllMedia() {
    // Stop local stream tracks
    if (_localStream != null) {
      _localStream!.getTracks().forEach((track) {
        track.stop();
        track.enabled = false;
      });
    }

    // Stop all remote stream tracks
    for (var renderer in _remoteRenderers.values) {
      if (renderer.srcObject != null) {
        renderer.srcObject!.getTracks().forEach((track) {
          track.stop();
          track.enabled = false;
        });
        // Immediately set to null to stop playback
        renderer.srcObject = null;
      }
    }

    // Close all peer connections immediately
    for (var pc in _peerConnections.values) {
      pc.close();
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    Logger.lOG("App lifecycle state changed to: $state");

    if (state == AppLifecycleState.paused || state == AppLifecycleState.inactive) {
      if (_isPipSupported && _pipPermissionGranted && _isStreaming && !_isPipActive) {
        Logger.lOG("App going to background - automatically starting PIP");
        final pipStarted = await _pipService.startPip();
        if (pipStarted) {
          Logger.lOG("PIP started successfully");
          return;
        } else {
          Logger.lOG("Failed to start PIP, proceeding with normal background handling");
        }
        Future.delayed(const Duration(seconds: 1), () {
          // ignore: use_build_context_synchronously
          context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
        });
      } else if (!_pipPermissionGranted && _isPipSupported) {
        Logger.lOG("PIP permission not granted, not starting PIP mode");
      }
    } else if (state == AppLifecycleState.resumed) {
      if (_isPipActive) {
        Logger.lOG("App resumed - automatically stopping PIP");
        await _pipService.stopPip();
      }

      // ignore: use_build_context_synchronously
      context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());

      return;
    }

    if (state == AppLifecycleState.detached || state == AppLifecycleState.paused) {
      Logger.lOG("App being terminated or going to background - initiating cleanup");

      // Force stop all media first
      _forceStopAllMedia();

      if (widget.args[1] == Prefobj.preferences?.get(Prefkeys.USER_ID)) {
        // Stream creator cleanup
        await disconnectroom();
        _stopExistingStream();
        await _localRenderer.dispose();
        socket.dispose();
        WakelockPlus.disable();
        if (mounted) {
          Navigator.pop(context, true);
          context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
        }
      } else {
        // Viewer cleanup
        await cleanUser();
        _stopExistingStream();
        await _localRenderer.dispose();
        socket.dispose();
        WakelockPlus.disable();
        if (mounted) {
          Navigator.pop(context, true);
          context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
        }
      }
    }
  }

  void debugMediaState() {
    Logger.lOG("=== Media State Debug ===");
    Logger.lOG("Local stream: ${_localStream != null}");
    Logger.lOG("Remote renderers: ${_remoteRenderers.length}");
    Logger.lOG("Peer connections: ${_peerConnections.length}");
    Logger.lOG("Remote stream IDs: ${_remoteStreamIds.length}");

    for (var entry in _remoteRenderers.entries) {
      Logger.lOG("Renderer ${entry.key}: ${entry.value.srcObject != null}");
      if (entry.value.srcObject != null) {
        for (var track in entry.value.srcObject!.getTracks()) {
          Logger.lOG("  Track ${track.kind}: enabled=${track.enabled}");
        }
      }
    }
    Logger.lOG("========================");
  }
  // void didChangeAppLifecycleState(AppLifecycleState state) async {
  //   Logger.lOG("App lifecycle state changed to: $state");

  //   if (state == AppLifecycleState.detached || state == AppLifecycleState.paused) {
  //     Logger.lOG("App being terminated or going to background - initiating cleanup");

  //     if (widget.args[1] == Prefobj.preferences?.get(Prefkeys.USER_ID)) {
  //       // Stream creator cleanup
  //       await disconnectroom();
  //       _stopExistingStream();
  //       await _localRenderer.dispose();
  //       socket.dispose();
  //       WakelockPlus.disable();
  //       if (mounted) {
  //         Navigator.pop(context, true);
  //       }
  //       if (mounted) {
  //         context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
  //       }
  //     } else {
  //       // Viewer cleanup
  //       await cleanUser();
  //       _stopExistingStream();
  //       await _localRenderer.dispose();
  //       socket.dispose();
  //       WakelockPlus.disable();
  //       if (mounted) {
  //         Navigator.pop(context, true);
  //       }
  //       if (mounted) {
  //         context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
  //       }
  //     }
  //   }
  // }

  Future<void> _initializeRenderers() async {
    await _localRenderer.initialize();
  }

  /// Initialize PIP service
  Future<void> _initializePip() async {
    try {
      _isPipSupported = await _pipService.isSupported();
      if (_isPipSupported) {
        final initialized = await _pipService.initialize();
        if (initialized) {
          // Set up PIP state change callback
          _pipService.onPipStateChanged = (state, error) {
            _handlePipStateChange(state, error);
          };
          Logger.lOG("PIP service initialized successfully");
        } else {
          Logger.lOG("Failed to initialize PIP service");
        }
      } else {
        Logger.lOG("PIP is not supported on this device");
      }
    } catch (e) {
      Logger.lOG("Error initializing PIP: $e");
    }
  }

  /// Request PIP permission from user
  Future<void> _requestPipPermission() async {
    if (!_isPipSupported) {
      Logger.lOG("PIP not supported, skipping permission request");
      return;
    }

    // Wait a bit for the screen to load before showing the dialog
    await Future.delayed(const Duration(seconds: 2));

    if (!mounted) return;

    final shouldGrantPermission = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(
          'Picture-in-Picture Permission',
          style: GoogleFonts.montserrat(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'This app would like to use Picture-in-Picture mode to continue showing the live stream when you switch to other apps. Do you want to allow this?',
          style: GoogleFonts.montserrat(
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              'Not Now',
              style: GoogleFonts.montserrat(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(
              'Allow',
              style: GoogleFonts.montserrat(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
        ],
      ),
    );

    if (mounted) {
      setState(() {
        _pipPermissionGranted = shouldGrantPermission ?? false;
      });

      if (_pipPermissionGranted) {
        Logger.lOG("PIP permission granted by user");
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          description: Text(
            'Picture-in-Picture enabled! The live stream will continue in a small window when you switch apps.',
            style: GoogleFonts.montserrat(
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 4),
        );
      } else {
        Logger.lOG("PIP permission denied by user");
        toastification.show(
          type: ToastificationType.info,
          showProgressBar: false,
          description: Text(
            'Picture-in-Picture disabled. The live stream will pause when you switch apps.',
            style: GoogleFonts.montserrat(
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
      }
    }

    Logger.lOG("PIP permission request completed. Granted: $_pipPermissionGranted, Supported: $_isPipSupported");
  }

  /// Handle PIP state changes and update UI accordingly
  void _handlePipStateChange(dynamic state, String? error) {
    if (!mounted) return;

    setState(() {
      _isPipActive = _pipService.isPipActiveCached;
    });

    // Handle different PIP states
    switch (state.toString()) {
      case 'PipState.pipStateStarted':
        Logger.lOG("PIP mode activated - minimizing UI elements");
        // Optionally hide comments or other UI elements in PIP mode
        if (mounted) {
          setState(() {
            _isPipActive = true;
            _isCommentsVisible = false; // Hide comments in PIP mode
          });
        }
        break;
      case 'PipState.pipStateStopped':
        Logger.lOG("PIP mode deactivated - restoring UI elements");
        // Restore UI elements when returning from PIP
        if (mounted) {
          setState(() {
            _isPipActive = false;
            _isCommentsVisible = true; // Show comments when back from PIP
          });
        }
        break;
      case 'PipState.pipStateFailed':
        Logger.lOG("PIP failed: $error");
        // Handle PIP failure - maybe show a toast or fallback behavior
        break;
    }
  }

  /// Safely handle navigation back with proper context management
  Future<void> _safeNavigateBack() async {
    if (mounted) {
      // Store context before any async operations
      final currentContext = context;

      // Update home feed
      currentContext.read<HomeFeedBloc>().add(GetLivestoryApiEvent());

      // Safe navigation with proper checks
      if (Navigator.of(currentContext).canPop()) {
        Navigator.pop(currentContext, true);
      } else {
        // If can't pop, navigate to bottom nav bar (main screen) instead
        Logger.lOG("Cannot pop - navigating to main screen");
        Navigator.of(currentContext).pushNamedAndRemoveUntil(
          '/bottom_bar_screen',
          (route) => false,
        );
      }
    }
  }

  void _initializeSocket() {
    socket = io.io(
      APIConfig.liveStreamUrl,
      io.OptionBuilder().setTransports(['websocket']).setPath('/socket.io/').disableAutoConnect().build(),
    );

    socket.onConnect((_) {
      Logger.lOG("Socket Connected Successfully");
      if (mounted) {
        setState(() {
          isConnected = true;
        });
      }
    });

    socket.onDisconnect((_) {
      Logger.lOG("Socket Disconnected");
      if (mounted) {
        setState(() {
          isConnected = false;
        });
      }
    });

    socket.on('check_room', (data) {
      if (data['valid']) {
        setState(() {
          _isRoomCreator = false;
        });
        _startStreaming(_currentRoom!);
      } else {
        _setStatus("Room does not exist! Please enter a valid room.", true);
      }
    });

    socket.on('room_created', (data) {
      _setStatus("Room '${data['room']}' created successfully!");
      setState(() {
        _isRoomCreator = true;
      });
      _startStreaming(data['room']);
    });

    socket.on('user_joined', (data) async {
      if (data['room'] == _currentRoom) {
        final pc = await _createPeerConnection(data['userId']);

        try {
          final offer = await pc.createOffer();
          await pc.setLocalDescription(offer);
          socket.emit('offer', {
            'offer': {'type': offer.type, 'sdp': offer.sdp},
            'userId': data['userId'],
            'room': _currentRoom,
          });
        } catch (e) {
          Logger.lOG("Error creating offer: $e");
        }
      }
    });

    socket.on('offer', (data) async {
      if (data['room'] == _currentRoom) {
        final pc = await _createPeerConnection(data['userId']);

        try {
          await pc.setRemoteDescription(
            RTCSessionDescription(data['offer']['sdp'], data['offer']['type']),
          );

          final answer = await pc.createAnswer();
          await pc.setLocalDescription(answer);

          socket.emit('answer', {
            'answer': {'type': answer.type, 'sdp': answer.sdp},
            'userId': data['userId'],
            'room': _currentRoom,
          });
        } catch (e) {
          Logger.lOG("Error handling offer: $e");
        }
      }
    });

    socket.on('answer', (data) async {
      if (data['room'] == _currentRoom) {
        try {
          final pc = _peerConnections[data['userId']];
          if (pc != null) {
            await pc.setRemoteDescription(
              RTCSessionDescription(
                data['answer']['sdp'],
                data['answer']['type'],
              ),
            );
          }
        } catch (e) {
          Logger.lOG("Error handling answer: $e");
        }
      }
    });

    socket.on('ice_candidate', (data) async {
      if (data['room'] == _currentRoom) {
        try {
          final pc = _peerConnections[data['userId']];
          if (pc != null) {
            await pc.addCandidate(
              RTCIceCandidate(
                data['candidate']['candidate'],
                data['candidate']['sdpMid'],
                data['candidate']['sdpMLineIndex'],
              ),
            );
          }
        } catch (e) {
          Logger.lOG("Error adding ICE candidate: $e");
        }
      }
    });

    socket.on('user_left', (data) {
      _cleanupUser(data['userId']);
    });

    socket.on('room_closed', (data) {
      if (data['room'] == _currentRoom && !_isRoomCreator) {
        _handleRoomClosed();
      }
    });

    // socket.on('new_comment', (data) {
    //   Logger.lOG("New comment received: $data");
    //   if (mounted && data['room'] == _currentRoom) {
    //     setState(() {
    //       _comments.add({
    //         'userId': data['userId'] ?? '',
    //         'username': data['username'] ?? '',
    //         'comment': data['comment'] ?? '',
    //         'timestamp': DateTime.now().millisecondsSinceEpoch,
    //         'is_animated': data['is_animated'] ?? false,
    //       });
    //       if (data['is_animated'] == true && data['comment'] is String && data['comment'].isNotEmpty) {
    //         _showEmojiAnimation(data['comment']);
    //       }
    //     });
    //   }
    // });
    // NEW: Listen for camera status updates
    socket.on('camera_status', (data) {
      Logger.lOG("Camera status updated: $data");
      if (mounted && data['room'] == _currentRoom) {
        setState(() {
          _isCameraOff = data['is_camera_on'] ?? true;
        });
        Logger.lOG("Streamer camera status updated to: $_isCameraOff");
      }
    });

    socket.on('camera_flipped', (data) {
      Logger.lOG("Camera flip notification received: $data");
      if (mounted && data['room'] == _currentRoom) {
        setState(() {
          _isFrontCamera = data['is_front_camera'] ?? true;
        });
        Logger.lOG("Streamer camera flipped to: ${_isFrontCamera ? 'front' : 'back'} camera");
      }
    });

    socket.on('current_users_number', (data) {
      Logger.lOG("Current Users Count Response: $data");
      if (data['room'] == _currentRoom) {
        setState(() {
          _currentUserCount = data['count'] ?? 0;
        });
      }
    });

    socket.on('comment_list', (data) {
      Logger.lOG("Comment list received: $data");
      if (mounted && data['room'] == _currentRoom) {
        setState(() {
          _comments.clear();
          if (data['comments'] != null) {
            _comments.addAll((data['comments'] as List)
                .map((comment) => {
                      'userId': comment['userId'] ?? '',
                      'username': comment['username'] ?? '',
                      'comment': comment['comment'] ?? '',
                      'timestamp': comment['timestamp'] ?? DateTime.now().millisecondsSinceEpoch,
                      'is_animated': comment['is_animated'] ?? false,
                    })
                .toList());
          }
          Logger.lOG("Updated comment list with ${_comments.length} comments");
        });
      }
    });

    socket.on('new_comment', (data) {
      Logger.lOG("New comment received: $data");
      if (mounted && data['room'] == _currentRoom) {
        setState(() {
          _comments.add({
            'userId': data['userId'] ?? '',
            'username': data['username'] ?? '',
            'comment': data['comment'] ?? '',
            'timestamp': DateTime.now().millisecondsSinceEpoch,
            'is_animated': data['is_animated'] ?? false,
          });
          if (data['is_animated'] == true && data['comment'] is String && data['comment'].isNotEmpty) {
            _showEmojiAnimation(data['comment']);
          }
        });
      }
    });

    socket.connect();
  }

  void _handleRoomClosed() {
    context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
    toastification.show(
      type: ToastificationType.warning,
      showProgressBar: false,
      title: Text(
        'The live has ended by the host.',
        style: GoogleFonts.montserrat(
          fontSize: 12.0.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
      autoCloseDuration: const Duration(seconds: 3),
    );

    _stopExistingStream();
    _localRenderer.dispose();
    socket.dispose();

    Navigator.of(context).pop();
  }

  void _setStatus(String message, [bool isError = false]) {
    setState(() {
      status = message;
      isStatusError = isError;
    });
  }

  Future<RTCPeerConnection> _createPeerConnection(String userId) async {
    if (_peerConnections.containsKey(userId)) {
      Logger.lOG("Peer connection already exists for: $userId");
      return _peerConnections[userId]!;
    }

    final config = {
      'iceServers': [
        {'urls': 'stun:stun.l.google.com:19302'},
        {'urls': 'stun:stun1.l.google.com:19302'},
      ],
    };

    final pc = await createPeerConnection(config);

    pc.onIceCandidate = (candidate) {
      socket.emit('ice_candidate', {
        'candidate': {
          'candidate': candidate.candidate,
          'sdpMid': candidate.sdpMid,
          'sdpMLineIndex': candidate.sdpMLineIndex,
        },
        'userId': userId,
        'room': _currentRoom,
      });
    };

    pc.onTrack = (RTCTrackEvent event) async {
      if (event.streams.isNotEmpty) {
        final streamId = event.streams[0].id;

        if (!_remoteStreamIds.contains(streamId)) {
          _remoteStreamIds.add(streamId);

          final renderer = RTCVideoRenderer();
          await renderer.initialize();
          renderer.srcObject = event.streams[0];

          setState(() {
            _remoteRenderers[userId] = renderer;
          });
        }
      }
    };

    // Only add local stream tracks if this is the room creator (host)
    if (_localStream != null) {
      _localStream?.getTracks().forEach((track) {
        pc.addTrack(track, _localStream!);
      });
    }

    _peerConnections[userId] = pc;
    return pc;
  }

  // void _stopExistingStream() {
  //   if (_localStream != null) {
  //     _localStream?.getTracks().forEach((track) => track.stop());
  //     _localStream = null;
  //     if (mounted) {
  //       setState(() {
  //         _isLoading = false;
  //       });
  //     }
  //     if (mounted) {
  //       setState(() {
  //         _isLoading = false;
  //       });
  //     }

  //     setState(() {
  //       _isStreaming = false;
  //     });

  //     for (var pc in _peerConnections.values) {
  //       pc.close();
  //     }
  //     _peerConnections.clear();

  //     for (var renderer in _remoteRenderers.values) {
  //       if (renderer.srcObject != null) {
  //         renderer.srcObject?.getTracks().forEach((track) {
  //           track.stop();
  //         });
  //       }
  //       renderer.srcObject = null;
  //       renderer.dispose();
  //     }
  //     _remoteRenderers.clear();

  //     // Clear tracked streams
  //     _remoteStreamIds.clear();
  //   }
  // }

  void _stopExistingStream() {
    Logger.lOG("Stopping existing stream");

    // Stop local stream
    if (_localStream != null) {
      _localStream!.getTracks().forEach((track) {
        track.stop();
        track.enabled = false;
      });
      _localStream = null;
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
        _isStreaming = false;
      });
    }

    // Close all peer connections
    for (var pc in _peerConnections.values) {
      pc.close();
    }
    _peerConnections.clear();

    // Stop and dispose all remote renderers
    for (var renderer in _remoteRenderers.values) {
      if (renderer.srcObject != null) {
        renderer.srcObject!.getTracks().forEach((track) {
          track.stop();
          track.enabled = false;
        });
        renderer.srcObject = null;
      }
      renderer.dispose();
    }
    _remoteRenderers.clear();

    // Clear tracked streams
    _remoteStreamIds.clear();
  }

  // void _cleanupUser(String userId) {
  //   if (_remoteRenderers.containsKey(userId)) {
  //     final renderer = _remoteRenderers[userId];
  //     // Stop all tracks from the remote stream before disposing
  //     if (renderer?.srcObject != null) {
  //       renderer?.srcObject?.getTracks().forEach((track) {
  //         track.stop();
  //       });
  //     }
  //     renderer?.srcObject = null;
  //     renderer?.dispose();
  //     _remoteRenderers.remove(userId);
  //   }

  //   if (_peerConnections.containsKey(userId)) {
  //     _peerConnections[userId]?.close();
  //     _peerConnections.remove(userId);
  //   }

  //   // Only update user count if we're not the room creator
  //   if (!_isRoomCreator) {
  //     setState(() {
  //       _currentUserCount = (_currentUserCount - 1).clamp(0, _currentUserCount);
  //     });
  //   }
  // }
  void _cleanupUser(String userId) {
    Logger.lOG("Cleaning up user: $userId");

    if (_remoteRenderers.containsKey(userId)) {
      final renderer = _remoteRenderers[userId]!;

      // Stop all tracks from the remote stream
      if (renderer.srcObject != null) {
        renderer.srcObject!.getTracks().forEach((track) {
          Logger.lOG("Stopping track: ${track.kind}");
          track.stop();
          track.enabled = false;
        });
        // Immediately set to null
        renderer.srcObject = null;
      }

      renderer.dispose();
      _remoteRenderers.remove(userId);
    }

    if (_peerConnections.containsKey(userId)) {
      final pc = _peerConnections[userId]!;
      pc.close();
      _peerConnections.remove(userId);
    }

    // Remove from tracked streams
    _remoteStreamIds.removeWhere((streamId) => streamId.contains(userId));

    // Only update user count if we're not the room creator
    if (!_isRoomCreator && mounted) {
      setState(() {
        _currentUserCount = (_currentUserCount - 1).clamp(0, _currentUserCount);
      });
    }
  }

  Future<void> _createRoom() async {
    final room = widget.args[0].toString();
    if (room.isEmpty) {
      _setStatus("Please enter a room name.", true);
      return;
    }

    _stopExistingStream();
    _currentRoom = room;
    socket.emit("create_room", {"room": room});
    Logger.lOG("Room created: $room");

    toastification.show(
      type: ToastificationType.success,
      showProgressBar: false,
      title: Text(
        'Successfully created the live video.',
        style: GoogleFonts.montserrat(
          fontSize: 12.0.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
      autoCloseDuration: const Duration(seconds: 3),
    );
  }

  Future<void> _joinRoom() async {
    final room = widget.args[0].toString();
    if (room.isEmpty) {
      _setStatus("Please enter a room name.", true);
      return;
    }

    _stopExistingStream();
    _currentRoom = room;

    setState(() {
      showVideoWhenJoining = false;
    });

    try {
      socket.emit("check_room", {"room": room});
      socket.emit("join_room", {"room": room});

      // Request comment list when joining
      Logger.lOG("Requesting comment list for room: $room");
      socket.emit('get_comment_list', {
        'room': room,
      });
    } catch (e) {
      Logger.lOG("Error joining room: $e");
      _setStatus("Failed to join room. Please try again.", true);
    }
  }

  Future<void> disconnectroom() async {
    final room = widget.args[0].toString();
    socket.emit("close_room", {"room": room});
    Logger.lOG("Room closed: $room");
    Logger.lOG("Room closed: $room");
  }

  // Future<void> cleanUser() async {
  //   final room = widget.args[0].toString();
  //   for (var renderer in _remoteRenderers.values) {
  //     if (renderer.srcObject != null) {
  //       renderer.srcObject!.getTracks().forEach((track) {
  //         track.stop();
  //       });
  //     }
  //   }
  //   socket.emit("leave_stream", {"room": room});
  //   // Don't close the room, just leave
  // }
  Future<void> cleanUser() async {
    final room = widget.args[0].toString();
    Logger.lOG("Viewer leaving room: $room");

    // Force stop all remote streams immediately
    for (var renderer in _remoteRenderers.values) {
      if (renderer.srcObject != null) {
        renderer.srcObject!.getTracks().forEach((track) {
          Logger.lOG("Force stopping viewer track: ${track.kind}");
          track.stop();
          track.enabled = false;
        });
        renderer.srcObject = null;
      }
    }

    // Close all peer connections
    for (var pc in _peerConnections.values) {
      pc.close();
    }

    socket.emit("leave_stream", {"room": room});
  }

  Future<void> _startStreaming(String room) async {
    if (_isRoomCreator) {
      // Only request permissions and media access for room creators (hosts)
      if (await _requestPermissions()) {
        try {
          final constraints = {
            'video': {
              'width': {'ideal': 640},
              'height': {'ideal': 480},
              'facingMode': _isFrontCamera ? 'user' : 'environment',
              'mandatory': {
                'minWidth': 320,
                'minHeight': 240,
                'maxWidth': 1280,
                'maxHeight': 720,
              },
            },
            'audio': _isRoomCreator, // Host always needs audio
          };

          _localStream = await navigator.mediaDevices.getUserMedia(constraints);
          _localRenderer.srcObject = _localStream;

          setState(() {
            _isStreaming = true;
          });

          socket.emit("join_room", {"room": room});
        } catch (e) {
          Logger.lOG("Error accessing media devices: $e");
          _setStatus("Error accessing camera and microphone!", true);
        }
      } else {
        openAppSettings();
      }
    } else {
      // Viewers don't need camera/mic access - just join the room
      try {
        setState(() {
          _isStreaming = true;
        });

        socket.emit("join_room", {"room": room});
        Logger.lOG("Viewer joined room without media access");
      } catch (e) {
        Logger.lOG("Error joining room as viewer: $e");
        _setStatus("Failed to join the live stream!", true);
      }
    }
  }

  Future<bool> _requestPermissions() async {
    if (_isRoomCreator) {
      // Only request permissions for room creators (hosts)
      Map<Permission, PermissionStatus> statuses = await [Permission.camera, Permission.microphone].request();

      bool cameraGranted = statuses[Permission.camera]?.isGranted == true;
      bool microphoneGranted = statuses[Permission.microphone]?.isGranted == true;

      if (!cameraGranted || !microphoneGranted) {
        Logger.lOG("Camera granted: $cameraGranted, Microphone granted: $microphoneGranted");
        _setStatus("Camera and microphone access required to start live streaming!", true);
      }

      return cameraGranted && microphoneGranted;
    } else {
      // Viewers don't need any permissions
      Logger.lOG("Viewer joining - no permissions required");
      return true;
    }
  }

  void _toggleMute() {
    if (_localStream != null) {
      setState(() {
        _isMuted = !_isMuted;
      });
      _localStream!.getAudioTracks().forEach((track) {
        track.enabled = !_isMuted;
      });
    }
  }

  void _toggleCamera() {
    if (_localStream != null) {
      setState(() {
        _isCameraOff = !_isCameraOff;
      });
      _localStream!.getVideoTracks().forEach((track) {
        track.enabled = !_isCameraOff;
      });
    }
    if (_isRoomCreator && _currentRoom != null) {
      socket.emit('update_camera_status', {
        'room': _currentRoom,
        'is_camera_on': _isCameraOff,
      });
      // Logger.lOG("Camera status updated: $_isCameraOff");
    }
  }

  Future<void> _flipCamera() async {
    if (_localStream != null && _isRoomCreator) {
      try {
        // Stop current video tracks
        _localStream!.getVideoTracks().forEach((track) {
          track.stop();
        });

        // Toggle camera facing mode
        setState(() {
          _isFrontCamera = !_isFrontCamera;
        });

        // Create new constraints with updated facing mode
        final constraints = {
          'video': {
            'width': {'ideal': 640},
            'height': {'ideal': 480},
            'facingMode': _isFrontCamera ? 'user' : 'environment',
            'mandatory': {
              'minWidth': 320,
              'minHeight': 240,
              'maxWidth': 1280,
              'maxHeight': 720,
            },
          },
          'audio': true, // Keep audio for creator
        };

        // Get completely new stream with new camera
        final newStream = await navigator.mediaDevices.getUserMedia(constraints);

        // 🔇 Ensure audio stays muted if previously muted
        if (_isMuted) {
          newStream.getAudioTracks().forEach((track) => track.enabled = false);
        }

        // Stop the old stream completely
        // if (_localStream != null) {
        _localStream!.getTracks().forEach((track) => track.stop());
        // }

        // Replace the local stream
        _localStream = newStream;

        // Update local renderer with new stream
        _localRenderer.srcObject = _localStream;

        // Force UI update to show new camera view
        if (mounted) {
          setState(() {
            // This will trigger a rebuild with the new camera view
          });
        }

        // Update all peer connections with new tracks
        for (final pc in _peerConnections.values) {
          final senders = await pc.getSenders();

          // Replace video track
          final videoSender = senders.firstWhere(
            (sender) => sender.track?.kind == 'video',
            orElse: () => throw StateError('No video sender found'),
          );

          if (videoSender.track != null) {
            await videoSender.replaceTrack(_localStream?.getVideoTracks().first);
          }

          // Replace audio track if needed
          final audioSender = senders.firstWhere(
            (sender) => sender.track?.kind == 'audio',
            orElse: () => throw StateError('No audio sender found'),
          );

          if (_localStream!.getAudioTracks().isNotEmpty) {
            await audioSender.replaceTrack(_localStream!.getAudioTracks().first);
          }
        }

        // Notify viewers about camera flip
        if (_currentRoom != null) {
          socket.emit('camera_flipped', {
            'room': _currentRoom,
            'is_front_camera': _isFrontCamera,
          });
        }

        Logger.lOG("Camera flipped to ${_isFrontCamera ? 'front' : 'back'} camera");
      } catch (e) {
        Logger.lOG("Error flipping camera: $e");
        // Revert the state if flip failed
        setState(() {
          _isFrontCamera = !_isFrontCamera;
        });
      }
    }
  }

  /// Toggle PIP mode manually
  Future<void> _togglePip() async {
    if (!_isPipSupported) {
      Logger.lOG("PIP not supported on this device");
      toastification.show(
        type: ToastificationType.error,
        showProgressBar: false,
        description: Text(
          'Picture-in-Picture is not supported on this device.',
          style: GoogleFonts.montserrat(
            fontSize: 12.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        autoCloseDuration: const Duration(seconds: 3),
      );
      return;
    }

    if (!_pipPermissionGranted) {
      Logger.lOG("PIP permission not granted");
      final shouldGrant = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(
            'Picture-in-Picture Permission Required',
            style: GoogleFonts.montserrat(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Text(
            'To use Picture-in-Picture mode, please grant permission first.',
            style: GoogleFonts.montserrat(
              fontSize: 14.sp,
              fontWeight: FontWeight.w400,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'Cancel',
                style: GoogleFonts.montserrat(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(
                'Grant Permission',
                style: GoogleFonts.montserrat(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          ],
        ),
      );

      if (shouldGrant == true && mounted) {
        setState(() {
          _pipPermissionGranted = true;
        });
        Logger.lOG("PIP permission granted manually");
      } else {
        return;
      }
    }

    try {
      if (_isPipActive) {
        // Stop PIP mode
        await _pipService.stopPip();
        Logger.lOG("PIP stopped manually");
        toastification.show(
          type: ToastificationType.info,
          showProgressBar: false,
          description: Text(
            'Picture-in-Picture mode stopped.',
            style: GoogleFonts.montserrat(
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 2),
        );
      } else {
        // Start PIP mode
        final pipStarted = await _pipService.startPip();
        if (pipStarted) {
          Logger.lOG("PIP started manually");
          toastification.show(
            type: ToastificationType.success,
            showProgressBar: false,
            description: Text(
              'Picture-in-Picture mode started.',
              style: GoogleFonts.montserrat(
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            autoCloseDuration: const Duration(seconds: 2),
          );
        } else {
          Logger.lOG("Failed to start PIP manually");
          toastification.show(
            type: ToastificationType.error,
            showProgressBar: false,
            description: Text(
              'Failed to start Picture-in-Picture mode.',
              style: GoogleFonts.montserrat(
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            autoCloseDuration: const Duration(seconds: 3),
          );
        }
      }
    } catch (e) {
      Logger.lOG("Error toggling PIP: $e");
      toastification.show(
        type: ToastificationType.error,
        showProgressBar: false,
        description: Text(
          'Error with Picture-in-Picture mode.',
          style: GoogleFonts.montserrat(
            fontSize: 12.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        autoCloseDuration: const Duration(seconds: 3),
      );
    }
  }

  /// Show PIP settings dialog
  Future<void> _showPipSettings() async {
    if (!_isPipSupported) {
      toastification.show(
        type: ToastificationType.info,
        showProgressBar: false,
        description: Text(
          'Picture-in-Picture is not supported on this device.',
          style: GoogleFonts.montserrat(
            fontSize: 12.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        autoCloseDuration: const Duration(seconds: 3),
      );
      return;
    }

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Picture-in-Picture Settings',
          style: GoogleFonts.montserrat(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Status: ${_pipPermissionGranted ? "Enabled" : "Disabled"}',
              style: GoogleFonts.montserrat(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: _pipPermissionGranted ? Colors.green : Colors.red,
              ),
            ),
            buildSizedBoxH(10.h),
            Text(
              'Picture-in-Picture allows you to continue watching the live stream in a small window when you switch to other apps.',
              style: GoogleFonts.montserrat(
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              'Cancel',
              style: GoogleFonts.montserrat(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(!_pipPermissionGranted),
            child: Text(
              _pipPermissionGranted ? 'Disable PIP' : 'Enable PIP',
              style: GoogleFonts.montserrat(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
        ],
      ),
    );

    if (result != null && mounted) {
      setState(() {
        _pipPermissionGranted = result;
      });

      toastification.show(
        type: ToastificationType.success,
        showProgressBar: false,
        description: Text(
          'Picture-in-Picture ${_pipPermissionGranted ? "enabled" : "disabled"}.',
          style: GoogleFonts.montserrat(
            fontSize: 12.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        autoCloseDuration: const Duration(seconds: 3),
      );

      Logger.lOG("PIP permission ${_pipPermissionGranted ? "enabled" : "disabled"} via settings");
    }
  }

  void _sendComment() {
    if (_commentController.text.trim().isEmpty) return;

    final comment = _commentController.text.trim();
    Logger.lOG("Sending comment: $comment");

    socket.emit('send_comment', {
      'room': _currentRoom,
      'comment': comment,
      'userId': Prefobj.preferences?.get(Prefkeys.USER_ID),
      'username': Prefobj.preferences?.get(Prefkeys.NAME),
      'is_animated': false,
    });

    _commentController.clear();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (widget.args[1] == Prefobj.preferences?.get(Prefkeys.USER_ID)) {
          final shouldPop = await showDialog<bool>(
            context: context,
            builder: (context) => CustomAlertDialog(
              title: "End your live stream?",
              subtitle: "If you end your Live, it'll also end for all of your viewers.",
              isLoading: false,
              onConfirmButtonPressed: () async {
                await disconnectroom();
                toastification.show(
                  type: ToastificationType.success,
                  showProgressBar: false,
                  title: Text(
                    'Successfully closed the live video.',
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                );
                context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
                _stopExistingStream();
                await _localRenderer.dispose();
                socket.dispose();
                Navigator.pop(context, true);
              },
              confirmButtonText: "End now",
            ),
          );
          Logger.lOG(shouldPop);

          if (shouldPop == true) {
            context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
            Navigator.pop(context, true);
            return false;
          }

          return false;
        } else {
          final shouldPop = await showDialog<bool>(
            context: context,
            builder: (context) => CustomAlertDialog(
              title: "Leave live stream?",
              subtitle: "Are you sure you want to leave the live stream?",
              isLoading: false,
              onConfirmButtonPressed: () async {
                // Force stop all media before cleanup
                _forceStopAllMedia();

                await cleanUser();
                context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
                _stopExistingStream();
                await _localRenderer.dispose();
                socket.dispose();
                Navigator.pop(context, true);
              },
              confirmButtonText: "Leave",
            ),
          );

          if (shouldPop == true) {
            context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
            Navigator.pop(context, true);
            return false;
          }

          return false;
        }
      },
      // onWillPop: () async {
      //   if (widget.args[1] == Prefobj.preferences?.get(Prefkeys.USER_ID)) {
      //     final shouldPop = await showDialog<bool>(
      //       context: context,
      //       builder: (context) => CustomAlertDialog(
      //         title: "End your live video?",
      //         subtitle: "If you end your Live, it'll also end for all of your viewers.",
      //         isLoading: false,
      //         onConfirmButtonPressed: () async {
      //           await disconnectroom();
      //           toastification.show(
      //             type: ToastificationType.success,
      //             showProgressBar: false,
      //             title: Text(
      //               'Successfully closed the live video.',
      //               style: GoogleFonts.montserrat(
      //                 fontSize: 12.0.sp,
      //                 fontWeight: FontWeight.w500,
      //               ),
      //             ),
      //             autoCloseDuration: const Duration(seconds: 3),
      //           );
      //           context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
      //           _stopExistingStream();
      //           await _localRenderer.dispose();
      //           socket.dispose();
      //           Navigator.pop(context, true);
      //         },
      //         confirmButtonText: "End now",
      //       ),
      //     );
      //     Logger.lOG(shouldPop);

      //     if (shouldPop == true) {
      //       context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
      //       Navigator.pop(context, true);
      //       return false;
      //     }

      //     return false;
      //   } else {
      //     final shouldPop = await showDialog<bool>(
      //       context: context,
      //       builder: (context) => CustomAlertDialog(
      //         title: "Leave live video?",
      //         subtitle: "Are you sure you want to leave the live video?",
      //         isLoading: false,
      //         onConfirmButtonPressed: () async {
      //           await cleanUser();
      //           context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
      //           _stopExistingStream();
      //           await _localRenderer.dispose();
      //           socket.dispose();
      //           Navigator.pop(context, true);
      //         },
      //         confirmButtonText: "Leave",
      //       ),
      //     );
      //     Logger.lOG(shouldPop);

      //     if (shouldPop == true) {
      //       context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
      //       Navigator.pop(context, true);
      //       return false;
      //     }

      //     return false;
      //   }
      // },
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle.light.copyWith(
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarIconBrightness: Brightness.light,
        ),
        child: BlocListener<ConnectivityBloc, ConnectivityState>(
          listener: (context, connectivityState) {
            _onConnectivityChanged(connectivityState);
          },
          child: Scaffold(
            extendBodyBehindAppBar: true,
            backgroundColor: Theme.of(context).customColors.black,
            appBar: _buildViewStreamAppBar(),
            body: _isStreaming ? _buildVideoView() : LoadingAnimationWidget(),
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildViewStreamAppBar() {
    return CustomAppbar(
      hasLeadingIcon: true,
      leading: [
        Padding(
          padding: EdgeInsets.only(top: 4.0.h),
          child: Row(
            children: [
              InkWell(
                onTap: () async {
                  final isOwner = widget.args[1] == Prefobj.preferences?.get(Prefkeys.USER_ID);
                  final shouldPop = await showDialog<bool>(
                    barrierDismissible: false,
                    context: context,
                    builder: (context) => CustomAlertDialog(
                      title: isOwner ? "End your live stream?" : "Leave live stream?",
                      subtitle: isOwner
                          ? "If you end your Live, it'll also end for all of your viewers."
                          : "Are you sure you want to leave the live stream?",
                      isLoading: false,
                      confirmButtonText: isOwner ? "End now" : "Leave",
                      onConfirmButtonPressed: () async {
                        if (isOwner) {
                          await disconnectroom();
                          toastification.show(
                            type: ToastificationType.success,
                            showProgressBar: false,
                            title: Text(
                              'Successfully closed the live stream.',
                              style: GoogleFonts.montserrat(
                                fontSize: 12.0.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            autoCloseDuration: const Duration(seconds: 3),
                          );
                          _stopExistingStream();
                          await _localRenderer.dispose();
                          socket.dispose();
                        } else {
                          await cleanUser();
                          _stopExistingStream();
                          await _localRenderer.dispose();
                          socket.dispose();
                        }
                        context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
                        Navigator.pop(context, true);
                      },
                    ),
                  );
                  Logger.lOG(shouldPop);

                  if (shouldPop == true) {
                    context.read<HomeFeedBloc>().add(GetLivestoryApiEvent());
                    Navigator.pop(context, true);
                  }
                },
                child: CustomImageView(
                  imagePath: AssetConstants.pngBack,
                  color: Theme.of(context).customColors.white,
                  height: 16.0.h,
                  margin: EdgeInsets.only(left: 13.0.w),
                ),
              ),
              buildSizedBoxW(13.w),
              _buildUserProfileImage(),
              buildSizedBoxW(6.w),
              SizedBox(
                // color: Colors.red,
                width: MediaQuery.of(context).size.width - 242.w,
                child: Padding(
                  padding: EdgeInsets.only(right: 10.w),
                  child: Text(
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    uerName.isNotEmpty ? uerName : Prefobj.preferences?.get(Prefkeys.NAME) ?? '',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).customColors.white,
                          fontWeight: FontWeight.w700,
                          fontSize: 16.0.sp,
                        ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
      actions: [
        SafeArea(
          child: Padding(
            padding: EdgeInsets.only(top: 4.0.h),
            child: Row(
              children: [
                _buildLiveTag(),
                buildSizedBoxW(10),
                _buildViewerCount(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUserProfileImage() {
    if (uerName.isNotEmpty) {
      return ClipRRect(
        clipBehavior: Clip.hardEdge,
        child: Padding(
          padding: const EdgeInsets.all(4.0),
          child: CustomImageView(
            radius: BorderRadius.circular(25.r),
            height: 36.0.h,
            width: 36.0.w,
            fit: BoxFit.cover,
            imagePath:
                userProfileImage.isEmpty ? AssetConstants.pngUserReomve : "${APIConfig.mainbaseURL}$userProfileImage",
            alignment: Alignment.center,
          ),
        ),
      );
    } else {
      return Container(
        height: 36.h,
        width: 36.w,
        decoration: BoxDecoration(
          color: Theme.of(context).customColors.white,
          shape: BoxShape.circle,
          boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 5)],
        ),
        child: ValueListenableBuilder<String>(
          valueListenable: profileImageNotifier,
          builder: (context, imagePath, child) {
            return Container(
              height: 36.0.h,
              width: 36.0.w,
              padding: imagePath == AssetConstants.pngUser ? EdgeInsets.all(8) : EdgeInsets.zero,
              child: CustomImageView(
                radius: imagePath == AssetConstants.pngUser ? null : BorderRadius.circular(100.0.r),
                fit: imagePath == AssetConstants.pngUser ? BoxFit.contain : BoxFit.cover,
                imagePath: imagePath,
                alignment: (imagePath == AssetConstants.pngUser && imagePath.isEmpty) ? Alignment.center : null,
              ),
            );
          },
        ),
      );
    }
  }

  Widget _buildLiveTag() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.red,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Row(
        children: [
          Icon(Icons.circle, color: Colors.white, size: 12),
          SizedBox(width: 4),
          Text(
            'LIVE',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).customColors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16.0.sp,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildViewerCount() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.5),
        borderRadius: BorderRadius.circular(10.r),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.5),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.remove_red_eye_rounded, color: Theme.of(context).customColors.white, size: 16.sp),
          SizedBox(width: 4),
          Text(
            '${_currentUserCount > 0 ? _currentUserCount - 2 : 0}',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoView() {
    if (_isRoomCreator) {
      return Stack(
        children: [
          _isCameraOff ? _buildCameraOffView() : _buildLocalVideoView(),
          _buildEmojiAnimation(),
          if (_isCommentsVisible) _buildBottomCommentOverlay(),
          _buildCreatorControls(),
        ],
      );
    } else {
      return _isLoading
          ? LoadingAnimationWidget()
          : Stack(
              children: [
                _isCameraOff ? _buildUserImageOffCameraView() : _buildRemoteVideoView(),
                if (!_isPipActive) ...[
                  Positioned(
                    top: 100.h,
                    right: 16.w,
                    child: Column(
                      children: [
                        // PIP toggle button for viewers (only show if PIP is supported and permission granted)
                        if (_isPipSupported && _pipPermissionGranted) ...[
                          _buildControlButton(
                            icon: _isPipActive ? Icons.picture_in_picture_alt : Icons.picture_in_picture,
                            isActive: _isPipActive,
                            onTap: _togglePip,
                          ),
                          buildSizedBoxH(10.0),
                        ],
                        // Comment visibility toggle button
                        _buildControlButton(
                          icon: _isCommentsVisible ? Icons.visibility_off : Icons.visibility,
                          isActive: !_isCommentsVisible,
                          onTap: () {
                            setState(() {
                              _isCommentsVisible = !_isCommentsVisible;
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                  _buildEmojiAnimation(),
                  if (_isCommentsVisible)
                    _buildBottomCommentOverlay(padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 8.h)),
                ],
              ],
            );
    }
  }

  Widget _buildCameraOffView() {
    return SizedBox.expand(
      child: Container(
        color: Colors.black,
        child: Center(
          child: ValueListenableBuilder<String>(
            valueListenable: profileImageNotifier,
            builder: (context, imagePath, child) {
              return Container(
                height: 200.0.h,
                width: 200.0.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Theme.of(context).customColors.white,
                ),
                padding: imagePath == AssetConstants.pngUser ? EdgeInsets.all(40) : EdgeInsets.zero,
                child: CustomImageView(
                  radius: imagePath == AssetConstants.pngUser ? null : BorderRadius.circular(100.0.r),
                  fit: imagePath == AssetConstants.pngUser ? BoxFit.contain : BoxFit.cover,
                  imagePath: imagePath,
                  alignment: (imagePath == AssetConstants.pngUser && imagePath.isEmpty) ? Alignment.center : null,
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildUserImageOffCameraView() {
    return SizedBox.expand(
      child: Container(
        color: Colors.black,
        child: Center(
          child: ValueListenableBuilder<String>(
            valueListenable: profileImageNotifier,
            builder: (context, imagePath, child) {
              return Container(
                height: 200.0.h,
                width: 200.0.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Theme.of(context).customColors.white,
                ),
                padding: imagePath == AssetConstants.pngUser ? EdgeInsets.all(40) : EdgeInsets.zero,
                child: CustomImageView(
                  radius: imagePath == AssetConstants.pngUser ? null : BorderRadius.circular(100.0.r),
                  fit: imagePath == AssetConstants.pngUser ? BoxFit.contain : BoxFit.cover,
                  imagePath: userProfileImage.isEmpty
                      ? AssetConstants.pngUserReomve
                      : "${APIConfig.mainbaseURL}$userProfileImage",
                  alignment: (imagePath == AssetConstants.pngUser && imagePath.isEmpty) ? Alignment.center : null,
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildLocalVideoView() {
    return SizedBox.expand(
      child: RTCVideoView(
        _localRenderer,
        objectFit: RTCVideoViewObjectFit.RTCVideoViewObjectFitCover,
        mirror: false,
        filterQuality: FilterQuality.medium,
      ),
    );
  }

  Widget _buildRemoteVideoView() {
    // Check if there are any remote renderers available
    if (_remoteRenderers.isEmpty) {
      return _buildWaitingView();
    }

    try {
      // Find the broadcaster's stream (first user who joined)
      final broadcasterRenderer = _remoteRenderers.values.firstWhere(
        (renderer) => renderer.srcObject != null,
        orElse: () => throw StateError('No active stream found'),
      );

      return SizedBox.expand(
        child: RTCVideoView(
          broadcasterRenderer,
          objectFit: RTCVideoViewObjectFit.RTCVideoViewObjectFitCover,
          mirror: false,
          filterQuality: FilterQuality.medium,
        ),
      );
    } catch (e) {
      Logger.lOG("Error in _buildRemoteVideoView: $e");
      return _buildWaitingView();
    }
  }

  Widget _buildWaitingView() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).primaryColor,
              ),
            ),
            buildSizedBoxH(20.h),
            Text(
              'Please wait...\nConnecting to live stream',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).customColors.white,
                    fontSize: 16.0.sp,
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomCommentOverlay({EdgeInsets? padding}) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: padding,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
            colors: [
              Colors.black.withOpacity(_isCommentsVisible ? 0.6 : 0.3), // Reduced opacity when comments are hidden
              Colors.transparent,
            ],
          ),
        ),
        child: _buildCommentSection(),
      ),
    );
  }

  Widget _buildCreatorControls() {
    return Positioned(
      top: 100.h,
      right: 16.w,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          if (_isRoomCreator) ...[
            _buildControlButton(
              icon: _isMuted ? Icons.mic_off_rounded : Icons.mic_rounded,
              isActive: _isMuted,
              onTap: _toggleMute,
            ),
            buildSizedBoxH(10.0),
            _buildControlButton(
              icon: _isCameraOff ? Icons.videocam_off_rounded : Icons.videocam_rounded,
              isActive: _isCameraOff,
              onTap: _toggleCamera,
            ),
            buildSizedBoxH(10.0),
            // Camera flip button
            if (!_isCameraOff)
              _buildControlButton(
                icon: _isFrontCamera ? Icons.flip_camera_ios_outlined : Icons.flip_camera_ios_rounded,
                isActive: !_isFrontCamera,
                onTap: _flipCamera,
              ),
            buildSizedBoxH(10.0),
            // PIP toggle button (only show if PIP is supported and permission granted)
            if (_isPipSupported && _pipPermissionGranted)
              _buildControlButton(
                icon: _isPipActive ? Icons.picture_in_picture_alt : Icons.picture_in_picture,
                isActive: _isPipActive,
                onTap: _togglePip,
              ),
            if (_isPipSupported && _pipPermissionGranted) buildSizedBoxH(10.0),
            // Comment visibility toggle button
            _buildControlButton(
              // icon: _isCommentsVisible ? Icons.visibility_off : Icons.visibility,
              icon: _isCommentsVisible ? Icons.visibility : Icons.visibility_off,
              isActive: !_isCommentsVisible,
              onTap: () {
                setState(() {
                  _isCommentsVisible = !_isCommentsVisible;
                });
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withOpacity(isActive ? 0.8 : 0.5),
          borderRadius: BorderRadius.circular(10.r),
          border: Border.all(color: Theme.of(context).primaryColor),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 22.sp,
        ),
      ),
    );
  }

  Widget _buildCommentItem(Map<String, dynamic> comment) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${comment['username']}: ',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12.sp,
            ),
          ),
          Expanded(
            child: Text(
              comment['comment'],
              style: TextStyle(
                color: Colors.white,
                fontSize: 12.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentSection() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Comments list and input field - only shown when _isCommentsVisible is true

        // Comments list
        //  if (-)
        if (_comments.isNotEmpty)
          Container(
            padding: EdgeInsets.only(top: 50.h),
            height: MediaQuery.of(context).size.height / 2.5,
            child: ListView.builder(
              padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 8.0.h),
              reverse: true,
              itemCount: _comments.length,
              itemBuilder: (context, index) => _buildCommentItem(_comments[_comments.length - 1 - index]),
            ),
          ),
        Container(
          height: 40.h,
          width: double.infinity,
          margin: EdgeInsets.only(bottom: 8.h),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.only(left: 16.w, right: 16.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _emojiText('💗'),
                _emojiText('💸'),
                _emojiText('🔥'),
                _emojiText('👏🏻'),
                _emojiText('😢'),
                _emojiText('😍'),
                _emojiText('😮'),
                _emojiText('😂'),
                _emojiText('🎉'),
                _emojiText('✨'),
                _emojiText('🌟'),
                _emojiText('💫'),
                _emojiText('💯'),
                _emojiText('🙌'),
                _emojiText('🤩'),
                _emojiText('🥳'),
                _emojiText('🎊'),
                _emojiText('🏆'),
                _emojiText('💪'),
                _emojiText('👍'),
                _emojiText('❤️'),
                _emojiText('💖'),
                _emojiText('💝'),
                _emojiText('💕'),
                _emojiText('💓'),
                _emojiText('💞'),
                _emojiText('💘'),
                _emojiText('💗'),
                _emojiText('💓'),
                _emojiText('💖'),
              ],
            ),
          ),
        ),
        // Comment input
        if (_isCommentsVisible)
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: Row(
              children: [
                Expanded(
                  child: FlowkarTextFormField(
                    context: context,
                    hintText: 'Add a comment...',
                    controller: _commentController,
                    onChanged: (value) {
                      _inputText.value = value;
                    },
                    onFieldSubmitted: (value) {
                      _sendComment();
                    },
                    textInputAction: TextInputAction.send,
                    fillColor: Theme.of(context).primaryColor.withOpacity(0.2),
                    borderDecoration: OutlineInputBorder(
                      borderSide: BorderSide(color: Theme.of(context).primaryColor),
                      borderRadius: BorderRadius.circular(10.r),
                    ),
                    filled: true,
                    textStyle: Theme.of(context)
                        .textTheme
                        .bodyMedium
                        ?.copyWith(fontSize: 16.5.sp, color: Theme.of(context).customColors.white),
                    hintStyle: Theme.of(context)
                        .textTheme
                        .bodyMedium
                        ?.copyWith(fontSize: 16.5.sp, color: Theme.of(context).customColors.white),
                    suffix: ValueListenableBuilder<String>(
                      valueListenable: _inputText,
                      builder: (_, inputTextValue, __) {
                        if (inputTextValue.isNotEmpty) {
                          return Padding(
                            padding: EdgeInsets.only(right: 0.0.w),
                            child: InkWell(
                                onTap: () {
                                  _sendComment();
                                },
                                child: Icon(
                                  Icons.send_rounded,
                                  color: Theme.of(context).customColors.white,
                                )),
                          );
                        } else {
                          return SizedBox.shrink();
                        }
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
      // Emoji row - always visible
    );
  }

  void _showEmojiAnimation(String emoji) {
    // Number of emojis to shoot
    final int emojiCount = Random().nextInt(2) + 8; // Random number between 3-4

    // Create multiple emojis with slight variations
    for (int i = 0; i < emojiCount; i++) {
      // Add slight delay between each emoji for a more dynamic effect
      Future.delayed(Duration(milliseconds: i * 100), () {
        if (!mounted) return;

        setState(() {
          // Calculate random starting position with some spread
          final screenWidth = MediaQuery.of(context).size.width;
          final baseX = Random().nextDouble() * (screenWidth - 100);
          final spreadX = (Random().nextDouble() - 0.5) * 100; // Spread of ±50 pixels
          final startX = (baseX + spreadX).clamp(0.0, screenWidth - 100);

          _activeEmojis.add({
            'emoji': emoji,
            'position': Offset(
              startX,
              MediaQuery.of(context).size.height, // Start from bottom
            ),
            'opacity': 1.0,
            'animation': null,
            'controller': AnimationController(
              vsync: this,
              duration: Duration(milliseconds: 1800), // Slightly varied duration
            ),
          });
        });

        // Start animation with slight delay
        Future.delayed(Duration(milliseconds: 20), () {
          if (mounted) {
            setState(() {
              final lastEmoji = _activeEmojis.last;
              final controller = lastEmoji['controller'] as AnimationController;

              // Add slight random variation to the end position
              final randomOffset = (Random().nextDouble() - 0.5) * 40; // ±20 pixels variation

              lastEmoji['animation'] = Tween<double>(
                begin: MediaQuery.of(context).size.height,
                end: -100.0 + randomOffset, // Slightly varied end position
              ).animate(CurvedAnimation(
                parent: controller,
                curve: Curves.easeOut,
              ));

              // Add listener to remove emoji when animation completes
              controller.addStatusListener((status) {
                if (status == AnimationStatus.completed) {
                  if (mounted && _activeEmojis.isNotEmpty) {
                    setState(() {
                      final emojiToRemove = _activeEmojis.removeAt(0);
                      emojiToRemove['controller'].dispose();
                    });
                  }
                }
              });

              controller.forward();
            });
          }
        });
      });
    }
  }

  Widget _buildEmojiAnimation() {
    return Stack(
      children: _activeEmojis.map((emojiData) {
        final animation = emojiData['animation'] as Animation<double>?;
        if (animation == null) return SizedBox.shrink();

        return AnimatedBuilder(
          animation: animation,
          builder: (context, child) {
            // Calculate opacity based on position
            final progress = animation.value / MediaQuery.of(context).size.height;
            final opacity = 1.0 - (progress * 1.5).clamp(0.0, 1.0); // Fade out faster near top

            return Positioned(
              left: emojiData['position'].dx,
              top: animation.value,
              child: Opacity(
                opacity: opacity,
                child: Text(
                  emojiData['emoji'],
                  style: TextStyle(fontSize: 50.sp),
                ),
              ),
            );
          },
        );
      }).toList(),
    );
  }

  Widget _emojiText(String emoji) {
    return InkWell(
      onTap: () {
        socket.emit('send_comment', {
          'room': _currentRoom,
          'comment': emoji,
          'userId': Prefobj.preferences?.get(Prefkeys.USER_ID),
          'username': Prefobj.preferences?.get(Prefkeys.NAME),
          'is_animated': true,
        });
        _showEmojiAnimation(emoji);
      },
      child: Container(
        margin: EdgeInsets.only(right: 4.w),
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withOpacity(0.5),
          // color: Colors.black,
          borderRadius: BorderRadius.circular(10.r),
          border: Border.all(color: Theme.of(context).primaryColor),
        ),
        child: Text(
          emoji,
          style: TextStyle(fontSize: 20.sp),
        ),
      ),
    );
  }
}
