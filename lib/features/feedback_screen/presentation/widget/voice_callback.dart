import 'dart:io';

import 'package:flowkar/core/utils/exports.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_asker/permission_asker.dart';
import 'package:rxdart/rxdart.dart';
import 'package:just_waveform/just_waveform.dart';
import 'package:path/path.dart' as p;

class VoiceFeedbackWidget extends StatefulWidget {
  final Function(File? audioFile)? onRecordingComplete;

  const VoiceFeedbackWidget({
    super.key,
    this.onRecordingComplete,
  });

  @override
  State<VoiceFeedbackWidget> createState() => _VoiceFeedbackWidgetState();
}

class _VoiceFeedbackWidgetState extends State<VoiceFeedbackWidget> {
  FlutterSoundRecorder? _recorder;
  FlutterSoundPlayer? _player;
  bool _isRecording = false;
  bool _isPlaying = false;
  String? _audioPath;
  File? _audioFile;

  // For waveform visualization
  final BehaviorSubject<WaveformProgress> _waveformProgressStream = BehaviorSubject<WaveformProgress>();
  final BehaviorSubject<double> _recorderLevelStream = BehaviorSubject<double>();

  Duration _recordingDuration = Duration.zero;

  @override
  void initState() {
    super.initState();
    _recorder = FlutterSoundRecorder();
    _player = FlutterSoundPlayer();
    initRecorder();
  }

  @override
  void dispose() {
    _recorder?.closeRecorder();
    _player?.closePlayer();
    _waveformProgressStream.close();
    _recorderLevelStream.close();
    super.dispose();
  }

  Future<void> initRecorder() async {
    await _recorder!.openRecorder();
    await _player!.openPlayer();
    await Permission.microphone.request();
  }

  Future<void> _startRecording() async {
    _recorderLevelStream.add(0.0); // Initial level
    Directory tempDir = await getTemporaryDirectory();
    String path = '${tempDir.path}/feedback_${DateTime.now().millisecondsSinceEpoch}.aac';

    await _recorder!.startRecorder(
      toFile: path,
      codec: Codec.aacADTS,
    );

    // Start monitoring recording levels for waveform animation
    _recorder!.setSubscriptionDuration(const Duration(milliseconds: 50));
    _recorder!.onProgress!.listen((event) {
      final level = event.decibels ?? 0;
      // Convert decibels to a 0.0-1.0 scale for visualization
      final normalizedLevel = (level + 60) / 60; // Assuming -60dB is silence
      _recorderLevelStream.add(normalizedLevel.clamp(0.0, 1.0));

      // Update recording duration
      setState(() {
        _recordingDuration = event.duration;
      });
    });

    setState(() {
      _isRecording = true;
      _audioPath = null;
      _audioFile = null;
      _recordingDuration = Duration.zero;
    });
  }

  Future<void> _stopRecording() async {
    final path = await _recorder!.stopRecorder();
    if (path != null) {
      final file = File(path);
      _audioFile = file;

      // Generate waveform data from the recording
      final waveFile = File(p.join((await getTemporaryDirectory()).path, 'waveform.wave'));
      JustWaveform.extract(audioInFile: file, waveOutFile: waveFile)
          .listen(_waveformProgressStream.add, onError: _waveformProgressStream.addError);

      setState(() {
        _isRecording = false;
        _audioPath = path;
      });

      if (widget.onRecordingComplete != null) {
        widget.onRecordingComplete!(file);
      }
    }
  }

  // Add clear recording function
  void _clearRecording() async {
    // Stop playback if it's playing
    if (_isPlaying) {
      await _player!.stopPlayer();
    }

    // Delete the audio file if it exists
    if (_audioFile != null && await _audioFile!.exists()) {
      await _audioFile!.delete();
    }

    setState(() {
      _audioPath = null;
      _audioFile = null;
      _isPlaying = false;
      _recordingDuration = Duration.zero;
    });

    // Notify parent that recording was cleared
    if (widget.onRecordingComplete != null) {
      widget.onRecordingComplete!(null);
    }
  }

  Future<void> _togglePlayback() async {
    if (_audioPath == null) return;

    if (_isPlaying) {
      await _player!.pausePlayer();
      setState(() => _isPlaying = false);
    } else {
      // Store the current position before playing

      await _player!.startPlayer(
        fromURI: _audioPath!,
        whenFinished: () {
          setState(() => _isPlaying = false);
          _player!.stopPlayer();
        },
      );

      setState(() => _isPlaying = true);

      // Monitor playback progress
      _player!.setSubscriptionDuration(const Duration(milliseconds: 50));
      _player!.onProgress!.listen((event) {
        setState(() {
          if (_isPlaying) {
            _recordingDuration = event.position;
          }
        });
      });
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String minutes = twoDigits(duration.inMinutes.remainder(60));
    String seconds = twoDigits(duration.inSeconds.remainder(60));
    return "$minutes:$seconds";
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.current.lbl_voice_feedback,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade300),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          child: Column(
            children: [
              // // Waveform visualization area
              // SizedBox(
              //   height: 64,
              //   child: _buildWaveformVisualization(),
              // ),
              // const SizedBox(height: 8),
              // Controls and status row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (_audioPath != null)
                    Row(
                      children: [
                        IconButton(
                          icon: Icon(
                            _isPlaying ? Icons.pause_circle_filled_rounded : Icons.play_circle_filled_rounded,
                            color: Theme.of(context).primaryColor,
                            size: 32,
                          ),
                          onPressed: _togglePlayback,
                        ),
                        IconButton(
                          icon: CustomImageView(
                            imagePath: Assets.images.svg.setting.svgDeleteAccount.path,
                            color: Theme.of(context).primaryColor,
                            height: 20.h,
                          ),
                          onPressed: _clearRecording,
                          tooltip: Lang.current.lbl_clear_recording,
                        ),
                      ],
                    )
                  else
                    const SizedBox(width: 48),
                  Text(
                    _formatDuration(_recordingDuration),
                    style: TextStyle(
                      color: _isRecording ? Colors.redAccent : Colors.grey.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  GestureDetector(
                    onLongPressStart: (_) => _startRecording(),
                    onLongPressEnd: (_) => _stopRecording(),
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _isRecording ? Colors.redAccent.withOpacity(0.9) : Theme.of(context).primaryColor,
                      ),
                      child: Icon(
                        Icons.mic,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                ],
              ),
              if (_isRecording) ...[
                const SizedBox(height: 8),
                Text(
                  Lang.current.msg_recording,
                  style: TextStyle(color: Colors.redAccent),
                ),
              ] else if (_audioPath != null) ...[
                const SizedBox(height: 8),
                Text(
                  Lang.current.msg_recording_saved,
                  style: TextStyle(color: Colors.green),
                ),
              ] else ...[
                const SizedBox(height: 8),
                Text(
                  Lang.current.msg_long_press_to_start_recording,
                  style: TextStyle(color: Colors.grey.shade700),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
