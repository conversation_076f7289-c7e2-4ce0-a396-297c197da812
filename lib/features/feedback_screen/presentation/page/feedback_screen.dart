import 'dart:io';

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/authentication/model/get_user_status.dart';
import 'package:flowkar/features/feedback_screen/bloc/feedback_bloc.dart';
import 'package:flowkar/features/feedback_screen/presentation/widget/voice_callback.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_sound/public/flutter_sound_player.dart';
import 'package:flutter_sound/public/flutter_sound_recorder.dart';
import 'package:permission_asker/permission_asker.dart';

class FeedbackScreen extends StatefulWidget {
  const FeedbackScreen({super.key});
  static Widget builder(BuildContext context) => const FeedbackScreen();

  @override
  State<FeedbackScreen> createState() => _FeedbackScreenState();
}

class _FeedbackScreenState extends State<FeedbackScreen> {
  TextEditingController nameController = TextEditingController();
  TextEditingController frequencieController = TextEditingController();
  TextEditingController discriptionController = TextEditingController();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  GetUserStatus? getUserStatus;
  double rating = 0;
  final List<String> frequencies = [
    'Less than 1 hour',
    'Between 1–2 hours',
    'More than 3 hours',
  ];

  bool ratingError = false;
  bool frequencyError = false;

  FlutterSoundRecorder? _recorder;
  FlutterSoundPlayer? _player;
  String? _audioPath;

  @override
  void initState() {
    super.initState();
    _recorder = FlutterSoundRecorder();
    _player = FlutterSoundPlayer();
    initRecorder();
    String name = Prefobj.preferences?.get(Prefkeys.USERNAME) ?? '';
    nameController.text = name;
  }

  Future<void> initRecorder() async {
    await _recorder!.openRecorder();
    await _player!.openPlayer();
    await Permission.microphone.request();
  }

  void validateAndSubmit() {
    setState(() {
      ratingError = rating == 0;
      frequencyError = frequencieController.text.isEmpty;
    });

    if (!ratingError && !frequencyError && formKey.currentState!.validate()) {
      context.read<FeedbackBloc>().add(
            FeedbackAPIEvent(
              frequency: frequencieController.text,
              description: discriptionController.text,
              stars: rating.toString(),
              file: _audioPath != null && _audioPath!.isNotEmpty ? File(_audioPath!) : null,
            ),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildFeedbackAppBar(context),
      body: Padding(
        padding: EdgeInsets.only(bottom: Platform.isIOS ? 0 : MediaQuery.of(context).viewPadding.bottom),
        child: BlocBuilder<FeedbackBloc, FeedbackState>(
          builder: (context, state) {
            return Form(
              key: formKey,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: SingleChildScrollView(
                  child: InkWell(
                    focusColor: Colors.transparent,
                    onTap: () {
                      FocusManager.instance.primaryFocus?.unfocus();
                    },
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        buildSizedBoxH(20.0),
                        _buildTextField(context, Lang.current.lbl_your_name),
                        buildSizedBoxH(20.0),
                        _buildappusingfrequency(),
                        if (frequencyError)
                          Padding(
                            padding: EdgeInsets.only(top: 5.h),
                            child: Text(
                              Lang.current.msg_please_select_a_app_using_frequency,
                              style: TextStyle(color: Colors.red, fontSize: 12.sp),
                            ),
                          ),
                        buildSizedBoxH(20.0),
                        Align(alignment: Alignment.topLeft, child: _buildRatting()),
                        if (ratingError)
                          Padding(
                            padding: EdgeInsets.only(top: 5.h),
                            child: Text(
                              Lang.current.msg_please_select_a_rating_star,
                              style: TextStyle(color: Colors.red, fontSize: 12.sp),
                            ),
                          ),
                        buildSizedBoxH(20.0),
                        VoiceFeedbackWidget(
                          onRecordingComplete: (File? audioFile) {
                            if (audioFile != null) {
                              setState(() {
                                _audioPath = audioFile.path;
                              });
                            }
                          },
                        ),
                        buildSizedBoxH(20.0),
                        _buildDescription(),
                        buildSizedBoxH(75.0),
                        Align(
                          alignment: Alignment.center,
                          child: CustomElevatedButton(
                            text: Lang.current.lbl_submit,
                            isDisabled: state.isLoginLoading,
                            isLoading: state.isLoginLoading,
                            onPressed: () {
                              validateAndSubmit();
                              formKey.currentState!.validate();
                            },
                          ),
                        ),
                        buildSizedBoxH(16.0),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildFeedbackAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          Lang.current.lbl_feedback,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  Widget _buildTextField(BuildContext context, String label, {int maxLines = 1}) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Text(label,
          //     style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontSize: 14.sp, fontWeight: FontWeight.w500)),
          // buildSizedBoxH(5.0),
          FlowkarTextFormField(
            fillColor: Theme.of(context).customColors.white,
            filled: true,
            context: context,
            maxLines: maxLines,
            readOnly: true,
            labelText: label,
            contentPadding: EdgeInsets.all(16.0),
            controller: nameController,
            // validator: AppValidations.validateUsername,
            textInputAction: TextInputAction.next,
            inputFormatters: [
              FilteringTextInputFormatter.deny(RegExp(r'\s')),
            ],
          ),
        ],
      );

  Widget _buildappusingfrequency() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Text(
        //   Lang.current.lbl_app_using_frequency,
        //   style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontSize: 14.sp),
        // ),
        // SizedBox(height: 8.h),
        FlowkarTextFormField(
          fillColor: Theme.of(context).customColors.white,
          filled: true,
          context: context,
          readOnly: true,
          hint: "Select frequency",
          labelText: frequencieController.text.isEmpty
              ? Lang.current.lbl_types_of_frequency
              : Lang.current.lbl_app_using_frequency,
          contentPadding: EdgeInsets.all(16.0),
          controller: frequencieController,
          suffixIcon: Icon(
            Icons.keyboard_arrow_down_rounded,
            size: 24.sp,
            color: Theme.of(context).primaryColor,
          ),
          onTap: _showFrequencyBottomSheet,
        ),
      ],
    );
  }

  void _showFrequencyBottomSheet() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.only(top: 20.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 40.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            SizedBox(height: 20.h),
            ...frequencies.map(
              (frequency) => Center(
                child: InkWell(
                  onTap: () {
                    setState(() {
                      frequencieController.text = frequency;
                    });
                    Navigator.pop(context);
                  },
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 20.w),
                        margin: EdgeInsets.symmetric(vertical: 6.h, horizontal: 20.w),
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(30.r),
                        ),
                        child: Text(
                          textAlign: TextAlign.center,
                          frequency,
                          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                fontSize: 16.sp,
                                color: frequencieController.text == frequency ? const Color(0xFF593C3C) : Colors.black,
                                fontWeight:
                                    frequencieController.text == frequency ? FontWeight.w600 : FontWeight.normal,
                              ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }

  Widget _buildRatting() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.current.lbl_rate_us,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontSize: 14.sp),
        ),
        buildSizedBoxH(10),
        RatingBar.builder(
          initialRating: rating,
          minRating: 0,
          direction: Axis.horizontal,
          allowHalfRating: true,
          itemCount: 5,
          itemSize: 50.w,
          unratedColor: Colors.grey.shade500,
          itemPadding: EdgeInsets.symmetric(horizontal: MediaQuery.of(context).size.width * 0.02),
          itemBuilder: (context, _) => Container(
            height: 52.h,
            width: 52.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Theme.of(context).primaryColor.withOpacity(0.2),
              border: Border.all(color: ratingError ? Colors.red : Colors.transparent, width: 2),
            ),
            child: Icon(
              Icons.star,
              size: 30.sp,
              color: const Color(0xFF593C3C),
            ),
          ),
          onRatingUpdate: (value) {
            setState(() {
              rating = value;
              ratingError = false;
            });
          },
        ),
      ],
    );
  }

  Widget _buildDescription() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Text(
        //   Lang.current.lbl_description,
        //   style: TextStyle(
        //     fontSize: 14.sp,
        //   ),
        // ),
        // buildSizedBoxH(8.0),
        FlowkarTextFormField(
          fillColor: Theme.of(context).customColors.white,
          filled: true,
          context: context,
          labelText: Lang.current.lbl_description,
          controller: discriptionController,
          maxLines: 5,
          contentPadding: EdgeInsets.all(16.0),
          textInputAction: TextInputAction.newline,
          validator: (_audioPath == null || _audioPath!.isEmpty)
              ? (p0) => AppValidations.validateRequired(p0, fieldName: "Description")
              : null,
        ),
      ],
    );
  }
}
