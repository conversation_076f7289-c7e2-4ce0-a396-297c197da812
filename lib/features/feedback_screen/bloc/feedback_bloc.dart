import 'dart:io';

import 'package:flowkar/core/helpers/vibration_helper.dart';
import 'package:flowkar/core/utils/exports.dart';

part 'feedback_event.dart';
part 'feedback_state.dart';

class FeedbackBloc extends Bloc<FeedbackEvent, FeedbackState> {
  ApiClient apiClient = ApiClient(Dio());
  FeedbackBloc(super.initialState) {
    on<FeedbackAPIEvent>(_feedbackApi);
  }
  Future<void> _feedbackApi(FeedbackAPIEvent event, Emitter<FeedbackState> emit) async {
    emit(state.copyWith(isLoginLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.feedbackApi(
        logInUserId: loginuserId,
        brandid: brandId,
        frequency: event.frequency,
        description: event.description,
        stars: event.stars,
        file: event.file,
      );

      if (result.status == true) {
        NavigatorService.goBack();
        VibrationHelper.singleShortBuzz();
        emit(state.copyWith(isLoginLoading: false));
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            result.message ?? '',
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
      } else {
        toastification.show(
          type: ToastificationType.error,
          showProgressBar: false,
          title: Text(
            result.message ?? '',
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        emit(state.copyWith(isLoginLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isLoginLoading: false));
      handleError(error);
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("_registerbrandApi Error: $error");
    }
  }
}
