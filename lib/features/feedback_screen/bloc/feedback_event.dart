part of 'feedback_bloc.dart';

abstract class FeedbackEvent extends Equatable {
  const FeedbackEvent();

  @override
  List<Object> get props => [];
}

class FeedbackInitial extends FeedbackEvent {
  @override
  List<Object> get props => [];
}

class FeedbackAPIEvent extends FeedbackEvent {
  final String frequency;
  final String description;
  final String stars;
  final File? file;

  const FeedbackAPIEvent({
    required this.frequency,
    required this.description,
    required this.stars,
    this.file,
  });
}
