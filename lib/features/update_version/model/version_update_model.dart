VersionModel deserializeVersionModel(Map<String, dynamic> json) => VersionModel.fromJson(json);

class VersionModel {
  bool? status;
  String? message;
  String? version;

  VersionModel({this.status, this.message, this.version});

  // From JSON constructor to parse the response
  VersionModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    version = json['version'];
  }

  // To JSON method to serialize the object
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['version'] = version;
    return data;
  }

  // CopyWith method to create a new instance with modified fields
  VersionModel copyWith({
    bool? status,
    String? message,
    String? version,
  }) {
    return VersionModel(
      status: status ?? this.status,
      message: message ?? this.message,
      version: version ?? this.version,
    );
  }
}
