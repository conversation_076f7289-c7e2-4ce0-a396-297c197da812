import 'dart:io';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/update_version/model/version_update_model.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

bool isDialogShowing = false; // Flag to manage dialog state

class FlowkarAppUpdateHelper {
  static Future<bool> checkForUpdate(BuildContext context, VersionModel versionModel) async {
    try {
      // Get current app version
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      String currentVersion = packageInfo.version;
      Logger.lOG("versionModel data version ${versionModel.version}");

      // Check if server version is different from current version
      if (versionModel.version != null && isNewerVersion(currentVersion, versionModel.version!)) {
        await _showUpdateDialog(
            context,
            currentVersion,
            versionModel.version!,
            Platform.isIOS
                ? 'https://apps.apple.com/in/app/flowkar/id6740058663'
                : 'https://play.google.com/store/apps/details?id=${packageInfo.packageName}',
            'A new version of the app is available.');
        return true;
      } else {
        // if (PrefObj.preferences!.get(PrefKeys.SUBSCRIPTIONID) != null) {
        //   context
        //       .read<RnwBottombarBloc>()
        //       .add(CreateUserDeviceTokenApiEvent(phoneNumber: PrefObj.preferences?.get(PrefKeys.PHONENUMBER).toString() ?? '', oneSignalID: PrefObj.preferences?.get(PrefKeys.SUBSCRIPTIONID) ?? ''));
        // }
        // context.read<HomeBloc>().add(GetStudentListAPIEvent());
        // context.read<BannerBloc>().add(GetBannerAPIEvent());
        return false;
      }
    } catch (e) {
      Logger.lOG('Error checking app update: $e');
      return false;
    }
  }

  static bool isNewerVersion(String currentVersion, String newVersion) {
    List<String> currentParts = currentVersion.split('.');
    List<String> newParts = newVersion.split('.');

    int length = currentParts.length > newParts.length ? currentParts.length : newParts.length;

    for (int i = 0; i < length; i++) {
      int currentPart = i < currentParts.length ? int.tryParse(currentParts[i]) ?? 0 : 0;
      int newPart = i < newParts.length ? int.tryParse(newParts[i]) ?? 0 : 0;

      if (newPart > currentPart) return true;
      if (newPart < currentPart) return false;
    }
    return false;
  }

  static Future<void> _showUpdateDialog(
      BuildContext context, String currentVersion, String newVersion, String? updateLink, String updateMessage) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext ctx) {
        // Check if the dialog is already open
        if (isDialogShowing) return Container();

        isDialogShowing = true; // Set a flag to indicate the dialog is showing

        return WillPopScope(
          onWillPop: () async => false,
          child: CustomAlertDialog(
            width: 380.w,
            isredius: true,
            imageheight: 45.h,
            imagewidth: 45.w,
            fit: BoxFit.contain,
            // imagePath: Assets.images.svg.homeFeed.svgSubscription.path,
            title: "Update Available",
            subtitle: "",
            onConfirmButtonPressed: () async {
              if (updateLink != null) {
                _launchUpdateUrl(updateLink);
                Logger.lOG("update Url:-- $updateLink");
                // NavigatorService.goBack();
              }
              // Navigator.of(context).pop();

              NavigatorService.goBack();
              isDialogShowing = false;
            },
            // onCancelButtonPressed: () {
            //   NavigatorService.goBack();

            //   // context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
            //   isDialogShowing = false;
            // },
            singleButton: true,
            confirmButtonText: "Update",
            // cancelButtonText: "Cancel",
            isLoading: false,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  updateMessage,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w400, fontSize: 14.sp, color: Theme.of(context).iconTheme.color),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 5),

                // Highlighted text
                Text(
                  'Current Version: $currentVersion',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w400,
                        fontSize: 14.sp,
                        color: Theme.of(context).iconTheme.color,
                      ),
                ),
                SizedBox(height: 5),

                // Highlighted text
                Text(
                  'New Version: $newVersion',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        fontSize: 14.sp,
                        color: Theme.of(context).iconTheme.color,
                      ),
                ),
                SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  static Future<void> _launchUpdateUrl(String updateLink) async {
    final Uri url = Uri.parse(updateLink);
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch $url');
    }
  }
}
