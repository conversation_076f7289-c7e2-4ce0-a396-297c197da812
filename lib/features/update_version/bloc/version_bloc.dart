import 'dart:io';

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/update_version/model/version_update_model.dart';

part 'version_event.dart';
part 'version_state.dart';

class VersionBloc extends Bloc<VersionEvent, VersionState> {
  final ApiClient apiClient = ApiClient(Dio());
  VersionBloc(super.initialState) {
    on<VersionUpdateEvent>(_onVersionApiCall);
  }
  _onVersionApiCall(VersionUpdateEvent event, Emitter<VersionState> emit) async {
    try {
      final versionModel = await apiClient.getVersionUpdate(
        platform: Platform.isAndroid ? "1" : "2",
      );
      if (versionModel.status == true) {
        emit(state.copyWith(versionModel: versionModel, versionCode: versionModel.version ?? '0.0'));
      }
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }
}
