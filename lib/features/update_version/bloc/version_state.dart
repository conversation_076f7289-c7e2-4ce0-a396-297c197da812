part of 'version_bloc.dart';

class VersionState extends Equatable {
  final VersionModel? versionModel;
  final String? versionCode;
  const VersionState({this.versionModel, this.versionCode});

  @override
  List<Object?> get props => [versionModel, versionCode];
  VersionState copyWith({
    VersionModel? versionModel,
    String? versionCode,
  }) {
    return VersionState(
      versionModel: versionModel ?? this.versionModel,
      versionCode: versionCode ?? this.versionCode,
    );
  }
}
