import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flowkar/core/utils/hive_storage.dart';

part 'splash_event.dart';
part 'splash_state.dart';

class SplashBloc extends Bloc<SplashEvent, SplashState> {
  SplashBloc() : super(SplashInitial()) {
    on<CheckAuthTokenEvent>(_onCheckAuthToken);
  }

  _onCheckAuthToken(CheckAuthTokenEvent event, Emitter<SplashState> emit) async {
    await Future.delayed(Duration(seconds: 3));
    final token = Prefobj.preferences?.get(Prefkeys.AUTHTOKEN);
    if (token != null && token.isNotEmpty) {
      emit(AuthTokenAvailable());
    } else {
      emit(AuthTokenUnavailable());
    }
  }
}
