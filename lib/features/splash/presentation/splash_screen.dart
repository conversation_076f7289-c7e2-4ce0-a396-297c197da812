import 'package:flowkar/core/socket/socket_service.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/authentication/bloc/auth_bloc.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/splash/presentation/no_internet_screen.dart';
import 'package:flowkar/features/update_version/bloc/version_bloc.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});
  static Widget builder(BuildContext context) {
    return const SplashScreen();
  }

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.white),
      child: Scaffold(
        body: BlocProvider(
          create: (context) => SplashBloc()..add(CheckAuthTokenEvent()),
          child: BlocConsumer<ConnectivityBloc, ConnectivityState>(
            listener: (context, connectivityState) {
              if (connectivityState.isConnected) {
                context.read<SplashBloc>().add(CheckAuthTokenEvent());

                final splashBloc = context.read<SplashBloc>().state;
                String onboarding = Prefobj.preferences?.get(Prefkeys.FIRSTTIME) ?? 'false';

                if (splashBloc is AuthTokenAvailable) {
                  SocketService.emit(APIConfig.joinSocket, {
                    'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
                  });
                  SocketService.response(APIConfig.joinSocket, (joinSocket) {
                    Logger.lOG(joinSocket);
                  });
                  context.read<AuthBloc>().add(GetUserStatusApi(context: context));
                  context.read<VersionBloc>().add(VersionUpdateEvent());
                } else if (splashBloc is AuthTokenUnavailable) {
                  Future.delayed(
                    Duration(seconds: 3),
                    () async {
                      if (onboarding == 'false') {
                        await NavigatorService.pushAndRemoveUntil(AppRoutes.onboardingmainPage);
                      } else {
                        await NavigatorService.pushAndRemoveUntil(AppRoutes.signinPage);
                      }
                    },
                  );
                }
              }
            },
            builder: (context, connectivityState) {
              if (connectivityState.isConnected == false) {
                return NoInternetScreen();
              }

              return BlocBuilder<AuthBloc, AuthState>(
                builder: (context, authState) {
                  return BlocListener<SplashBloc, SplashState>(
                    listener: (context, state) {
                      String onboarding = Prefobj.preferences?.get(Prefkeys.FIRSTTIME) ?? 'false';

                      if (state is AuthTokenAvailable) {
                        SocketService.emit(APIConfig.joinSocket, {
                          'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
                        });
                        SocketService.response(APIConfig.joinSocket, (joinSocket) {
                          Logger.lOG(joinSocket);
                        });
                        context.read<AuthBloc>().add(GetUserStatusApi(context: context));
                        context.read<VersionBloc>().add(VersionUpdateEvent());
                      } else if (state is AuthTokenUnavailable) {
                        Future.delayed(
                          Duration(seconds: 3),
                          () async {
                            if (onboarding == 'false') {
                              await NavigatorService.pushAndRemoveUntil(AppRoutes.onboardingmainPage);
                            } else {
                              await NavigatorService.pushAndRemoveUntil(AppRoutes.signinPage);
                            }
                          },
                        );
                      }
                    },
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CustomImageView(
                            width: 150.w,
                            height: 150.h,
                            imagePath: Assets.images.pngs.flowkar.path,
                          ),
                          SizedBox(height: 20.h),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }
}

// import 'package:flowkar/core/socket/socket_service.dart';
// import 'package:flowkar/core/utils/exports.dart';
// import 'package:flowkar/features/authentication/bloc/auth_bloc.dart';
// import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
// import 'package:flowkar/features/splash/presentation/no_internet_screen.dart';
// import 'package:flowkar/features/update_version/bloc/version_bloc.dart';

// class SplashScreen extends StatefulWidget {
//   const SplashScreen({super.key});
//   static Widget builder(BuildContext context) {
//     return const SplashScreen();
//   }

//   @override
//   State<SplashScreen> createState() => _SplashScreenState();
// }

// class _SplashScreenState extends State<SplashScreen> {
//   @override
//   Widget build(BuildContext context) {
//     return AnnotatedRegion<SystemUiOverlayStyle>(
//       value: SystemUiOverlayStyle(
//           statusBarColor: Colors.transparent,
//           statusBarIconBrightness: Brightness.dark,
//           systemNavigationBarIconBrightness:
//               // themeState.isDarkThemeOn
//               //     ? Brightness.light:
//               Brightness.dark,
//           systemNavigationBarColor: Colors.white),
//       child: Scaffold(
//         body: BlocProvider(
//           create: (context) => SplashBloc()..add(CheckAuthTokenEvent()),
//           child: BlocConsumer<ConnectivityBloc, ConnectivityState>(
//             listener: (context, connectivityState) {
//               if (connectivityState.isConnected) {
//                 final splashBloc = context.read<SplashBloc>().state;
//                 String onboarding = Prefobj.preferences?.get(Prefkeys.FIRSTTIME) ?? 'false';

//                 if (splashBloc is AuthTokenAvailable) {
//                   SocketService.emit(APIConfig.joinSocket, {
//                     'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
//                   });
//                   SocketService.response(APIConfig.joinSocket, (joinSocket) {
//                     Logger.lOG(joinSocket);
//                   });
//                   context.read<AuthBloc>().add(GetUserStatusApi(context: context));
//                   context.read<VersionBloc>().add(VersionUpdateEvent());
//                 } else if (splashBloc is AuthTokenUnavailable) {
//                   Future.delayed(
//                     Duration(seconds: 3),
//                     () async {
//                       if (onboarding == 'false') {
//                         await NavigatorService.pushAndRemoveUntil(AppRoutes.onboardingmainPage);
//                       } else {
//                         await NavigatorService.pushAndRemoveUntil(AppRoutes.signinPage);
//                       }
//                     },
//                   );
//                 }
//               }
//             },
//             builder: (context, connectivityState) {
//               if (connectivityState.isConnected == false) {
//                 return NoInternetScreen();
//               }
//               return BlocBuilder<AuthBloc, AuthState>(
//                 builder: (context, authState) {
//                   return BlocListener<SplashBloc, SplashState>(
//                     listener: (context, state) {
//                       String onboarding = Prefobj.preferences?.get(Prefkeys.FIRSTTIME) ?? 'false';

//                       if (state is AuthTokenAvailable) {
//                         SocketService.emit(APIConfig.joinSocket, {
//                           'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
//                         });
//                         SocketService.response(APIConfig.joinSocket, (joinSocket) {
//                           Logger.lOG(joinSocket);
//                         });
//                         context.read<AuthBloc>().add(GetUserStatusApi(context: context));
//                         context.read<VersionBloc>().add(VersionUpdateEvent());
//                       } else if (state is AuthTokenUnavailable) {
//                         Future.delayed(
//                           Duration(
//                             seconds: 3,
//                           ),
//                           () async {
//                             if (onboarding == 'false') {
//                               await NavigatorService.pushAndRemoveUntil(AppRoutes.onboardingmainPage);
//                             } else {
//                               await NavigatorService.pushAndRemoveUntil(AppRoutes.signinPage);
//                             }
//                           },
//                         );
//                       }
//                     },
//                     child: Center(
//                       child: Column(
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         children: [
//                           CustomImageView(
//                             width: 150.w,
//                             height: 150.h,
//                             imagePath: Assets.images.pngs.flowkar.path,
//                           ),
//                           SizedBox(height: 20.h),
//                         ],
//                       ),
//                     ),
//                   );
//                 },
//               );
//             },
//           ),
//         ),
//       ),
//     );
//   }
// }
