import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/survey_form/bloc/survey_bloc.dart';

class NoInternetScreen extends StatelessWidget {
  const NoInternetScreen({super.key});

  static Widget builder(BuildContext context) {
    return NoInternetScreen();
  }

  @override
  Widget build(BuildContext context) {
    return MediaQuery(
      data: MediaQuery.of(context).copyWith(
        highContrast: true,
        displayFeatures: MediaQuery.of(context).displayFeatures,
        gestureSettings: MediaQuery.of(context).gestureSettings,
        textScaler: TextScaler.noScaling,
        invertColors: false,
        boldText: false,
      ),
      child: Scaffold(
        body: Center(
          child: Padding(
            padding: EdgeInsets.only(top: 100.0.h),
            child: ExceptionWidget(
              imagePath: AssetConstants.pngNoInternet,
              title: Lang.of(context).lbl_no_internet_title,
              subtitle: Lang.of(context).lbl_no_internet_subtitle,
              onButtonPressed: () {
                context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
                context.read<ConnectivityBloc>().add(ConnectivityChecked());
              },
              buttonText: Lang.of(context).lbl_try_again,
            ),
          ),
        ),
      ),
    );
  }
}
