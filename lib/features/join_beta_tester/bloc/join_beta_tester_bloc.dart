import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/survey_form/bloc/survey_bloc.dart';

part 'join_beta_tester_event.dart';
part 'join_beta_tester_state.dart';

class JoinBetaTesterBloc extends Bloc<JoinBetaTesterEvent, JoinBetaTesterState> {
  ApiClient apiClient = ApiClient(Dio());
  JoinBetaTesterBloc(super.initialState) {
    on<SignUpJoinBetaTesterEvent>(_onSignUpJoinBetaTesterEvent);
  }
  Future<void> _onSignUpJoinBetaTesterEvent(SignUpJoinBetaTesterEvent event, Emitter<JoinBetaTesterState> emit) async {
    emit(state.copyWith(isLoginLoading: true));
    try {
      final result = await apiClient.registerBetaUser(
        name: event.name,
        email: event.email,
        platformStatus: event.userType,
        age: (event.age == null) ? "" : event.age,
        mobile: (event.mobile == null) ? "" : event.mobile,
      );

      if (result.status == true) {
        event.context!.read<SurveyBloc>().add(UserHomeDataAPI(context: event.context));
        Future.delayed(const Duration(seconds: 1), () {
          emit(state.copyWith(isLoginLoading: false));
        });
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            result.message,
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        NavigatorService.goBack();
      } else {
        toastification.show(
          type: ToastificationType.error,
          showProgressBar: false,
          title: Text(
            result.message,
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        emit(state.copyWith(isLoginLoading: false));
      }
    } catch (error) {
      toastification.show(
        type: ToastificationType.warning,
        showProgressBar: false,
        title: Text(
          "Something went wrong.",
          style: GoogleFonts.montserrat(
            fontSize: 12.0.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        autoCloseDuration: const Duration(seconds: 3),
      );
      emit(state.copyWith(isLoginLoading: false));
      Logger.lOG("_registerbrandApi Error: $error");
    }
  }
}
