part of 'join_beta_tester_bloc.dart';

class JoinBetaTesterState extends Equatable {
  final bool? isLoginLoading;

  const JoinBetaTesterState({this.isLoginLoading = false});
  @override
  List<Object?> get props => [
        isLoginLoading,
      ];
  JoinBetaTesterState copyWith({
    bool? isLoginLoading,
  }) {
    return JoinBetaTesterState(
      isLoginLoading: isLoginLoading ?? this.isLoginLoading,
    );
  }
}
