part of 'join_beta_tester_bloc.dart';

sealed class JoinBetaTesterEvent extends Equatable {
  const JoinBetaTesterEvent();

  @override
  List<Object> get props => [];
}

class SignUpJoinBetaTesterEvent extends JoinBetaTesterEvent {
  final String name;
  final String email;
  final String userType;
  final String? age;
  final String? mobile;
  final BuildContext? context;
  const SignUpJoinBetaTesterEvent({required this.name, required this.email, required this.userType, this.age, this.mobile, this.context});

  @override
  List<Object> get props => [
        name,
        email,
        userType,
        age ?? '',
        mobile ?? '',
        context!,
      ];
}
