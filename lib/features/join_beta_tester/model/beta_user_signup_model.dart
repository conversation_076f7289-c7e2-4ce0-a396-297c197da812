import 'package:json_annotation/json_annotation.dart';

part 'beta_user_signup_model.g.dart';

BetaUserSignupModel deserializeBetaUserSignupModel(Map<String, dynamic> json) => BetaUserSignupModel.fromJson(json);

@JsonSerializable()
class BetaUserSignupModel {
  final bool status;
  final String message;

  BetaUserSignupModel({
    required this.status,
    required this.message,
  });

  factory BetaUserSignupModel.fromJson(Map<String, dynamic> json) => _$BetaUserSignupModelFromJson(json);

  Map<String, dynamic> toJson() => _$BetaUserSignupModelToJson(this);
}
