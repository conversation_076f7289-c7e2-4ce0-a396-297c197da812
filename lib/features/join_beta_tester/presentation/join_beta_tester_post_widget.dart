// ignore_for_file: deprecated_member_use

import 'package:flowkar/core/helpers/vibration_helper.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_manager.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_player.dart';
import 'package:cron/cron.dart';
import 'package:flowkar/features/join_beta_tester/presentation/beta_user_signup_popup.dart';

// ignore: must_be_immutable
class JoinBetaTesterPostWidg extends StatefulWidget {
  final String profileImage;
  final List<String> thumbnailImage;

  // final List<AnalyticsData> analytics;
  final int postId;
  final String name;
  final String username;
  final List<String> postMedia;
  final String caption;
  bool isLiked;

  // final HomeFeedState? homeFeedState;
  final String latestcomments;

  JoinBetaTesterPostWidg({
    super.key,
    required this.postId,
    required this.profileImage,
    required this.thumbnailImage,
    required this.name,
    required this.username,
    required this.postMedia,
    required this.caption,
    required this.isLiked,
    required this.latestcomments,
  });

  @override
  State<JoinBetaTesterPostWidg> createState() => _PostWidgetState();
}

class _PostWidgetState extends State<JoinBetaTesterPostWidg> with SingleTickerProviderStateMixin {
  final PageController _postcontroller = PageController();
  late FlickMultiManager flickMultiManager;
  bool _isindicatorVisible = true;
  int _currentMediaIndex = 0;
  late AnimationController _controller;
  late Animation<double> _animation;
  bool isAnimationVisible = false;
  late Cron _cron;
  String currentUserId = '';

  @override
  void initState() {
    flickMultiManager = FlickMultiManager();
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    )..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          _controller.reverse();
        }
      });
    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
    );
    _cron = Cron();
    _startAutoHide();
    currentUserId = Prefobj.preferences!.get(Prefkeys.USER_ID).toString();
  }

  void _startAutoHide() {
    _cron.schedule(Schedule.parse('*/10 * * * * *'), () {
      if (_isindicatorVisible) {
        setState(() {
          _isindicatorVisible = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _cron.close();
    super.dispose();
  }

  void _resetHideTimer() {
    _cron.close();
    _cron = Cron();
    _startAutoHide();
    // setState(() {
    _isindicatorVisible = true;
    // });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildUserDetail(context),
        Stack(
          children: [
            Column(
              children: [
                _buildPostMedia(context),
                _buildPostDetail(
                  context,
                ),
                buildSizedBoxH(16),
              ],
            ),
            _buildLikeCommentShare(),
          ],
        ),
      ],
    );
  }

  Widget _buildUserDetail(
    BuildContext context,
  ) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 18.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
      decoration: BoxDecoration(
          color: Theme.of(context).customColors.white,
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).customColors.black.withOpacity(0.2),
              blurRadius: 2,
            )
          ],
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          )),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              ClipRRect(
                child: Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: Container(
                    height: 43.0.h,
                    width: 43.0.w,
                    padding: widget.profileImage.isEmpty ? EdgeInsets.all(10) : EdgeInsets.zero,
                    decoration: BoxDecoration(
                        border: Border.all(color: Theme.of(context).primaryColor.withOpacity(0.2), width: 2),
                        // borderRadius: BorderRadius.circular(100.r),
                        shape: BoxShape.circle),
                    child: CustomImageView(
                      onTap: () {},
                      radius: widget.profileImage.isEmpty ? null : BorderRadius.circular(100.r),
                      fit: BoxFit.cover,
                      imagePath: widget.profileImage,
                      alignment: Alignment.center,
                    ),
                  ),
                ),
              ),
              buildSizedBoxW(8),
              InkWell(
                onTap: () {
                  // if (widget.userId.toString() == currentUserId) {
                  //   PersistentNavBarNavigator.pushNewScreen(context, screen: UserProfileScreen(stackonScreen: false), withNavBar: false);
                  //   // NavigatorService.pushNamed(AppRoutes.userProfileScreen);
                  // } else {
                  //   PersistentNavBarNavigator.pushNewScreen(context, screen: GetUserProfileById(userId: widget.userId, stackonScreen: false), withNavBar: false);
                  // }
                },
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.name,
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge!
                          .copyWith(fontSize: 15.sp, fontWeight: FontWeight.w700, color: Color(0xff292D32)),
                    ),
                    buildSizedBoxH(1),
                    Text(
                      widget.username,
                      style: Theme.of(context)
                          .textTheme
                          .headlineSmall!
                          .copyWith(fontSize: 12.sp, fontWeight: FontWeight.w700, color: Color(0xff575353)),
                    ),
                  ],
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPostMedia(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      children: [
        SizedBox(
          height: 310.h,
          child: PageView.builder(
            controller: _postcontroller,
            onPageChanged: (index) {
              setState(() {
                _currentMediaIndex = index;
                _resetHideTimer();
              });
            },
            itemCount: widget.postMedia.length,
            itemBuilder: (context, index) {
              final mediaUrl = widget.postMedia[index];
              return isVideo(mediaUrl)
                  ? FlickMultiPlayer(
                      key: ObjectKey(mediaUrl),
                      url: mediaUrl,
                      flickMultiManager: flickMultiManager,
                      image: (widget.thumbnailImage.length > index && widget.thumbnailImage[index].isNotEmpty)
                          ? widget.thumbnailImage[index]
                          : AssetConstants.pngPlaceholder,
                    )
                  : CustomImageView(
                      imagePath: mediaUrl,
                      fit: BoxFit.cover,
                      width: double.infinity,
                    );
            },
          ),
        ),
        if (isAnimationVisible)
          Center(
            child: AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _animation.value,
                  child: Icon(
                    Icons.favorite_rounded,
                    size: 100.h,
                    color: Colors.red.withOpacity(0.7),
                  ),
                );
              },
            ),
          ),
        if (widget.postMedia.length > 1)
          _isindicatorVisible
              ? Positioned(
                  right: 18.w,
                  top: 10,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                    decoration: BoxDecoration(
                      color: Colors.black38,
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Text(
                      '${_currentMediaIndex + 1}/${widget.postMedia.length}',
                      style: Theme.of(context)
                          .textTheme
                          .bodyMedium!
                          .copyWith(color: Theme.of(context).customColors.white, fontSize: 10.0.sp),
                    ),
                  ),
                )
              : const SizedBox.shrink(),
      ],
    );
  }

  Widget _buildLikeCommentShare() {
    return Container(
      margin: EdgeInsets.only(top: 285.h, left: 18.w),
      height: 50.h,
      // width: 140.w,
      padding: EdgeInsets.symmetric(horizontal: 10.0.w),
      decoration: BoxDecoration(
          color: Color(0xffF6F6F6),
          borderRadius: BorderRadius.circular(34.r),
          boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 5)]),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildActionButton(
            context,
            onTap: () {
              VibrationHelper.singleShortBuzz();
              showDialog(
                context: context,
                barrierDismissible: true,
                builder: (BuildContext context) {
                  return const BetaUserSignupPopup();
                },
              );
              Logger.lOG("JOIN NOW");
            },
            colors: widget.isLiked ? Theme.of(context).customColors.white : Color(0xff555555),
            iconPath:
                widget.isLiked ? Assets.images.icons.homeFeed.icLike.path : Assets.images.icons.homeFeed.icUnlike.path,
            color: Theme.of(context).primaryColor,
          ),
          // buildSizedBoxW(16),
          // _buildActionButton(context, onTap: widget.commentonTap, iconPath: Assets.images.icons.homeFeed.icComment.path),
          // // buildSizedBoxW(16),
          // // _buildActionButton(context, onTap: () {
          // //   Logger.lOG("Share");
          // // }, iconPath: Assets.images.icons.homeFeed.icShare.path),
        ],
      ),
    );
  }

  Widget _buildActionButton(BuildContext context,
      {required String iconPath, Color? color, Color? colors, EdgeInsetsGeometry? padding, void Function()? onTap}) {
    return InkWell(
      onTap: onTap,
      child: Container(
          height: 36.h,
          padding: padding ?? EdgeInsets.symmetric(horizontal: 18.0.w, vertical: 6.0.h),
          decoration: BoxDecoration(
            color: color ?? Color(0xffF6F6F6),
            // shape: BoxShape.circle,
            borderRadius: BorderRadius.circular(34.r),
          ),
          child: Row(
            children: [
              Text("Join now",
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w700,
                        color: Theme.of(context).customColors.white,
                      )),
            ],
          )),
    );
  }

  Widget _buildPostDetail(
    BuildContext context,
  ) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 18.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
      decoration: BoxDecoration(
          color: Theme.of(context).customColors.white,
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).customColors.black.withOpacity(0.2),
              blurRadius: 2,
            )
          ],
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(20.r),
            bottomRight: Radius.circular(20.r),
          )),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildSizedBoxH(20.h),
                Text("Hello ",
                    // widget.isLiked ? 'Liked by you and ${widget.likes} others' : 'Liked by ${widget.likes} others',
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge!
                        .copyWith(fontSize: 12.5.sp, fontWeight: FontWeight.w700)),
                buildSizedBoxH(1),
                widget.caption.isEmpty || widget.caption == "null"
                    ? const SizedBox.shrink()
                    : Text(widget.caption,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontSize: 12.5.sp)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class MyBehavior extends ScrollBehavior {
  @override
  Widget buildOverscrollIndicator(BuildContext context, Widget child, ScrollableDetails details) {
    return child;
  }
}

class TaggedUser {
  final String username;
  final int id;

  TaggedUser({required this.username, required this.id});
}
