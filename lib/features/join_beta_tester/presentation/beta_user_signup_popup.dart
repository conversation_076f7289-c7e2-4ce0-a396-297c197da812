import 'package:flowkar/core/helpers/vibration_helper.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/join_beta_tester/bloc/join_beta_tester_bloc.dart';

class BetaUserSignupPopup extends StatefulWidget {
  const BetaUserSignupPopup({super.key});

  static Future<void> show(BuildContext context) {
    return showDialog(
      // barrierDismissible: false,
      context: context,
      builder: (context) => const BetaUserSignupPopup(),
    );
  }

  @override
  State<BetaUserSignupPopup> createState() => _BetaUserSignupPopupState();
}

class _BetaUserSignupPopupState extends State<BetaUserSignupPopup> {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _ageController = TextEditingController();
  final TextEditingController _mobileController = TextEditingController();
  final TextEditingController _feedbackController = TextEditingController();

  bool _isAndroidSelected = false;
  bool _isIosSelected = false;
  bool _isWebSelected = false;
  bool _isUserTypeValid = true;

  @override
  initState() {
    super.initState();

    String name = Prefobj.preferences?.get(Prefkeys.USERNAME) ?? '';
    _nameController.text = name;
    String email = Prefobj.preferences?.get(Prefkeys.USEREMAIL) ?? '';
    _emailController.text = email;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _ageController.dispose();
    _mobileController.dispose();
    _feedbackController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<JoinBetaTesterBloc, JoinBetaTesterState>(
      builder: (context, state) {
        return Form(
          key: formKey,
          child: Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: 500.w,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: Padding(
                padding: EdgeInsets.all(16.0.w),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // IconButton(onPressed: (){}, icon: Icon(Icons.close))

                    Flexible(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Row(
                              children: [
                                // _buildHeaderImage(),
                                _buildTitle(context),
                                Spacer(),
                                GestureDetector(
                                  onTap: () {
                                    Navigator.of(context).pop();
                                  },
                                  child: Align(
                                    alignment: Alignment.topRight,
                                    child: Container(
                                      padding: EdgeInsets.all(5),
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        border: Border.all(color: Theme.of(context).primaryColor, width: 1),
                                      ),
                                      child: Icon(
                                        Icons.close_rounded,
                                        size: 16.sp,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            buildSizedBoxH(20),
                            _buildTextField(
                              context,
                              "Name *",
                              controller: _nameController,
                              validator: AppValidations.validateUsername,
                            ),
                            buildSizedBoxH(20.0),
                            _buildTextField(
                              context,
                              "Email * ",
                              controller: _emailController,
                              validator: AppValidations.validateEmail,
                              isReadOnly: true,
                            ),
                            buildSizedBoxH(20.0),
                            _buildLabelWithAsterisk('Platform Type *'),
                            buildSizedBoxH(8.0),
                            Row(
                              children: [
                                _buildCheckbox('Android', _isAndroidSelected, (value) {
                                  setState(() {
                                    _isAndroidSelected = value ?? false;
                                  });
                                }),
                                const SizedBox(width: 16.0),
                                _buildCheckbox('iOS', _isIosSelected, (value) {
                                  setState(() {
                                    _isIosSelected = value ?? false;
                                  });
                                }),
                                const SizedBox(width: 16.0),
                                _buildCheckbox('Web', _isWebSelected, (value) {
                                  setState(() {
                                    _isWebSelected = value ?? false;
                                  });
                                }),
                              ],
                            ),
                            if (!_isUserTypeValid) // एरर दिखाने के लिए
                              Padding(
                                padding: const EdgeInsets.only(top: 5.0),
                                child: Text(
                                  "Please select at least one platform.",
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        color: Theme.of(context).colorScheme.error,
                                        fontSize: 12.sp,
                                      ),
                                ),
                              ),
                            buildSizedBoxH(20.0),
                            FlowkarTextFormField(
                              context: context,
                              controller: _ageController,
                              maxLength: 2,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                              labelText: "Age",
                              filled: true,
                              textInputType: TextInputType.phone,
                              borderDecoration: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.0),
                                borderSide: BorderSide(color: Colors.grey.shade300),
                              ),
                              contentPadding: const EdgeInsets.all(12.0),
                            ),
                            buildSizedBoxH(20.0),
                            FlowkarTextFormField(
                              context: context,
                              controller: _mobileController,
                              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                              textInputType: TextInputType.phone,
                              labelText: "Mobile Number",
                              validator: (value) {
                                if (value != null && value.isNotEmpty && value.length < 10) {
                                  return "Please enter a valid 10-digit mobile number";
                                }
                                return null;
                              },
                              maxLength: 10,
                              filled: true,
                              borderDecoration: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.0),
                                borderSide: BorderSide(color: Colors.grey.shade300),
                              ),
                              contentPadding: const EdgeInsets.all(12.0),
                            ),
                            buildSizedBoxH(20.0),
                          ],
                        ),
                      ),
                    ),
                    Center(
                      child: CustomElevatedButton(
                        isDisabled: state.isLoginLoading ?? false,
                        isLoading: state.isLoginLoading ?? false,
                        text: "Submit",
                        onPressed: () {
                          setState(() {
                            _isUserTypeValid = _isAndroidSelected || _isIosSelected || _isWebSelected;
                          });
                          if (formKey.currentState!.validate() && _isUserTypeValid) {
                            String userType = "";
                            if (_isAndroidSelected) userType += "1";
                            if (_isIosSelected) userType += "2";
                            if (_isWebSelected) userType += "3";
                            Logger.lOG("Name: ${_nameController.text}");
                            Logger.lOG("Email: ${_emailController.text}");
                            Logger.lOG("Age: ${_ageController.text}");
                            Logger.lOG("Mobile: ${_mobileController.text}");
                            Logger.lOG("Android: $_isAndroidSelected");
                            Logger.lOG("iOS: $_isIosSelected");
                            Logger.lOG("Web: $_isWebSelected");
                            context.read<JoinBetaTesterBloc>().add(
                                  SignUpJoinBetaTesterEvent(
                                    name: _nameController.text.trim(),
                                    email: _emailController.text.trim(),
                                    userType: userType,
                                    age: _ageController.text.trim(),
                                    mobile: _mobileController.text.trim(),
                                    context: context,
                                  ),
                                );
                            VibrationHelper.singleShortBuzz();
                          }
                          // if (formKey.currentState!.validate() && !_isAndroidSelected && !_isIosSelected && !_isWebSelected) {}
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // Widget _buildHeaderImage() => Align(
  //       alignment: Alignment.center,
  //       child: CustomImageView(
  //         height: 30.h,
  //         width: 30.h,
  //         imagePath: Assets.images.svg.other.svgSurveyForm.path,
  //       ),
  //     );

  Widget _buildTitle(BuildContext context) => Align(
        alignment: Alignment.center,
        child: Text(
          "Become A Beta User",
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w800,
                fontSize: 20.sp,
              ),
        ),
      );

  Widget _buildTextField(
    BuildContext context,
    String label, {
    int maxLines = 1,
    String? heint,
    String? Function(String?)? validator,
    required TextEditingController controller,
    bool? isReadOnly = false,
  }) =>
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Text(
          //   label,
          //   style: Theme.of(context)
          //       .textTheme
          //       .bodyLarge
          //       ?.copyWith(fontSize: 14.sp),
          // ),
          // buildSizedBoxH(5.0),
          FlowkarTextFormField(
            readOnly: true,
            labelText: label,
            hintText: heint,
            context: context,
            controller: controller,
            maxLines: maxLines,
            borderDecoration: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xffE0E0E0)),
              borderRadius: BorderRadius.circular(12.r),
            ),
            validator: validator,
          ),
        ],
      );
  Widget _buildLabelWithAsterisk(String label) {
    return Row(
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                // fontWeight: FontWeight.w800,
                color: Theme.of(context).customColors.black,
                fontWeight: FontWeight.w400,
                fontSize: 14.sp,
              ),
        ),
      ],
    );
  }

  Widget _buildCheckbox(String label, bool value, Function(bool?) onChanged) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 24,
          height: 24,
          child: Checkbox(
            value: value,
            onChanged: onChanged,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            side: BorderSide(
              color: Theme.of(context).primaryColor,
              width: 0.8,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4.0),
              side: BorderSide(
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
        ),
        const SizedBox(width: 4.0),
        Text(
          label,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w400,
                color: Theme.of(context).customColors.black,
                fontSize: 14.sp,
              ),
        ),
      ],
    );
  }
}
