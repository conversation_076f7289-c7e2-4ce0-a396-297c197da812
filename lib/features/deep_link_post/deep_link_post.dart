import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/comment_bottom_sheet_widget.dart';
import 'package:flowkar/features/survey_form/bloc/survey_bloc.dart';
import 'package:flowkar/features/widgets/custom/custom_conditional_scroll_physics.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';

class DeepLinkPostScreen extends StatefulWidget {
  final String? postId;
  final String? shareType;
  final bool? showCommentBottomSheet;
  final bool? isNotificationPost;

  const DeepLinkPostScreen({
    super.key,
    required this.postId,
    this.shareType,
    this.showCommentBottomSheet = false,
    this.isNotificationPost = false,
  });

  static Widget builder(BuildContext context, {String? postId, String? shareType, bool? showCommentBottomSheet}) {
    return DeepLinkPostScreen(
        postId: postId, shareType: shareType, showCommentBottomSheet: showCommentBottomSheet ?? false);
  }

  @override
  State<DeepLinkPostScreen> createState() => _DeepLinkPostScreenState();
}

class _DeepLinkPostScreenState extends State<DeepLinkPostScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
    context.read<HomeFeedBloc>().add(FetchDeepLinkPostEvent(postId: widget.postId ?? "", shareType: widget.shareType));
    super.initState();
    if (widget.showCommentBottomSheet == true) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showModalBottomSheet(
          context: context,
          useRootNavigator: true,
          isScrollControlled: true,
          builder: (context) => CommentsBottomSheet(postId: int.tryParse(widget.postId ?? '') ?? 0),
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: BlocListener<ConnectivityBloc, ConnectivityState>(
        listener: (context, state) {
          if (state.isReconnected) {
            PersistentNavBarNavigator.pop(context);
          }
        },
        child: LiquidPullToRefresh(
          color: Theme.of(context).primaryColor.withOpacity(0.5),
          showChildOpacityTransition: false,
          backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
          onRefresh: () async {
            return context
                .read<HomeFeedBloc>()
                .add(FetchDeepLinkPostEvent(postId: widget.postId ?? "", shareType: widget.shareType));
          },
          child: BlocBuilder<HomeFeedBloc, HomeFeedState>(
            builder: (context, state) {
              return BlocBuilder<ConnectivityBloc, ConnectivityState>(
                builder: (context, connectivityState) {
                  if (!connectivityState.isConnected) {
                    return HomeFeedShimmer();
                  } else if (state.isDeepLinkPostLoading) {
                    return HomeFeedShimmer();
                  } else if (state.deepLinkPostModel == null) {
                    return ListView(
                      physics: AlwaysScrollableScrollPhysics(),
                      children: [
                        buildSizedBoxH(MediaQuery.of(context).size.height / 6),
                        ExceptionWidget(
                          imagePath: Assets.images.svg.exception.svgNodatafound.path,
                          showButton: false,
                          title: Lang.of(context).lbl_no_data_found,
                          subtitle: Lang.of(context).lbl_no_post,
                        ),
                      ],
                    );
                  } else {
                    return ListView(
                      controller: _scrollController,
                      physics: ConditionalAlwaysScrollPhysics(controller: _scrollController),
                      children: [
                        buildSizedBoxH(8),
                        PostWidget(
                          width: 1024,
                          height: 1024,
                          userpost: true,
                          userByIDpost: false,
                          userByIDvideo: false,
                          userVideo: false,
                          isTextPost: ((state.deepLinkPostModel?.files.isEmpty ?? true) ? true : false),
                          state: context.read<HomeFeedBloc>().state,
                          latestcomments: state.deepLinkPostModel?.latestComment.toString() ?? '',
                          index: 0,
                          userId: state.deepLinkPostModel?.user.userId,
                          postId: state.deepLinkPostModel?.id ?? 0,
                          profileImage: state.deepLinkPostModel?.user.profileImage ?? "",
                          name: state.deepLinkPostModel?.user.name ?? "",
                          username: state.deepLinkPostModel?.user.username ?? "",
                          postMedia: state.deepLinkPostModel?.files ?? [],
                          thumbnailImage: [],
                          taggedIn: state.deepLinkPostModel?.taggedIn,
                          title: state.deepLinkPostModel?.title ?? "",
                          caption:
                              "${state.deepLinkPostModel?.title == "''" || state.deepLinkPostModel!.title.isEmpty ? '' : state.deepLinkPostModel?.title}${state.deepLinkPostModel!.description.isEmpty ? '' : state.deepLinkPostModel?.title == "''" || state.deepLinkPostModel!.title.isEmpty ? state.deepLinkPostModel?.description : "\n${state.deepLinkPostModel?.description}"}",
                          likes: state.deepLinkPostModel?.likes.toString() ?? "",
                          comments: state.deepLinkPostModel?.commentsCount.toString() ?? "",
                          postTime: state.deepLinkPostModel?.createdAt.toIso8601String() ?? "",
                          isLiked: state.deepLinkPostModel?.isLiked ?? false,
                          isSaved: state.deepLinkPostModel?.isSaved ?? false,
                          screenType: "Shared Post",
                          isprofilrpostDelete: false,
                          isNotificationPost: widget.isNotificationPost ?? false,
                          doubleTap: () {
                            if (state.deepLinkPostModel?.isLiked == false) {
                              context
                                  .read<HomeFeedBloc>()
                                  .add(LikePostSocketEvent(postId: state.deepLinkPostModel?.id ?? 0));
                            }
                          },
                          likeonTap: () {
                            context
                                .read<HomeFeedBloc>()
                                .add(LikePostSocketEvent(postId: state.deepLinkPostModel?.id ?? 0));
                          },
                          commentonTap: () {
                            showModalBottomSheet(
                              context: context,
                              useRootNavigator: true,
                              isScrollControlled: true,
                              builder: (context) => CommentsBottomSheet(postId: state.deepLinkPostModel?.id ?? 0),
                            );
                          },
                          shareonTap: () {},
                          saveonTap: () {},
                        ),
                        buildSizedBoxH(16),
                      ],
                    );
                  }
                },
              );
            },
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        IconButton(
          icon: CustomImageView(
            imagePath: Assets.images.svg.authentication.icBackArrow.path,
            height: 16.h,
          ),
          onPressed: () {
            context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
            Navigator.pop(context);
          },
        ),
        buildSizedBoxW(20.w),
        Text(
          Lang.of(context).lbl_post,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }
}
