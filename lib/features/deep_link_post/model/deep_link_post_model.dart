DeepLinkPostModel deserializeDeepLinkPostModel(Map<String, dynamic> json) => DeepLinkPostModel.fromJson(json);

class DeepLinkPostModel {
  final int id;
  final String title;
  final String description;
  final String location;
  final int likes;
  final int dislikes;
  final int commentsCount;
  final List<String> taggedIn;
  final DateTime createdAt;
  final List<String> files;
  final double height;
  final double width;
  final String latestComment;
  final UserModel user;
  final bool isLiked;
  final bool isSaved;
  final String shareType;

  DeepLinkPostModel({
    required this.id,
    required this.title,
    required this.description,
    required this.location,
    required this.likes,
    required this.dislikes,
    required this.commentsCount,
    required this.taggedIn,
    required this.createdAt,
    required this.files,
    required this.height,
    required this.width,
    required this.latestComment,
    required this.user,
    required this.isLiked,
    required this.isSaved,
    required this.shareType,
  });

  factory DeepLinkPostModel.fromJson(Map<String, dynamic> json) {
    return DeepLinkPostModel(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      location: json['location'] ?? '',
      likes: json['likes'] ?? 0,
      dislikes: json['dislikes'] ?? 0,
      commentsCount: json['comments_count'] ?? 0,
      taggedIn: (json['tagged_in'] as List<dynamic>?)?.map((e) => e.toString()).toList() ?? [],
      createdAt: DateTime.tryParse(json['created_at'] ?? '') ?? DateTime.now(),
      files: (json['files'] as List<dynamic>?)?.map((e) => e.toString()).toList() ?? [],
      height: json['height']?.toDouble() ?? 0.0,
      width: json['width']?.toDouble() ?? 0.0,
      latestComment: json['latest_comment'] ?? '',
      user: UserModel.fromJson(json['user'] ?? {}),
      isLiked: json['is_liked'] ?? false,
      isSaved: json['is_saved'] ?? false,
      shareType: json['share_type'] ?? '',
    );
  }

  DeepLinkPostModel copyWith({
    int? id,
    String? title,
    String? description,
    String? location,
    int? likes,
    int? dislikes,
    int? commentsCount,
    List<String>? taggedIn,
    DateTime? createdAt,
    List<String>? files,
    double? height,
    double? width,
    String? latestComment,
    UserModel? user,
    bool? isLiked,
    bool? isSaved,
    String? shareType,
  }) {
    return DeepLinkPostModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      likes: likes ?? this.likes,
      dislikes: dislikes ?? this.dislikes,
      commentsCount: commentsCount ?? this.commentsCount,
      taggedIn: taggedIn ?? this.taggedIn,
      createdAt: createdAt ?? this.createdAt,
      files: files ?? this.files,
      height: height ?? this.height,
      width: width ?? this.width,
      latestComment: latestComment ?? this.latestComment,
      user: user ?? this.user,
      isLiked: isLiked ?? this.isLiked,
      isSaved: isSaved ?? this.isSaved,
      shareType: shareType ?? this.shareType,
    );
  }
}

class UserModel {
  final int userId;
  final String username;
  final String name;
  final String profileImage;

  UserModel({
    required this.userId,
    required this.username,
    required this.name,
    required this.profileImage,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      userId: json['user_id'] ?? 0,
      username: json['username'] ?? '',
      name: json['name'] ?? '',
      profileImage: json['profile_image'] ?? '',
    );
  }
}
