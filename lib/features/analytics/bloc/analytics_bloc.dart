import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/analytics/model/get_analytics_platforms_model.dart';
import 'package:flowkar/features/analytics/presentation/page/facebook_analytics/model/facebook_impressions_follower_model.dart';
import 'package:flowkar/features/analytics/presentation/page/facebook_analytics/model/facebook_post_graph_model.dart';
import 'package:flowkar/features/analytics/presentation/page/instagram_analytics/model/instagram_line_graph_model.dart';
import 'package:flowkar/features/analytics/presentation/page/instagram_analytics/model/instagram_pie_chart_model.dart';
import 'package:flowkar/features/analytics/presentation/page/linkedin_analytics/model/follower_impretion_posts_model.dart';
import 'package:flowkar/features/analytics/presentation/page/linkedin_analytics/model/linkedin_engagement_share_model.dart';
import 'package:flowkar/features/analytics/presentation/page/linkedin_analytics/model/linkedin_pie_chart_model.dart';
import 'package:flowkar/features/analytics/presentation/page/pinterest/model/pinterest_line_graph_model.dart';
import 'package:flowkar/features/analytics/presentation/page/pinterest/model/pintrest_total_count_model.dart';
import 'package:flowkar/features/analytics/presentation/page/thread_analytics/model/thread_country_model.dart';
import 'package:flowkar/features/analytics/presentation/page/thread_analytics/model/thread_line_graph_model.dart';
import 'package:flowkar/features/analytics/presentation/page/youtube_analytics/model/youtube_subscribe_like_comment_graph_model.dart';
import 'package:flowkar/features/analytics/presentation/page/youtube_analytics/model/youtube_video_graph_model.dart';

part 'analytics_event.dart';
part 'analytics_state.dart';

class AnalyticsBloc extends Bloc<AnalyticsEvent, AnalyticsState> {
  ApiClient apiClient = ApiClient(Dio());

  AnalyticsBloc(super.initialState) {
    on<GetAnalyticsEventEvent>(_onInitialize);
    on<GetAnalyticsSocialEvent>(_onGetSocoalPlatforms);
    on<LinkedinFollowerImpretionsPostAPI>(_onFollowerImpretionPost);
    on<LinkedinPiechartAPI>(_onLinkedinPieChart);
    on<LinkedinengagementAndShareAPI>(_onLinkednEngagementAndShareChart);
    on<InstagramLineGraphAPI>(_onInstagramLineGraphAPI);
    on<InstagramPieChartAPI>(_onInstagramPieChartAPI);
    on<FacebookImpressionFollowerAPI>(_onFacebookImpressionFolowerAPI);
    on<FacebookPostLikeCommentShareAPI>(_onFacebookPostGraphAPI);
    on<YoutubeVideoGraphAPI>(_onYoutubeVideoGraphAPI);
    on<YoutubeSubscribeLikeCommentGraphAPI>(_onYoutubeSubscribeLikeCommentGraphAPI);
    on<ThreadLineGraphAPI>(_onThreadLineGraphAPI);
    on<ThreadPieChartAPI>(_onThreadPieGraphAPI);
    on<PinterestLineGraphAPI>(_onPinterestLineGraphAPI);
  }

  Future<void> _onInitialize(GetAnalyticsEventEvent event, Emitter<AnalyticsState> emit) async {
    emit(state.copyWith(
      isFollowerImpressionsPostLoading: false,
      isLinkedinEngagementLoading: false,
      isInstagramLineGraphLoading: false,
      isThreadlinegraphLoading: false,
      isFacebookImpretionAndFollower: false,
      isFacebookPostLikeCommentShareLoading: false,
      isInstagramPieChartLoading: false,
      isLinkedinPiechartLoading: false,
      isYoutubeSubscribeLikeCommentLoading: false,
      isYoutubeVideoGraphLoading: false,
    ));
  }

// =============================== Get Analytics Platforms =========================

  Future<void> _onGetSocoalPlatforms(GetAnalyticsSocialEvent event, Emitter<AnalyticsState> emit) async {
    emit(state.copyWith(isGetAnalyticsPlatformLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final result = await apiClient.getAnalyticsPlatforms(logInUserId: loginuserId, brand: brandId.toString());

      if (result.status == true) {
        Logger.lOG("_onGetSocoalPlatforms: ${result.data.toString()}");
        emit(state.copyWith(isGetAnalyticsPlatformLoading: false, getAnalyticsPlatformsModel: result));
      } else {
        emit(state.copyWith(isGetAnalyticsPlatformLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isGetAnalyticsPlatformLoading: false));
      Logger.lOG(" Error: $error");
    }
  }

  // =============================== Linkedin Analytics =========================

  Future<void> _onFollowerImpretionPost(LinkedinFollowerImpretionsPostAPI event, Emitter<AnalyticsState> emit) async {
    emit(state.copyWith(isFollowerImpressionsPostLoading: true));
    try {
      String subscriptionId = Prefobj.preferences?.get(Prefkeys.SUBSCRIPTION);
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      final result = await apiClient.getLinkedInFollowerImpretionPostAnalytics(
        logInUserId: loginuserId,
        endDate: event.endDate,
        startDate: event.startDate,
        brand: brandId.toString(),
        subscription: subscriptionId,
      );

      if (result.status == true) {
        Logger.lOG("FollowerImpretionPostModel: ${result.data.toString()}");
        emit(state.copyWith(
          isFollowerImpressionsPostLoading: false,
          followerImpressionsPostModel: result,
        ));
      } else {
        emit(state.copyWith(isFollowerImpressionsPostLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isFollowerImpressionsPostLoading: false));
      handleError(error);
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
    }
  }

  Future<void> _onLinkedinPieChart(LinkedinPiechartAPI event, Emitter<AnalyticsState> emit) async {
    emit(state.copyWith(isLinkedinPiechartLoading: true));
    try {
      String subscriptionId = Prefobj.preferences?.get(Prefkeys.SUBSCRIPTION);
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      final result = await apiClient.getLinkedInPieChartAnalytics(
        logInUserId: loginuserId,
        endDate: event.endDate,
        startDate: event.startDate,
        brand: brandId.toString(),
        subscription: subscriptionId,
      );

      if (result.status == true) {
        Logger.lOG(": ${result.data.toString()}");
        emit(state.copyWith(
          isLinkedinPiechartLoading: false,
          linkedinPiechartModel: result,
        ));
      } else {
        emit(state.copyWith(isLinkedinPiechartLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isLinkedinPiechartLoading: false));
      handleError(error);
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
    }
  }

  Future<void> _onLinkednEngagementAndShareChart(
      LinkedinengagementAndShareAPI event, Emitter<AnalyticsState> emit) async {
    emit(state.copyWith(isLinkedinEngagementLoading: true));
    try {
      String subscriptionId = Prefobj.preferences?.get(Prefkeys.SUBSCRIPTION);
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      final result = await apiClient.getLinkedinEngagementandShare(
        logInUserId: loginuserId,
        endDate: event.endDate,
        startDate: event.startDate,
        brand: brandId.toString(),
        subscription: subscriptionId,
      );

      if (result.status == true) {
        Logger.lOG(": ${result.data?.totalEngagement.toString()}");
        emit(state.copyWith(
          isLinkedinEngagementLoading: false,
          linkedinEngagementModel: result,
        ));
      } else {
        emit(state.copyWith(isLinkedinEngagementLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isLinkedinEngagementLoading: false));
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
    }
  }

// =============================== Instagram Analytics =========================
  Future<void> _onInstagramLineGraphAPI(InstagramLineGraphAPI event, Emitter<AnalyticsState> emit) async {
    emit(state.copyWith(isInstagramLineGraphLoading: true));
    try {
      String subscriptionId = Prefobj.preferences?.get(Prefkeys.SUBSCRIPTION);
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      final result = await apiClient.getInstagramLinegraph(
        logInUserId: loginuserId,
        endDate: event.endDate,
        startDate: event.startDate,
        brand: brandId.toString(),
        subscription: subscriptionId,
      );

      if (result.status == true) {
        Logger.lOG(": ${result.data.followersCount}");
        emit(state.copyWith(
          isInstagramLineGraphLoading: false,
          instagramLineGraph: result,
        ));
      } else {
        emit(state.copyWith(isInstagramLineGraphLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isInstagramLineGraphLoading: false));
      Logger.lOG(" Error: $error");
    }
  }

  Future<void> _onInstagramPieChartAPI(InstagramPieChartAPI event, Emitter<AnalyticsState> emit) async {
    emit(state.copyWith(isInstagramPieChartLoading: true));
    try {
      String subscriptionId = Prefobj.preferences?.get(Prefkeys.SUBSCRIPTION);
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      final result = await apiClient.getInstagramPiechart(
        logInUserId: loginuserId,
        endDate: event.endDate,
        startDate: event.startDate,
        brand: brandId.toString(),
        subscription: subscriptionId,
      );

      if (result.status == true) {
        Logger.lOG(": ${result.data}");
        emit(state.copyWith(
          isInstagramPieChartLoading: false,
          instagramPieChart: result,
        ));
      } else {
        emit(state.copyWith(isInstagramPieChartLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isInstagramPieChartLoading: false));
      Logger.lOG(" Error: $error");
    }
  }

  // ============================= Facebook Analytics =============================
  Future<void> _onFacebookImpressionFolowerAPI(
      FacebookImpressionFollowerAPI event, Emitter<AnalyticsState> emit) async {
    emit(state.copyWith(isFacebookImpretionAndFollower: true));
    try {
      String subscriptionId = Prefobj.preferences?.get(Prefkeys.SUBSCRIPTION);
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      final result = await apiClient.getFacebookImpressionFollower(
        logInUserId: loginuserId,
        endDate: event.endDate,
        startDate: event.startDate,
        brand: brandId.toString(),
        subscription: subscriptionId,
      );

      if (result.status == true) {
        Logger.lOG(": ${result.data}");
        emit(state.copyWith(
          isFacebookImpretionAndFollower: false,
          facebookImpretionAndFollowerModel: result,
        ));
      } else {
        emit(state.copyWith(isFacebookImpretionAndFollower: false));
      }
    } catch (error) {
      emit(state.copyWith(isFacebookImpretionAndFollower: false));
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
    }
  }

  Future<void> _onFacebookPostGraphAPI(FacebookPostLikeCommentShareAPI event, Emitter<AnalyticsState> emit) async {
    emit(state.copyWith(isFacebookPostLikeCommentShareLoading: true));
    try {
      String subscriptionId = Prefobj.preferences?.get(Prefkeys.SUBSCRIPTION);
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      final result = await apiClient.getFacebookPostShareComment(
        logInUserId: loginuserId,
        endDate: event.endDate,
        startDate: event.startDate,
        brand: brandId.toString(),
        subscription: subscriptionId,
      );

      if (result.status == true) {
        Logger.lOG(": ${result.data}");
        emit(state.copyWith(
          isFacebookPostLikeCommentShareLoading: false,
          facebookPostGraphModel: result,
        ));
      } else {
        emit(state.copyWith(isFacebookPostLikeCommentShareLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isFacebookPostLikeCommentShareLoading: false));
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
    }
  }

  // =============================  Youtube Analytics =============================
  Future<void> _onYoutubeVideoGraphAPI(YoutubeVideoGraphAPI event, Emitter<AnalyticsState> emit) async {
    emit(state.copyWith(isYoutubeVideoGraphLoading: true));
    try {
      String subscriptionId = Prefobj.preferences?.get(Prefkeys.SUBSCRIPTION);
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      final result = await apiClient.getYoutubeVideoGraph(
        logInUserId: loginuserId,
        endDate: event.endDate,
        startDate: event.startDate,
        brand: brandId.toString(),
        subscription: subscriptionId,
      );

      if (result.status == true) {
        Logger.lOG(": ${result.data}");
        emit(state.copyWith(
          isYoutubeVideoGraphLoading: false,
          youtubeVideoGraphModel: result,
        ));
      } else {
        emit(state.copyWith(isYoutubeVideoGraphLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isYoutubeVideoGraphLoading: false));
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
    }
  }

  Future<void> _onYoutubeSubscribeLikeCommentGraphAPI(
      YoutubeSubscribeLikeCommentGraphAPI event, Emitter<AnalyticsState> emit) async {
    emit(state.copyWith(isYoutubeSubscribeLikeCommentLoading: true));
    try {
      String subscriptionId = Prefobj.preferences?.get(Prefkeys.SUBSCRIPTION);
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';

      final result = await apiClient.getYoutubeSubscribeLikeCommentGraph(
        logInUserId: loginuserId,
        endDate: event.endDate,
        startDate: event.startDate,
        brand: brandId.toString(),
        subscription: subscriptionId,
      );

      if (result.status == true) {
        Logger.lOG(": ${result.data}");
        emit(state.copyWith(
          isYoutubeSubscribeLikeCommentLoading: false,
          youtubeSubscribeLikeCommentModel: result,
        ));
      } else {
        emit(state.copyWith(isYoutubeSubscribeLikeCommentLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isYoutubeSubscribeLikeCommentLoading: false));
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
    }
  }

  // ============================= Thread Analytics =============================
  Future<void> _onThreadLineGraphAPI(ThreadLineGraphAPI event, Emitter<AnalyticsState> emit) async {
    emit(state.copyWith(isThreadlinegraphLoading: true));
    try {
      String subscriptionId = Prefobj.preferences?.get(Prefkeys.SUBSCRIPTION);
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';

      final result = await apiClient.getThreadLineGraph(
        logInUserId: loginuserId,
        endDate: event.endDate,
        startDate: event.startDate,
        brand: brandId.toString(),
        subscription: subscriptionId,
      );

      if (result.status == true) {
        Logger.lOG(": ${result.data}");
        emit(state.copyWith(
          isThreadlinegraphLoading: false,
          threadLineGraphModel: result,
        ));
      } else {
        emit(state.copyWith(isThreadlinegraphLoading: false, threadLineGraphModel: null));
      }
    } catch (error) {
      emit(state.copyWith(isThreadlinegraphLoading: false, threadLineGraphModel: null));
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                )
          : Logger.lOG("Dio--- Error: $error");
    }
  }

  Future<void> _onThreadPieGraphAPI(ThreadPieChartAPI event, Emitter<AnalyticsState> emit) async {
    emit(state.copyWith(isThreadPieChartLoading: true));
    try {
      String subscriptionId = Prefobj.preferences?.get(Prefkeys.SUBSCRIPTION);
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      final result = event.chartType == 1
          ? await apiClient.getThreadCountry(
              logInUserId: loginuserId,
              brand: brandId.toString(),
              subscription: subscriptionId,
            )
          : event.chartType == 2
              ? await apiClient.getThreadcity(
                  logInUserId: loginuserId,
                  brand: brandId.toString(),
                  subscription: subscriptionId,
                )
              : event.chartType == 3
                  ? await apiClient.getThreadage(
                      logInUserId: loginuserId,
                      brand: brandId.toString(),
                      subscription: subscriptionId,
                    )
                  : await apiClient.getThreadgender(
                      logInUserId: loginuserId,
                      brand: brandId.toString(),
                      subscription: subscriptionId,
                    );

      if (result.status == true) {
        Logger.lOG(": ${result.data}");
        emit(state.copyWith(
          isThreadPieChartLoading: false,
          threadCountryModel: result,
        ));
      } else {
        emit(state.copyWith(isThreadPieChartLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isThreadPieChartLoading: false));
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
    }
  }
// ============================= Pinterest Analytics =============================

  Future<void> _onPinterestLineGraphAPI(PinterestLineGraphAPI event, Emitter<AnalyticsState> emit) async {
    emit(state.copyWith(isPinterestLineGraphLoading: true));
    try {
      String subscriptionId = Prefobj.preferences?.get(Prefkeys.SUBSCRIPTION);
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';

      final result = await apiClient.getPinterestLineGraph(
        logInUserId: loginuserId,
        endDate: event.endDate,
        startDate: event.startDate,
        brand: brandId.toString(),
        subscription: subscriptionId,
      );
      final resultTotalcountAPi = await apiClient.getPintresttotalcount(
        logInUserId: loginuserId,
        brand: brandId.toString(),
        subscription: subscriptionId,
      );

      if (result.status == true && resultTotalcountAPi.status == true) {
        Logger.lOG(": ${result.engagement}");
        emit(state.copyWith(
            isPinterestLineGraphLoading: false,
            pinterestAnalyticsModel: result,
            pinterestTotalCountModel: resultTotalcountAPi));
      } else {
        emit(state.copyWith(isPinterestLineGraphLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isPinterestLineGraphLoading: false));
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
    }
  }
}
