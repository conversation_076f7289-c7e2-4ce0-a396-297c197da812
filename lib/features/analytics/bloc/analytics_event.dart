part of 'analytics_bloc.dart';

sealed class AnalyticsEvent extends Equatable {
  const AnalyticsEvent();

  @override
  List<Object> get props => [];
}

class GetAnalyticsEventEvent extends AnalyticsEvent {
  @override
  List<Object> get props => [];
}

class GetAnalyticsSocialEvent extends AnalyticsEvent {
  @override
  List<Object> get props => [];
}

// Linkedin Analytics Event
class LinkedinFollowerImpretionsPostAPI extends AnalyticsEvent {
  final String startDate;
  final String endDate;
  const LinkedinFollowerImpretionsPostAPI({required this.startDate, required this.endDate});

  @override
  List<Object> get props => [
        startDate,
        endDate,
      ];
}

class LinkedinPiechartAPI extends AnalyticsEvent {
  final String startDate;
  final String endDate;
  const LinkedinPiechartAPI({required this.startDate, required this.endDate});

  @override
  List<Object> get props => [
        startDate,
        endDate,
      ];
}

class LinkedinengagementAndShareAPI extends AnalyticsEvent {
  final String startDate;
  final String endDate;
  const LinkedinengagementAndShareAPI({required this.startDate, required this.endDate});

  @override
  List<Object> get props => [startDate, endDate];
}

// Instagram Analytics

class InstagramLineGraphAPI extends AnalyticsEvent {
  final String startDate;
  final String endDate;
  const InstagramLineGraphAPI({required this.startDate, required this.endDate});

  @override
  List<Object> get props => [startDate, endDate];
}

class InstagramPieChartAPI extends AnalyticsEvent {
  final String startDate;
  final String endDate;
  const InstagramPieChartAPI({required this.startDate, required this.endDate});

  @override
  List<Object> get props => [startDate, endDate];
}

// Facebook analytics
class FacebookImpressionFollowerAPI extends AnalyticsEvent {
  final String startDate;
  final String endDate;
  const FacebookImpressionFollowerAPI({required this.startDate, required this.endDate});
  @override
  List<Object> get props => [startDate, endDate];
}

class FacebookPostLikeCommentShareAPI extends AnalyticsEvent {
  final String startDate;
  final String endDate;
  const FacebookPostLikeCommentShareAPI({required this.startDate, required this.endDate});
  @override
  List<Object> get props => [startDate, endDate];
}

// Youtube Analytics
class YoutubeVideoGraphAPI extends AnalyticsEvent {
  final String startDate;
  final String endDate;
  const YoutubeVideoGraphAPI({required this.startDate, required this.endDate});
  @override
  List<Object> get props => [startDate, endDate];
}

class YoutubeSubscribeLikeCommentGraphAPI extends AnalyticsEvent {
  final String startDate;
  final String endDate;
  const YoutubeSubscribeLikeCommentGraphAPI({required this.startDate, required this.endDate});
  @override
  List<Object> get props => [startDate, endDate];
}

// Thread Analytics
class ThreadLineGraphAPI extends AnalyticsEvent {
  final String startDate;
  final String endDate;
  const ThreadLineGraphAPI({required this.startDate, required this.endDate});
  @override
  List<Object> get props => [startDate, endDate];
}

class ThreadPieChartAPI extends AnalyticsEvent {
  final int chartType;

  const ThreadPieChartAPI({required this.chartType});
  @override
  List<Object> get props => [chartType];
}

class PinterestLineGraphAPI extends AnalyticsEvent {
  final String startDate;
  final String endDate;
  const PinterestLineGraphAPI({required this.startDate, required this.endDate});
  @override
  List<Object> get props => [startDate, endDate];
}
