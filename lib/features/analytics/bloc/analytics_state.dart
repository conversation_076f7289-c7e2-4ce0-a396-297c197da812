part of 'analytics_bloc.dart';

class AnalyticsState extends Equatable {
  // Linkedin Analytics State
  final bool isFollowerImpressionsPostLoading;
  final FollowerImpretionPostModel? followerImpressionsPostModel;
  final bool isLinkedinPiechartLoading;
  final LinkedinPieChartModel? linkedinPiechartModel;
  final bool isLinkedinEngagementLoading;
  final LinkedinEngagementAndShareModel? linkedinEngagementModel;

  // Instagram Analytics
  final bool isInstagramLineGraphLoading;
  final InstagramLineGraph? instagramLineGraph;
  final bool isInstagramPieChartLoading;
  final InstagramPieChartModel? instagramPieChart;
  // Facebook analytics
  final bool isFacebookImpretionAndFollower;
  final FacebookImpretionAndFollowerModel? facebookImpretionAndFollowerModel;
  final bool isFacebookPostLikeCommentShareLoading;
  final FacebookPostGraphModel? facebookPostGraphModel;
  // Youtube analytics
  final bool isYoutubeVideoGraphLoading;
  final YoutubeVideoGraphModel? youtubeVideoGraphModel;
  final bool isYoutubeSubscribeLikeCommentLoading;
  final YoutubeSubscribeLikeCommentModel? youtubeSubscribeLikeCommentModel;
  // Thread analytics
  final bool isThreadlinegraphLoading;
  final ThreadLineGraphModel? threadLineGraphModel;
  final bool isThreadPieChartLoading;
  final ThreadCountryModel? threadCountryModel;
  // Pinterest analytics
  final bool isPinterestLineGraphLoading;
  final PinterestAnalyticsModel? pinterestAnalyticsModel;
  final PnterestTotalCountModel? pinterestTotalCountModel;
  // Get Analytics Platform
  final bool isGetAnalyticsPlatformLoading;
  final GetAnalyticsPlatformsModel? getAnalyticsPlatformsModel;

  const AnalyticsState({
    // Linkedin Analytics
    this.isFollowerImpressionsPostLoading = false,
    this.followerImpressionsPostModel,
    this.isLinkedinPiechartLoading = false,
    this.linkedinPiechartModel,
    this.isLinkedinEngagementLoading = false,
    this.linkedinEngagementModel,
    // Instagram Analytics
    this.isInstagramLineGraphLoading = false,
    this.instagramLineGraph,
    this.isInstagramPieChartLoading = false,
    this.instagramPieChart,
    // Facebook Analytics
    this.isFacebookImpretionAndFollower = false,
    this.facebookImpretionAndFollowerModel,
    this.isFacebookPostLikeCommentShareLoading = false,
    this.facebookPostGraphModel,
    // Youtube Analytics
    this.isYoutubeVideoGraphLoading = false,
    this.youtubeVideoGraphModel,
    this.isYoutubeSubscribeLikeCommentLoading = false,
    this.youtubeSubscribeLikeCommentModel,
    this.isThreadlinegraphLoading = false,
    this.threadLineGraphModel,
    this.isThreadPieChartLoading = false,
    this.threadCountryModel,
    this.isPinterestLineGraphLoading = false,
    this.pinterestAnalyticsModel,
    this.pinterestTotalCountModel,
    this.isGetAnalyticsPlatformLoading = false,
    this.getAnalyticsPlatformsModel,
  });

  @override
  List<Object?> get props => [
        isFollowerImpressionsPostLoading,
        followerImpressionsPostModel,
        isLinkedinPiechartLoading,
        linkedinPiechartModel,
        isLinkedinEngagementLoading,
        linkedinEngagementModel,
        isInstagramLineGraphLoading,
        instagramLineGraph,
        isInstagramPieChartLoading,
        instagramPieChart,
        isFacebookImpretionAndFollower,
        facebookImpretionAndFollowerModel,
        isFacebookPostLikeCommentShareLoading,
        facebookPostGraphModel,
        isYoutubeVideoGraphLoading,
        youtubeVideoGraphModel,
        isYoutubeSubscribeLikeCommentLoading,
        youtubeSubscribeLikeCommentModel,
        isThreadlinegraphLoading,
        threadLineGraphModel,
        isThreadPieChartLoading,
        threadCountryModel,
        isPinterestLineGraphLoading,
        pinterestAnalyticsModel,
        pinterestTotalCountModel,
        isGetAnalyticsPlatformLoading,
        getAnalyticsPlatformsModel,
      ];
  AnalyticsState copyWith({
    // Linkedin Analytics
    bool? isFollowerImpressionsPostLoading,
    FollowerImpretionPostModel? followerImpressionsPostModel,
    bool? isLinkedinPiechartLoading,
    LinkedinPieChartModel? linkedinPiechartModel,
    bool? isLinkedinEngagementLoading,
    LinkedinEngagementAndShareModel? linkedinEngagementModel,
    // Instagram Analytics
    bool? isInstagramLineGraphLoading,
    InstagramLineGraph? instagramLineGraph,
    bool? isInstagramPieChartLoading,
    InstagramPieChartModel? instagramPieChart,
    // Facebook Analytics
    bool? isFacebookImpretionAndFollower,
    FacebookImpretionAndFollowerModel? facebookImpretionAndFollowerModel,
    bool? isFacebookPostLikeCommentShareLoading,
    FacebookPostGraphModel? facebookPostGraphModel,
    // Youtube Analytics
    bool? isYoutubeVideoGraphLoading,
    YoutubeVideoGraphModel? youtubeVideoGraphModel,
    bool? isYoutubeSubscribeLikeCommentLoading,
    YoutubeSubscribeLikeCommentModel? youtubeSubscribeLikeCommentModel,
    ThreadLineGraphModel? threadLineGraphModel,
    bool? isThreadlinegraphLoading,
    bool? isThreadPieChartLoading,
    ThreadCountryModel? threadCountryModel,
    bool? isPinterestLineGraphLoading,
    PinterestAnalyticsModel? pinterestAnalyticsModel,
    PnterestTotalCountModel? pinterestTotalCountModel,
    bool? isGetAnalyticsPlatformLoading,
    GetAnalyticsPlatformsModel? getAnalyticsPlatformsModel,
  }) {
    return AnalyticsState(
      // Linkedin Analytics
      isFollowerImpressionsPostLoading: isFollowerImpressionsPostLoading ?? this.isFollowerImpressionsPostLoading,
      followerImpressionsPostModel: followerImpressionsPostModel ?? this.followerImpressionsPostModel,
      isLinkedinPiechartLoading: isLinkedinPiechartLoading ?? this.isLinkedinPiechartLoading,
      linkedinPiechartModel: linkedinPiechartModel ?? this.linkedinPiechartModel,
      isLinkedinEngagementLoading: isLinkedinEngagementLoading ?? this.isLinkedinEngagementLoading,
      linkedinEngagementModel: linkedinEngagementModel ?? this.linkedinEngagementModel,
      // Instagram Analytics
      isInstagramLineGraphLoading: isInstagramLineGraphLoading ?? this.isInstagramLineGraphLoading,
      instagramLineGraph: instagramLineGraph ?? this.instagramLineGraph,
      isInstagramPieChartLoading: isInstagramPieChartLoading ?? this.isInstagramPieChartLoading,
      instagramPieChart: instagramPieChart ?? this.instagramPieChart,
      // Facebook Analytics
      isFacebookImpretionAndFollower: isFacebookImpretionAndFollower ?? this.isFacebookImpretionAndFollower,
      facebookImpretionAndFollowerModel: facebookImpretionAndFollowerModel ?? this.facebookImpretionAndFollowerModel,
      isFacebookPostLikeCommentShareLoading:
          isFacebookPostLikeCommentShareLoading ?? this.isFacebookImpretionAndFollower,
      facebookPostGraphModel: facebookPostGraphModel ?? this.facebookPostGraphModel,
      // Youtube Analytics
      isYoutubeVideoGraphLoading: isYoutubeVideoGraphLoading ?? this.isYoutubeVideoGraphLoading,
      youtubeVideoGraphModel: youtubeVideoGraphModel ?? this.youtubeVideoGraphModel,
      isYoutubeSubscribeLikeCommentLoading:
          isYoutubeSubscribeLikeCommentLoading ?? this.isYoutubeSubscribeLikeCommentLoading,
      youtubeSubscribeLikeCommentModel: youtubeSubscribeLikeCommentModel ?? this.youtubeSubscribeLikeCommentModel,
      isThreadlinegraphLoading: isThreadlinegraphLoading ?? this.isThreadlinegraphLoading,
      threadLineGraphModel: threadLineGraphModel ?? this.threadLineGraphModel,
      isThreadPieChartLoading: isThreadPieChartLoading ?? this.isThreadPieChartLoading,
      threadCountryModel: threadCountryModel ?? this.threadCountryModel,
      isPinterestLineGraphLoading: isPinterestLineGraphLoading ?? this.isPinterestLineGraphLoading,
      pinterestAnalyticsModel: pinterestAnalyticsModel ?? this.pinterestAnalyticsModel,
      pinterestTotalCountModel: pinterestTotalCountModel ?? this.pinterestTotalCountModel,
      isGetAnalyticsPlatformLoading: isGetAnalyticsPlatformLoading ?? this.isGetAnalyticsPlatformLoading,
      getAnalyticsPlatformsModel: getAnalyticsPlatformsModel ?? this.getAnalyticsPlatformsModel,
    );
  }
}
