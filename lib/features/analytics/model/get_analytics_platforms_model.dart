GetAnalyticsPlatformsModel deserializeGetAnalyticsPlatformsModel(Map<String, dynamic> json) =>
    GetAnalyticsPlatformsModel.fromJson(json);

// class GetAnalyticsPlatformsModel {
//   final bool status;
//   final String message;
//   final Map<String, bool> data;

//   GetAnalyticsPlatformsModel({
//     required this.status,
//     required this.message,
//     required this.data,
//   });

//   factory GetAnalyticsPlatformsModel.fromJson(Map<String, dynamic> json) {
//     return GetAnalyticsPlatformsModel(
//       status: json['status'] ?? false,
//       message: json['message'] ?? '',
//       data: Map<String, bool>.from(json['data'] ?? {}),
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'status': status,
//       'message': message,
//       'data': data,
//     };
//   }

//   GetAnalyticsPlatformsModel copyWith({
//     bool? status,
//     String? message,
//     Map<String, bool>? data,
//   }) {
//     return GetAnalyticsPlatformsModel(
//       status: status ?? this.status,
//       message: message ?? this.message,
//       data: data ?? this.data,
//     );
//   }
// }

class GetAnalyticsPlatformsModel {
  bool? status;
  String? message;
  Data? data;

  GetAnalyticsPlatformsModel({this.status, this.message, this.data});

  GetAnalyticsPlatformsModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  bool? facebook;
  bool? instagram;
  bool? twitter;
  bool? youTube;
  bool? linkedIn;
  bool? pinterest;
  bool? tiktok;
  bool? threads;

  Data(
      {this.facebook,
      this.instagram,
      this.twitter,
      this.youTube,
      this.linkedIn,
      this.pinterest,
      this.tiktok,
      this.threads});

  Data.fromJson(Map<String, dynamic> json) {
    facebook = json['Facebook'];
    instagram = json['Instagram'];
    twitter = json['Twitter'];
    youTube = json['YouTube'];
    linkedIn = json['LinkedIn'];
    pinterest = json['Pinterest'];
    tiktok = json['tiktok'];
    threads = json['threads'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Facebook'] = facebook;
    data['Instagram'] = instagram;
    data['Twitter'] = twitter;
    data['YouTube'] = youTube;
    data['LinkedIn'] = linkedIn;
    data['Pinterest'] = pinterest;
    data['tiktok'] = tiktok;
    data['threads'] = threads;
    return data;
  }
}
