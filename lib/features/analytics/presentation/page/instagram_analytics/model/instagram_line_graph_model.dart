InstagramLineGraph deserializeInstagramLineGraph(Map<String, dynamic> json) => InstagramLineGraph.from<PERSON><PERSON>(json);

class InstagramLineGraph {
  final bool status;
  final String message;
  final GraphData data;

  InstagramLineGraph({
    required this.status,
    required this.message,
    required this.data,
  });

  factory InstagramLineGraph.fromJson(Map<String, dynamic> json) {
    return InstagramLineGraph(
      status: json['status'],
      message: json['message'],
      data: GraphData.fromJson(json['data']['graph_data']),
    );
  }

  InstagramLineGraph copyWith({
    bool? status,
    String? message,
    GraphData? data,
  }) {
    return InstagramLineGraph(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

class GraphData {
  final int followingCount;
  final int followersCount;
  final int totalPosts;
  final Map<String, int> postCounts;
  final int totalPostsInDateRange;
  final Map<String, int> followersInsights;
  final int totalFollowersGraph;
  final Map<String, int> impressionsCounts;
  final int totalImpressionsInDateRange;
  final Map<String, int> reachCounts;
  final int totalReachInDateRange;
  final Map<String, int> likesCounts;
  final int totalLikesInDateRange;
  final Map<String, int> commentsCounts;
  final int totalCommentsInDateRange;
  final Map<String, int> sharesCounts;
  final int totalSharesInDateRange;
  final Map<String, int> profileVisitsCounts;
  final int totalProfileVisitsInDateRange;
  final Map<String, int> totalInteractionsCounts;
  final int totalTotalInteractionsInDateRange;
  final Map<String, int> profileActivityCounts;
  final int totalProfileActivityInDateRange;
  final Map<String, int> savedCounts;
  final int totalSavedInDateRange;
  final Graph1Metric graph1Metric;
  final Graph3Metric graph3Metric;

  GraphData({
    required this.followingCount,
    required this.followersCount,
    required this.totalPosts,
    required this.postCounts,
    required this.totalPostsInDateRange,
    required this.followersInsights,
    required this.totalFollowersGraph,
    required this.impressionsCounts,
    required this.totalImpressionsInDateRange,
    required this.reachCounts,
    required this.totalReachInDateRange,
    required this.likesCounts,
    required this.totalLikesInDateRange,
    required this.commentsCounts,
    required this.totalCommentsInDateRange,
    required this.sharesCounts,
    required this.totalSharesInDateRange,
    required this.profileVisitsCounts,
    required this.totalProfileVisitsInDateRange,
    required this.totalInteractionsCounts,
    required this.totalTotalInteractionsInDateRange,
    required this.profileActivityCounts,
    required this.totalProfileActivityInDateRange,
    required this.savedCounts,
    required this.totalSavedInDateRange,
    required this.graph1Metric,
    required this.graph3Metric,
  });

  factory GraphData.fromJson(Map<String, dynamic> json) {
    return GraphData(
      followingCount: json['following_count'],
      followersCount: json['followers_count'],
      totalPosts: json['total_posts'],
      postCounts: Map<String, int>.from(json['post_counts']),
      totalPostsInDateRange: json['total_posts_in_date_range'],
      followersInsights: Map<String, int>.from(json['followers_insights']),
      totalFollowersGraph: json['total_followers_graph'],
      impressionsCounts: Map<String, int>.from(json['impressions_counts']),
      totalImpressionsInDateRange: json['total_impressions_in_date_range'],
      reachCounts: Map<String, int>.from(json['reach_counts']),
      totalReachInDateRange: json['total_reach_in_date_range'],
      likesCounts: Map<String, int>.from(json['likes_counts']),
      totalLikesInDateRange: json['total_likes_in_date_range'],
      commentsCounts: Map<String, int>.from(json['comments_counts']),
      totalCommentsInDateRange: json['total_comments_in_date_range'],
      sharesCounts: Map<String, int>.from(json['shares_counts']),
      totalSharesInDateRange: json['total_shares_in_date_range'],
      profileVisitsCounts: Map<String, int>.from(json['profile_visits_counts']),
      totalProfileVisitsInDateRange: json['total_profile_visits_in_date_range'],
      totalInteractionsCounts: Map<String, int>.from(json['total_interactions_counts']),
      totalTotalInteractionsInDateRange: json['total_total_interactions_in_date_range'],
      profileActivityCounts: Map<String, int>.from(json['profile_activity_counts']),
      totalProfileActivityInDateRange: json['total_profile_activity_in_date_range'],
      savedCounts: Map<String, int>.from(json['saved_counts']),
      totalSavedInDateRange: json['total_saved_in_date_range'],
      graph1Metric: Graph1Metric.fromJson(json['graph_1_metric']),
      graph3Metric: Graph3Metric.fromJson(json['graph_3_metric']),
    );
  }

  GraphData copyWith({
    int? followingCount,
    int? followersCount,
    int? totalPosts,
    Map<String, int>? postCounts,
    int? totalPostsInDateRange,
    Map<String, int>? followersInsights,
    int? totalFollowersGraph,
    Map<String, int>? impressionsCounts,
    int? totalImpressionsInDateRange,
    Map<String, int>? reachCounts,
    int? totalReachInDateRange,
    Map<String, int>? likesCounts,
    int? totalLikesInDateRange,
    Map<String, int>? commentsCounts,
    int? totalCommentsInDateRange,
    Map<String, int>? sharesCounts,
    int? totalSharesInDateRange,
    Map<String, int>? profileVisitsCounts,
    int? totalProfileVisitsInDateRange,
    Map<String, int>? totalInteractionsCounts,
    int? totalTotalInteractionsInDateRange,
    Map<String, int>? profileActivityCounts,
    int? totalProfileActivityInDateRange,
    Map<String, int>? savedCounts,
    int? totalSavedInDateRange,
    Graph1Metric? graph1Metric,
    Graph3Metric? graph3Metric,
  }) {
    return GraphData(
      followingCount: followingCount ?? this.followingCount,
      followersCount: followersCount ?? this.followersCount,
      totalPosts: totalPosts ?? this.totalPosts,
      postCounts: postCounts ?? this.postCounts,
      totalPostsInDateRange: totalPostsInDateRange ?? this.totalPostsInDateRange,
      followersInsights: followersInsights ?? this.followersInsights,
      totalFollowersGraph: totalFollowersGraph ?? this.totalFollowersGraph,
      impressionsCounts: impressionsCounts ?? this.impressionsCounts,
      totalImpressionsInDateRange: totalImpressionsInDateRange ?? this.totalImpressionsInDateRange,
      reachCounts: reachCounts ?? this.reachCounts,
      totalReachInDateRange: totalReachInDateRange ?? this.totalReachInDateRange,
      likesCounts: likesCounts ?? this.likesCounts,
      totalLikesInDateRange: totalLikesInDateRange ?? this.totalLikesInDateRange,
      commentsCounts: commentsCounts ?? this.commentsCounts,
      totalCommentsInDateRange: totalCommentsInDateRange ?? this.totalCommentsInDateRange,
      sharesCounts: sharesCounts ?? this.sharesCounts,
      totalSharesInDateRange: totalSharesInDateRange ?? this.totalSharesInDateRange,
      profileVisitsCounts: profileVisitsCounts ?? this.profileVisitsCounts,
      totalProfileVisitsInDateRange: totalProfileVisitsInDateRange ?? this.totalProfileVisitsInDateRange,
      totalInteractionsCounts: totalInteractionsCounts ?? this.totalInteractionsCounts,
      totalTotalInteractionsInDateRange: totalTotalInteractionsInDateRange ?? this.totalTotalInteractionsInDateRange,
      profileActivityCounts: profileActivityCounts ?? this.profileActivityCounts,
      totalProfileActivityInDateRange: totalProfileActivityInDateRange ?? this.totalProfileActivityInDateRange,
      savedCounts: savedCounts ?? this.savedCounts,
      totalSavedInDateRange: totalSavedInDateRange ?? this.totalSavedInDateRange,
      graph1Metric: graph1Metric ?? this.graph1Metric,
      graph3Metric: graph3Metric ?? this.graph3Metric,
    );
  }
}

class Graph1Metric {
  final int followersInRange;
  final double dailyFollowers;
  final double followersPerPost;
  final double dailyPosts;
  final double postsPerWeek;

  Graph1Metric({
    required this.followersInRange,
    required this.dailyFollowers,
    required this.followersPerPost,
    required this.dailyPosts,
    required this.postsPerWeek,
  });

  factory Graph1Metric.fromJson(Map<String, dynamic> json) {
    return Graph1Metric(
      followersInRange: json['followers_in_range'],
      dailyFollowers: (json['daily_followers'] ?? 0).toDouble(),
      followersPerPost: (json['followers_per_post'] ?? 0).toDouble(),
      dailyPosts: (json['daily_posts'] ?? 0).toDouble(),
      postsPerWeek: (json['posts_per_week'] ?? 0).toDouble(),
    );
  }

  Graph1Metric copyWith({
    int? followersInRange,
    double? dailyFollowers,
    double? followersPerPost,
    double? dailyPosts,
    double? postsPerWeek,
  }) {
    return Graph1Metric(
      followersInRange: followersInRange ?? this.followersInRange,
      dailyFollowers: dailyFollowers ?? this.dailyFollowers,
      followersPerPost: followersPerPost ?? this.followersPerPost,
      dailyPosts: dailyPosts ?? this.dailyPosts,
      postsPerWeek: postsPerWeek ?? this.postsPerWeek,
    );
  }
}

class Graph3Metric {
  final double dailyLikes;
  final double likesPerPost;
  final double commentsPerPost;
  final double dailyComments;

  Graph3Metric({
    required this.dailyLikes,
    required this.likesPerPost,
    required this.commentsPerPost,
    required this.dailyComments,
  });

  factory Graph3Metric.fromJson(Map<String, dynamic> json) {
    return Graph3Metric(
      dailyLikes: (json['daily_likes'] ?? 0).toDouble(),
      likesPerPost: (json['likes_per_post'] ?? 0).toDouble(),
      commentsPerPost: (json['comments_per_post'] ?? 0).toDouble(),
      dailyComments: (json['daily_comments '] ?? 0).toDouble(), // note the space at the end!
    );
  }

  Graph3Metric copyWith({
    double? dailyLikes,
    double? likesPerPost,
    double? commentsPerPost,
    double? dailyComments,
  }) {
    return Graph3Metric(
      dailyLikes: dailyLikes ?? this.dailyLikes,
      likesPerPost: likesPerPost ?? this.likesPerPost,
      commentsPerPost: commentsPerPost ?? this.commentsPerPost,
      dailyComments: dailyComments ?? this.dailyComments,
    );
  }
}
