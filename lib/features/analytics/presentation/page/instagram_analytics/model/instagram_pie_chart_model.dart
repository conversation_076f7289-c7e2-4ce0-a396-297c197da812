InstagramPieChartModel deserializeInstagramPieChartModel(Map<String, dynamic> json) =>
    InstagramPieChartModel.fromJson(json);

class InstagramPieChartModel {
  final bool status;
  final String message;
  final InstagramPieChartData data;

  InstagramPieChartModel({
    required this.status,
    required this.message,
    required this.data,
  });

  factory InstagramPieChartModel.fromJson(Map<String, dynamic> json) {
    return InstagramPieChartModel(
      status: json['status'],
      message: json['message'],
      data: InstagramPieChartData.fromJson(json['data']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data.toJson(),
    };
  }

  InstagramPieChartModel copyWith({
    bool? status,
    String? message,
    InstagramPieChartData? data,
  }) {
    return InstagramPieChartModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

class InstagramPieChartData {
  final List<InstagramPieChartItem> age;
  final List<InstagramPieChartItem> city;
  final List<InstagramPieChartItem> country;
  final List<InstagramPieChartItem> gender;

  InstagramPieChartData({
    required this.age,
    required this.city,
    required this.country,
    required this.gender,
  });

  factory InstagramPieChartData.fromJson(Map<String, dynamic> json) {
    return InstagramPieChartData(
      age: List<InstagramPieChartItem>.from(json['age'].map((x) => InstagramPieChartItem.fromJson(x))),
      city: List<InstagramPieChartItem>.from(json['city'].map((x) => InstagramPieChartItem.fromJson(x))),
      country: List<InstagramPieChartItem>.from(json['country'].map((x) => InstagramPieChartItem.fromJson(x))),
      gender: List<InstagramPieChartItem>.from(json['gender'].map((x) => InstagramPieChartItem.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'age': age.map((x) => x.toJson()).toList(),
      'city': city.map((x) => x.toJson()).toList(),
      'country': country.map((x) => x.toJson()).toList(),
      'gender': gender.map((x) => x.toJson()).toList(),
    };
  }

  InstagramPieChartData copyWith({
    List<InstagramPieChartItem>? age,
    List<InstagramPieChartItem>? city,
    List<InstagramPieChartItem>? country,
    List<InstagramPieChartItem>? gender,
  }) {
    return InstagramPieChartData(
      age: age ?? this.age,
      city: city ?? this.city,
      country: country ?? this.country,
      gender: gender ?? this.gender,
    );
  }
}

class InstagramPieChartItem {
  final String name;
  final double persanatge;

  InstagramPieChartItem({
    required this.name,
    required this.persanatge,
  });

  factory InstagramPieChartItem.fromJson(Map<String, dynamic> json) {
    return InstagramPieChartItem(
      name: json['name'],
      persanatge: (json['persanatge'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'persanatge': persanatge,
    };
  }

  InstagramPieChartItem copyWith({
    String? name,
    double? persanatge,
  }) {
    return InstagramPieChartItem(
      name: name ?? this.name,
      persanatge: persanatge ?? this.persanatge,
    );
  }
}
