ThreadLineGraphModel deserializeThreadLineGraphModel(Map<String, dynamic> json) => ThreadLineGraphModel.fromJson(json);

class ThreadLineGraphModel {
  final bool status;
  final ThreadLineGraphData data;

  ThreadLineGraphModel({
    required this.status,
    required this.data,
  });

  factory ThreadLineGraphModel.fromJson(Map<String, dynamic> json) {
    return ThreadLineGraphModel(
      status: json['status'],
      data: ThreadLineGraphData.fromJson(json['data']),
    );
  }

  Map<String, dynamic> toJson() => {
        'status': status,
        'data': data.toJson(),
      };

  ThreadLineGraphModel copyWith({
    bool? status,
    ThreadLineGraphData? data,
  }) {
    return ThreadLineGraphModel(
      status: status ?? this.status,
      data: data ?? this.data,
    );
  }
}

class ThreadLineGraphData {
  final List<ViewEntry> views;
  final int totalViews;

  ThreadLineGraphData({
    required this.views,
    required this.totalViews,
  });

  factory ThreadLineGraphData.fromJson(Map<String, dynamic> json) {
    return ThreadLineGraphData(
      views: (json['views'] as List).map((e) => ViewEntry.fromJson(e)).toList(),
      totalViews: json['total_views'],
    );
  }

  Map<String, dynamic> toJson() => {
        'views': views.map((e) => e.toJson()).toList(),
        'total_views': totalViews,
      };

  ThreadLineGraphData copyWith({
    List<ViewEntry>? views,
    int? totalViews,
  }) {
    return ThreadLineGraphData(
      views: views ?? this.views,
      totalViews: totalViews ?? this.totalViews,
    );
  }
}

class ViewEntry {
  final String date;
  final int value;

  ViewEntry({
    required this.date,
    required this.value,
  });

  factory ViewEntry.fromJson(Map<String, dynamic> json) {
    return ViewEntry(
      date: json['date'],
      value: json['value'],
    );
  }

  Map<String, dynamic> toJson() => {
        'date': date,
        'value': value,
      };

  ViewEntry copyWith({
    String? date,
    int? value,
  }) {
    return ViewEntry(
      date: date ?? this.date,
      value: value ?? this.value,
    );
  }
}
