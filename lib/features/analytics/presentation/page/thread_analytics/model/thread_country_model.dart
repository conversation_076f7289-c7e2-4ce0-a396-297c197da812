ThreadCountryModel deserializeThreadCountryModel(Map<String, dynamic> json) => ThreadCountryModel.fromJson(json);

class ThreadCountryModel {
  final bool status;
  final List<dynamic> data;

  ThreadCountryModel({
    required this.status,
    required this.data,
  });

  factory ThreadCountryModel.fromJson(Map<String, dynamic> json) {
    return ThreadCountryModel(
      status: json['status'] ?? false,
      data: (json['data'] as List).map((item) {
        if (item is bool) return item;
        if (item is Map<String, dynamic>) {
          return GenericMapData.fromJson(item);
        }
        return item;
      }).toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'data': data.map((item) {
        if (item is bool) return item;
        if (item is GenericMapData) return item.toJson();
        return item;
      }).toList(),
    };
  }
}

class GenericMapData {
  final Map<String, int> map;

  GenericMapData({required this.map});

  factory GenericMapData.fromJson(Map<String, dynamic> json) {
    final firstKey = json.keys.first;
    final rawMap = Map<String, dynamic>.from(json[firstKey]);

    final intMap = rawMap.map((key, value) {
      return MapEntry(key, int.tryParse(value.toString()) ?? 0);
    });

    return GenericMapData(map: intMap);
  }

  Map<String, dynamic> toJson() {
    return {
      'map': map,
    };
  }
}
