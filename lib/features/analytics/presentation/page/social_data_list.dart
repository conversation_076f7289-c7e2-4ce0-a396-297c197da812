import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/analytics/bloc/analytics_bloc.dart';
import 'package:flowkar/features/analytics/presentation/widget/line_chart/analytics_charts.dart';
import 'package:flowkar/features/analytics/presentation/page/linkedin_analytics/page/analytics_bar_chart.dart';
import 'package:intl/intl.dart';

class SocialDataList extends StatelessWidget {
  final String? socialName;
  const SocialDataList({super.key, this.socialName});

  static Widget builder(BuildContext context) {
    return const SocialDataList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: _buildPlatformContent(context),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          socialName ?? "",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w700,
                fontSize: 18.sp,
              ),
        ),
      ],
    );
  }

  Widget _buildPlatformContent(BuildContext context) {
    final items = getPlatformItems();

    return items.isEmpty
        ? _buildComingSoon()
        : ListView.separated(
            padding: EdgeInsets.all(16.w),
            itemCount: items.length,
            separatorBuilder: (_, __) => buildSizedBoxH(20),
            itemBuilder: (context, sectionIndex) {
              final section = items[sectionIndex];
              return _buildDataContainer(context, section, sectionIndex);
            },
          );
  }

  Widget _buildComingSoon() {
    return Center(
      child: ExceptionWidget(
        imagePath: Assets.images.pngs.exception.pngComingSoon.path,
        showButton: false,
        title: 'Exciting things are on the way!',
        subtitle: """Stay tuned for updates.""",
      ),
    );
  }

  Widget _buildDataContainer(BuildContext context, List<String> items, int sectionIndex) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 11.0.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.white,
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            offset: Offset(0, 2),
            blurRadius: 14.r,
            spreadRadius: 0,
            color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
          ),
        ],
      ),
      child: Column(
        children: items.asMap().entries.map((entry) {
          final itemIndex = entry.key;
          final item = entry.value;
          return Column(
            children: [
              _buildDataItem(context, item, sectionIndex, itemIndex),
              if (item != items.last) _buildDivider(context),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildDataItem(BuildContext context, String text, int sectionIndex, int itemIndex) {
    return InkWell(
      onTap: () {
        handleItemTap(context, socialName ?? '', sectionIndex, itemIndex, text);
      },
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 16.0.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              text,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w400,
                    fontSize: 16.sp,
                    color: Theme.of(context).customColors.black,
                  ),
            ),
            CustomImageView(
              imagePath: Assets.images.svg.setting.svgRightArrow.path,
              margin: EdgeInsets.symmetric(horizontal: 14.0.w),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Divider(
      color: Theme.of(context).customColors.dividerColor.withOpacity(0.1),
      height: 1,
    );
  }

  List<List<String>> getPlatformItems() {
    final platformData = {
      Lang.current.lbl_linkedIn: _linkedInItems,
      Lang.current.lbl_instagram: _instagramItems,
      Lang.current.lbl_facebook: _facebookItems,
      Lang.current.lbl_youtube: _youTubeItems,
      Lang.current.lbl_pintrest: _pinterestItems,
      Lang.current.lbl_thread: _threadsItems,
    };

    return platformData[socialName] ?? [];
  }

  void handleItemTap(BuildContext context, String platform, int sectionIndex, int itemIndex, String itemName) {
    final now = DateTime.now();
    final thirtyDaysAgo = now.subtract(const Duration(days: 30));
    final formatter = DateFormat('dd-MM-yyyy');
    final endDateFormatted = formatter.format(now);
    final startDateFormatted = formatter.format(thirtyDaysAgo);

    switch (platform) {
      case 'LinkedIn':
        if (itemName == 'Followers' || itemName == 'Impressions') {
          context.read<AnalyticsBloc>().add(
                LinkedinFollowerImpretionsPostAPI(
                  endDate: endDateFormatted,
                  startDate: startDateFormatted,
                ),
              );
          PersistentNavBarNavigator.pushNewScreen(
            context,
            screen: AnalyticsCharts(title: itemName, platform: platform),
          );
        } else if (itemName == 'Posts') {
          context.read<AnalyticsBloc>().add(
                LinkedinFollowerImpretionsPostAPI(
                  endDate: endDateFormatted,
                  startDate: startDateFormatted,
                ),
              );
          PersistentNavBarNavigator.pushNewScreen(context,
              screen: AnalyticsCharts(title: itemName, platform: platform));
        } else if (itemName == 'Demographic Data') {
          context.read<AnalyticsBloc>().add(
                LinkedinPiechartAPI(endDate: endDateFormatted, startDate: startDateFormatted),
              );
          PersistentNavBarNavigator.pushNewScreen(
            context,
            screen: LinkedInPieChartDisplay(title: itemName, platform: platform),
          );
        } else if (itemName == 'Engagement & Share') {
          context.read<AnalyticsBloc>().add(
                LinkedinengagementAndShareAPI(
                  endDate: endDateFormatted,
                  startDate: startDateFormatted,
                ),
              );
          PersistentNavBarNavigator.pushNewScreen(
            context,
            screen: AnalyticsCharts(title: itemName, platform: platform),
          );
        }
        break;

      case 'Instagram':
        final now = DateTime.now();
        final thirtyDaysAgo = now.subtract(const Duration(days: 30));
        final formatter = DateFormat('yyyy-MM-dd');
        final endDateFormatted = formatter.format(now);
        final startDateFormatted = formatter.format(thirtyDaysAgo);
        if (itemName == "Followers & Followings" ||
            itemName == "Impressions" ||
            itemName == "Reach" ||
            itemName == "Profile Visits" ||
            itemName == "Posts") {
          context
              .read<AnalyticsBloc>()
              .add(InstagramLineGraphAPI(startDate: startDateFormatted, endDate: endDateFormatted));
          PersistentNavBarNavigator.pushNewScreen(
            context,
            screen: AnalyticsCharts(title: itemName, platform: platform),
          );
        } else if (itemName == "Demographic Data") {
          context
              .read<AnalyticsBloc>()
              .add(InstagramPieChartAPI(startDate: startDateFormatted, endDate: endDateFormatted));
          PersistentNavBarNavigator.pushNewScreen(
            context,
            screen: LinkedInPieChartDisplay(title: itemName, platform: platform),
          );
        }
        break;

      case 'Facebook':
        final now = DateTime.now();
        final thirtyDaysAgo = now.subtract(const Duration(days: 30));
        final formatter = DateFormat('yyyy-MM-dd');
        final endDateFormatted = formatter.format(now);
        final startDateFormatted = formatter.format(thirtyDaysAgo);
        if (itemName == "Followers" || itemName == 'Impressions') {
          context
              .read<AnalyticsBloc>()
              .add(FacebookImpressionFollowerAPI(startDate: startDateFormatted, endDate: endDateFormatted));
          PersistentNavBarNavigator.pushNewScreen(context,
              screen: AnalyticsCharts(title: itemName, platform: platform));
        } else if (itemName == "Share" ||
            itemName == "Interactions" ||
            itemName == "Post" ||
            itemName == "Like" ||
            itemName == "Comments") {
          context
              .read<AnalyticsBloc>()
              .add(FacebookPostLikeCommentShareAPI(startDate: startDateFormatted, endDate: endDateFormatted));
          PersistentNavBarNavigator.pushNewScreen(context,
              screen: AnalyticsCharts(title: itemName, platform: platform));
        }
        // Add Facebook-specific logic if needed
        break;

      case 'Youtube':
        final now = DateTime.now();
        final thirtyDaysAgo = now.subtract(const Duration(days: 30));
        final formatter = DateFormat('yyyy-MM-dd');
        final endDateFormatted = formatter.format(now);
        final startDateFormatted = formatter.format(thirtyDaysAgo);
        if (itemName == "Videos") {
          context
              .read<AnalyticsBloc>()
              .add(YoutubeVideoGraphAPI(startDate: startDateFormatted, endDate: endDateFormatted));
          PersistentNavBarNavigator.pushNewScreen(context,
              screen: AnalyticsCharts(title: itemName, platform: platform));
        } else if (itemName == "Subscribers" || itemName == 'Views' || itemName == 'Likes, Comments') {
          context
              .read<AnalyticsBloc>()
              .add(YoutubeSubscribeLikeCommentGraphAPI(startDate: startDateFormatted, endDate: endDateFormatted));
          PersistentNavBarNavigator.pushNewScreen(context,
              screen: AnalyticsCharts(title: itemName, platform: platform));
        }
        // Add YouTube-specific logic if needed
        break;

      case 'Pintrest':
        final now = DateTime.now();
        final thirtyDaysAgo = now.subtract(const Duration(days: 30));
        final formatter = DateFormat('yyyy-MM-dd');
        final endDateFormatted = formatter.format(now);
        final startDateFormatted = formatter.format(thirtyDaysAgo);
        // if (itemName == "Pin Click") {
        context
            .read<AnalyticsBloc>()
            .add(PinterestLineGraphAPI(startDate: startDateFormatted, endDate: endDateFormatted));
        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: AnalyticsCharts(title: itemName, platform: platform),
        );
        // }

        break;

      case 'Thread':
        final now = DateTime.now();
        final formatter = DateFormat('yyyy-MM-dd');
        final endDateFormatted = formatter.format(now);
        final startDateFormatted = formatter.format(now.subtract(const Duration(days: 30)));

        void navigateToLineGraph(String itemName) {
          context.read<AnalyticsBloc>().add(
                ThreadLineGraphAPI(startDate: startDateFormatted, endDate: endDateFormatted),
              );
          PersistentNavBarNavigator.pushNewScreen(
            context,
            screen: AnalyticsCharts(title: itemName, platform: platform),
          );
        }

        void navigateToPieChart(String itemName) {
          final chartConfig = {
            "Followers by Country": {"type": 1, "title": "Country Distribution"},
            "Followers by City": {"type": 2, "title": "City Distribution"},
            "Followers by Age": {"type": 3, "title": "Age Distribution"},
            "Followers by Gender": {"type": 4, "title": "Gender Distribution"},
          };

          final config = chartConfig[itemName];
          if (config != null) {
            context.read<AnalyticsBloc>().add(
                  ThreadPieChartAPI(chartType: config['type'] as int),
                );
            PersistentNavBarNavigator.pushNewScreen(
              context,
              screen: LinkedInPieChartDisplay(
                title: itemName,
                platform: platform,
                chartTitle: config['title'] as String,
              ),
            );
          }
        }

        if (itemName == "Views") {
          navigateToLineGraph(itemName);
        } else if (["Followers by Country", "Followers by City", "Followers by Age", "Followers by Gender"]
            .contains(itemName)) {
          navigateToPieChart(itemName);
        }
        break;
      default:
        break;
    }
  }

  // Platform data
  static const List<List<String>> _linkedInItems = [
    ['Followers', 'Impressions', 'Demographic Data'],
    ['Posts', 'Engagement & Share'],
  ];

  static const List<List<String>> _instagramItems = [
    ['Followers & Followings', 'Impressions', 'Reach', 'Profile Visits', 'Demographic Data'],
    ['Posts'],
  ];

  static const List<List<String>> _facebookItems = [
    ['Followers', 'Impressions', 'Share', 'Interactions'],
    ['Post', 'Like', 'Comments']
  ];

  static const List<List<String>> _youTubeItems = [
    ['Subscribers'],
    ['Videos', 'Views'],
    ['Likes, Comments']
  ];

  static const List<List<String>> _pinterestItems = [
    ['Pin Click', 'Impressions', 'Engagement'],
  ];

  static const List<List<String>> _threadsItems = [
    ['Views', 'Followers by Country', 'Followers by City', 'Followers by Age', 'Followers by Gender'],
  ];
}
