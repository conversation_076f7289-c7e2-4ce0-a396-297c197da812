import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/analytics/presentation/widget/post_container_widget.dart';
import 'package:flowkar/features/analytics/presentation/widget/view_post_screen_shimmer.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

class ViewPostScreen extends StatelessWidget {
  const ViewPostScreen({super.key, bool? stackonScreen});

  static Widget builder(BuildContext context) {
    return const ViewPostScreen();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildSignUpAppBar(context),
      body: BlocListener<ConnectivityBloc, ConnectivityState>(
        listener: (context, state) {
          if (state.isReconnected) {
            context.read<HomeFeedBloc>().add(GetProfilePostEvent());
          }
        },
        child: <PERSON><PERSON><PERSON>er<HomeFeedBloc, HomeFeedState>(
          builder: (context, state) {
            return BlocBuilder<ConnectivityBloc, ConnectivityState>(
              builder: (context, connectivityState) {
                if (!connectivityState.isConnected && (state.userProfileModel?.data.posts.isEmpty ?? true)) {
                  return ViewPostScreenShimmer();
                } else if (state.isGetPostLoading) {
                  return ViewPostScreenShimmer();
                } else if (state.userProfileModel!.data.posts.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        CustomImageView(
                          imagePath: Assets.images.pngs.pngNoPost.path,
                          height: 200.h,
                        ),
                        buildSizedBoxH(30),
                        Center(
                            child: Text(
                          Lang.of(context).lbl_no_post,
                          style: Theme.of(context)
                              .textTheme
                              .titleLarge
                              ?.copyWith(fontWeight: FontWeight.w700, fontSize: 24.sp),
                        )),
                        buildSizedBoxH(16),
                        Text(
                          Lang.of(context).msg__no_post_massage,
                          style: Theme.of(context)
                              .textTheme
                              .titleLarge
                              ?.copyWith(fontWeight: FontWeight.w400, fontSize: 12.sp),
                        ),
                        buildSizedBoxH(10),
                        Text(
                          Lang.of(context).lbl_share_your_first_post,
                          style: Theme.of(context)
                              .textTheme
                              .titleLarge
                              ?.copyWith(fontWeight: FontWeight.w700, fontSize: 12.sp),
                        ),
                        buildSizedBoxH(30),
                      ],
                    ),
                  );
                } else if (state.userProfileModel?.status == true) {
                  return Column(
                    children: [
                      Flexible(
                        child: SingleChildScrollView(
                          child: AnimationLimiter(
                            child: ListView.builder(
                              padding: EdgeInsets.only(bottom: 80.h),
                              shrinkWrap: true,
                              physics: NeverScrollableScrollPhysics(),
                              itemCount: state.userProfileModel!.data.posts.length,
                              itemBuilder: (context, index) {
                                var post = state.userProfileModel!.data.posts[index];
                                String? description = post.description;
                                Map<String, bool> platformsMap = {
                                  "facebook": post.facebook,
                                  "instagram": post.instagram,
                                  "linkedin": post.linkedin,
                                  "pinterest": post.pinterest,
                                  "vimeo": post.vimeo,
                                  "youtube": post.youtube,
                                  "dailymotion": post.dailymotion,
                                  "reddit": post.reddit,
                                  "tumblr": post.tumblr,
                                  "twitter": post.twitter,
                                };
                                List<String> platforms = platformsMap.entries
                                    .where((entry) => entry.value)
                                    .map((entry) => entry.key)
                                    .toList();

                                return Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 16.0.w),
                                  child: AnimationConfiguration.staggeredList(
                                    position: index,
                                    duration: const Duration(milliseconds: 500),
                                    child: SlideAnimation(
                                      verticalOffset: 50.0,
                                      child: FadeInAnimation(
                                        child: InkWell(
                                          onTap: () {
                                            // context.read<HomeFeedBloc>().add(AnalyticsApiEvent(
                                            //     postId: state.userProfileModel!.data.posts[index].id));
                                            // PersistentNavBarNavigator.pushNewScreen(context,
                                            //     screen: PostAnalyticsScreen(
                                            //       post: state.plannerPostData[index],
                                            //       index: index,
                                            //       // stackonScreen: true,
                                            //     ),
                                            //     withNavBar: false);
                                            // NavigatorService.pushNamed(AppRoutes.postanalyticsscreen, arguments: [state.userProfileModel!.data.posts[index], index]);
                                          },
                                          child: PostContainer(
                                            imageUrl: state.userProfileModel!.data.posts[index].files.isEmpty
                                                ? Assets.images.svg.other.svgNodatafound.path
                                                : state.userProfileModel!.data.posts[index].files[0],
                                            platforms: platforms,
                                            postText: description,
                                            totalComment: state.userProfileModel!.data.posts[index].comments_count,
                                            totallike: state.userProfileModel!.data.posts[index].likes,
                                            totalShare: 0,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      )
                    ],
                  );
                } else {
                  return SizedBox.shrink();
                }
              },
            );
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildSignUpAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: false,
      alignment: Alignment.center,
      title: "Analytics",
      textStyle: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
    );
  }
}
