import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/comment_bottom_sheet_widget.dart';
import 'package:flowkar/features/upload_post/model/user_profile_model.dart';

class PostAnalyticsScreen extends StatelessWidget {
  final Post? post;
  final int? index;
  const PostAnalyticsScreen({super.key, this.post, this.index});
  static Widget builder(BuildContext context) {
    final List args = ModalRoute.of(context)?.settings.arguments as List;
    return PostAnalyticsScreen(
      post: args[0],
      index: args[1],
    );
  }

  @override
  Widget build(BuildContext context) {
    final posts = post;

    return Scaffold(
      appBar: _buildPostAnalyticsAppBar(context),
      body: BlocListener<ConnectivityBloc, ConnectivityState>(
        listener: (context, state) {
          if (state.isReconnected) {
            PersistentNavBarNavigator.pop(context);
          }
        },
        child: Bloc<PERSON>uilder<HomeFeedBloc, HomeFeedState>(
          builder: (context, state) {
            return BlocBuilder<ConnectivityBloc, ConnectivityState>(
              builder: (context, connectivityState) {
                if (!connectivityState.isConnected && state.posts.isEmpty) {
                  return HomeFeedShimmer();
                } else if (state.homeFeedLoading) {
                  return HomeFeedShimmer();
                } else if (state.posts.isEmpty) {
                  return ListView(
                    physics: AlwaysScrollableScrollPhysics(),
                    children: [
                      buildSizedBoxH(MediaQuery.of(context).size.height / 6),
                      ExceptionWidget(
                        imagePath: Assets.images.svg.exception.svgNodatafound.path,
                        showButton: false,
                        title: Lang.of(context).lbl_no_data_found,
                        subtitle: Lang.of(context).lbl_no_post,
                      ),
                    ],
                  );
                } else {
                  return ListView(
                    children: [
                      buildSizedBoxH(8),
                      PostWidget(
                        width: post?.width ?? 0,
                        height: post?.height ?? 0,
                        userByIDpost: false,
                        userByIDvideo: false,
                        userVideo: false,
                        userpost: false,
                        editePost: false,
                        taggedIn: posts?.tagged_in,
                        isanalytics: true,
                        state: state,
                        index: index ?? 0,
                        userId: post?.user.user_id,
                        latestcomments: '',
                        postId: post?.id ?? 0,
                        profileImage: "${post?.user.profile_image}",
                        name: "${post?.user.name}",
                        username: "${post?.user.username}",
                        postMedia: post?.files ?? [],
                        thumbnailImage: [],
                        title: "${post?.title == "''" || post!.title.isEmpty ? '' : post?.title}",
                        caption:
                            "${post?.title == "''" || post!.title.isEmpty ? '' : post?.title}${post!.description.isEmpty ? '' : post?.title == "''" || post!.title.isEmpty ? post?.description : "\n${post?.description}"}",
                        likes: "${post?.likes}",
                        comments: "${post?.comments_count}",
                        postTime: post?.created_at ?? "",
                        isLiked: false,
                        isSaved: false,
                        doubleTap: () {},
                        likeonTap: () {},
                        commentonTap: () {
                          showModalBottomSheet(
                            context: context,
                            useRootNavigator: true,
                            isScrollControlled: true,
                            builder: (context) => CommentsBottomSheet(postId: post?.id ?? 0),
                          );
                          // NavigatorService.pushNamed(AppRoutes.commentBottomSheet, arguments: [post?.id]);
                        },
                        shareonTap: () {},
                        saveonTap: () {},
                      ),
                      buildSizedBoxH(16),
                    ],
                  );
                }
              },
            );
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildPostAnalyticsAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            PersistentNavBarNavigator.pop(context);
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          "Analytics ",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }
}
