PinterestAnalyticsModel deserializePinterestAnalyticsModel(Map<String, dynamic> json) =>
    PinterestAnalyticsModel.fromJson(json);

class PinterestAnalyticsModel {
  final bool status;
  final int impression;
  final int engagement;
  final int pinClick;
  final int outboundClick;
  final int save;
  final int interaction;
  final List<DailyData> dailyData;

  PinterestAnalyticsModel({
    required this.status,
    required this.impression,
    required this.engagement,
    required this.pinClick,
    required this.outboundClick,
    required this.save,
    required this.interaction,
    required this.dailyData,
  });

  factory PinterestAnalyticsModel.fromJson(Map<String, dynamic> json) {
    return PinterestAnalyticsModel(
      status: json['status'],
      impression: json['IMPRESSION'],
      engagement: json['ENGAGEMENT'],
      pinClick: json['PIN_CLICK'],
      outboundClick: json['OUTBOUND_CLICK'],
      save: json['SAVE'],
      interaction: json['INTERACTION'],
      dailyData: (json['daily_data'] as List).map((e) => DailyData.fromJson(e)).toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'IMPRESSION': impression,
      'ENGAGEMENT': engagement,
      'PIN_CLICK': pinClick,
      'OUTBOUND_CLICK': outboundClick,
      'SAVE': save,
      'INTERACTION': interaction,
      'daily_data': dailyData.map((e) => e.toJson()).toList(),
    };
  }

  PinterestAnalyticsModel copyWith({
    bool? status,
    int? impression,
    int? engagement,
    int? pinClick,
    int? outboundClick,
    int? save,
    int? interaction,
    List<DailyData>? dailyData,
  }) {
    return PinterestAnalyticsModel(
      status: status ?? this.status,
      impression: impression ?? this.impression,
      engagement: engagement ?? this.engagement,
      pinClick: pinClick ?? this.pinClick,
      outboundClick: outboundClick ?? this.outboundClick,
      save: save ?? this.save,
      interaction: interaction ?? this.interaction,
      dailyData: dailyData ?? this.dailyData,
    );
  }
}

class DailyData {
  final String date;
  final int impression;
  final int engagement;
  final int pinClick;
  final int outboundClick;
  final int save;
  final int interaction;

  DailyData({
    required this.date,
    required this.impression,
    required this.engagement,
    required this.pinClick,
    required this.outboundClick,
    required this.save,
    required this.interaction,
  });

  factory DailyData.fromJson(Map<String, dynamic> json) {
    return DailyData(
      date: json['date'],
      impression: json['IMPRESSION'],
      engagement: json['ENGAGEMENT'],
      pinClick: json['PIN_CLICK'],
      outboundClick: json['OUTBOUND_CLICK'],
      save: json['SAVE'],
      interaction: json['INTERACTION'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'IMPRESSION': impression,
      'ENGAGEMENT': engagement,
      'PIN_CLICK': pinClick,
      'OUTBOUND_CLICK': outboundClick,
      'SAVE': save,
      'INTERACTION': interaction,
    };
  }

  DailyData copyWith({
    String? date,
    int? impression,
    int? engagement,
    int? pinClick,
    int? outboundClick,
    int? save,
    int? interaction,
  }) {
    return DailyData(
      date: date ?? this.date,
      impression: impression ?? this.impression,
      engagement: engagement ?? this.engagement,
      pinClick: pinClick ?? this.pinClick,
      outboundClick: outboundClick ?? this.outboundClick,
      save: save ?? this.save,
      interaction: interaction ?? this.interaction,
    );
  }
}
