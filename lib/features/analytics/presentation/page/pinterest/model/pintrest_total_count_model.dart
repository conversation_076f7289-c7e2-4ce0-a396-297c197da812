PnterestTotalCountModel deserializePnterestTotalCountModel(Map<String, dynamic> json) =>
    PnterestTotalCountModel.fromJson(json);

class PnterestTotalCountModel {
  final bool? status;
  final String? message;
  final PinterestData? data;

  PnterestTotalCountModel({
    this.status,
    this.message,
    this.data,
  });

  factory PnterestTotalCountModel.fromJson(Map<String, dynamic> json) {
    return PnterestTotalCountModel(
      status: json['status'] as bool?,
      message: json['message'] as String?,
      data: json['data'] != null ? PinterestData.fromJson(json['data']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson(),
    };
  }

  PnterestTotalCountModel copyWith({
    bool? status,
    String? message,
    PinterestData? data,
  }) {
    return PnterestTotalCountModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

class PinterestData {
  final int? followerCount;
  final int? followingCount;
  final int? pinCount;
  final int? monthlyViews;
  final int? boardCount;

  PinterestData({
    this.followerCount,
    this.followingCount,
    this.pinCount,
    this.monthlyViews,
    this.boardCount,
  });

  factory PinterestData.fromJson(Map<String, dynamic> json) {
    return PinterestData(
      followerCount: json['follower_count'] as int?,
      followingCount: json['following_count'] as int?,
      pinCount: json['pin_count'] as int?,
      monthlyViews: json['monthly_views'] as int?,
      boardCount: json['board_count'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'follower_count': followerCount,
      'following_count': followingCount,
      'pin_count': pinCount,
      'monthly_views': monthlyViews,
      'board_count': boardCount,
    };
  }

  PinterestData copyWith({
    int? followerCount,
    int? followingCount,
    int? pinCount,
    int? monthlyViews,
    int? boardCount,
  }) {
    return PinterestData(
      followerCount: followerCount ?? this.followerCount,
      followingCount: followingCount ?? this.followingCount,
      pinCount: pinCount ?? this.pinCount,
      monthlyViews: monthlyViews ?? this.monthlyViews,
      boardCount: boardCount ?? this.boardCount,
    );
  }
}
