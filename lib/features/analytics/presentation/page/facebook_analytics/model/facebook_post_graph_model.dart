FacebookPostGraphModel deserializeFacebookPostGraphModel(Map<String, dynamic> json) =>
    FacebookPostGraphModel.fromJson(json);

class FacebookPostGraphModel {
  final bool status;
  final String message;
  final FacebookPostData data;

  FacebookPostGraphModel({
    required this.status,
    required this.message,
    required this.data,
  });

  factory FacebookPostGraphModel.fromJson(Map<String, dynamic> json) {
    return FacebookPostGraphModel(
      status: json['status'],
      message: json['message'],
      data: FacebookPostData.fromJson(json['data']),
    );
  }

  Map<String, dynamic> toJson() => {
        'status': status,
        'message': message,
        'data': data.toJson(),
      };

  FacebookPostGraphModel copyWith({
    bool? status,
    String? message,
    FacebookPostData? data,
  }) {
    return FacebookPostGraphModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

class FacebookPostData {
  final FacebookPostMetrics metrics;
  final FacebookGraphData graphData;

  FacebookPostData({
    required this.metrics,
    required this.graphData,
  });

  factory FacebookPostData.fromJson(Map<String, dynamic> json) {
    return FacebookPostData(
      metrics: FacebookPostMetrics.fromJson(json['metrics']),
      graphData: FacebookGraphData.fromJson(json['graph_data']),
    );
  }

  Map<String, dynamic> toJson() => {
        'metrics': metrics.toJson(),
        'graph_data': graphData.toJson(),
      };

  FacebookPostData copyWith({
    FacebookPostMetrics? metrics,
    FacebookGraphData? graphData,
  }) {
    return FacebookPostData(
      metrics: metrics ?? this.metrics,
      graphData: graphData ?? this.graphData,
    );
  }
}

class FacebookPostMetrics {
  final double dailyReactions;
  final double reactionPerPost;
  final double dailyComments;
  final double commentsPerPost;
  final double sharesPerDay;
  final double sharesPerPost;

  FacebookPostMetrics({
    required this.dailyReactions,
    required this.reactionPerPost,
    required this.dailyComments,
    required this.commentsPerPost,
    required this.sharesPerDay,
    required this.sharesPerPost,
  });

  factory FacebookPostMetrics.fromJson(Map<String, dynamic> json) {
    return FacebookPostMetrics(
      dailyReactions: (json['daily_reactions'] as num).toDouble(),
      reactionPerPost: (json['reaction_per_post'] as num).toDouble(),
      dailyComments: (json['daily_comments'] as num).toDouble(),
      commentsPerPost: (json['comments_per_post'] as num).toDouble(),
      sharesPerDay: (json['shares_per_day'] as num).toDouble(),
      sharesPerPost: (json['shares_per_post'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() => {
        'daily_reactions': dailyReactions,
        'reaction_per_post': reactionPerPost,
        'daily_comments': dailyComments,
        'comments_per_post': commentsPerPost,
        'shares_per_day': sharesPerDay,
        'shares_per_post': sharesPerPost,
      };

  FacebookPostMetrics copyWith({
    double? dailyReactions,
    double? reactionPerPost,
    double? dailyComments,
    double? commentsPerPost,
    double? sharesPerDay,
    double? sharesPerPost,
  }) {
    return FacebookPostMetrics(
      dailyReactions: dailyReactions ?? this.dailyReactions,
      reactionPerPost: reactionPerPost ?? this.reactionPerPost,
      dailyComments: dailyComments ?? this.dailyComments,
      commentsPerPost: commentsPerPost ?? this.commentsPerPost,
      sharesPerDay: sharesPerDay ?? this.sharesPerDay,
      sharesPerPost: sharesPerPost ?? this.sharesPerPost,
    );
  }
}

class FacebookGraphData {
  final List<FacebookGraphDataItem> data;
  final int totalImpressions;
  final int totalUniqueImpressions;
  final int totalPaidImpressions;
  final int totalPosts;
  final int totalLikes;
  final int totalComments;
  final int totalShares;
  final int totalEngagements;
  final int totalInteractions;

  FacebookGraphData({
    required this.data,
    required this.totalImpressions,
    required this.totalUniqueImpressions,
    required this.totalPaidImpressions,
    required this.totalPosts,
    required this.totalLikes,
    required this.totalComments,
    required this.totalShares,
    required this.totalEngagements,
    required this.totalInteractions,
  });

  factory FacebookGraphData.fromJson(Map<String, dynamic> json) {
    return FacebookGraphData(
      data: (json['data'] as List).map((e) => FacebookGraphDataItem.fromJson(e)).toList(),
      totalImpressions: json['total_impressions'],
      totalUniqueImpressions: json['total_unique_impressions'],
      totalPaidImpressions: json['total_paid_impressions'],
      totalPosts: json['total_posts'],
      totalLikes: json['total_likes'],
      totalComments: json['total_comments'],
      totalShares: json['total_shares'],
      totalEngagements: json['total_engagements'],
      totalInteractions: json['total_interactions'],
    );
  }

  Map<String, dynamic> toJson() => {
        'data': data.map((e) => e.toJson()).toList(),
        'total_impressions': totalImpressions,
        'total_unique_impressions': totalUniqueImpressions,
        'total_paid_impressions': totalPaidImpressions,
        'total_posts': totalPosts,
        'total_likes': totalLikes,
        'total_comments': totalComments,
        'total_shares': totalShares,
        'total_engagements': totalEngagements,
        'total_interactions': totalInteractions,
      };

  FacebookGraphData copyWith({
    List<FacebookGraphDataItem>? data,
    int? totalImpressions,
    int? totalUniqueImpressions,
    int? totalPaidImpressions,
    int? totalPosts,
    int? totalLikes,
    int? totalComments,
    int? totalShares,
    int? totalEngagements,
    int? totalInteractions,
  }) {
    return FacebookGraphData(
      data: data ?? this.data,
      totalImpressions: totalImpressions ?? this.totalImpressions,
      totalUniqueImpressions: totalUniqueImpressions ?? this.totalUniqueImpressions,
      totalPaidImpressions: totalPaidImpressions ?? this.totalPaidImpressions,
      totalPosts: totalPosts ?? this.totalPosts,
      totalLikes: totalLikes ?? this.totalLikes,
      totalComments: totalComments ?? this.totalComments,
      totalShares: totalShares ?? this.totalShares,
      totalEngagements: totalEngagements ?? this.totalEngagements,
      totalInteractions: totalInteractions ?? this.totalInteractions,
    );
  }
}

class FacebookGraphDataItem {
  final String date;
  final int impressions;
  final int uniqueImpressions;
  final int paidImpressions;
  final int engagements;
  final int posts;
  final int likes;
  final int comments;
  final int shares;
  final int interactions;

  FacebookGraphDataItem({
    required this.date,
    required this.impressions,
    required this.uniqueImpressions,
    required this.paidImpressions,
    required this.engagements,
    required this.posts,
    required this.likes,
    required this.comments,
    required this.shares,
    required this.interactions,
  });

  factory FacebookGraphDataItem.fromJson(Map<String, dynamic> json) {
    return FacebookGraphDataItem(
      date: json['date'],
      impressions: json['impressions'],
      uniqueImpressions: json['unique_impressions'],
      paidImpressions: json['paid_impressions'],
      engagements: json['engagements'],
      posts: json['posts'],
      likes: json['likes'],
      comments: json['comments'],
      shares: json['shares'],
      interactions: json['interactions'],
    );
  }

  Map<String, dynamic> toJson() => {
        'date': date,
        'impressions': impressions,
        'unique_impressions': uniqueImpressions,
        'paid_impressions': paidImpressions,
        'engagements': engagements,
        'posts': posts,
        'likes': likes,
        'comments': comments,
        'shares': shares,
        'interactions': interactions,
      };

  FacebookGraphDataItem copyWith({
    String? date,
    int? impressions,
    int? uniqueImpressions,
    int? paidImpressions,
    int? engagements,
    int? posts,
    int? likes,
    int? comments,
    int? shares,
    int? interactions,
  }) {
    return FacebookGraphDataItem(
      date: date ?? this.date,
      impressions: impressions ?? this.impressions,
      uniqueImpressions: uniqueImpressions ?? this.uniqueImpressions,
      paidImpressions: paidImpressions ?? this.paidImpressions,
      engagements: engagements ?? this.engagements,
      posts: posts ?? this.posts,
      likes: likes ?? this.likes,
      comments: comments ?? this.comments,
      shares: shares ?? this.shares,
      interactions: interactions ?? this.interactions,
    );
  }
}
