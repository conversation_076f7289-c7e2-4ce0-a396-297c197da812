YoutubeSubscribeLikeCommentModel deserializeYoutubeSubscribeLikeCommentModel(Map<String, dynamic> json) =>
    YoutubeSubscribeLikeCommentModel.fromJson(json);

class YoutubeSubscribeLikeCommentModel {
  final bool status;
  final String message;
  final YoutubeSubscribeLikeCommentData data;
  final int subscriberCount;

  YoutubeSubscribeLikeCommentModel({
    required this.status,
    required this.message,
    required this.data,
    required this.subscriberCount,
  });

  factory YoutubeSubscribeLikeCommentModel.fromJson(Map<String, dynamic> json) {
    return YoutubeSubscribeLikeCommentModel(
      status: json['status'],
      message: json['message'],
      data: YoutubeSubscribeLikeCommentData.fromJson(json['data']),
      subscriberCount: json['subscriber_count'],
    );
  }

  Map<String, dynamic> toJson() => {
        'status': status,
        'message': message,
        'data': data.toJson(),
        'subscriber_count': subscriberCount,
      };

  YoutubeSubscribeLikeCommentModel copyWith({
    bool? status,
    String? message,
    YoutubeSubscribeLikeCommentData? data,
    int? subscriberCount,
  }) {
    return YoutubeSubscribeLikeCommentModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
      subscriberCount: subscriberCount ?? this.subscriberCount,
    );
  }
}

class YoutubeSubscribeLikeCommentData {
  final List<Analytics> analytics;
  final Total total;

  YoutubeSubscribeLikeCommentData({
    required this.analytics,
    required this.total,
  });

  factory YoutubeSubscribeLikeCommentData.fromJson(Map<String, dynamic> json) {
    return YoutubeSubscribeLikeCommentData(
      analytics: List<Analytics>.from(json['analytics'].map((x) => Analytics.fromJson(x))),
      total: Total.fromJson(json['total']),
    );
  }

  Map<String, dynamic> toJson() => {
        'analytics': analytics.map((x) => x.toJson()).toList(),
        'total': total.toJson(),
      };

  YoutubeSubscribeLikeCommentData copyWith({
    List<Analytics>? analytics,
    Total? total,
  }) {
    return YoutubeSubscribeLikeCommentData(
      analytics: analytics ?? this.analytics,
      total: total ?? this.total,
    );
  }
}

class Analytics {
  final String date;
  final int views;
  final int comments;
  final int likes;
  final int dislikes;
  final int estimatedMinutesWatched;
  final int averageViewDuration;
  final int subscribersGained;
  final int subscribersLost;
  final int shares;

  Analytics({
    required this.date,
    required this.views,
    required this.comments,
    required this.likes,
    required this.dislikes,
    required this.estimatedMinutesWatched,
    required this.averageViewDuration,
    required this.subscribersGained,
    required this.subscribersLost,
    required this.shares,
  });

  factory Analytics.fromJson(Map<String, dynamic> json) {
    return Analytics(
      date: json['date'],
      views: json['views'],
      comments: json['comments'],
      likes: json['likes'],
      dislikes: json['dislikes'],
      estimatedMinutesWatched: json['estimatedMinutesWatched'],
      averageViewDuration: json['averageViewDuration'],
      subscribersGained: json['subscribersGained'],
      subscribersLost: json['subscribersLost'],
      shares: json['shares'],
    );
  }

  Map<String, dynamic> toJson() => {
        'date': date,
        'views': views,
        'comments': comments,
        'likes': likes,
        'dislikes': dislikes,
        'estimatedMinutesWatched': estimatedMinutesWatched,
        'averageViewDuration': averageViewDuration,
        'subscribersGained': subscribersGained,
        'subscribersLost': subscribersLost,
        'shares': shares,
      };

  Analytics copyWith({
    String? date,
    int? views,
    int? comments,
    int? likes,
    int? dislikes,
    int? estimatedMinutesWatched,
    int? averageViewDuration,
    int? subscribersGained,
    int? subscribersLost,
    int? shares,
  }) {
    return Analytics(
      date: date ?? this.date,
      views: views ?? this.views,
      comments: comments ?? this.comments,
      likes: likes ?? this.likes,
      dislikes: dislikes ?? this.dislikes,
      estimatedMinutesWatched: estimatedMinutesWatched ?? this.estimatedMinutesWatched,
      averageViewDuration: averageViewDuration ?? this.averageViewDuration,
      subscribersGained: subscribersGained ?? this.subscribersGained,
      subscribersLost: subscribersLost ?? this.subscribersLost,
      shares: shares ?? this.shares,
    );
  }
}

class Total {
  final int views;
  final int comments;
  final int likes;
  final int dislikes;
  final int estimatedMinutesWatched;
  final int averageViewDuration;
  final int subscribersGained;
  final int subscribersLost;
  final int shares;

  Total({
    required this.views,
    required this.comments,
    required this.likes,
    required this.dislikes,
    required this.estimatedMinutesWatched,
    required this.averageViewDuration,
    required this.subscribersGained,
    required this.subscribersLost,
    required this.shares,
  });

  factory Total.fromJson(Map<String, dynamic> json) {
    return Total(
      views: json['views'],
      comments: json['comments'],
      likes: json['likes'],
      dislikes: json['dislikes'],
      estimatedMinutesWatched: json['estimatedMinutesWatched'],
      averageViewDuration: json['averageViewDuration'],
      subscribersGained: json['subscribersGained'],
      subscribersLost: json['subscribersLost'],
      shares: json['shares'],
    );
  }

  Map<String, dynamic> toJson() => {
        'views': views,
        'comments': comments,
        'likes': likes,
        'dislikes': dislikes,
        'estimatedMinutesWatched': estimatedMinutesWatched,
        'averageViewDuration': averageViewDuration,
        'subscribersGained': subscribersGained,
        'subscribersLost': subscribersLost,
        'shares': shares,
      };

  Total copyWith({
    int? views,
    int? comments,
    int? likes,
    int? dislikes,
    int? estimatedMinutesWatched,
    int? averageViewDuration,
    int? subscribersGained,
    int? subscribersLost,
    int? shares,
  }) {
    return Total(
      views: views ?? this.views,
      comments: comments ?? this.comments,
      likes: likes ?? this.likes,
      dislikes: dislikes ?? this.dislikes,
      estimatedMinutesWatched: estimatedMinutesWatched ?? this.estimatedMinutesWatched,
      averageViewDuration: averageViewDuration ?? this.averageViewDuration,
      subscribersGained: subscribersGained ?? this.subscribersGained,
      subscribersLost: subscribersLost ?? this.subscribersLost,
      shares: shares ?? this.shares,
    );
  }
}
