YoutubeVideoGraphModel deserializeYoutubeVideoGraphModel(Map<String, dynamic> json) =>
    YoutubeVideoGraphModel.fromJson(json);

class YoutubeVideoGraphModel {
  final bool status;
  final String message;
  final YoutubeVideoGraphData data;

  YoutubeVideoGraphModel({
    required this.status,
    required this.message,
    required this.data,
  });

  YoutubeVideoGraphModel copyWith({
    bool? status,
    String? message,
    YoutubeVideoGraphData? data,
  }) {
    return YoutubeVideoGraphModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  factory YoutubeVideoGraphModel.fromJson(Map<String, dynamic> json) {
    return YoutubeVideoGraphModel(
      status: json['status'] ?? false,
      message: json['message'] ?? '',
      data: YoutubeVideoGraphData.from<PERSON>son(json['data'] ?? {}),
    );
  }

  Map<String, dynamic> to<PERSON>son() {
    return {
      'status': status,
      'message': message,
      'data': data.toJson(),
    };
  }
}

class YoutubeVideoGraphData {
  final Map<String, int> videoCountByDate;
  final int totalVideos;

  YoutubeVideoGraphData({
    required this.videoCountByDate,
    required this.totalVideos,
  });

  YoutubeVideoGraphData copyWith({
    Map<String, int>? videoCountByDate,
    int? totalVideos,
  }) {
    return YoutubeVideoGraphData(
      videoCountByDate: videoCountByDate ?? this.videoCountByDate,
      totalVideos: totalVideos ?? this.totalVideos,
    );
  }

  factory YoutubeVideoGraphData.fromJson(Map<String, dynamic> json) {
    return YoutubeVideoGraphData(
      videoCountByDate: Map<String, int>.from(json['video_count_by_date'] ?? {}),
      totalVideos: json['total_videos'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'video_count_by_date': videoCountByDate,
      'total_videos': totalVideos,
    };
  }
}
