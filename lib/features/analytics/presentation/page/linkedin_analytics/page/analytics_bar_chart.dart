import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/analytics/bloc/analytics_bloc.dart';
import 'package:flowkar/features/analytics/presentation/page/instagram_analytics/model/instagram_pie_chart_model.dart';
import 'package:flowkar/features/analytics/presentation/page/linkedin_analytics/model/linkedin_pie_chart_model.dart';
import 'package:flowkar/features/analytics/presentation/page/thread_analytics/model/thread_country_model.dart';
import 'package:flowkar/features/analytics/presentation/widget/pie_chart/pie_chart_widget.dart';
import 'package:intl/intl.dart';

class LinkedInPieChartDisplay extends StatefulWidget {
  final String title;
  final String platform;
  final String? chartTitle;
  const LinkedInPieChartDisplay({super.key, required this.title, required this.platform, this.chartTitle});

  @override
  State<LinkedInPieChartDisplay> createState() => _LinkedInPieChartDisplayState();
}

class _LinkedInPieChartDisplayState extends State<LinkedInPieChartDisplay> {
  late DateTime startDate;
  late DateTime endDate;
  final DateFormat _dateFormat = DateFormat('dd-MMM-yyyy');

  @override
  void initState() {
    super.initState();
    endDate = DateTime.now();
    startDate = endDate.subtract(Duration(days: 7));
  }

  @override
  Widget build(BuildContext context) {
    // Check if we have valid data

    return Scaffold(
      appBar: _buildAppBar(context),
      body: BlocBuilder<AnalyticsBloc, AnalyticsState>(
        builder: (context, state) {
          if (state.isLinkedinPiechartLoading) {
            return Center(child: LoadingAnimationWidget());
          } else if (state.isInstagramPieChartLoading) {
            return Center(child: LoadingAnimationWidget());
          } else if (state.isThreadPieChartLoading) {
            return Center(child: LoadingAnimationWidget());
          }

          final graphData = state.linkedinPiechartModel?.data?.followers?.graphData!;
          final instagramData = state.instagramPieChart?.data;
          final threadModel = state.threadCountryModel;
          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (widget.platform != "Thread") _buildHeaderWithDatePicker(),
                if (widget.platform == "LinkedIn") _linkedinPiechart(graphData, state),
                if (widget.platform == "Instagram") _instagramPiechart(instagramData, state),
                if (widget.platform == "Thread") _threadPiechart(threadModel, state, widget.chartTitle ?? ""),
              ],
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          widget.title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w700,
                fontSize: 18.sp,
              ),
        ),
      ],
    );
  }

  Widget _buildHeaderWithDatePicker() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Account",
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      letterSpacing: 0.28,
                      fontWeight: FontWeight.w900,
                      color: Theme.of(context).customColors.black.withAlpha(150),
                      fontSize: 16.sp,
                    ),
              ),
              InkWell(
                onTap: () {
                  _showDateRangePicker();
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: Theme.of(context).primaryColor.withOpacity(0.2),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.calendar_today_rounded,
                        color: Theme.of(context).primaryColor,
                        size: 16.sp,
                      ),
                      buildSizedBoxW(6.0),
                      Text(
                        '${_dateFormat.format(startDate)} - ${_dateFormat.format(endDate)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).primaryColor,
                              fontSize: 12.sp,
                            ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showDateRangePicker() {
    // Create temporary copies of the dates to work with in the modal
    DateTime tempStartDate = startDate;
    DateTime tempEndDate = endDate;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Make sure it has enough space
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            return Container(
              padding: EdgeInsets.all(16.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Select Date Range',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w700,
                          fontSize: 18.sp,
                        ),
                  ),
                  buildSizedBoxH(20),
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Start Date',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Theme.of(context).customColors.black.withAlpha(170),
                                    fontSize: 14.sp,
                                  ),
                            ),
                            buildSizedBoxH(8),
                            InkWell(
                              onTap: () async {
                                final DateTime? pickedDate = await showDatePicker(
                                  context: context,
                                  initialDate: tempStartDate,
                                  firstDate: DateTime(2020),
                                  lastDate: DateTime.now(),
                                );

                                if (pickedDate != null) {
                                  // Update the state within the modal
                                  setModalState(() {
                                    tempStartDate = pickedDate;

                                    // Ensure end date is not before start date
                                    if (tempEndDate.isBefore(tempStartDate)) {
                                      tempEndDate = tempStartDate;
                                    }
                                  });
                                }
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Theme.of(context).primaryColor.withOpacity(0.2),
                                  ),
                                  borderRadius: BorderRadius.circular(8.r),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      _dateFormat.format(tempStartDate),
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                            fontSize: 14.sp,
                                          ),
                                    ),
                                    Icon(
                                      Icons.calendar_today_rounded,
                                      size: 18.sp,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      buildSizedBoxW(16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'End Date',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Theme.of(context).customColors.black.withAlpha(170),
                                    fontSize: 14.sp,
                                  ),
                            ),
                            buildSizedBoxH(8),
                            InkWell(
                              onTap: () async {
                                final DateTime? pickedDate = await showDatePicker(
                                  context: context,
                                  initialDate: tempEndDate,
                                  firstDate: tempStartDate,
                                  lastDate: DateTime.now(),
                                );

                                if (pickedDate != null) {
                                  // Update the state within the modal
                                  setModalState(() {
                                    tempEndDate = pickedDate;
                                  });
                                }
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Theme.of(context).primaryColor.withOpacity(0.2),
                                  ),
                                  borderRadius: BorderRadius.circular(8.r),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      _dateFormat.format(tempEndDate),
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                            fontSize: 14.sp,
                                          ),
                                    ),
                                    Icon(
                                      Icons.calendar_today_rounded,
                                      size: 18.sp,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  buildSizedBoxH(24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        // Close bottom sheet first
                        Navigator.pop(context);

                        // Then update the parent widget state with selected dates
                        if (widget.platform == "Instagram") {
                          setState(() {
                            startDate = tempStartDate;
                            endDate = tempEndDate;
                            // Optional debug print to verify dates are updated
                            Logger.lOG(
                                'Updated dates: ${_dateFormat.format(startDate)} - ${_dateFormat.format(endDate)}');
                            final formatter = DateFormat('yyyy-MM-dd');
                            final endDateFormatted = formatter.format(endDate);
                            final startDateFormatted = formatter.format(startDate);
                            context
                                .read<AnalyticsBloc>()
                                .add(InstagramPieChartAPI(endDate: endDateFormatted, startDate: startDateFormatted));
                          });
                        } else {
                          setState(() {
                            startDate = tempStartDate;
                            endDate = tempEndDate;
                            // Optional debug print to verify dates are updated
                            Logger.lOG(
                                'Updated dates: ${_dateFormat.format(startDate)} - ${_dateFormat.format(endDate)}');
                            final formatter = DateFormat('dd-MM-yyyy');
                            final endDateFormatted = formatter.format(endDate);
                            final startDateFormatted = formatter.format(startDate);
                            context
                                .read<AnalyticsBloc>()
                                .add(LinkedinPiechartAPI(endDate: endDateFormatted, startDate: startDateFormatted));
                          });
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).primaryColor,
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        'Apply',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).customColors.white,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  _linkedinPiechart(LinkedinGraphData? graphData, AnalyticsState state) {
    return graphData?.area == null ||
            (graphData?.area?.isEmpty ?? true) &&
                (graphData?.seniority?.isEmpty ?? true) &&
                (graphData?.industry?.isEmpty ?? true) &&
                (graphData?.country?.isEmpty ?? true) &&
                (graphData?.function?.isEmpty ?? true) &&
                (graphData?.companySize?.isEmpty ?? true)
        ? Padding(
            padding: EdgeInsets.only(top: 80.h),
            child: ExceptionWidget(
              imagePath: Assets.images.svg.exception.svgNodatafound.path,
              title: "No data available",
              subtitle: " Try refreshing the data or check your filters.",
              showButton: false,
            ),
          )
        : Column(
            children: [
              if (graphData?.country != null && graphData!.country!.isNotEmpty)
                ChartSection(
                  isLoading: state.isLinkedinPiechartLoading,
                  title: 'Country Distribution',
                  chartData: graphData.country!
                      .map((item) => ChartItem(
                          name: item.countryName ?? 'Unknown', percentage: item.persantageValue?.toDouble() ?? 0))
                      .toList(),
                ),

              // Area distribution
              if (graphData?.area != null && graphData!.area!.isNotEmpty)
                ChartSection(
                  isLoading: state.isLinkedinPiechartLoading,
                  title: 'Area Distribution',
                  chartData: graphData.area!
                      .map((item) => ChartItem(
                          name: item.areaName ?? 'Unknown', percentage: item.persantageValue?.toDouble() ?? 0))
                      .toList(),
                ),

              // Seniority distribution
              if (graphData?.seniority != null && graphData!.seniority!.isNotEmpty)
                ChartSection(
                  isLoading: state.isLinkedinPiechartLoading,
                  title: 'Seniority Distribution',
                  chartData: graphData.seniority!
                      .map((item) => ChartItem(
                          name: item.seniorityName ?? 'Unknown', percentage: item.persantageValue?.toDouble() ?? 0))
                      .toList(),
                ),

              // Industry distribution
              if (graphData?.industry != null && graphData!.industry!.isNotEmpty)
                ChartSection(
                  isLoading: state.isLinkedinPiechartLoading,
                  title: 'Industry Distribution',
                  chartData: graphData.industry!
                      .map((item) => ChartItem(
                          name: item.industryName ?? 'Unknown', percentage: item.persantageValue?.toDouble() ?? 0))
                      .toList(),
                ),

              // Function distribution
              if (graphData?.function != null && graphData!.function!.isNotEmpty)
                ChartSection(
                  isLoading: state.isLinkedinPiechartLoading,
                  title: 'Function Distribution',
                  chartData: graphData.function!
                      .map((item) => ChartItem(
                          name: item.functionName ?? 'Unknown', percentage: item.persantageValue?.toDouble() ?? 0))
                      .toList(),
                ),

              // Company size distribution
              if (graphData?.companySize != null && graphData!.companySize!.isNotEmpty)
                ChartSection(
                  isLoading: state.isLinkedinPiechartLoading,
                  title: 'Company Size Distribution',
                  chartData: graphData.companySize!
                      .map((item) => ChartItem(
                          name: item.functionName ?? item.functionName ?? 'Unknown',
                          percentage: item.persantageValue?.toDouble() ?? 0))
                      .toList(),
                ),
            ],
          );
  }

  _instagramPiechart(InstagramPieChartData? graphData, AnalyticsState state) {
    return graphData?.gender == null ||
            graphData!.gender.isEmpty ||
            graphData.age.isEmpty && graphData.city.isEmpty && graphData.country.isEmpty
        ? Padding(
            padding: EdgeInsets.only(top: 80.h),
            child: ExceptionWidget(
              imagePath: Assets.images.svg.exception.svgNodatafound.path,
              title: "No data available",
              subtitle: " Try refreshing the data or check your filters.",
              showButton: false,
            ),
          )
        : Column(
            children: [
              if (graphData.age.isNotEmpty)
                ChartSection(
                  isLoading: state.isInstagramPieChartLoading,
                  title: 'Age Distribution',
                  chartData: graphData.age
                      .map((item) => ChartItem(name: item.name, percentage: item.persanatge.toDouble()))
                      .toList(),
                ),
              if (graphData.gender.isNotEmpty)
                ChartSection(
                  isLoading: state.isInstagramPieChartLoading,
                  title: 'Gender Distribution',
                  chartData: graphData.gender
                      .map((item) => ChartItem(name: item.name, percentage: item.persanatge.toDouble()))
                      .toList(),
                ),
              if (graphData.city.isNotEmpty)
                ChartSection(
                  isLoading: state.isInstagramPieChartLoading,
                  title: 'City Distribution',
                  chartData: graphData.city
                      .map((item) => ChartItem(name: item.name, percentage: item.persanatge.toDouble()))
                      .toList(),
                ),

              if (graphData.country.isNotEmpty)
                ChartSection(
                  isLoading: state.isInstagramPieChartLoading,
                  title: 'Country Distribution',
                  chartData: graphData.country
                      .map((item) => ChartItem(name: item.name, percentage: item.persanatge.toDouble()))
                      .toList(),
                ),

              // Area distribution
            ],
          );
  }

  List<ChartItem> _parseThreadCountryData(GenericMapData mapData) {
    List<ChartItem> result = [];

    int total = mapData.map.values.fold(0, (sum, value) => sum + value);

    mapData.map.forEach((key, value) {
      if (total > 0) {
        double percentage = (value / total) * 100;
        result.add(ChartItem(name: key, percentage: percentage));
      }
    });

    return result;
  }

  Widget _threadPiechart(ThreadCountryModel? threadModel, AnalyticsState state, String chartTitle) {
    // if (threadModel == null || threadModel.data.length < 2 || threadModel.data[1] is! GenericMapData) {
    if (threadModel?.data[0] == false) {
      return Padding(
        padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 5),
        child: ExceptionWidget(
          imagePath: Assets.images.svg.exception.svgNodatafound.path,
          title: "No data available",
          subtitle: "",
          showButton: false,
        ),
      );
    }
    // }

    final mapData = threadModel?.data[1];
    List<ChartItem> chartItems = _parseThreadCountryData(mapData);

    return Column(
      children: [
        if (chartItems.isNotEmpty)
          ChartSection(
            isLoading: state.isThreadPieChartLoading,
            title: chartTitle,
            chartData: chartItems,
          ),
      ],
    );
  }
}
