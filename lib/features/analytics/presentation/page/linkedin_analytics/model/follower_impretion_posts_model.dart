FollowerImpretionPostModel deserializeFollowerImpretionPostModel(Map<String, dynamic> json) =>
    FollowerImpretionPostModel.fromJson(json);

class FollowerImpretionPostModel {
  final bool status;
  final String message;
  final FollowerImpretionPostData data;

  FollowerImpretionPostModel({
    required this.status,
    required this.message,
    required this.data,
  });

  factory FollowerImpretionPostModel.fromJson(Map<String, dynamic> json) {
    return FollowerImpretionPostModel(
      status: json['status'],
      message: json['message'],
      data: FollowerImpretionPostData.from<PERSON>son(json['data']),
    );
  }
}

class FollowerImpretionPostData {
  final LinkedinFollowers followers;
  final LinkedinPosts posts;
  final LinkedinImpressions impressions;
  final LinkedinCards cards;

  FollowerImpretionPostData({
    required this.followers,
    required this.posts,
    required this.impressions,
    required this.cards,
  });

  factory FollowerImpretionPostData.fromJson(Map<String, dynamic> json) {
    return FollowerImpretionPostData(
      followers: LinkedinFollowers.fromJson(json['followers']),
      posts: LinkedinPosts.fromJson(json['posts']),
      impressions: LinkedinImpressions.fromJson(json['impressions']),
      cards: LinkedinCards.fromJson(json['cards']),
    );
  }
}

class LinkedinFollowers {
  final int totalFollowers;
  final List<Map<String, int>> graphData;

  LinkedinFollowers({
    required this.totalFollowers,
    required this.graphData,
  });

  factory LinkedinFollowers.fromJson(Map<String, dynamic> json) {
    List<Map<String, int>> parsedGraph =
        (json['graph_data'] as List).map((item) => Map<String, int>.from(item)).toList();

    return LinkedinFollowers(
      totalFollowers: json['total_followers'],
      graphData: parsedGraph,
    );
  }
}

class LinkedinImpressions {
  final int totalImpressions;
  final List<Map<String, int>> graphData;

  LinkedinImpressions({
    required this.totalImpressions,
    required this.graphData,
  });

  factory LinkedinImpressions.fromJson(Map<String, dynamic> json) {
    List<Map<String, int>> parsedGraph =
        (json['graph_data'] as List).map((item) => Map<String, int>.from(item)).toList();

    return LinkedinImpressions(
      totalImpressions: json['total_impressions'],
      graphData: parsedGraph,
    );
  }
}

class LinkedinPosts {
  final List<List<PostGraphData>> graphData;
  final int total;

  LinkedinPosts({
    required this.graphData,
    required this.total,
  });

  factory LinkedinPosts.fromJson(Map<String, dynamic> json) {
    List<List<PostGraphData>> parsedGraph = (json['graph_data'] as List)
        .map((subList) => (subList as List).map((e) => PostGraphData.fromJson(e)).toList())
        .toList();

    return LinkedinPosts(
      graphData: parsedGraph,
      total: json['total'],
    );
  }
}

class PostGraphData {
  final String date;
  final int count;

  PostGraphData({
    required this.date,
    required this.count,
  });

  factory PostGraphData.fromJson(Map<String, dynamic> json) {
    return PostGraphData(
      date: json['date'],
      count: json['count'],
    );
  }
}

class LinkedinCards {
  final int followers;
  final double dailyFollowers;
  final double followersPerPost;
  final double dailyImpressions;
  final double dailyPosts;

  LinkedinCards({
    required this.followers,
    required this.dailyFollowers,
    required this.followersPerPost,
    required this.dailyImpressions,
    required this.dailyPosts,
  });

  factory LinkedinCards.fromJson(Map<String, dynamic> json) {
    return LinkedinCards(
      followers: json['followers'],
      dailyFollowers: (json['daily_followers'] as num).toDouble(),
      followersPerPost: (json['followers_per_post'] as num).toDouble(),
      dailyImpressions: (json['daily_impressions'] as num).toDouble(),
      dailyPosts: (json['daily_posts'] as num).toDouble(),
    );
  }
}
