LinkedinEngagementAndShareModel deserializeLinkedinEngagementAndShareModel(Map<String, dynamic> json) =>
    LinkedinEngagementAndShareModel.fromJson(json);

class LinkedinEngagementAndShareModel {
  final bool status;
  final String message;
  final GraphData? data;

  LinkedinEngagementAndShareModel({
    required this.status,
    required this.message,
    this.data,
  });

  factory LinkedinEngagementAndShareModel.fromJson(Map<String, dynamic> json) {
    return LinkedinEngagementAndShareModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null ? GraphData.fromJson(json['data']) : null,
    );
  }
}

class GraphData {
  final List<ChartPoint<double>> engagement;
  final List<ChartPoint<int>> uniqueImpressions;
  final List<ChartPoint<int>> shareCount;
  final List<ChartPoint<int>> clickCount;
  final List<ChartPoint<int>> likeCount;
  final List<ChartPoint<int>> impressionCount;
  final List<ChartPoint<int>> commentCount;

  final double totalEngagement;
  final int totalUniqueImpressions;
  final int totalShareCount;
  final int totalClickCount;
  final int totalLikeCount;
  final int totalImpressionCount;
  final int totalCommentCount;
  final Cards cards;

  GraphData({
    required this.engagement,
    required this.uniqueImpressions,
    required this.shareCount,
    required this.clickCount,
    required this.likeCount,
    required this.impressionCount,
    required this.commentCount,
    required this.totalEngagement,
    required this.totalUniqueImpressions,
    required this.totalShareCount,
    required this.totalClickCount,
    required this.totalLikeCount,
    required this.totalImpressionCount,
    required this.totalCommentCount,
    required this.cards,
  });

  factory GraphData.fromJson(Map<String, dynamic> json) {
    return GraphData(
      engagement: _mapListDouble(json['graph_data']['engagement']),
      uniqueImpressions: _mapListInt(json['graph_data']['unique_impressions']),
      shareCount: _mapListInt(json['graph_data']['share_count']),
      clickCount: _mapListInt(json['graph_data']['click_count']),
      likeCount: _mapListInt(json['graph_data']['like_count']),
      impressionCount: _mapListInt(json['graph_data']['impression_count']),
      commentCount: _mapListInt(json['graph_data']['comment_count']),
      totalEngagement: (json['graph_data']['total_engagement'] ?? 0).toDouble(),
      totalUniqueImpressions: json['graph_data']['total_unique_impressions'] ?? 0,
      totalShareCount: json['graph_data']['total_share_count'] ?? 0,
      totalClickCount: json['graph_data']['total_click_count'] ?? 0,
      totalLikeCount: json['graph_data']['total_like_count'] ?? 0,
      totalImpressionCount: json['graph_data']['total_impression_count'] ?? 0,
      totalCommentCount: json['graph_data']['total_comment_count'] ?? 0,
      cards: Cards.fromJson(json['graph_data']['cards']),
    );
  }

  static List<ChartPoint<T>> _mapListInt<T extends num>(List<dynamic> list) {
    return list.map((e) {
      final key = e.keys.first;
      final value = e[key];
      return ChartPoint<T>(date: key, value: value);
    }).toList();
  }

  static List<ChartPoint<T>> _mapListDouble<T extends num>(List<dynamic> list) {
    return list.map((e) {
      final key = e.keys.first;
      final value = e[key];
      return ChartPoint<T>(date: key, value: value.toDouble());
    }).toList();
  }
}

class ChartPoint<T extends num> {
  final String date;
  final T value;

  ChartPoint({required this.date, required this.value});
}

class Cards {
  final double dailyLikes;
  final double likesPerPost;
  final double dailyComments;
  final double commentsPerPost;
  final double dailyClicks;
  final double clicksPerPost;

  Cards({
    required this.dailyLikes,
    required this.likesPerPost,
    required this.dailyComments,
    required this.commentsPerPost,
    required this.dailyClicks,
    required this.clicksPerPost,
  });

  factory Cards.fromJson(Map<String, dynamic> json) {
    return Cards(
      dailyLikes: (json['daily_likes'] ?? 0).toDouble(),
      likesPerPost: (json['likes_per_post'] ?? 0).toDouble(),
      dailyComments: (json['daily_comments'] ?? 0).toDouble(),
      commentsPerPost: (json['comments_per_post'] ?? 0).toDouble(),
      dailyClicks: (json['daily_clicks'] ?? 0).toDouble(),
      clicksPerPost: (json['clicks_per_post'] ?? 0).toDouble(),
    );
  }
}
