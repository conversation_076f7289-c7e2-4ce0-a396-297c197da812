
import 'package:json_annotation/json_annotation.dart';
part 'linkedin_pie_chart_model.g.dart';

LinkedinPieChartModel deserializeLinkedinPieChartModel(Map<String, dynamic> json) =>
    LinkedinPieChartModel.fromJson(json);


@JsonSerializable(explicitToJson: true)
class LinkedinPieChartModel {
  final bool status;
  final String message;
  final LinkedinPieChartData? data;

  LinkedinPieChartModel({
    required this.status,
    required this.message,
    this.data,
  });

  factory LinkedinPieChartModel.fromJson(Map<String, dynamic> json) =>
      _$LinkedinPieChartModelFromJson(json);

  Map<String, dynamic> toJson() => _$LinkedinPieChartModelToJson(this);

  LinkedinPieChartModel copyWith({
    bool? status,
    String? message,
    LinkedinPieChartData? data,
  }) {
    return LinkedinPieChartModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

@JsonSerializable(explicitToJson: true)
class LinkedinPieChartData {
  final LinkedinFollowers? followers;

  LinkedinPieChartData({this.followers});

  factory LinkedinPieChartData.fromJson(Map<String, dynamic> json) =>
      _$LinkedinPieChartDataFromJson(json);

  Map<String, dynamic> toJson() => _$LinkedinPieChartDataToJson(this);

  LinkedinPieChartData copyWith({
    LinkedinFollowers? followers,
  }) {
    return LinkedinPieChartData(
      followers: followers ?? this.followers,
    );
  }
}

@JsonSerializable(explicitToJson: true)
class LinkedinFollowers {
  @JsonKey(name: 'total_followers')
  final int? totalFollowers;

  @JsonKey(name: 'graph_data')
  final LinkedinGraphData? graphData;

  LinkedinFollowers({
    this.totalFollowers,
    this.graphData,
  });

  factory LinkedinFollowers.fromJson(Map<String, dynamic> json) =>
      _$LinkedinFollowersFromJson(json);

  Map<String, dynamic> toJson() => _$LinkedinFollowersToJson(this);

  LinkedinFollowers copyWith({
    int? totalFollowers,
    LinkedinGraphData? graphData,
  }) {
    return LinkedinFollowers(
      totalFollowers: totalFollowers ?? this.totalFollowers,
      graphData: graphData ?? this.graphData,
    );
  }
}

@JsonSerializable(explicitToJson: true)
class LinkedinGraphData {
  final List<Country>? country;
  final List<Area>? area;
  final List<Seniority>? seniority;
  final List<Industry>? industry;
  final List<FunctionData>? function;
  @JsonKey(name: 'company_size')
  final List<CompanySize>? companySize;

  LinkedinGraphData({
    this.country,
    this.area,
    this.seniority,
    this.industry,
    this.function,
    this.companySize,
  });

  factory LinkedinGraphData.fromJson(Map<String, dynamic> json) =>
      _$LinkedinGraphDataFromJson(json);

  Map<String, dynamic> toJson() => _$LinkedinGraphDataToJson(this);

  LinkedinGraphData copyWith({
    List<Country>? country,
    List<Area>? area,
    List<Seniority>? seniority,
    List<Industry>? industry,
    List<FunctionData>? function,
    List<CompanySize>? companySize,
  }) {
    return LinkedinGraphData(
      country: country ?? this.country,
      area: area ?? this.area,
      seniority: seniority ?? this.seniority,
      industry: industry ?? this.industry,
      function: function ?? this.function,
      companySize: companySize ?? this.companySize,
    );
  }
}

@JsonSerializable()
class Country {
  @JsonKey(name: 'country_name')
  final String? countryName;

  @JsonKey(name: 'persantage_value')
  final double? persantageValue;

  Country({this.countryName, this.persantageValue});

  factory Country.fromJson(Map<String, dynamic> json) =>
      _$CountryFromJson(json);

  Map<String, dynamic> toJson() => _$CountryToJson(this);

  Country copyWith({
    String? countryName,
    double? persantageValue,
  }) {
    return Country(
      countryName: countryName ?? this.countryName,
      persantageValue: persantageValue ?? this.persantageValue,
    );
  }
}

@JsonSerializable()
class Area {
  @JsonKey(name: 'area_name')
  final String? areaName;

  @JsonKey(name: 'persantage_value')
  final double? persantageValue;

  Area({this.areaName, this.persantageValue});

  factory Area.fromJson(Map<String, dynamic> json) => _$AreaFromJson(json);

  Map<String, dynamic> toJson() => _$AreaToJson(this);

  Area copyWith({
    String? areaName,
    double? persantageValue,
  }) {
    return Area(
      areaName: areaName ?? this.areaName,
      persantageValue: persantageValue ?? this.persantageValue,
    );
  }
}

@JsonSerializable()
class Seniority {
  @JsonKey(name: 'seniority_name')
  final String? seniorityName;

  @JsonKey(name: 'persantage_value')
  final double? persantageValue;

  Seniority({this.seniorityName, this.persantageValue});

  factory Seniority.fromJson(Map<String, dynamic> json) =>
      _$SeniorityFromJson(json);

  Map<String, dynamic> toJson() => _$SeniorityToJson(this);

  Seniority copyWith({
    String? seniorityName,
    double? persantageValue,
  }) {
    return Seniority(
      seniorityName: seniorityName ?? this.seniorityName,
      persantageValue: persantageValue ?? this.persantageValue,
    );
  }
}

@JsonSerializable()
class Industry {
  @JsonKey(name: 'industry_name')
  final String? industryName;

  @JsonKey(name: 'persantage_value')
  final double? persantageValue;

  Industry({this.industryName, this.persantageValue});

  factory Industry.fromJson(Map<String, dynamic> json) =>
      _$IndustryFromJson(json);

  Map<String, dynamic> toJson() => _$IndustryToJson(this);

  Industry copyWith({
    String? industryName,
    double? persantageValue,
  }) {
    return Industry(
      industryName: industryName ?? this.industryName,
      persantageValue: persantageValue ?? this.persantageValue,
    );
  }
}

@JsonSerializable()
class FunctionData {
  @JsonKey(name: 'function_name')
  final String? functionName;

  @JsonKey(name: 'persantage_value')
  final double? persantageValue;

  FunctionData({this.functionName, this.persantageValue});

  factory FunctionData.fromJson(Map<String, dynamic> json) =>
      _$FunctionDataFromJson(json);

  Map<String, dynamic> toJson() => _$FunctionDataToJson(this);

  FunctionData copyWith({
    String? functionName,
    double? persantageValue,
  }) {
    return FunctionData(
      functionName: functionName ?? this.functionName,
      persantageValue: persantageValue ?? this.persantageValue,
    );
  }
}

@JsonSerializable()
class CompanySize {
  @JsonKey(name: 'function_name')
  final String? functionName;

  @JsonKey(name: 'persantage_value')
  final double? persantageValue;

  CompanySize({this.functionName, this.persantageValue});

  factory CompanySize.fromJson(Map<String, dynamic> json) =>
      _$CompanySizeFromJson(json);

  Map<String, dynamic> toJson() => _$CompanySizeToJson(this);

  CompanySize copyWith({
    String? functionName,
    double? persantageValue,
  }) {
    return CompanySize(
      functionName: functionName ?? this.functionName,
      persantageValue: persantageValue ?? this.persantageValue,
    );
  }
}
