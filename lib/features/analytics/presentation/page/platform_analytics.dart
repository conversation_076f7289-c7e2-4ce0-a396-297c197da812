import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/analytics/bloc/analytics_bloc.dart';
import 'package:flowkar/features/analytics/presentation/page/social_data_list.dart';
import 'package:flowkar/features/analytics/presentation/widget/platform_analytics_shimmer.dart';
import 'package:flowkar/features/analytics/presentation/widget/view_post_screen_shimmer.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/social_connect/bloc/social_connec_bloc.dart';
import 'package:flowkar/features/widgets/common/coming_soon_screen.dart';

bool isNoDatafound = false;

class PlatformAnalytics extends StatefulWidget {
  const PlatformAnalytics({super.key, bool? stackonScreen});

  static Widget builder(BuildContext context) {
    return const PlatformAnalytics();
  }

  @override
  State<PlatformAnalytics> createState() => _PlatformAnalyticsState();
}

class _PlatformAnalyticsState extends State<PlatformAnalytics> {
  @override
  void initState() {
    context.read<AnalyticsBloc>().add(GetAnalyticsSocialEvent());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: BlocBuilder<SocialConnecBloc, SocialConnectState>(
        builder: (context, state) {
          if (state.isConnectLoading) {
            return ViewPostScreenShimmer();
          }
          return BlocConsumer<ConnectivityBloc, ConnectivityState>(
            listener: (context, connectivityState) {
              if (connectivityState.isReconnected) {
                context.read<AnalyticsBloc>().add(GetAnalyticsSocialEvent());
              }
            },
            builder: (context, connectivityState) {
              return BlocBuilder<AnalyticsBloc, AnalyticsState>(
                builder: (context, analyticsState) {
                  if (!connectivityState.isConnected) {
                    return AnalyticsShimmerScreen();
                  } else if (analyticsState.isGetAnalyticsPlatformLoading) {
                    return AnalyticsShimmerScreen();
                  } else if (analyticsState.getAnalyticsPlatformsModel?.data == null) {
                    return ListView(
                      physics: AlwaysScrollableScrollPhysics(),
                      children: [
                        buildSizedBoxH(MediaQuery.of(context).size.height / 6),
                        ExceptionWidget(
                          imagePath: Assets.images.svg.exception.svgNodatafound.path,
                          showButton: false,
                          title: Lang.of(context).lbl_no_data_found,
                          subtitle: Lang.of(context).lbl_no_post,
                        ),
                      ],
                    );
                  } else {
                    return ListView(
                      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom + 85.h),
                      children: [
                        // buildSizedBoxH(10.h),
                        // _buildAnalyticsSection(context, Lang.current.lbl_summary, () {
                        //   PersistentNavBarNavigator.pushNewScreen(
                        //     context,
                        //     screen: ComingSoonScreen(title: Lang.current.lbl_summary),
                        //   );
                        // }),
                        // buildSizedBoxH(20.h),
                        _buildSocialPlatformsSection(context, analyticsState),
                      ],
                    );
                  }
                },
              );
            },
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: false,
      alignment: Alignment.center,
      title: Lang.current.lbl_analytics,
      textStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w700,
            fontSize: 18.sp,
          ),
    );
  }

  // Widget _buildAnalyticsSection(BuildContext context, String title, VoidCallback onTap) {
  Widget _buildSocialPlatformsSection(BuildContext context, AnalyticsState analyticsState) {
    final platforms = [
      {
        'image': Assets.images.icons.social.linkedin.path,
        'text': Lang.current.lbl_linkedIn,
        'socialName': 'linkedin',
      },
      {
        'image': Assets.images.icons.social.insta.path,
        'text': Lang.current.lbl_instagram,
        'socialName': 'instagram',
      },
      {
        'image': Assets.images.icons.social.facebook.path,
        'text': Lang.current.lbl_facebook,
        'socialName': 'facebook',
      },
      {
        'image': Assets.images.icons.social.icYoutube.path,
        'text': Lang.current.lbl_youtube,
        'socialName': 'youtube',
      },
      {
        'image': Assets.images.icons.social.icPintrest.path,
        'text': Lang.current.lbl_pintrest,
        'socialName': 'pinterest',
      },
      {
        'image': Assets.images.icons.social.icThread.path,
        'text': Lang.current.lbl_thread,
        'socialName': 'thread',
      },
    ];

    return ValueListenableBuilder<Map<String, bool>>(
      valueListenable: socialPlatformsStatus,
      builder: (context, statusMap, _) {
        final modelData = analyticsState.getAnalyticsPlatformsModel?.data;

        final connectedPlatforms = platforms.where((platform) {
          final key = platform['socialName']?.toUpperCase();

          final isEnabledInPrefs = statusMap[key] == true;

          final isEnabledInModel = () {
            switch (key) {
              case 'FACEBOOK':
                return modelData?.facebook == true;
              case 'INSTAGRAM':
                return modelData?.instagram == true;
              case 'TWITTER':
                return modelData?.twitter == true;
              case 'YOUTUBE':
                return modelData?.youTube == true;
              case 'LINKEDIN':
                return modelData?.linkedIn == true;
              case 'PINTEREST':
                return modelData?.pinterest == true;
              case 'TIKTOK':
                return modelData?.tiktok == true;
              case 'THREAD':
                return modelData?.threads == true;
              default:
                return false;
            }
          }();

          return isEnabledInPrefs && isEnabledInModel;
        }).toList();

        if (connectedPlatforms.isEmpty) {
          return Center(
            child: Padding(
              padding: EdgeInsets.only(top: 150.h),
              child: ExceptionWidget(
                imagePath: Assets.images.svg.exception.svgNodatafound.path,
                showButton: false,
                title: "No data found",
                subtitle: "Please connect any platform to see analytics data.",
              ),
            ),
          );
        }
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: _buildSectionContainer(
            context,
            child: Column(
              children: connectedPlatforms.asMap().entries.map((entry) {
                final index = entry.key;
                final platform = entry.value;
                final socialName = platform['socialName'] ?? "";

                return Column(
                  children: [
                    _buildSettingsItem(
                      context,
                      imagePath: platform['image'] ?? "",
                      text: platform['text'] ?? "",
                      padding: EdgeInsets.all(6.0),
                      onTap: () => _handlePlatformTap(context, socialName),
                    ),
                    if (index != connectedPlatforms.length - 1) _buildDivider(context),
                  ],
                );
              }).toList()
                ..add(Column(children: [buildSizedBoxH(8.w)])),
            ),
          ),
        );
      },
    );
  }

  void _handlePlatformTap(BuildContext context, String socialName) {
    switch (socialName) {
      case 'linkedin':
        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: SocialDataList(socialName: Lang.current.lbl_linkedIn),
          withNavBar: false,
        );
        break;
      case 'instagram':
        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: SocialDataList(socialName: Lang.current.lbl_instagram),
          withNavBar: false,
        );
        break;
      case 'facebook':
        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: SocialDataList(socialName: Lang.current.lbl_facebook),
          withNavBar: false,
        );
        break;
      case 'youtube':
        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: SocialDataList(socialName: Lang.current.lbl_youtube),
          withNavBar: false,
        );
        break;
      case 'pinterest':
        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: SocialDataList(socialName: Lang.current.lbl_pintrest),
          withNavBar: false,
        );
        break;

      case 'thread':
        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: SocialDataList(socialName: Lang.current.lbl_thread),
          withNavBar: false,
        );
        break;
      default:
        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: ComingSoonScreen(title: Lang.current.lbl_coming_soon),
        );
    }
  }

  Widget _buildSectionContainer(BuildContext context, {required Widget child}) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 11.0.w),
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.white,
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            offset: Offset(0, 2),
            blurRadius: 14.r,
            spreadRadius: 0,
            color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
          )
        ],
      ),
      child: child,
    );
  }

  Widget _buildSettingsItem(BuildContext context,
      {required String imagePath,
      required String text,
      VoidCallback? onTap,
      EdgeInsetsGeometry? padding,
      bool showBGColor = false}) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.0.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                showBGColor
                    ? _buildIconWithBackground(context, imagePath, padding)
                    : CustomImageView(
                        height: 40.0.h,
                        width: 40.0.w,
                        imagePath: imagePath,
                        fit: BoxFit.contain,
                      ),
                buildSizedBoxW(20),
                Text(
                  text,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w400,
                        fontSize: 16.sp,
                        color: Theme.of(context).customColors.black,
                      ),
                ),
              ],
            ),
            CustomImageView(
              margin: EdgeInsets.symmetric(horizontal: 14.0.w),
              imagePath: Assets.images.svg.setting.svgRightArrow.path,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIconWithBackground(BuildContext context, String imagePath, EdgeInsetsGeometry? padding) {
    return Container(
      height: 40.0.h,
      width: 40.0.w,
      padding: padding ?? EdgeInsets.all(9.0),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Theme.of(context).customColors.settingCardIconBGColor.withOpacity(0.2),
      ),
      child: CustomImageView(imagePath: imagePath, fit: BoxFit.contain),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Divider(
      color: Theme.of(context).customColors.dividerColor.withOpacity(0.1),
      height: 1,
    );
  }
}
