import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/analytics/bloc/analytics_bloc.dart';
import 'package:flowkar/features/analytics/presentation/page/facebook_analytics/model/facebook_impressions_follower_model.dart';
import 'package:flowkar/features/analytics/presentation/page/facebook_analytics/model/facebook_post_graph_model.dart';
import 'package:flowkar/features/analytics/presentation/page/pinterest/model/pinterest_line_graph_model.dart';
import 'package:flowkar/features/analytics/presentation/page/thread_analytics/model/thread_line_graph_model.dart';
import 'package:flowkar/features/analytics/presentation/page/youtube_analytics/model/youtube_subscribe_like_comment_graph_model.dart';
import 'package:flowkar/features/analytics/presentation/widget/common_analytics_widget.dart';
import 'package:intl/intl.dart';

class AnalyticsCharts extends StatefulWidget {
  final String? title;
  final String? platform;
  const AnalyticsCharts({super.key, this.title, this.platform});

  static Widget builder(BuildContext context) {
    return AnalyticsCharts();
  }

  @override
  State<AnalyticsCharts> createState() => _AnalyticsChartsState();
}

class _AnalyticsChartsState extends State<AnalyticsCharts> {
  List<Map<String, dynamic>>? graphData;
  List<Map<String, dynamic>>? graphData2;
  String? graphData2Lable;
  double totalPrimaryMetric = 0;
  double dailyPrimaryMetric = 0;
  int totalSecondaryMetric = 0;
  double dailySecondaryMetric = 0;
  int totalTertiaryMetric = 0;
  double dailyTertiaryMetric = 0;
  String primaryMetricLabel = '';
  String secondaryMetricLabel = '';
  String tertiaryMetricLabel = '';
  dynamic Function(DateTime, DateTime)? onDateRangeSubmit;
  List<Map<String, dynamic>> secondaryMetricsData = [];
  String endDateFormatted = "";
  String startDateFormatted = "";
  bool isLoading = false;

  List<Map<String, int>> convertPostDataToGraphFormat(dynamic postGraphData) {
    List<Map<String, int>> result = [];
    if (postGraphData != null && postGraphData is List) {
      for (var item in postGraphData) {
        if (item is List && item.isNotEmpty) {
          for (var entry in item) {
            if (entry != null && entry.date != null && entry.count != null) {
              result.add({entry.date: entry.count});
            }
          }
        }
      }
    }
    return result;
  }

  List<Map<String, dynamic>> convertEngagementToGraphFormat(dynamic engagementGraphData) {
    List<Map<String, dynamic>> result = [];

    if (engagementGraphData != null && engagementGraphData is List) {
      for (var entry in engagementGraphData) {
        final date = entry.date;
        final count = entry.value;
        if (date != null && count != null) {
          result.add({date: count});
        }
      }
    }
    return result;
  }

  List<Map<String, dynamic>> convertFollowersInsightsToGraphData(dynamic followersInsights) {
    List<Map<String, dynamic>> graphData = [];

    if (followersInsights != null && followersInsights is Map<String, dynamic>) {
      followersInsights.forEach((dates, counts) {
        try {
          DateTime parsedDate = DateTime.parse(dates);
          String formattedDate = DateFormat('dd-MM-yyyy').format(parsedDate);
          final value = counts;
          graphData.add({formattedDate: value});
        } catch (e) {
          graphData.add({dates: counts});
        }
      });
    }

    return graphData;
  }

  List<Map<String, dynamic>> convertFacebookFollowerGraphData(List<FacebookFollowerGraphEntry> graphData) {
    List<Map<String, dynamic>> result = [];
    final formatter = DateFormat('dd-MM-yyyy');

    for (var entry in graphData) {
      // Parse the original date string
      DateTime parsedDate = DateTime.parse(entry.date);

      // Format to dd-MM-yyyy
      String formattedDate = formatter.format(parsedDate);

      // Create a map with formatted date as key and follows as value
      result.add({formattedDate: entry.follows});
    }

    return result;
  }

  List<Map<String, dynamic>> convertFacebookImpressionGraphData(List<FacebookFollowerGraphEntry> graphData) {
    List<Map<String, dynamic>> result = [];
    final formatter = DateFormat('dd-MM-yyyy');

    for (var entry in graphData) {
      // Parse the original date string
      DateTime parsedDate = DateTime.parse(entry.date);

      // Format to dd-MM-yyyy
      String formattedDate = formatter.format(parsedDate);

      // Create a map with formatted date as key and follows as value
      result.add({formattedDate: entry.impressions});
    }

    return result;
  }

  List<Map<String, dynamic>> convertFacebookshareGraphData(List<FacebookGraphDataItem> graphData,
      {bool shares = false, bool interactions = false, bool post = false, bool like = false, bool comment = false}) {
    List<Map<String, dynamic>> result = [];
    final formatter = DateFormat('dd-MM-yyyy');

    for (var entry in graphData) {
      // Parse the original date string
      DateTime parsedDate = DateTime.parse(entry.date);

      // Format to dd-MM-yyyy
      String formattedDate = formatter.format(parsedDate);
      final value = shares
          ? entry.shares
          : interactions
              ? entry.interactions
              : post
                  ? entry.posts
                  : like
                      ? entry.likes
                      : comment
                          ? entry.comments
                          : entry.shares;

      // Create a map with formatted date as key and follows as value
      result.add({formattedDate: value});
    }

    return result;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<AnalyticsBloc, AnalyticsState>(
        builder: (context, state) {
          isLoading = state.isFollowerImpressionsPostLoading ||
              state.isLinkedinEngagementLoading ||
              state.isLinkedinPiechartLoading ||
              state.isInstagramLineGraphLoading ||
              state.isFacebookImpretionAndFollower;

          if (state.isFollowerImpressionsPostLoading) {
            Center(child: LoadingAnimationWidget());
          } else if (state.isLinkedinEngagementLoading || state.isFacebookPostLikeCommentShareLoading) {
            Center(child: LoadingAnimationWidget());
          } else if (state.isInstagramLineGraphLoading || state.isFacebookImpretionAndFollower) {
            isLoading = state.isFacebookImpretionAndFollower || state.isInstagramLineGraphLoading;
            Center(child: LoadingAnimationWidget());
          }

          switch (widget.platform) {
            case "LinkedIn":
              switch (widget.title) {
                case "Followers":
                  graphData = state.followerImpressionsPostModel?.data.followers.graphData;
                  totalPrimaryMetric =
                      state.followerImpressionsPostModel?.data.followers.totalFollowers.toDouble() ?? 0;
                  dailyPrimaryMetric = state.followerImpressionsPostModel?.data.cards.dailyFollowers ?? 0;
                  totalSecondaryMetric = state.followerImpressionsPostModel?.data.impressions.totalImpressions ?? 0;
                  dailySecondaryMetric = state.followerImpressionsPostModel?.data.cards.dailyImpressions ?? 0;
                  totalTertiaryMetric = state.followerImpressionsPostModel?.data.posts.total ?? 0;
                  dailyTertiaryMetric = state.followerImpressionsPostModel?.data.cards.dailyPosts ?? 0;
                  primaryMetricLabel = 'Followers';
                  secondaryMetricLabel = 'Impressions';
                  tertiaryMetricLabel = 'Posts';
                  onDateRangeSubmit = (startDate, endDate) {
                    final formatter = DateFormat('dd-MM-yyyy');
                    setState(() {
                      endDateFormatted = formatter.format(endDate);
                      startDateFormatted = formatter.format(startDate);
                    });
                    context.read<AnalyticsBloc>().add(
                          LinkedinFollowerImpretionsPostAPI(
                            endDate: endDateFormatted,
                            startDate: startDateFormatted,
                          ),
                        );
                  };
                  secondaryMetricsData = [
                    {
                      'label': secondaryMetricLabel,
                      'total': totalSecondaryMetric,
                      'daily': dailySecondaryMetric,
                      'color': Color(0xff0077B5),
                    },
                    {
                      'label': tertiaryMetricLabel,
                      'total': totalTertiaryMetric,
                      'daily': dailyTertiaryMetric,
                      'color': Color(0xff0077B5),
                    },
                  ];
                  break;

                case "Impressions":
                  graphData = state.followerImpressionsPostModel?.data.impressions.graphData;
                  totalPrimaryMetric =
                      state.followerImpressionsPostModel?.data.impressions.totalImpressions.toDouble() ?? 0;
                  dailyPrimaryMetric = state.followerImpressionsPostModel?.data.cards.dailyImpressions ?? 0;
                  totalSecondaryMetric = state.followerImpressionsPostModel?.data.followers.totalFollowers ?? 0;
                  dailySecondaryMetric = state.followerImpressionsPostModel?.data.cards.dailyFollowers ?? 0;
                  totalTertiaryMetric = state.followerImpressionsPostModel?.data.posts.total ?? 0;
                  dailyTertiaryMetric = state.followerImpressionsPostModel?.data.cards.dailyPosts ?? 0;
                  primaryMetricLabel = 'Impressions';
                  secondaryMetricLabel = 'Followers';
                  tertiaryMetricLabel = 'Posts';
                  onDateRangeSubmit = (startDate, endDate) {
                    final formatter = DateFormat('dd-MM-yyyy');
                    setState(() {
                      endDateFormatted = formatter.format(endDate);
                      startDateFormatted = formatter.format(startDate);
                    });
                    context.read<AnalyticsBloc>().add(
                        LinkedinFollowerImpretionsPostAPI(endDate: endDateFormatted, startDate: startDateFormatted));
                  };
                  secondaryMetricsData = [
                    {
                      'label': secondaryMetricLabel,
                      'total': totalSecondaryMetric,
                      'daily': dailySecondaryMetric,
                      'color': Color(0xff0077B5),
                    },
                    {
                      'label': tertiaryMetricLabel,
                      'total': totalTertiaryMetric,
                      'daily': dailyTertiaryMetric,
                      'color': Color(0xff0077B5),
                    },
                  ];
                  break;
                case "Posts":
                  // Convert post data to the same format as other metrics
                  graphData = convertPostDataToGraphFormat(state.followerImpressionsPostModel?.data.posts.graphData);

                  totalPrimaryMetric = state.followerImpressionsPostModel?.data.posts.total.toDouble() ?? 0;
                  dailyPrimaryMetric = state.followerImpressionsPostModel?.data.cards.dailyPosts ?? 0;
                  totalSecondaryMetric = state.followerImpressionsPostModel?.data.followers.totalFollowers ?? 0;
                  dailySecondaryMetric = state.followerImpressionsPostModel?.data.cards.dailyFollowers ?? 0;
                  totalTertiaryMetric = state.followerImpressionsPostModel?.data.impressions.totalImpressions ?? 0;
                  dailyTertiaryMetric = state.followerImpressionsPostModel?.data.cards.dailyImpressions ?? 0;
                  primaryMetricLabel = 'Posts';
                  secondaryMetricLabel = 'Followers';
                  tertiaryMetricLabel = 'Impressions';
                  onDateRangeSubmit = (startDate, endDate) {
                    final formatter = DateFormat('dd-MM-yyyy');
                    setState(() {
                      endDateFormatted = formatter.format(endDate);
                      startDateFormatted = formatter.format(startDate);
                    });
                    context.read<AnalyticsBloc>().add(
                          LinkedinFollowerImpretionsPostAPI(
                            endDate: endDateFormatted,
                            startDate: startDateFormatted,
                          ),
                        );
                  };
                  secondaryMetricsData = [
                    {
                      'label': secondaryMetricLabel,
                      'total': totalSecondaryMetric,
                      'daily': dailySecondaryMetric,
                      'color': Color(0xff0077B5),
                    },
                    {
                      'label': tertiaryMetricLabel,
                      'total': totalTertiaryMetric,
                      'daily': dailyTertiaryMetric,
                      'color': Color(0xff0077B5),
                    },
                  ];
                  break;
                case "Engagement & Share":
                  final engagementmodel = state.linkedinEngagementModel;
                  graphData = convertEngagementToGraphFormat(engagementmodel?.data?.engagement);

                  totalPrimaryMetric = engagementmodel?.data?.totalEngagement.toDouble() ?? 0;
                  dailyPrimaryMetric = 0;
                  totalSecondaryMetric = engagementmodel?.data?.totalLikeCount.toInt() ?? 0;
                  dailySecondaryMetric = engagementmodel?.data?.cards.dailyLikes ?? 0.0;
                  totalTertiaryMetric = engagementmodel?.data?.totalCommentCount ?? 0;
                  dailyTertiaryMetric = engagementmodel?.data?.cards.dailyComments ?? 0;
                  primaryMetricLabel = 'Engagement & Share';
                  secondaryMetricLabel = 'Followers';
                  tertiaryMetricLabel = 'Posts';
                  onDateRangeSubmit = (startDate, endDate) {
                    final formatter = DateFormat('dd-MM-yyyy');
                    setState(() {
                      endDateFormatted = formatter.format(endDate);
                      startDateFormatted = formatter.format(startDate);
                    });
                    context
                        .read<AnalyticsBloc>()
                        .add(LinkedinengagementAndShareAPI(endDate: endDateFormatted, startDate: startDateFormatted));
                  };
                  secondaryMetricsData = [
                    {
                      'label': "Likes",
                      'total': totalSecondaryMetric,
                      'daily': dailySecondaryMetric,
                      'color': Color(0xff0077B5),
                    },
                    {
                      'label': "Comments",
                      'total': totalTertiaryMetric,
                      'daily': dailyTertiaryMetric,
                      'color': Color(0xff0077B5),
                    },
                    {
                      'label': "Daily Clicks",
                      'total': engagementmodel?.data?.totalClickCount ?? 0,
                      'daily': engagementmodel?.data?.cards.dailyClicks ?? 0,
                      'color': Color(0xff0077B5),
                    },
                  ];
                default:
                  graphData = [];
              }
              break;
            case "Instagram":
              _handleInstagramCases(state);

              break;
            case "Facebook":
              _handleFacebookCases(state);
              break;
            case "Youtube":
              _handleYoutubeCases(state);
              break;
            case "Thread":
              _handleThreadCases(state);
              break;
            case "Pintrest":
              _handlePinterestCases(state);
              break;
          }

          return CommonAnalyticsWidget(
            title: widget.title ?? "",
            primaryMetricLabel: primaryMetricLabel,
            tertiaryMetricLabel: tertiaryMetricLabel,
            primaryColor: const Color(0xFF8D6E63),
            secondaryColor: Theme.of(context).customColors.blueColor,
            tertiaryColor: Theme.of(context).customColors.blueColor,
            graphData: graphData,
            graphData2: graphData2 ?? [],
            graphData2Lable: graphData2Lable ?? "",
            totalPrimaryMetric: totalPrimaryMetric,
            dailyPrimaryMetric: dailyPrimaryMetric,
            totalSecondaryMetric: totalSecondaryMetric,
            dailySecondaryMetric: dailySecondaryMetric,
            totalTertiaryMetric: totalTertiaryMetric,
            dailyTertiaryMetric: dailyTertiaryMetric,
            isLoading: isLoading,
            onDateRangeSubmit: onDateRangeSubmit,
            secondaryMetricsData: secondaryMetricsData,
            endDate: endDateFormatted,
            startDate: startDateFormatted,
          );
        },
      ),
    );
  }

  void _handleInstagramCases(AnalyticsState state) {
    final formatter = DateFormat('yyyy-MM-dd');
    final instagramLineGraph = state.instagramLineGraph;
    switch (widget.title) {
      case "Followers & Followings":
        graphData = convertFollowersInsightsToGraphData(instagramLineGraph?.data.followersInsights ?? {});

        totalPrimaryMetric = instagramLineGraph?.data.followersCount.toDouble() ?? 0.0;
        dailyPrimaryMetric = instagramLineGraph?.data.graph1Metric.dailyFollowers ?? 0.0;

        primaryMetricLabel = 'Followers & Followings';
        secondaryMetricLabel = 'Followers';
        tertiaryMetricLabel = 'Posts';
        onDateRangeSubmit = (startDate, endDate) {
          // final formatter = DateFormat('yyyy-MM-dd');
          setState(() {
            endDateFormatted = formatter.format(endDate);
            startDateFormatted = formatter.format(startDate);
          });
          context
              .read<AnalyticsBloc>()
              .add(InstagramLineGraphAPI(endDate: endDateFormatted, startDate: startDateFormatted));
        };
        secondaryMetricsData = [
          {
            'label': "Followers In Range",
            'total': instagramLineGraph?.data.graph1Metric.followersInRange,
            'daily': instagramLineGraph?.data.graph1Metric.dailyFollowers,
            'color': Color(0xff0077B4),
          },
          {
            'label': "Posts Per Week",
            'total': instagramLineGraph?.data.graph1Metric.postsPerWeek,
            'daily': instagramLineGraph?.data.graph1Metric.dailyPosts,
            'color': Color(0xff00BBFF),
          },
          {
            'label': "Likes Per Post",
            'total': instagramLineGraph?.data.graph3Metric.likesPerPost,
            'daily': instagramLineGraph?.data.graph3Metric.dailyLikes,
            'color': Color(0xff00BBFF),
          },
          {
            'label': "Comments Per Post",
            'total': instagramLineGraph?.data.graph3Metric.commentsPerPost,
            'daily': instagramLineGraph?.data.graph3Metric.dailyComments,
            'color': Color(0xff00BBFF),
          },
        ];
        break;
      case "Impressions":
        graphData = convertFollowersInsightsToGraphData(instagramLineGraph?.data.impressionsCounts ?? {});

        totalPrimaryMetric = instagramLineGraph?.data.totalImpressionsInDateRange.toDouble() ?? 0.0;
        dailyPrimaryMetric = 0.00;

        primaryMetricLabel = 'Impressions';
        secondaryMetricLabel = 'Followers';
        tertiaryMetricLabel = 'Posts';
        onDateRangeSubmit = (startDate, endDate) {
          // final formatter = DateFormat('yyyy-MM-dd');
          setState(() {
            endDateFormatted = formatter.format(endDate);
            startDateFormatted = formatter.format(startDate);
          });
          context
              .read<AnalyticsBloc>()
              .add(InstagramLineGraphAPI(endDate: endDateFormatted, startDate: startDateFormatted));
        };
        secondaryMetricsData = [
          {
            'label': "Followers In Range",
            'total': instagramLineGraph?.data.graph1Metric.followersInRange,
            'daily': instagramLineGraph?.data.graph1Metric.dailyFollowers,
            'color': Color(0xff0077B4),
          },
          {
            'label': "Posts Per Week",
            'total': instagramLineGraph?.data.graph1Metric.postsPerWeek,
            'daily': instagramLineGraph?.data.graph1Metric.dailyPosts,
            'color': Color(0xff00BBFF),
          },
          {
            'label': "Likes Per Post",
            'total': instagramLineGraph?.data.graph3Metric.likesPerPost,
            'daily': instagramLineGraph?.data.graph3Metric.dailyLikes,
            'color': Color(0xff00BBFF),
          },
          {
            'label': "Comments Per Post",
            'total': instagramLineGraph?.data.graph3Metric.commentsPerPost,
            'daily': instagramLineGraph?.data.graph3Metric.dailyComments,
            'color': Color(0xff00BBFF),
          },
        ];
        break;
      case "Reach":
        graphData = convertFollowersInsightsToGraphData(instagramLineGraph?.data.reachCounts ?? {});

        totalPrimaryMetric = instagramLineGraph?.data.totalReachInDateRange.toDouble() ?? 0.0;
        dailyPrimaryMetric = 0.0;

        primaryMetricLabel = 'Reach';
        secondaryMetricLabel = 'Followers';
        tertiaryMetricLabel = 'Posts';
        onDateRangeSubmit = (startDate, endDate) {
          // final formatter = DateFormat('yyyy-MM-dd');
          setState(() {
            endDateFormatted = formatter.format(endDate);
            startDateFormatted = formatter.format(startDate);
          });
          context
              .read<AnalyticsBloc>()
              .add(InstagramLineGraphAPI(endDate: endDateFormatted, startDate: startDateFormatted));
        };
        secondaryMetricsData = [
          {
            'label': "Followers In Range",
            'total': instagramLineGraph?.data.graph1Metric.followersInRange,
            'daily': instagramLineGraph?.data.graph1Metric.dailyFollowers,
            'color': Color(0xff0077B4),
          },
          {
            'label': "Posts Per Week",
            'total': instagramLineGraph?.data.graph1Metric.postsPerWeek,
            'daily': instagramLineGraph?.data.graph1Metric.dailyPosts,
            'color': Color(0xff00BBFF),
          },
          {
            'label': "Likes Per Post",
            'total': instagramLineGraph?.data.graph3Metric.likesPerPost,
            'daily': instagramLineGraph?.data.graph3Metric.dailyLikes,
            'color': Color(0xff00BBFF),
          },
          {
            'label': "Comments Per Post",
            'total': instagramLineGraph?.data.graph3Metric.commentsPerPost,
            'daily': instagramLineGraph?.data.graph3Metric.dailyComments,
            'color': Color(0xff00BBFF),
          },
        ];
        break;
      case "Profile Visits":
        graphData = convertFollowersInsightsToGraphData(instagramLineGraph?.data.profileVisitsCounts ?? {});

        totalPrimaryMetric = instagramLineGraph?.data.totalProfileVisitsInDateRange.toDouble() ?? 0.0;
        dailyPrimaryMetric = 0.0;

        primaryMetricLabel = 'Profile Visits';
        secondaryMetricLabel = 'Followers';
        tertiaryMetricLabel = 'Posts';
        onDateRangeSubmit = (startDate, endDate) {
          // final formatter = DateFormat('yyyy-MM-dd');
          setState(() {
            endDateFormatted = formatter.format(endDate);
            startDateFormatted = formatter.format(startDate);
          });
          context
              .read<AnalyticsBloc>()
              .add(InstagramLineGraphAPI(endDate: endDateFormatted, startDate: startDateFormatted));
        };
        secondaryMetricsData = [
          {
            'label': "Followers In Range",
            'total': instagramLineGraph?.data.graph1Metric.followersInRange,
            'daily': instagramLineGraph?.data.graph1Metric.dailyFollowers,
            'color': Color(0xff0077B4),
          },
          {
            'label': "Posts Per Week",
            'total': instagramLineGraph?.data.graph1Metric.postsPerWeek,
            'daily': instagramLineGraph?.data.graph1Metric.dailyPosts,
            'color': Color(0xff00BBFF),
          },
          {
            'label': "Likes Per Post",
            'total': instagramLineGraph?.data.graph3Metric.likesPerPost,
            'daily': instagramLineGraph?.data.graph3Metric.dailyLikes,
            'color': Color(0xff00BBFF),
          },
          {
            'label': "Comments Per Post",
            'total': instagramLineGraph?.data.graph3Metric.commentsPerPost,
            'daily': instagramLineGraph?.data.graph3Metric.dailyComments,
            'color': Color(0xff00BBFF),
          },
        ];
        break;
      case "Posts":
        graphData = convertFollowersInsightsToGraphData(instagramLineGraph?.data.postCounts ?? {});

        totalPrimaryMetric = instagramLineGraph?.data.totalPosts.toDouble() ?? 0.0;
        dailyPrimaryMetric = 0.0;

        primaryMetricLabel = 'Posts';
        secondaryMetricLabel = 'Followers';
        tertiaryMetricLabel = 'Posts';
        onDateRangeSubmit = (startDate, endDate) {
          // final formatter = DateFormat('yyyy-MM-dd');
          setState(() {
            endDateFormatted = formatter.format(endDate);
            startDateFormatted = formatter.format(startDate);
          });
          context
              .read<AnalyticsBloc>()
              .add(InstagramLineGraphAPI(endDate: endDateFormatted, startDate: startDateFormatted));
        };
        secondaryMetricsData = [
          {
            'label': "Posts Per Week",
            'total': instagramLineGraph?.data.graph1Metric.postsPerWeek,
            'daily': instagramLineGraph?.data.graph1Metric.dailyPosts,
            'color': Color(0xff00BBFF),
          },
          {
            'label': "Likes Per Post",
            'total': instagramLineGraph?.data.graph3Metric.likesPerPost,
            'daily': instagramLineGraph?.data.graph3Metric.dailyLikes,
            'color': Color(0xff00BBFF),
          },
          {
            'label': "Comments Per Post",
            'total': instagramLineGraph?.data.graph3Metric.commentsPerPost,
            'daily': instagramLineGraph?.data.graph3Metric.dailyComments,
            'color': Color(0xff00BBFF),
          },
        ];
        break;
    }
  }

  void _handleFacebookCases(AnalyticsState state) {
    final facebookImpressionFollower = state.facebookImpretionAndFollowerModel;
    switch (widget.title) {
      case "Followers" || 'Impressions':
        isLoading = state.isFacebookImpretionAndFollower;

        bool isfollower = widget.title == 'Followers' ? true : false;
        List<FacebookFollowerGraphEntry>? followerEntries = facebookImpressionFollower?.data.graphData.data;
        graphData = isfollower
            ? convertFacebookFollowerGraphData(followerEntries ?? [])
            : convertFacebookImpressionGraphData(followerEntries ?? []);
        totalPrimaryMetric = isfollower
            ? facebookImpressionFollower?.data.graphData.totalFollows.toDouble() ?? 0
            : facebookImpressionFollower?.data.graphData.totalImpressions.toDouble() ?? 0;

        primaryMetricLabel = isfollower ? 'Followers' : 'Impressions';
        secondaryMetricLabel = 'Impressions';
        tertiaryMetricLabel = 'Posts';
        onDateRangeSubmit = (startDate, endDate) {
          final formatter = DateFormat('yyyy-MM-dd');
          setState(() {
            endDateFormatted = formatter.format(endDate);
            startDateFormatted = formatter.format(startDate);
          });
          context.read<AnalyticsBloc>().add(
                FacebookImpressionFollowerAPI(
                  endDate: endDateFormatted,
                  startDate: startDateFormatted,
                ),
              );
        };
        secondaryMetricsData = [
          {
            'label': "Likes",
            'total': facebookImpressionFollower?.data.metrics.likes,
            'daily': facebookImpressionFollower?.data.metrics.dailyLikes,
            'color': Theme.of(context).customColors.blueColor,
          },
          {
            'label': "Page View",
            'total': facebookImpressionFollower?.data.graphData.totalViewsTotal,
            'daily': facebookImpressionFollower?.data.metrics.dailyPageView,
            'color': Theme.of(context).customColors.blueColor,
          },
        ];
        break;
      case "Share":
        isLoading = state.isFacebookPostLikeCommentShareLoading;
        final shareEntries = state.facebookPostGraphModel?.data;
        graphData = convertFacebookshareGraphData(shareEntries?.graphData.data ?? [], shares: true);
        totalPrimaryMetric = shareEntries?.graphData.totalShares.toDouble() ?? 0;
        dailyPrimaryMetric = shareEntries?.metrics.sharesPerDay ?? 0.0;
        primaryMetricLabel = 'Shares';
        secondaryMetricLabel = 'Impressions';
        tertiaryMetricLabel = 'Posts';
        onDateRangeSubmit = (startDate, endDate) {
          final formatter = DateFormat('yyyy-MM-dd');
          setState(() {
            endDateFormatted = formatter.format(endDate);
            startDateFormatted = formatter.format(startDate);
          });
          context.read<AnalyticsBloc>().add(
                FacebookPostLikeCommentShareAPI(
                  endDate: endDateFormatted,
                  startDate: startDateFormatted,
                ),
              );
        };
        secondaryMetricsData = [
          {
            'label': "Post",
            'total': shareEntries?.graphData.totalPosts,
            'daily': 0,
            'color': Theme.of(context).customColors.blueColor,
          },
          {
            'label': "Likes",
            'total': shareEntries?.graphData.totalLikes,
            'daily': 0,
            'color': Theme.of(context).customColors.blueColor,
          },
          {
            'label': "Comments",
            'total': shareEntries?.graphData.totalComments,
            'daily': 0,
            'color': Theme.of(context).customColors.blueColor,
          },
          {
            'label': "Impressions",
            'total': shareEntries?.graphData.totalImpressions,
            'daily': 0,
            'color': Theme.of(context).customColors.blueColor,
          },
        ];
        break;
      case "Interactions" || "Post":
        isLoading = state.isFacebookPostLikeCommentShareLoading;
        bool isInteractions = widget.title == 'Interactions' ? true : false;
        final shareEntries = state.facebookPostGraphModel?.data;
        graphData = convertFacebookshareGraphData(shareEntries?.graphData.data ?? [],
            interactions: isInteractions ? true : false, post: isInteractions ? false : true);
        totalPrimaryMetric = isInteractions
            ? shareEntries?.graphData.totalInteractions.toDouble() ?? 0
            : shareEntries?.graphData.totalPosts.toDouble() ?? 0;
        dailyPrimaryMetric = 0.0;
        primaryMetricLabel = isInteractions ? 'Interactions' : "Post";
        secondaryMetricLabel = 'Impressions';
        tertiaryMetricLabel = 'Posts';
        onDateRangeSubmit = (startDate, endDate) {
          final formatter = DateFormat('yyyy-MM-dd');
          setState(() {
            endDateFormatted = formatter.format(endDate);
            startDateFormatted = formatter.format(startDate);
          });
          context.read<AnalyticsBloc>().add(
                FacebookPostLikeCommentShareAPI(
                  endDate: endDateFormatted,
                  startDate: startDateFormatted,
                ),
              );
        };
        secondaryMetricsData = [
          if (isInteractions)
            {
              'label': "Post",
              'total': shareEntries?.graphData.totalPosts,
              'daily': 0,
              'color': Theme.of(context).customColors.blueColor,
            },
          {
            'label': "Likes",
            'total': shareEntries?.graphData.totalLikes,
            'daily': 0,
            'color': Theme.of(context).customColors.blueColor,
          },
          {
            'label': "Comments",
            'total': shareEntries?.graphData.totalComments,
            'daily': 0,
            'color': Theme.of(context).customColors.blueColor,
          },
          {
            'label': "Share",
            'total': shareEntries?.graphData.totalShares,
            'daily': 0,
            'color': Theme.of(context).customColors.blueColor,
          },
          {
            'label': "Impressions",
            'total': shareEntries?.graphData.totalImpressions,
            'daily': 0,
            'color': Theme.of(context).customColors.blueColor,
          },
        ];
        break;
      case "Like" || "Comments":
        isLoading = state.isFacebookPostLikeCommentShareLoading;
        bool isInteractions = widget.title == 'Like' ? true : false;
        final shareEntries = state.facebookPostGraphModel?.data;
        graphData = convertFacebookshareGraphData(shareEntries?.graphData.data ?? [],
            like: isInteractions ? true : false, comment: isInteractions ? false : true);
        totalPrimaryMetric = isInteractions
            ? shareEntries?.graphData.totalInteractions.toDouble() ?? 0
            : shareEntries?.graphData.totalPosts.toDouble() ?? 0;
        dailyPrimaryMetric = 0.0;
        primaryMetricLabel = isInteractions ? 'Like' : "Comment";
        secondaryMetricLabel = 'Impressions';
        tertiaryMetricLabel = 'Posts';
        onDateRangeSubmit = (startDate, endDate) {
          final formatter = DateFormat('yyyy-MM-dd');
          setState(() {
            endDateFormatted = formatter.format(endDate);
            startDateFormatted = formatter.format(startDate);
          });
          context.read<AnalyticsBloc>().add(
                FacebookPostLikeCommentShareAPI(
                  endDate: endDateFormatted,
                  startDate: startDateFormatted,
                ),
              );
        };
        secondaryMetricsData = [
          {
            'label': "Post",
            'total': shareEntries?.graphData.totalPosts,
            'daily': 0,
            'color': Theme.of(context).customColors.blueColor,
          },
          if (!isInteractions)
            {
              'label': "Likes",
              'total': shareEntries?.graphData.totalLikes,
              'daily': 0,
              'color': Theme.of(context).customColors.blueColor,
            },
          if (isInteractions)
            {
              'label': "Comments",
              'total': shareEntries?.graphData.totalComments,
              'daily': 0,
              'color': Theme.of(context).customColors.blueColor,
            },
          {
            'label': "Share",
            'total': shareEntries?.graphData.totalShares,
            'daily': 0,
            'color': Theme.of(context).customColors.blueColor,
          },
          {
            'label': "Impressions",
            'total': shareEntries?.graphData.totalImpressions,
            'daily': 0,
            'color': Theme.of(context).customColors.blueColor,
          },
        ];
        break;

      default:
        graphData = [];
    }
  }

  void _handleYoutubeCases(AnalyticsState state) {
    List<Map<String, dynamic>> convertYoutubeSubscribeLikeCommentData(List<Analytics> graphData,
        {bool isComment = false}) {
      List<Map<String, dynamic>> result = [];
      final formatter = DateFormat('dd-MM-yyyy');

      for (var entry in graphData) {
        DateTime parsedDate = DateTime.parse(entry.date);
        String formattedDate = formatter.format(parsedDate);
        final value = isComment ? entry.comments : entry.subscribersGained;
        result.add({formattedDate: value});
      }
      return result;
    }

    List<Map<String, dynamic>> convertYoutubeSubscribeData(List<Analytics> graphData,
        {bool isviews = false, bool isLike = false}) {
      List<Map<String, dynamic>> result = [];
      final formatter = DateFormat('dd-MM-yyyy');

      for (var entry in graphData) {
        // Parse the original date string
        DateTime parsedDate = DateTime.parse(entry.date);

        // Format to dd-MM-yyyy
        String formattedDate = formatter.format(parsedDate);
        final value = isviews
            ? entry.views
            : isLike
                ? entry.likes
                : entry.subscribersLost;
        result.add({formattedDate: value});
      }

      return result;
    }

    switch (widget.title) {
      case "Videos":
        isLoading = state.isYoutubeVideoGraphLoading;
        final shareEntries = state.youtubeVideoGraphModel?.data;
        graphData = convertFollowersInsightsToGraphData(
          shareEntries?.videoCountByDate ?? [],
        );
        totalPrimaryMetric = shareEntries?.totalVideos.toDouble() ?? 0;
        dailyPrimaryMetric = 0.0;
        primaryMetricLabel = "Videos";
        secondaryMetricLabel = 'Impressions';
        tertiaryMetricLabel = 'Posts';
        onDateRangeSubmit = (startDate, endDate) {
          final formatter = DateFormat('yyyy-MM-dd');
          setState(() {
            endDateFormatted = formatter.format(endDate);
            startDateFormatted = formatter.format(startDate);
          });
          context.read<AnalyticsBloc>().add(
                YoutubeVideoGraphAPI(
                  endDate: endDateFormatted,
                  startDate: startDateFormatted,
                ),
              );
        };
        secondaryMetricsData = [];

        break;
      case "Subscribers":
        isLoading = state.isYoutubeSubscribeLikeCommentLoading;
        final shareEntries = state.youtubeSubscribeLikeCommentModel?.data;
        graphData = convertYoutubeSubscribeLikeCommentData(
          shareEntries?.analytics ?? [],
        );
        graphData2 = convertYoutubeSubscribeData(
          shareEntries?.analytics ?? [],
        );
        totalPrimaryMetric = shareEntries?.total.subscribersGained.toDouble() ?? 0;
        dailyPrimaryMetric = 0.0;
        graphData2Lable = "Subscribers Lost";
        primaryMetricLabel = "Subscribers Gained";
        secondaryMetricLabel = 'Impressions';
        tertiaryMetricLabel = 'Posts';
        onDateRangeSubmit = (startDate, endDate) {
          final formatter = DateFormat('yyyy-MM-dd');
          setState(() {
            endDateFormatted = formatter.format(endDate);
            startDateFormatted = formatter.format(startDate);
          });
          context.read<AnalyticsBloc>().add(
                YoutubeSubscribeLikeCommentGraphAPI(endDate: endDateFormatted, startDate: startDateFormatted),
              );
        };
        secondaryMetricsData = [
          {
            'label': "Subscribers Lost",
            'total': shareEntries?.total.subscribersLost.toDouble() ?? 0,
            'daily': 0,
            'color': Color(0xff0077B5),
          },
          {
            'label': "Share",
            'total': shareEntries?.total.shares.toDouble() ?? 0,
            'daily': 0,
            'color': Color(0xff0077B5),
          },
          {
            'label': "Views",
            'total': shareEntries?.total.views.toDouble() ?? 0,
            'daily': 0,
            'color': Color(0xff0077B5),
          },
        ];

        break;
      case "Views":
        isLoading = state.isYoutubeSubscribeLikeCommentLoading;
        final shareEntries = state.youtubeSubscribeLikeCommentModel?.data;
        graphData = convertYoutubeSubscribeData(
          shareEntries?.analytics ?? [],
          isviews: true,
        );
        totalPrimaryMetric = shareEntries?.total.views.toDouble() ?? 0;
        dailyPrimaryMetric = 0.0;
        primaryMetricLabel = "Views";
        secondaryMetricLabel = 'Impressions';
        tertiaryMetricLabel = 'Posts';
        onDateRangeSubmit = (startDate, endDate) {
          final formatter = DateFormat('yyyy-MM-dd');
          setState(() {
            endDateFormatted = formatter.format(endDate);
            startDateFormatted = formatter.format(startDate);
          });
          context.read<AnalyticsBloc>().add(
                YoutubeSubscribeLikeCommentGraphAPI(
                  endDate: endDateFormatted,
                  startDate: startDateFormatted,
                ),
              );
        };
        secondaryMetricsData = [
          {
            'label': "Likes",
            'total': shareEntries?.total.likes.toDouble() ?? 0,
            'daily': 0,
            'color': Color(0xff0077B5),
          },
          {
            'label': "Comments",
            'total': shareEntries?.total.comments.toDouble() ?? 0,
            'daily': 0,
            'color': Color(0xff0077B5),
          },
          {
            'label': "shares",
            'total': shareEntries?.total.shares.toDouble() ?? 0,
            'daily': 0,
            'color': Color(0xff0077B5),
          },
        ];

        break;
      case "Likes, Comments":
        isLoading = state.isYoutubeSubscribeLikeCommentLoading;
        final shareEntries = state.youtubeSubscribeLikeCommentModel?.data;
        graphData = convertYoutubeSubscribeData(shareEntries?.analytics ?? [], isLike: true, isviews: false);
        graphData2 = convertYoutubeSubscribeLikeCommentData(shareEntries?.analytics ?? [], isComment: true);
        totalPrimaryMetric = shareEntries?.total.likes.toDouble() ?? 0;
        dailyPrimaryMetric = 0.0;
        primaryMetricLabel = "Likes";
        graphData2Lable = "Comments";
        secondaryMetricLabel = 'Impressions';
        tertiaryMetricLabel = 'Posts';
        onDateRangeSubmit = (startDate, endDate) {
          final formatter = DateFormat('yyyy-MM-dd');
          setState(() {
            endDateFormatted = formatter.format(endDate);
            startDateFormatted = formatter.format(startDate);
          });
          context.read<AnalyticsBloc>().add(
                YoutubeSubscribeLikeCommentGraphAPI(endDate: endDateFormatted, startDate: startDateFormatted),
              );
        };
        secondaryMetricsData = [
          {
            'label': "Comments",
            'total': shareEntries?.total.comments.toDouble() ?? 0,
            'daily': 0,
            'color': Color(0xff0077B5),
          },
          {
            'label': "View",
            'total': shareEntries?.total.views.toDouble() ?? 0,
            'daily': 0,
            'color': Color(0xff0077B5),
          },
          {
            'label': "shares",
            'total': shareEntries?.total.shares.toDouble() ?? 0,
            'daily': 0,
            'color': Color(0xff0077B5),
          },
        ];

        break;
    }
  }

  void _handleThreadCases(AnalyticsState state) {
    List<Map<String, dynamic>> convertYoutubeSubscribeLikeCommentData(List<ViewEntry> graphData,
        {bool isComment = false}) {
      List<Map<String, dynamic>> result = [];
      final formatter = DateFormat('dd-MM-yyyy');

      for (var entry in graphData) {
        // Parse the original date string
        DateTime parsedDate = DateTime.parse(entry.date);

        // Format to dd-MM-yyyy
        String formattedDate = formatter.format(parsedDate);
        // final value = isComment ? entry.comments : entry.subscribersGained;

        // Create a map with formatted date as key and follows as value
        result.add({formattedDate: entry.value});
      }

      return result;
    }

    switch (widget.title) {
      case "Views":
        isLoading = state.isThreadlinegraphLoading;
        final shareEntries = state.threadLineGraphModel?.data;
        graphData = convertYoutubeSubscribeLikeCommentData(shareEntries?.views ?? []);
        totalPrimaryMetric = shareEntries?.totalViews.toDouble() ?? 0.0;
        dailyPrimaryMetric = 0.0;
        primaryMetricLabel = "Views";
        onDateRangeSubmit = (startDate, endDate) {
          final formatter = DateFormat('yyyy-MM-dd');
          setState(() {
            endDateFormatted = formatter.format(endDate);
            startDateFormatted = formatter.format(startDate);
          });
          context.read<AnalyticsBloc>().add(
                ThreadLineGraphAPI(
                  endDate: endDateFormatted,
                  startDate: startDateFormatted,
                ),
              );
        };
        secondaryMetricsData = [];

        break;
    }
  }

  void _handlePinterestCases(AnalyticsState state) {
    List<Map<String, dynamic>> convertPinterestData(
      List<DailyData> graphData, {
      bool impressions = false,
      bool outpost = false,
      bool engagement = false,
    }) {
      final formatter = DateFormat('dd-MM-yyyy');
      return graphData.map((entry) {
        final formattedDate = formatter.format(DateTime.parse(entry.date));
        final value = outpost
            ? entry.outboundClick
            : impressions
                ? entry.impression
                : engagement
                    ? entry.engagement
                    : entry.pinClick;
        return {formattedDate: value};
      }).toList();
    }

    void setCommonDateRangeHandler() {
      final formatter = DateFormat('yyyy-MM-dd');
      onDateRangeSubmit = (startDate, endDate) {
        setState(() {
          startDateFormatted = formatter.format(startDate);
          endDateFormatted = formatter.format(endDate);
        });
        context.read<AnalyticsBloc>().add(
              PinterestLineGraphAPI(
                startDate: startDateFormatted,
                endDate: endDateFormatted,
              ),
            );
      };
    }

    final shareEntries = state.pinterestAnalyticsModel?.dailyData ?? [];
    final totalCounts = state.pinterestTotalCountModel?.data;

    isLoading = state.isPinterestLineGraphLoading;
    dailyPrimaryMetric = 0.0;

    switch (widget.title) {
      case "Pin Click":
        graphData = convertPinterestData(shareEntries);
        graphData2 = convertPinterestData(shareEntries, outpost: true);
        totalPrimaryMetric = state.pinterestAnalyticsModel?.pinClick.toDouble() ?? 0.0;
        primaryMetricLabel = "Pin Click";
        graphData2Lable = "OutBound Click";
        secondaryMetricLabel = "Impressions";
        tertiaryMetricLabel = "Posts";
        setCommonDateRangeHandler();
        secondaryMetricsData = [
          _metric("Followers", totalCounts?.followerCount),
          _metric("Following", totalCounts?.followingCount),
          _metric("Monthly Views", totalCounts?.monthlyViews),
        ];
        break;

      case "Impressions":
        graphData = convertPinterestData(shareEntries, impressions: true);
        totalPrimaryMetric = state.pinterestAnalyticsModel?.impression.toDouble() ?? 0.0;
        primaryMetricLabel = "Impressions";
        secondaryMetricLabel = "Impressions";
        tertiaryMetricLabel = "Posts";
        setCommonDateRangeHandler();
        secondaryMetricsData = [
          _metric("Pin Count", totalCounts?.pinCount),
          _metric("Followers", totalCounts?.followerCount),
          _metric("Following", totalCounts?.followingCount),
          _metric("Monthly Views", totalCounts?.monthlyViews),
        ];
        break;

      case "Engagement":
        graphData = convertPinterestData(shareEntries, engagement: true);
        totalPrimaryMetric = state.pinterestAnalyticsModel?.engagement.toDouble() ?? 0.0;
        primaryMetricLabel = "Engagement";
        setCommonDateRangeHandler();
        secondaryMetricsData = [
          _metric("Pin Count", totalCounts?.pinCount),
          _metric("Followers", totalCounts?.followerCount),
          _metric("Following", totalCounts?.followingCount),
          _metric("Monthly Views", totalCounts?.monthlyViews),
        ];
        break;
    }
  }

  Map<String, dynamic> _metric(String label, num? value) {
    return {
      'label': label,
      'total': value?.toDouble() ?? 0.0,
      'daily': 0,
      'color': const Color(0xff0077B5),
    };
  }
}
