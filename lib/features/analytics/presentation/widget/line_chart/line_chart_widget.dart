import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

class LinkedinLineChartWidget extends StatelessWidget {
  final List<FlSpot> data;
  final List<FlSpot>? data2;
  final List<Map<String, int>> dates;
  final String label;
  final String label2;
  final Color lineColor;
  final Color fillColor;

  const LinkedinLineChartWidget({
    super.key,
    required this.data,
    this.data2,
    required this.dates,
    this.label = '',
    this.label2 = '',
    this.lineColor = const Color(0xFF8D6E63),
    this.fillColor = const Color(0xFFD7CCC8),
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          LineChart(
            LineChartData(
              gridData: FlGridData(
                show: true,
                horizontalInterval: 10,
                drawVerticalLine: true,
                getDrawingHorizontalLine: (value) => FlLine(
                  color: Colors.grey.withOpacity(0.3),
                  strokeWidth: 1.w,
                ),
                getDrawingVerticalLine: (value) => FlLine(
                  color: Colors.grey.withOpacity(0.3),
                  strokeWidth: 1.w,
                ),
              ),
              titlesData: FlTitlesData(
                show: true,
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 30.h,
                    getTitlesWidget: (value, meta) {
                      int index = value.toInt();
                      if (index >= 0 && index < dates.length) {
                        return Text(
                          dates[index].entries.first.key.substring(0, 5),
                          style: TextStyle(color: Colors.black, fontSize: 10.sp),
                        );
                      } else {
                        return const Text('');
                      }
                    },
                  ),
                ),
                rightTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 40.w,
                  ),
                ),
                leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
              ),
              borderData: FlBorderData(show: false),
              minX: 0,
              minY: 0,
              maxY: data.every((spot) => spot.y == 0)
                  ? 10
                  : (data.map((spot) => spot.y).reduce((a, b) => a > b ? a : b)) + 10,
              lineBarsData: [
                LineChartBarData(
                  spots: data,
                  isCurved: true,
                  color: lineColor,
                  barWidth: 2.w,

                  // isStrokeCapRound: false,

                  // isStrokeCapRound: true,
                  // isStrokeJoinRound: false,
                  preventCurveOverShooting: true,
                  dotData: FlDotData(show: false),
                  belowBarData: BarAreaData(
                    show: true,
                    color: fillColor.withOpacity(0.5),
                  ),
                ),
                if (data2 != null && data2!.isNotEmpty)
                  LineChartBarData(
                    spots: data2 ?? [],
                    isCurved: true,
                    color: Colors.blue,
                    barWidth: 2.w,

                    // isStrokeCapRound: false,

                    // isStrokeCapRound: true,
                    // isStrokeJoinRound: false,
                    preventCurveOverShooting: true,
                    dotData: FlDotData(show: false),
                    belowBarData: BarAreaData(
                      show: true,
                      color: fillColor.withOpacity(0.5),
                    ),
                  ),
              ],
              lineTouchData: LineTouchData(
                touchTooltipData: LineTouchTooltipData(
                  getTooltipColor: (_) => lineColor,
                  getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                    return touchedBarSpots.map((barSpot) {
                      final date = _getDateLabel(barSpot.x);

                      // label based on which line the data belongs to
                      String datasetLabel = barSpot.barIndex == 0 ? label : label2;

                      return LineTooltipItem(
                        "${barSpot.barIndex == 0 ? "" : "\n"}$datasetLabel\n$date\n${barSpot.y.toStringAsFixed(2)}",
                        TextStyle(
                          color: Colors.white,
                          fontSize: 14.sp,
                        ),
                      );
                    }).toList();
                  },
                ),
                handleBuiltInTouches: true,
              ),
            ),
          ),
          Positioned(
            top: 0,
            right: 50.w,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(borderRadius: BorderRadius.circular(2.r), color: lineColor),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        label,
                        style: TextStyle(fontSize: 12.sp),
                      ),
                      const SizedBox(width: 8),
                    ],
                  ),
                  if (label2.isNotEmpty)
                    Row(
                      children: [
                        Container(
                          width: 12.w,
                          height: 12.h,
                          decoration: BoxDecoration(borderRadius: BorderRadius.circular(2.r), color: Colors.blue),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          label2,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 12.sp),
                        ),
                        const SizedBox(width: 8),
                      ],
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getDateLabel(double value) {
    int index = value.toInt();
    if (index >= 0 && index < dates.length) {
      String dateStr = dates[index].keys.first;
      try {
        final parts = dateStr.split('-');
        if (parts.length == 3) {
          final formattedDate = DateTime(
            int.parse(parts[2]), // Year
            int.parse(parts[1]), // Month
            int.parse(parts[0]), // Day
          );
          return DateFormat("MMM d").format(formattedDate);
        }
      } catch (e) {
        return dateStr;
      }
    }
    return '';
  }
}
