import 'package:flowkar/core/utils/exports.dart';

class AnalyticsShimmerScreen extends StatelessWidget {
  const AnalyticsShimmerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        margin: EdgeInsets.symmetric(horizontal: 16.0.w),
        padding: EdgeInsets.symmetric(horizontal: 11.0.w),
        decoration: BoxDecoration(
          color: Theme.of(context).customColors.white,
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              offset: Offset(0, 2),
              blurRadius: 14.r,
              spreadRadius: 0,
              color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
            )
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [1, 2, 3, 4, 5, 6].asMap().entries.map(
            (entry) {
              final index = entry.key;
              return Column(
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 12.0.h),
                    child: Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            height: 40.0.h,
                            width: 40.0.w,
                            decoration: const BoxDecoration(color: Colors.white, shape: BoxShape.circle),
                          ),
                          buildSizedBoxW(20),
                          Expanded(child: Container(height: 16.h, width: 100.w, color: Colors.white)),
                        ],
                      ),
                    ),
                  ),
                  if (index != [1, 2, 3, 4, 5, 6].length - 1) _buildDivider(context),
                ],
              );
            },
          ).toList()
            ..add(Column(children: [buildSizedBoxH(8.w)])),
        ),
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Divider(color: Theme.of(context).customColors.dividerColor.withOpacity(0.1), height: 1);
  }
}
