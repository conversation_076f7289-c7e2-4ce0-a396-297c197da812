import 'package:flowkar/core/utils/exports.dart';

class PostContainer extends StatelessWidget {
  final String postText;
  final String imageUrl;
  final List<String> platforms;
  final int maxVisibleIcons;
  final int totallike;
  final int totalComment;
  final int totalShare;
  final bool isPlanner;
  final bool status;

  const PostContainer({
    super.key,
    required this.postText,
    required this.imageUrl,
    required this.platforms,
    this.maxVisibleIcons = 7,
    required this.totalComment,
    required this.totalShare,
    required this.totallike,
    this.isPlanner = false,
    this.status = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: isPlanner ? 60.h : 90.h,
      margin: EdgeInsets.symmetric(
        vertical: isPlanner ? 0 : 8.h,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.white,
        borderRadius: BorderRadius.circular(14.r),
        border: Border.all(
          // width: 2,
          color: isPlanner ? Theme.of(context).primaryColor.withOpacity(0.5) : Theme.of(context).customColors.white,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).customColors.black.withOpacity(0.2),
            blurRadius: 2,
          )
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
              padding: EdgeInsets.only(left: 10.w),
              child: Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(100.r),
                    border: Border.all(
                      color: Theme.of(context).customColors.primaryColor.withOpacity(0.1),
                    )),
                child: CustomImageView(
                  height: isPlanner ? 30.h : 48.h,
                  width: isPlanner ? 30.h : 48.w,
                  radius: BorderRadius.circular(100.r),
                  fit: BoxFit.fill,
                  imagePath: _isVideo(imageUrl) ? Assets.images.svg.other.icPlayVideo.path : imageUrl,
                ),
              )),
          Flexible(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // if (postText.isNotEmpty)
                  Flexible(
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        textAlign: TextAlign.start,
                        postText.isNotEmpty ? postText : "No post discription",
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 12.sp, color: Colors.black),
                      ),
                    ),
                  ),
                  // Text(
                  //   textAlign: TextAlign.start,
                  //   "Scheduled At : ${scheduledDateTime.formatWithTimeOnly()}",
                  //   maxLines: 1,
                  //   overflow: TextOverflow.ellipsis,
                  //   style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 12.sp, color: Colors.black),
                  // ),
                  // buildSizedBoxH(8.0),
                  if (!isPlanner && platforms.isNotEmpty) _buildPlatformIcons(context),
                ],
              ),
            ),
          ),
          // if (!isPlanner)
          Padding(
            padding: EdgeInsets.only(right: 6.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                if (!isPlanner)
                  CustomElevatedButton(
                    alignment: Alignment.centerRight,
                    width: 80.w,
                    height: 38.h,
                    isDisabled: status ? false : true,
                    text: status ? "Posted" : "Scheduled",
                    // fontSize: 13.sp,
                    brderRadius: 8.r,

                    buttonTextStyle: Theme.of(context).textTheme.labelLarge?.copyWith(
                          fontWeight: FontWeight.w700,
                          fontSize: 13.0.sp,
                          color: status
                              ? Theme.of(context).customColors.white
                              : Theme.of(context).customColors.primaryColor,
                        ),
                  ),
                buildSizedBoxW(10.0),

                // _buildLikeCommentShareCount(
                //     imagePath: Assets.images.icons.homeFeed.icLikeFill.path,
                //     text: totallike.toString(),
                //     context: context),
                // _buildLikeCommentShareCount(
                //     imagePath: Assets.images.svg.other.icComment.path,
                //     text: totalComment.toString(),
                //     context: context),
                // _buildLikeCommentShareCount(
                //     imagePath: Assets.images.icons.homeFeed.icShare.path,
                //     text: totalShare.toString(),
                //     context: context),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget buildLikeCommentShareCount({required String imagePath, required String text, required BuildContext context}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 5.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomImageView(
            imagePath: imagePath,
            height: 16.h,
            width: 16.w,
          ),
          buildSizedBoxH(10),
          Text(
            text,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).primaryColor,
                  fontSize: 12.sp,
                ),
          ),
        ],
      ),
    );
  }

  bool _isVideo(String url) {
    final videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.flv'];
    return videoExtensions.any((ext) => url.toLowerCase().endsWith(ext));
  }

  Widget _buildPlatformIcons(BuildContext context) {
    int extraIconsCount = platforms.length > maxVisibleIcons ? platforms.length - maxVisibleIcons : 0;

    return Align(
      alignment: Alignment.bottomLeft,
      child: SizedBox(
        width: (16 + (maxVisibleIcons * 30)).w,
        height: 26.h,
        child: Stack(
          alignment: Alignment.centerLeft,
          children: [
            for (int i = 0; i < platforms.length; i++)
              if (i < maxVisibleIcons)
                Positioned(
                  left: (i * 20.0).w,
                  child: Container(
                    padding: EdgeInsets.all(3),
                    height: 26.h,
                    width: 26.h,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.black45),
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(50.r),
                    ),
                    child: Center(
                      child: CustomImageView(
                        height: 22.h,
                        fit: BoxFit.fill,
                        radius: BorderRadius.circular(100),
                        imagePath: _getPlatformIcon(platforms[i]),
                      ),
                    ),
                  ),
                ),
            if (extraIconsCount > 0)
              Positioned(
                left: (maxVisibleIcons * 20.0).w,
                child: GestureDetector(
                  onTap: () => _showPlatformPopup(context),
                  child: CircleAvatar(
                    radius: 13.sp,
                    backgroundColor: Colors.black26,
                    child: Text(
                      '+$extraIconsCount',
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showPlatformPopup(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Platforms Posted'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: platforms
                .map((platform) => ListTile(
                      leading: CustomImageView(
                        imagePath: _getPlatformIcon(platform),
                        width: 24.w,
                        height: 24.h,
                      ),
                      title: Text(platform),
                    ))
                .toList(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            )
          ],
        );
      },
    );
  }

  String _getPlatformIcon(String platform) {
    switch (platform.toLowerCase()) {
      case 'vimeo':
        return Assets.images.icons.social.v.path;
      case 'instagram':
        return Assets.images.icons.social.insta.path;
      case 'linkedin':
        return Assets.images.icons.social.linkedin.path;
      case 'pinterest':
        return Assets.images.icons.social.icPintrest.path;
      case 'reddit':
        return Assets.images.icons.social.icReddit.path;
      case 'tumblr':
        return Assets.images.icons.social.icTumblr.path;
      case 'thread':
        return Assets.images.icons.social.icThread.path;
      case 'facebook':
        return Assets.images.icons.social.facebook.path;
      case 'tiktok':
        return Assets.images.icons.social.icTictok.path;
      case 'youtube':
        return Assets.images.icons.social.icYoutube.path;
      case 'x':
        return Assets.images.icons.social.twitter.path;
      case 'blusky':
        return Assets.images.icons.social.svgBluesky.path;
      default:
        return Assets.images.pngs.flowkar.path;
    }
  }
}
