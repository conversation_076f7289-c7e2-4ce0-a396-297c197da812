import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/widgets/common/coming_soon_screen.dart';

Widget buildLinkedinDataAccountList(BuildContext context, String? socialName) {
  return Column(
    children: [
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Account",
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).customColors.black,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
            ),
            buildSizedBoxH(14.h),
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(14.r),
                boxShadow: [
                  BoxShadow(
                    offset: Offset(0, 2),
                    blurRadius: 16.r,
                    spreadRadius: 0,
                    color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSeetingsWidget(
                    context,
                    text: "Followers",
                    padding: EdgeInsets.all(6.0),
                    onTap: () {
                      PersistentNavBarNavigator.pushNewScreen(context,
                          screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                    },
                  ),
                  Divider(color: Theme.of(context).customColors.white.withOpacity(0.2), height: 1),
                  _buildSeetingsWidget(
                    context,
                    text: "Impressions",
                    padding: EdgeInsets.all(6.0),
                    onTap: () {
                      PersistentNavBarNavigator.pushNewScreen(context,
                          screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                    },
                  ),
                  Divider(color: Theme.of(context).customColors.white.withOpacity(0.2), height: 1),
                  _buildSeetingsWidget(
                    context,
                    text: "Demographic Data",
                    padding: EdgeInsets.all(6.0),
                    onTap: () {
                      PersistentNavBarNavigator.pushNewScreen(context,
                          screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      buildSizedBoxH(20),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Post",
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).customColors.black,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
            ),
            buildSizedBoxH(14.h),
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(14.r),
                boxShadow: [
                  BoxShadow(
                    offset: Offset(0, 2),
                    blurRadius: 16.r,
                    spreadRadius: 0,
                    color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSeetingsWidget(
                    context,
                    text: "Posts",
                    padding: EdgeInsets.all(6.0),
                    onTap: () {
                      PersistentNavBarNavigator.pushNewScreen(context,
                          screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                    },
                  ),
                  Divider(color: Theme.of(context).customColors.white.withOpacity(0.2), height: 1),
                  _buildSeetingsWidget(
                    context,
                    text: "Engagement & Share",
                    padding: EdgeInsets.all(6.0),
                    onTap: () {
                      PersistentNavBarNavigator.pushNewScreen(context,
                          screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ],
  );
}

Widget buildInstagramDataAccountList(BuildContext context, String? socialName) {
  return Column(
    children: [
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Account",
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).customColors.black,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
            ),
            buildSizedBoxH(14.h),
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(14.r),
                boxShadow: [
                  BoxShadow(
                    offset: Offset(0, 2),
                    blurRadius: 16.r,
                    spreadRadius: 0,
                    color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSeetingsWidget(
                    context,
                    text: "Followers & Followings",
                    padding: EdgeInsets.all(6.0),
                    onTap: () {
                      PersistentNavBarNavigator.pushNewScreen(context,
                          screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                    },
                  ),
                  Divider(color: Theme.of(context).customColors.white.withOpacity(0.2), height: 1),
                  _buildSeetingsWidget(
                    context,
                    text: "Impressions",
                    padding: EdgeInsets.all(6.0),
                    onTap: () {
                      PersistentNavBarNavigator.pushNewScreen(context,
                          screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                    },
                  ),
                  Divider(color: Theme.of(context).customColors.white.withOpacity(0.2), height: 1),
                  _buildSeetingsWidget(
                    context,
                    text: "Reach",
                    padding: EdgeInsets.all(6.0),
                    onTap: () {
                      PersistentNavBarNavigator.pushNewScreen(context,
                          screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                    },
                  ),
                  Divider(color: Theme.of(context).customColors.white.withOpacity(0.2), height: 1),
                  _buildSeetingsWidget(
                    context,
                    text: "Profile Visits",
                    padding: EdgeInsets.all(6.0),
                    onTap: () {
                      PersistentNavBarNavigator.pushNewScreen(context,
                          screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                    },
                  ),
                  Divider(color: Theme.of(context).customColors.white.withOpacity(0.2), height: 1),
                  _buildSeetingsWidget(
                    context,
                    text: "Demographic Data",
                    padding: EdgeInsets.all(6.0),
                    onTap: () {
                      PersistentNavBarNavigator.pushNewScreen(context,
                          screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      buildSizedBoxH(20),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Post",
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).customColors.black,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
            ),
            buildSizedBoxH(14.h),
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(14.r),
                boxShadow: [
                  BoxShadow(
                    offset: Offset(0, 2),
                    blurRadius: 16.r,
                    spreadRadius: 0,
                    color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSeetingsWidget(
                    context,
                    text: "Posts",
                    padding: EdgeInsets.all(6.0),
                    onTap: () {
                      PersistentNavBarNavigator.pushNewScreen(context,
                          screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ],
  );
}

Widget buildFacebookDataAccountList(BuildContext context, String? socialName) {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 16.0),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Account",
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).customColors.black,
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
        ),
        buildSizedBoxH(14.h),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(14.r),
            boxShadow: [
              BoxShadow(
                offset: Offset(0, 2),
                blurRadius: 16.r,
                spreadRadius: 0,
                color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSeetingsWidget(
                context,
                text: "Followers",
                padding: EdgeInsets.all(6.0),
                onTap: () {
                  PersistentNavBarNavigator.pushNewScreen(context,
                      screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                },
              ),
              Divider(color: Theme.of(context).customColors.white.withOpacity(0.2), height: 1),
              _buildSeetingsWidget(
                context,
                text: "Impressions",
                padding: EdgeInsets.all(6.0),
                onTap: () {
                  PersistentNavBarNavigator.pushNewScreen(context,
                      screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                },
              ),
              Divider(color: Theme.of(context).customColors.white.withOpacity(0.2), height: 1),
              _buildSeetingsWidget(
                context,
                text: "Page Summary",
                padding: EdgeInsets.all(6.0),
                onTap: () {
                  PersistentNavBarNavigator.pushNewScreen(context,
                      screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                },
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

Widget buildYouTubeDataAccountList(BuildContext context, String? socialName) {
  return Column(
    children: [
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Account",
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).customColors.black,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
            ),
            buildSizedBoxH(14.h),
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(14.r),
                boxShadow: [
                  BoxShadow(
                    offset: Offset(0, 2),
                    blurRadius: 16.r,
                    spreadRadius: 0,
                    color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSeetingsWidget(
                    context,
                    text: "Subscribers",
                    padding: EdgeInsets.all(6.0),
                    onTap: () {
                      PersistentNavBarNavigator.pushNewScreen(context,
                          screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      buildSizedBoxH(20),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Post",
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).customColors.black,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
            ),
            buildSizedBoxH(14.h),
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(14.r),
                boxShadow: [
                  BoxShadow(
                    offset: Offset(0, 2),
                    blurRadius: 16.r,
                    spreadRadius: 0,
                    color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSeetingsWidget(
                    context,
                    text: "Videos",
                    padding: EdgeInsets.all(6.0),
                    onTap: () {
                      PersistentNavBarNavigator.pushNewScreen(context,
                          screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                    },
                  ),
                  Divider(color: Theme.of(context).customColors.white.withOpacity(0.2), height: 1),
                  _buildSeetingsWidget(
                    context,
                    text: "Views, Likes, Comments",
                    padding: EdgeInsets.all(6.0),
                    onTap: () {
                      PersistentNavBarNavigator.pushNewScreen(context,
                          screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ],
  );
}

Widget buildPinterestDataAccountList(BuildContext context, String? socialName) {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 16.0),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Post",
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).customColors.black,
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
        ),
        buildSizedBoxH(14.h),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(14.r),
            boxShadow: [
              BoxShadow(
                offset: Offset(0, 2),
                blurRadius: 16.r,
                spreadRadius: 0,
                color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSeetingsWidget(
                context,
                text: "Posts",
                padding: EdgeInsets.all(6.0),
                onTap: () {
                  PersistentNavBarNavigator.pushNewScreen(context,
                      screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                },
              ),
              Divider(color: Theme.of(context).customColors.white.withOpacity(0.2), height: 1),
              _buildSeetingsWidget(
                context,
                text: "Likes, Comments",
                padding: EdgeInsets.all(6.0),
                onTap: () {
                  PersistentNavBarNavigator.pushNewScreen(context,
                      screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                },
              ),
              Divider(color: Theme.of(context).customColors.white.withOpacity(0.2), height: 1),
              _buildSeetingsWidget(
                context,
                text: "Views",
                padding: EdgeInsets.all(6.0),
                onTap: () {
                  PersistentNavBarNavigator.pushNewScreen(context,
                      screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                },
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

Widget buildThreadsDataAccountList(BuildContext context, String? socialName) {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 16.0),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Account",
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).customColors.black,
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
        ),
        buildSizedBoxH(14.h),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(14.r),
            boxShadow: [
              BoxShadow(
                offset: Offset(0, 2),
                blurRadius: 16.r,
                spreadRadius: 0,
                color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSeetingsWidget(
                context,
                text: "Views",
                padding: EdgeInsets.all(6.0),
                onTap: () {
                  PersistentNavBarNavigator.pushNewScreen(context,
                      screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                },
              ),
              Divider(color: Theme.of(context).customColors.white.withOpacity(0.2), height: 1),
              _buildSeetingsWidget(
                context,
                text: "Followers by Country",
                padding: EdgeInsets.all(6.0),
                onTap: () {
                  PersistentNavBarNavigator.pushNewScreen(context,
                      screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                },
              ),
              Divider(color: Theme.of(context).customColors.white.withOpacity(0.2), height: 1),
              _buildSeetingsWidget(
                context,
                text: "Followers by City",
                padding: EdgeInsets.all(6.0),
                onTap: () {
                  PersistentNavBarNavigator.pushNewScreen(context,
                      screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                },
              ),
              Divider(color: Theme.of(context).customColors.white.withOpacity(0.2), height: 1),
              _buildSeetingsWidget(
                context,
                text: "Followers by Age",
                padding: EdgeInsets.all(6.0),
                onTap: () {
                  PersistentNavBarNavigator.pushNewScreen(context,
                      screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                },
              ),
              Divider(color: Theme.of(context).customColors.white.withOpacity(0.2), height: 1),
              _buildSeetingsWidget(
                context,
                text: "Followers by Gender",
                padding: EdgeInsets.all(6.0),
                onTap: () {
                  PersistentNavBarNavigator.pushNewScreen(context,
                      screen: ComingSoonScreen(title: socialName ?? "Coming Soon"));
                },
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

Widget _buildSeetingsWidget(BuildContext context,
    {required String text, Function()? onTap, EdgeInsetsGeometry? padding}) {
  return InkWell(
    onTap: onTap,
    child: Padding(
      padding: EdgeInsets.symmetric(vertical: 14.0.h, horizontal: 12.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Text(
                text,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w500,
                      fontSize: 14.sp,
                      color: Theme.of(context).customColors.white,
                    ),
              ),
            ],
          ),
          CustomImageView(
            height: 12.h,
            margin: EdgeInsets.symmetric(horizontal: 14.0.w),
            imagePath: Assets.images.svg.setting.svgRightArrow.path,
            color: Theme.of(context).customColors.white,
          ),
        ],
      ),
    ),
  );
}
