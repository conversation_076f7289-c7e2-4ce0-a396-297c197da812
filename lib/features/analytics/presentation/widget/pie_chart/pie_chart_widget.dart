import 'package:flowkar/core/utils/exports.dart';

class ChartSection extends StatelessWidget {
  final String title;
  final List<ChartItem> chartData;
  final bool isLoading;

  const ChartSection({
    super.key,
    required this.title,
    required this.chartData,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    final adjustedChartData = _getAdjustedChartData(chartData);

    return Padding(
      padding: EdgeInsets.all(16.0.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "$title :",
            style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w700),
          ),
          buildSizedBoxH(16.0),
          SizedBox(
            height: 200,
            child: Row(
              children: [
                Expanded(
                  flex: 4,
                  child: Align(
                      alignment: Alignment.centerLeft,
                      child: _buildCustomPieChart(context, chartData: adjustedChartData)),
                ),
              ],
            ),
          ),
          _buildChartLegend(context, chartData: adjustedChartData),
          const SizedBox(height: 16),
          Divider(color: Theme.of(context).customColors.dividerColor.withOpacity(0.1), height: 1),
        ],
      ),
    );
  }

  /// 🧠 Function to calculate and add "Other" if needed
  // List<ChartItem> _getAdjustedChartData(List<ChartItem> originalData) {
  //   double total = originalData.fold(0, (sum, item) => sum + item.percentage);

  //   if (total < 100) {
  //     double otherPercentage = 100 - total;
  //     return [
  //       ...originalData,
  //       ChartItem(name: 'Other', percentage: otherPercentage, overrideColor: Colors.grey),
  //     ];
  //   }
  //   return originalData;
  // }
  /// 🧠 Function to calculate and add "Other" if needed
  List<ChartItem> _getAdjustedChartData(List<ChartItem> originalData) {
    double total = originalData.fold(0, (sum, item) => sum + item.percentage);

    // Create a list to collect items with percentage greater than or equal to 2%
    List<ChartItem> adjustedData = originalData.where((item) => item.percentage >= 2.0).toList();

    // Calculate the remaining percentage to be added to "Other"
    double otherPercentage = originalData.fold(0, (sum, item) => item.percentage < 2.0 ? sum + item.percentage : sum);

    // If the total is less than 100%, add "Other" category
    if (total < 100) {
      otherPercentage += (100 - total);
    }

    // Add "Other" if there is any percentage to add
    if (otherPercentage > 0) {
      adjustedData.add(
        ChartItem(
          name: 'Other',
          percentage: otherPercentage,
          overrideColor: Colors.grey,
        ),
      );
    }

    return adjustedData;
  }

  Widget _buildCustomPieChart(BuildContext context, {required List<ChartItem> chartData}) {
    return PieChart(
      PieChartData(
        sectionsSpace: 1,
        centerSpaceRadius: 40,
        sections: chartData
            .map((item) => PieChartSectionData(
                  color: item.color,
                  value: item.percentage,
                  showTitle: false,
                  title: '${item.percentage.toStringAsFixed(0)}%',
                  radius: 40,
                  titleStyle: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildChartLegend(BuildContext context, {required List<ChartItem> chartData}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: chartData
          .map((item) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: Row(
                  children: [
                    Container(
                      width: 16.w,
                      height: 16.h,
                      decoration: BoxDecoration(
                        color: item.color,
                        shape: BoxShape.circle,
                      ),
                    ),
                    buildSizedBoxW(8),
                    Expanded(
                      child: Text(
                        item.name,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(fontSize: 12.sp),
                      ),
                    ),
                    Text(
                      '${item.percentage.toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: item.color,
                      ),
                    ),
                  ],
                ),
              ))
          .toList(),
    );
  }
}

/// 🧱 Updated ChartItem class
class ChartItem {
  final String name;
  final double percentage;
  final Color? overrideColor;

  ChartItem({
    required this.name,
    required this.percentage,
    this.overrideColor,
  });

  // Color logic with optional override
  Color get color {
    if (overrideColor != null) return overrideColor!;

    // Fixed color assignment based on percentage ranges
    if (percentage < 2.0) {
      return Colors.grey; // "Other" category
    } else if (percentage < 10) {
      return Colors.red;
    } else if (percentage < 20) {
      return Colors.orange;
    } else if (percentage < 30) {
      return Colors.yellow;
    } else if (percentage < 40) {
      return Colors.green;
    } else if (percentage < 50) {
      return Colors.blue;
    } else if (percentage < 60) {
      return Colors.purple;
    } else if (percentage < 70) {
      return Colors.cyan;
    } else if (percentage < 80) {
      return Colors.indigo;
    } else if (percentage < 90) {
      return Colors.teal;
    } else {
      return Color(0XFF563D39);
    }
  }
}
