import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/sm_chat/model/flowkar_chat_model/search_user_model.dart';
import 'package:flowkar/features/sm_chat/presentation/flowkar_chat_screen.dart';
import 'package:flowkar/features/sm_chat/presentation/sm_chat_screen.dart';
import 'package:flowkar/features/widgets/custom/custom_transition.dart';

class FlowkarSearchUserListWidget extends StatefulWidget {
  final List<SearchUserData> userList;
  final TextEditingController searchcontroller;
  final void Function() onclose;
  const FlowkarSearchUserListWidget(
      {super.key, this.userList = const [], required this.searchcontroller, required this.onclose});

  @override
  State<FlowkarSearchUserListWidget> createState() => _FlowkarSearchUserListWidgetState();
}

class _FlowkarSearchUserListWidgetState extends State<FlowkarSearchUserListWidget> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themestate) {
        return Column(
          children: [
            Expanded(
              child: widget.userList.isEmpty
                  ? _buildNoChatListFound(themestate)
                  : _buildUserList(themestate, widget.userList),
            ),
          ],
        );
      },
    );
  }

  Widget _buildUserList(
    ThemeState themestate,
    List<SearchUserData> userList,
  ) {
    return ShowUpTransition(
      forward: true,
      delay: const Duration(milliseconds: 300),
      child: ListView.builder(
        padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom + 80.h),
        shrinkWrap: true,
        itemCount: userList.length,
        itemBuilder: (context, index) {
          return _buildUserDetail(context, themestate, index, userList);
        },
      ),
    );
  }

  Widget _buildUserDetail(BuildContext context, ThemeState themestate, int index, List<SearchUserData> userList) {
    return InkWell(
      onTap: () {
        FocusScope.of(context).unfocus();
        widget.searchcontroller.clear();
        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: FlowkarChatScreen(
            args: {
              'item': userList[index],
              'callback': () {
                widget.onclose();
              }
            },
          ),
          withNavBar: false,
          customPageRoute: CustomCupertinoPageRoute(
            builder: (context) => FlowkarChatScreen(
              args: {
                'item': userList[index],
                'callback': () {
                  widget.onclose();
                }
              },
            ),
          ),
        );
        // NavigatorService.pushNamed(AppRoutes.flowkarChatscreen, arguments: {
        //   'item': userList[index],
        //   'callback': () {
        //     widget.onclose();
        //   }
        // });
      },
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.0.w, vertical: 8.0.w),
        child: Container(
          decoration:
              BoxDecoration(color: ThemeData().customColors.messagecolor, borderRadius: BorderRadius.circular(10.0.r)),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 14.0.w, vertical: 8.0.h),
            child: Row(
              children: [
                ClipRRect(
                  clipBehavior: Clip.hardEdge,
                  child: Padding(
                    padding: const EdgeInsets.all(4.0),
                    child: CustomImageView(
                      radius: BorderRadius.circular(30.r),
                      height: 60.0.h,
                      width: 60.0.w,
                      fit: BoxFit.cover,
                      imagePath: userList[index].profileImage == null || userList[index].profileImage == ""
                          ? AssetConstants.pngUserReomve
                          : "${APIConfig.mainbaseURL}${userList[index].profileImage}",
                      alignment: Alignment.center,
                    ),
                  ),
                ),
                buildSizedBoxW(8),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        userList[index].name ?? '',
                        style: Theme.of(context)
                            .textTheme
                            .titleLarge!
                            .copyWith(fontSize: 13.sp, fontWeight: FontWeight.w700),
                      ),
                      buildSizedBoxH(6.0),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Flexible(
                            child: Text(
                              userList[index].userName ?? '',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context).textTheme.headlineSmall!.copyWith(fontSize: 12.sp),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNoChatListFound(ThemeState themestate) {
    return Center(
        child: ExceptionWidget(
      imagePath: AssetConstants.pngNoResultFound,
      title: Lang.of(context).lbl_no_result_found,
      subtitle: Lang.of(context).lbl_no_result_found_subtitle,
      showButton: false,
    ));
  }
}
