import 'package:flowkar/core/utils/exports.dart';

class ChatDetailShimmer extends StatelessWidget {
  const ChatDetailShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ListView.builder(
            shrinkWrap: true,
            // physics: const NeverScrollableScrollPhysics(),
            itemCount: 10,
            reverse: true,
            itemBuilder: (context, index) {
              final isReceivedMessage = index % 2 == 0;

              return Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 10.0),
                    child: Align(
                      alignment: isReceivedMessage ? Alignment.centerLeft : Alignment.centerRight,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (isReceivedMessage)
                            Container(
                              height: 40.0.h,
                              width: 40.0.w,
                              margin: const EdgeInsets.only(right: 8),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                            ),
                          Container(
                            constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.75),
                            padding: EdgeInsets.only(
                              left: 16.w,
                              top: 10.h,
                              bottom: 10.h,
                              right: !isReceivedMessage ? 22.w : 16.w,
                            ),
                            margin: EdgeInsets.fromLTRB(5.w, 0.h, 6.w, 2.h),
                            decoration: BoxDecoration(
                              color: ThemeData().customColors.messagecolor,
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(!isReceivedMessage ? 10.r : 0.r),
                                topRight: Radius.circular(10.r),
                                bottomRight: Radius.circular(!isReceivedMessage ? 0.r : 10.r),
                                bottomLeft: Radius.circular(10.r),
                              ),
                            ),
                            child: SizedBox(
                              width: 80 + (index * 20) % 100,
                              height: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
