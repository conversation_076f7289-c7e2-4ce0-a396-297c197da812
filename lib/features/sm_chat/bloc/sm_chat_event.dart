part of 'sm_chat_bloc.dart';

sealed class SmChatEvent extends Equatable {
  const SmChatEvent();

  @override
  List<Object> get props => [];
}

class SmChatInitialEvent extends SmChatEvent {
  @override
  List<Object> get props => [];
}

class GetSmchatListEvent extends SmChatEvent {
  @override
  List<Object> get props => [];
}

class GetsinglechatdetailsEvent extends SmChatEvent {
  final int platformId;
  final String conversationId;
  final String ownerId;
  const GetsinglechatdetailsEvent({required this.platformId, required this.conversationId, required this.ownerId});
  @override
  List<Object> get props => [platformId, conversationId, ownerId];
}

class SendInstaMessageEvent extends SmChatEvent {
  final String? fromuserId;
  final String? touserId;
  final int? messageType;
  final String? message;
  final File? file;
  const SendInstaMessageEvent(
      {required this.fromuserId,
      required this.touserId,
      required this.messageType,
      required this.message,
      required this.file});
  @override
  List<Object> get props => [];
}

class SendFacebookMessageEvent extends SmChatEvent {
  final String? fromuserId;
  final String? touserId;
  final int? messageType;
  final String? message;
  final File? file;
  const SendFacebookMessageEvent(
      {required this.fromuserId,
      required this.touserId,
      required this.messageType,
      required this.message,
      required this.file});
  @override
  List<Object> get props => [];
}

class SendMessageEvent extends SmChatEvent {
  final int touserId;
  final String message;
  final String type;
  final String file;
  const SendMessageEvent({required this.touserId, required this.message, required this.type, required this.file});

  @override
  List<Object> get props => [touserId, message, type, file];
}

class GetChatListEvent extends SmChatEvent {
  final int page;
  final bool isReload;

  const GetChatListEvent({required this.page, required this.isReload});
  @override
  List<Object> get props => [page, isReload];
}

class GetChatMessageListEvent extends SmChatEvent {
  final int page;
  final String userId;
  const GetChatMessageListEvent({required this.page, required this.userId});
  @override
  List<Object> get props => [page, userId];
}

class UpdateChatMessageSocketEvent extends SmChatEvent {
  final int id;
  final String message;
  final String type;
  final String createdat;
  final int sentby;
  const UpdateChatMessageSocketEvent(
      {required this.id, required this.message, required this.type, required this.createdat, required this.sentby});
  @override
  List<Object> get props => [id, message, type, createdat, sentby];
}

class TypingSocketEvent extends SmChatEvent {
  final int userId;
  final String isTyping;

  const TypingSocketEvent({required this.userId, required this.isTyping});
  @override
  List<Object> get props => [userId, isTyping];
}

class SearchUserListEvent extends SmChatEvent {
  final String searchtext;
  const SearchUserListEvent({required this.searchtext});
  @override
  List<Object> get props => [searchtext];
}

class ChatListMessageEvent extends SmChatEvent {
  final int page;
  const ChatListMessageEvent({required this.page});

  @override
  List<Object> get props => [page];
}

class DeleteChatApiEvent extends SmChatEvent {
  final int userId;

  const DeleteChatApiEvent({required this.userId});
  @override
  List<Object> get props => [userId];
}

class RefreshChatGetApiEvent extends SmChatEvent {
  final String userId;

  const RefreshChatGetApiEvent({required this.userId});

  @override
  List<Object> get props => [userId];
}

class FetchDeepLinkChatEvent extends SmChatEvent {
  final BuildContext context;
  final String reciverId;
  final dynamic args;

  const FetchDeepLinkChatEvent(this.context, {required this.reciverId, this.args});

  @override
  List<Object> get props => [context, reciverId, args];
}

class GetTelegramUerListAPI extends SmChatEvent {
  const GetTelegramUerListAPI();
}

class GetTelegramUerChatListAPI extends SmChatEvent {
  final String targetUserId;
  const GetTelegramUerChatListAPI({required this.targetUserId});
  @override
  List<Object> get props => [targetUserId];
}

class TelegramSendMassage extends SmChatEvent {
  final String chatId;
  final String message;
  const TelegramSendMassage({required this.message, required this.chatId});
  @override
  List<Object> get props => [message, chatId];
}

class AIgenerateMassageEvent extends SmChatEvent {
  final String massageText;
  final BuildContext context;
  const AIgenerateMassageEvent({required this.massageText, required this.context});

  @override
  List<Object> get props => [massageText, context];
}

class ClearAIGeneratedMassageEvent extends SmChatEvent {}

class DeleteMessageEvent extends SmChatEvent {
  final int messageId;

  const DeleteMessageEvent({required this.messageId});

  @override
  List<Object> get props => [messageId];
}

class RemoveDeletedMessageEvent extends SmChatEvent {
  final int messageId;

  const RemoveDeletedMessageEvent({required this.messageId});

  @override
  List<Object> get props => [messageId];
}

class UpdateChatListEvent extends SmChatEvent {
  final List<FlowkarChatData> chatList;

  const UpdateChatListEvent({required this.chatList});

  @override
  List<Object> get props => [chatList];
}
