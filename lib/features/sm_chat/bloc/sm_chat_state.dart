part of 'sm_chat_bloc.dart';

class SmChatState extends Equatable {
  final bool isloding;
  final SmchatListModel? smchatListModel;
  final bool issinglechatloding;
  final SingleChatModel? singleChatModel;
  final SendinstagreamMessageModel? sendinstagreamMessageModel;
  final List<FlowkarChatData> chatList;
  final List<SearchUserData> searchuserList;
  final bool searchuserListLoading;
  final List<ChatMessageData> chatMessageList;
  final bool isLoadingMore;
  final ResetPasswordResponseModel? deletechatmodel;
  final ScrollController? scrollController;
  final ChatListModel? chatListModel;
  final bool hasMore;
  final int page;
  final bool allFetch;
  final TextEditingController? chatController;
  final TextEditingController? searchController;
  final ChatMessageListModel? chatMessageListModel;
  final DeepLinkChatModel? deepLinkChatModel;
  final TelegramUserListModel? telegramUserListModel;
  final TelegramUserChatListModel? telegramUserChatListModel;
  final TelegramSandMassageModel? telegramSendMassageModel;
  final bool isAIGeneratingMassageloading;
  final AiGenerateMassageOrComment? aiGenerateMassageOrCommentModel;

  const SmChatState({
    this.isloding = false,
    this.smchatListModel,
    this.issinglechatloding = false,
    this.singleChatModel,
    this.sendinstagreamMessageModel,
    this.chatList = const [],
    this.searchuserList = const [],
    this.isLoadingMore = false,
    this.scrollController,
    this.hasMore = false,
    this.searchuserListLoading = false,
    this.page = 1,
    this.allFetch = false,
    this.deletechatmodel,
    this.chatController,
    this.chatListModel,
    this.chatMessageListModel,
    this.chatMessageList = const [],
    this.searchController,
    this.deepLinkChatModel,
    this.telegramUserListModel,
    this.telegramUserChatListModel,
    this.telegramSendMassageModel,
    this.isAIGeneratingMassageloading = false,
    this.aiGenerateMassageOrCommentModel,
  });

  @override
  List<Object?> get props => [
        isloding,
        smchatListModel,
        issinglechatloding,
        singleChatModel,
        sendinstagreamMessageModel,
        chatList,
        searchuserList,
        isLoadingMore,
        scrollController,
        hasMore,
        searchuserListLoading,
        deletechatmodel,
        page,
        allFetch,
        chatController,
        chatListModel,
        chatMessageListModel,
        chatMessageList,
        searchController,
        deepLinkChatModel,
        telegramUserListModel,
        telegramUserChatListModel,
        telegramSendMassageModel,
        isAIGeneratingMassageloading,
        aiGenerateMassageOrCommentModel,
      ];

  SmChatState copyWith({
    bool? isloding,
    SmchatListModel? smchatListModel,
    bool? issinglechatloding,
    SingleChatModel? singleChatModel,
    SendinstagreamMessageModel? sendinstagreamMessageModel,
    List<FlowkarChatData>? chatList,
    List<SearchUserData>? searchuserList,
    bool? isLoadingMore,
    ScrollController? scrollController,
    ChatListModel? chatListModel,
    bool? hasMore,
    bool? searchuserListLoading,
    int? page,
    ResetPasswordResponseModel? deletechatmodel,
    bool? allFetch,
    TextEditingController? chatController,
    ChatMessageListModel? chatMessageListModel,
    List<ChatMessageData>? chatMessageList,
    TextEditingController? searchController,
    DeepLinkChatModel? deepLinkChatModel,
    TelegramUserListModel? telegramUserListModel,
    TelegramUserChatListModel? telegramUserChatListModel,
    TelegramSandMassageModel? telegramSendMassageModel,
    bool? isAIGeneratingMassageloading,
    AiGenerateMassageOrComment? aiGenerateMassageOrCommentModel,
  }) {
    return SmChatState(
      isloding: isloding ?? this.isloding,
      smchatListModel: smchatListModel ?? this.smchatListModel,
      issinglechatloding: issinglechatloding ?? this.issinglechatloding,
      singleChatModel: singleChatModel ?? this.singleChatModel,
      sendinstagreamMessageModel: sendinstagreamMessageModel ?? this.sendinstagreamMessageModel,
      deletechatmodel: deletechatmodel ?? this.deletechatmodel,
      allFetch: allFetch ?? this.allFetch,
      chatList: chatList ?? this.chatList,
      searchuserList: searchuserList ?? this.searchuserList,
      hasMore: hasMore ?? this.hasMore,
      searchuserListLoading: searchuserListLoading ?? this.searchuserListLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      page: page ?? this.page,
      scrollController: scrollController ?? this.scrollController,
      chatListModel: chatListModel ?? this.chatListModel,
      chatController: chatController ?? this.chatController,
      chatMessageListModel: chatMessageListModel ?? this.chatMessageListModel,
      chatMessageList: chatMessageList ?? this.chatMessageList,
      searchController: searchController ?? this.searchController,
      deepLinkChatModel: deepLinkChatModel ?? this.deepLinkChatModel,
      telegramUserListModel: telegramUserListModel ?? this.telegramUserListModel,
      telegramUserChatListModel: telegramUserChatListModel ?? this.telegramUserChatListModel,
      telegramSendMassageModel: telegramSendMassageModel ?? this.telegramSendMassageModel,
      isAIGeneratingMassageloading: isAIGeneratingMassageloading ?? this.isAIGeneratingMassageloading,
      aiGenerateMassageOrCommentModel: aiGenerateMassageOrCommentModel ?? this.aiGenerateMassageOrCommentModel,
    );
  }
}
