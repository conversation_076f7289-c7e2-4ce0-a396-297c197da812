import 'dart:convert';
import 'dart:io';

import 'package:flowkar/core/utils/date_time_utils.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_manager.dart';
import 'package:flowkar/features/sm_chat/bloc/sm_chat_bloc.dart';
import 'package:flowkar/features/sm_chat/model/telegram_user_chat_list_model.dart';
import 'package:flowkar/features/sm_chat/widget/sm_chat_screen_shimmer.dart';
import 'package:flutter/cupertino.dart';

class TelegramChatDetailScreen extends StatefulWidget {
  final dynamic args;
  const TelegramChatDetailScreen({super.key, required this.args});

  static Widget builder(BuildContext context) {
    var args = ModalRoute.of(context)?.settings.arguments;
    //args[0]-->name
    //args[1]-->platformInfo
    //args[2]-->targetUserId
    //args[3]-->ownerId (optional for telegram)
    //args[4]-->type (type )
    return TelegramChatDetailScreen(args: args);
  }

  @override
  State<TelegramChatDetailScreen> createState() => _TelegramChatDetailScreenState();
}

class _TelegramChatDetailScreenState extends State<TelegramChatDetailScreen> {
  ScrollController? _scrollController;
  late FlickMultiManager flickMultiManager;
  final ValueNotifier<String> _inputText = ValueNotifier('');
  TextEditingController chatController = TextEditingController();

  @override
  void initState() {
    super.initState();

    context.read<SmChatBloc>().add(ClearAIGeneratedMassageEvent());
    chatController.clear();

    flickMultiManager = FlickMultiManager();
    _loadTelegramChat();
  }

  void _loadTelegramChat() {
    context.read<SmChatBloc>().add(GetTelegramUerChatListAPI(
          targetUserId: widget.args[2].toString(),
        ));
    context.read<SmChatBloc>().add(GetTelegramUerListAPI());
  }

  bool _isChannel() {
    // You can determine this based on platformInfo or other data
    // For example, if platformInfo contains channel information
    final platformInfo = widget.args[4];

    // Method 1: Check if platformInfo contains "channel" keyword
    if (platformInfo != null && platformInfo.toString().toLowerCase().contains('channel')) {
      return true;
    }

    // Method 2: Check if the chat type is channel (if available in your data structure)
    // You might need to modify this based on your actual data structure
    // if (platformInfo is Map && platformInfo['type'] == 'channel') {
    //   return true;
    // }

    return false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildTelegramAppBar(context, widget.args),
      body: BlocListener<ConnectivityBloc, ConnectivityState>(
        listener: (context, state) {
          if (state.isReconnected) {
            _loadTelegramChat();
          }
        },
        child: SafeArea(
          child: BlocBuilder<SmChatBloc, SmChatState>(
            builder: (context, state) {
              return Column(
                children: [
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20.0.w),
                      child: Column(
                        children: [
                          Expanded(
                            child:
                                BlocBuilder<ConnectivityBloc, ConnectivityState>(builder: (context, connectivityState) {
                              if (!connectivityState.isConnected &&
                                  state.telegramUserChatListModel?.data.isEmpty == true) {
                                return ChatDetailShimmer();
                              } else if (state.issinglechatloding) {
                                return ChatDetailShimmer();
                              } else if (state.telegramUserChatListModel?.data.isEmpty == true) {
                                return ExceptionWidget(
                                  imagePath: Assets.images.svg.exception.svgNoMassage.path,
                                  showButton: false,
                                  title: Lang.of(context).lbl_no_massage_found,
                                  subtitle: Lang.of(context).lbl_no_message,
                                );
                              } else {
                                return _buildTelegramMessageList(state.telegramUserChatListModel);
                              }
                            }),
                          ),
                          buildSizedBoxH(20.0),
                          if (state.aiGenerateMassageOrCommentModel?.result != null &&
                              (state.aiGenerateMassageOrCommentModel?.result.isNotEmpty ?? false))
                            _buildAIResponseSection(),
                          Align(
                              alignment: Alignment.bottomCenter,
                              child: _isChannel() ? _buildChannelInfoMessage() : _buildMessageTextField()),
                          Platform.isIOS ? buildSizedBoxH(30.0) : buildSizedBoxH(20.0),
                        ],
                      ),
                    ),
                  )
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildTelegramAppBar(BuildContext context, dynamic args) {
    return CustomAppbar(
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
            NavigatorService.goBack();
            context.read<SmChatBloc>().add(GetTelegramUerListAPI());
          },
          child: CustomImageView(
            margin: EdgeInsets.all(10),
            imagePath: AssetConstants.pngBack,
            height: 16.0.h,
          ),
        ),
        buildSizedBoxW(5.w),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            ClipRRect(
              clipBehavior: Clip.hardEdge,
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: CustomImageView(
                  imagePath: AssetConstants.pngUserReomve,
                  height: 40.0.h,
                  width: 40.0.w,
                  fit: BoxFit.cover,
                  radius: BorderRadius.circular(100.r),
                ),
              ),
            ),
            buildSizedBoxW(8),
            Text(
              args[0].toString(),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700, fontSize: 16.0.sp),
            )
          ],
        ),
      ],
    );
  }

  Widget _buildTelegramMessageList(TelegramUserChatListModel? telegramChatModel) {
    final groupedMessages = _groupTelegramMessagesByDate(telegramChatModel?.data);

    return BlocBuilder<SmChatBloc, SmChatState>(
      builder: (context, state) {
        return ListView.builder(
          padding: EdgeInsets.zero,
          controller: _scrollController,
          itemCount: groupedMessages.length,
          reverse: true,
          shrinkWrap: true,
          itemBuilder: (context, index) {
            final date = groupedMessages.keys.elementAt(index);
            final messages = groupedMessages[date]!;

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        date.toString(),
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              fontSize: 12.0.sp,
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).customColors.black.withOpacity(0.5),
                            ),
                      ),
                    ),
                  ],
                ),
                ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  reverse: true,
                  itemCount: messages.length,
                  itemBuilder: (context, messageIndex) {
                    return _buildTelegramChatBubble(messages[messageIndex]);
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildTelegramChatBubble(TelegramMessage? message) {
    final isOutgoing = message?.isOutgoing ?? false;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: isOutgoing ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          Visibility(
            visible: !isOutgoing,
            child: ClipRRect(
              clipBehavior: Clip.hardEdge,
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: CustomImageView(
                  imagePath: AssetConstants.pngUserReomve,
                  height: 40.0.h,
                  width: 40.0.w,
                  fit: BoxFit.cover,
                  radius: BorderRadius.circular(100.r),
                ),
              ),
            ),
          ),
          Container(
            constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.75),
            padding: EdgeInsets.only(left: 16.w, top: 10.h, bottom: 10.h, right: isOutgoing ? 22.w : 16.w),
            margin: EdgeInsets.fromLTRB(5.w, 0.h, 6.w, 2.h),
            decoration: BoxDecoration(
              color: isOutgoing ? Theme.of(context).primaryColor : ThemeData().customColors.messagecolor,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(isOutgoing ? 10.r : 0.r),
                topRight: Radius.circular(10.r),
                bottomRight: Radius.circular(isOutgoing ? 0.r : 10.r),
                bottomLeft: Radius.circular(10.r),
              ),
            ),
            child: _buildTelegramMessageContent(message, isOutgoing),
          ),
        ],
      ),
    );
  }

  Widget _buildTelegramMessageContent(TelegramMessage? message, bool isOutgoing) {
    // Handle media messages
    if (message?.mediaType != null && message!.mediaType!.isNotEmpty) {
      switch (message.mediaType) {
        case 'photo':
          return _buildPhotoMessage(message);
        case 'video':
          return _buildVideoMessage(message);
        case 'document':
          return _buildDocumentMessage(message, isOutgoing);
        case 'audio':
          return Text(
            "⚠️ Unsupported media type",
            style: TextStyle(
              color: isOutgoing ? Colors.white : Colors.grey[600],
              fontSize: 12,
            ),
          );
        case 'sticker':
          return _buildStickerMessage(message);
        case 'text':
          return _buildStickerMessage(message);
        default:
          return _buildTextMessage(message, isOutgoing);
      }
    } else {
      _buildTextMessage(message, isOutgoing);
    }

    // Handle text messages
    return _buildTextMessage(message, isOutgoing);
  }

  Widget _buildTextMessage(TelegramMessage? message, bool isOutgoing) {
    return Text(
      message?.text?.trim() != "" ? message?.text ?? '' : "⚠️ Unsupported media type",
      textAlign: TextAlign.start,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: isOutgoing ? ThemeData().customColors.white : ThemeData().customColors.black,
            fontSize: 16.0.sp,
            fontWeight: FontWeight.w500,
          ),
    );
  }

  Widget _buildPhotoMessage(TelegramMessage message) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (message.mediaInfo != null)
          CustomImageView(
            imagePath: AssetConstants.pngPlaceholder,
            // height: 200,s
            width: double.infinity,
            fit: BoxFit.cover,
            radius: BorderRadius.circular(8.0.r),
          ),
        if (message.text?.isNotEmpty ?? true)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: _buildTextMessage(message, message.isOutgoing ?? false),
          ),
      ],
    );
  }

  Widget _buildVideoMessage(TelegramMessage message) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (message.mediaInfo != null)
          CustomImageView(
            imagePath: AssetConstants.pngPlaceholder,
            // height: 200,s
            width: double.infinity,
            fit: BoxFit.cover,
            radius: BorderRadius.circular(8.0.r),
          ),
        // FlickMultiPlayer(
        //   key: ObjectKey(message.mediaInfo['url']),
        //   url: message.mediaInfo['url'] ?? '',
        //   flickMultiManager: flickMultiManager,
        //   image: AssetConstants.pngUserReomve,
        // ),
        if (message.text?.isNotEmpty ?? true)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: _buildTextMessage(message, message.isOutgoing ?? false),
          ),
      ],
    );
  }

  Widget _buildDocumentMessage(TelegramMessage message, bool isOutgoing) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(12.0),
          decoration: BoxDecoration(
            color: isOutgoing ? Colors.white.withOpacity(0.2) : Colors.grey.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: Row(
            children: [
              Icon(
                Icons.description,
                color: isOutgoing ? Colors.white : Colors.grey[600],
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      message.mediaInfo?['filename'] ?? 'Document',
                      style: TextStyle(
                        color: isOutgoing ? Colors.white : Colors.black,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (message.mediaInfo?['size'] != null)
                      Text(
                        _formatFileSize(message.mediaInfo['size']),
                        style: TextStyle(
                          color: isOutgoing ? Colors.white.withOpacity(0.7) : Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
        if (message.text?.isNotEmpty ?? false)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: _buildTextMessage(message, isOutgoing),
          ),
      ],
    );
  }

  Widget buildVoiceMessage(TelegramMessage message, bool isOutgoing) {
    return Container(
      padding: const EdgeInsets.all(12.0),
      child: Row(
        children: [
          Icon(
            Icons.play_arrow,
            color: isOutgoing ? Colors.white : Theme.of(context).primaryColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Container(
              height: 4,
              decoration: BoxDecoration(
                color: isOutgoing ? Colors.white.withOpacity(0.3) : Colors.grey.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            message.mediaInfo?['duration'] ?? '0:00',
            style: TextStyle(
              color: isOutgoing ? Colors.white : Colors.grey[600],
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStickerMessage(TelegramMessage message) {
    return Text(
      message.mediaInfo?['alt'] ?? '',
      style: TextStyle(
        fontSize: 12,
      ),
    );

    // CustomImageView(
    //   imagePath: message.mediaInfo?['url'] ?? '',
    //   height: 120,
    //   width: 120,
    //   fit: BoxFit.contain,
    // );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  Map<String, List<TelegramMessage>> _groupTelegramMessagesByDate(List<TelegramMessage>? messages) {
    if (messages == null || messages.isEmpty) return {};

    // Sort messages in descending order (newest first)
    messages.sort((a, b) => b.date.compareTo(a.date));

    final Map<String, List<TelegramMessage>> groupedMessages = {};

    for (var message in messages) {
      // Adjust for timezone if needed
      final adjustedDateTime = message.date.add(Duration(hours: 5, minutes: 30));
      final formattedDate = adjustedDateTime.formatForChatMessage();

      groupedMessages.putIfAbsent(formattedDate, () => []).add(message);
    }

    return groupedMessages;
  }

  // New method to build channel info message
  Widget _buildChannelInfoMessage() {
    return Container(
      height: 59.0.h,
      padding: EdgeInsets.symmetric(horizontal: 16.0.w, vertical: 16.0.h),
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.white,
        borderRadius: BorderRadius.circular(12.0.r),
        border: Border.all(
          color: Theme.of(context).customColors.black.withOpacity(0.1),
          width: 1.0,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Theme.of(context).customColors.black.withOpacity(0.6),
            size: 20.0,
          ),
          buildSizedBoxW(12.0),
          Expanded(
            child: Text(
              'This is a channel. You can only view messages.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).customColors.black.withOpacity(0.6),
                    fontSize: 14.0.sp,
                    fontWeight: FontWeight.w400,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAIResponseSection() {
    return BlocBuilder<SmChatBloc, SmChatState>(
      builder: (context, state) {
        return Container(
          margin: EdgeInsets.only(bottom: 10.0.h),
          padding: EdgeInsets.symmetric(horizontal: 12.0.w, vertical: 12.0.h),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Theme.of(context).primaryColor.withOpacity(0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.auto_fix_high_rounded, color: Theme.of(context).primaryColor, size: 16),
                  buildSizedBoxW(8),
                  Text(
                    'AI Generated Replay Massage',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).primaryColor,
                        ),
                  ),
                ],
              ),
              buildSizedBoxH(8),
              Text(
                state.aiGenerateMassageOrCommentModel?.result ?? "",
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              buildSizedBoxH(8),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () {
                      context.read<SmChatBloc>().add(ClearAIGeneratedMassageEvent());
                    },
                    child: Text(
                      'Dismiss',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).primaryColor,
                          ),
                    ),
                  ),
                  buildSizedBoxW(8),
                  ElevatedButton(
                    onPressed: () {
                      // final rawAiResponse = state.aiGenerateMassageOrCommentModel?.result ?? "";
                      // final cleanedResponse = rawAiResponse.isNotEmpty ? json.decode(rawAiResponse) : "";
                      // if (state.isAIGeneratingMassageloading) {
                      //   _inputText.value = '';
                      // } else {
                      //   chatController.text = cleanedResponse;
                      //   _inputText.value = cleanedResponse;
                      //   setState(() {});
                      // }

                      // context.read<SmChatBloc>().add(ClearAIGeneratedMassageEvent());
                      final rawAiResponse = state.aiGenerateMassageOrCommentModel?.result ?? "";
                      String cleanedResponse = '';

                      if (rawAiResponse.isNotEmpty) {
                        try {
                          cleanedResponse = json.decode(rawAiResponse) as String;
                        } catch (e) {
                          // જો JSON decoding fail થાય, fallback to raw text
                          cleanedResponse = rawAiResponse;
                        }
                      }

                      if (state.isAIGeneratingMassageloading) {
                        _inputText.value = '';
                      } else {
                        chatController.text = cleanedResponse;
                        _inputText.value = cleanedResponse;
                        setState(() {});
                      }

                      context.read<SmChatBloc>().add(ClearAIGeneratedMassageEvent());
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: Text('Apply'),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMessageTextField() {
    return SizedBox(
      height: 59.0.h,
      child: FlowkarTextFormField(
        filled: true,
        fillColor: Theme.of(context).customColors.white,
        controller: chatController,
        hintText: 'Type a message...',
        context: context,
        contentPadding: EdgeInsets.all(16.0),
        onChanged: (inputText) {
          _inputText.value = inputText;
          if (chatController.text.isEmpty) {
            setState(() {
              context.read<SmChatBloc>().add(ClearAIGeneratedMassageEvent());
              chatController.clear();
            });
          }
        },
        suffixIcon: Padding(
          padding: EdgeInsets.only(left: 6.0.w),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              BlocBuilder<SmChatBloc, SmChatState>(
                builder: (context, state) {
                  return ValueListenableBuilder<String>(
                    valueListenable: _inputText,
                    builder: (_, inputTextValue, __) {
                      TelegramMessage? lastSameUserMessage;
                      if (state.telegramUserChatListModel?.data.isNotEmpty ?? false) {
                        lastSameUserMessage = state.telegramUserChatListModel!.data
                                .where((message) => message.isOutgoing != true)
                                .isNotEmpty
                            ? state.telegramUserChatListModel!.data.where((message) => message.isOutgoing != true).first
                            : null;
                      }
                      bool isLastMessageMedia = false;
                      if (lastSameUserMessage != null) {
                        bool hasMediaType = lastSameUserMessage.mediaType != null &&
                            lastSameUserMessage.mediaType!.isNotEmpty &&
                            lastSameUserMessage.mediaType != 'text';

                        bool isLink = false;
                        if (lastSameUserMessage.text?.isNotEmpty ?? false) {
                          String message = lastSameUserMessage.text!;
                          isLink = message.contains('http://') ||
                              message.contains('https://') ||
                              message.contains('www.') ||
                              RegExp(r'^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}').hasMatch(message);
                        }

                        bool isTextEmpty = lastSameUserMessage.text?.trim().isEmpty ?? true;

                        isLastMessageMedia =
                            hasMediaType || isLink || (isTextEmpty && lastSameUserMessage.mediaInfo != null);
                      }

                      bool isAIButtonEnabled = (lastSameUserMessage != null && !isLastMessageMedia) ||
                          ((isLastMessageMedia || lastSameUserMessage == null) && inputTextValue.trim().isNotEmpty);

                      return InkWell(
                        onTap: !isAIButtonEnabled
                            ? null
                            : () {
                                String messageToPass = '';

                                if (lastSameUserMessage != null && !isLastMessageMedia) {
                                  messageToPass = lastSameUserMessage.text ?? '';
                                  Logger.lOG('Passing last same user text message: $messageToPass');
                                } else if ((isLastMessageMedia || lastSameUserMessage == null) &&
                                    inputTextValue.trim().isNotEmpty) {
                                  messageToPass = inputTextValue.trim();
                                  Logger.lOG('Passing text field content: $messageToPass');
                                }

                                if (messageToPass.isNotEmpty) {
                                  context
                                      .read<SmChatBloc>()
                                      .add(AIgenerateMassageEvent(context: context, massageText: messageToPass));
                                }
                              },
                        child: CircleAvatar(
                          radius: 14.r,
                          backgroundColor: !isAIButtonEnabled
                              ? Theme.of(context).primaryColor.withOpacity(0.5)
                              : Theme.of(context).primaryColor,
                          child: state.isAIGeneratingMassageloading
                              ? CupertinoActivityIndicator(
                                  color: Colors.white,
                                )
                              : Icon(Icons.auto_fix_high_rounded,
                                  color: !isAIButtonEnabled ? Colors.white.withOpacity(0.5) : Colors.white, size: 20),
                        ),
                      );
                    },
                  );
                },
              ),

              // Send Button
              ValueListenableBuilder<String>(
                valueListenable: _inputText,
                builder: (_, inputTextValue, __) {
                  if (inputTextValue.isNotEmpty) {
                    return InkWell(
                      onTap: () {
                        final messageText = chatController.text.trim();
                        if (messageText.isNotEmpty) {
                          context
                              .read<SmChatBloc>()
                              .add(TelegramSendMassage(chatId: widget.args[2].toString(), message: messageText));

                          context.read<SmChatBloc>().add(ClearAIGeneratedMassageEvent());

                          chatController.clear();
                          _inputText.value = '';
                          FocusScope.of(context).requestFocus(FocusNode());
                          context.read<SmChatBloc>().add(GetTelegramUerListAPI());
                        }
                      },
                      child: CustomImageView(
                          imagePath: Assets.images.icons.other.icSend.path, margin: EdgeInsets.all(12), height: 20.0.h),
                    );
                  } else {
                    return SizedBox.shrink();
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
