import 'dart:convert';
import 'dart:io';

import 'package:flowkar/core/utils/date_time_utils.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_manager.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_player.dart';
import 'package:flowkar/features/sm_chat/bloc/sm_chat_bloc.dart';
import 'package:flowkar/features/sm_chat/model/single_chat_model.dart';
import 'package:flowkar/features/sm_chat/widget/sm_chat_screen_shimmer.dart';
import 'package:flutter/cupertino.dart';

class SmChatDetailScreen extends StatefulWidget {
  final dynamic args;
  const SmChatDetailScreen({super.key, required this.args});

  static Widget builder(BuildContext context) {
    var args = ModalRoute.of(context)?.settings.arguments;
    //args[0]-->name
    //args[1]-->platformInfo
    //args[2]-->conversationId
    //args[3]-->ownerId,
    //args[4]-->touserId,
    return SmChatDetailScreen(args: args);
  }

  @override
  State<SmChatDetailScreen> createState() => _SmChatDetailScreenState();
}

class _SmChatDetailScreenState extends State<SmChatDetailScreen> {
  ScrollController? _scrollController;
  late FlickMultiManager flickMultiManager;
  final ValueNotifier<String> _inputText = ValueNotifier('');
  TextEditingController chatController = TextEditingController();

  @override
  void initState() {
    // Clear AI model when entering the screen
    context.read<SmChatBloc>().add(ClearAIGeneratedMassageEvent());
    chatController.clear();

    context.read<SmChatBloc>().add(GetsinglechatdetailsEvent(
        conversationId: widget.args[2].toString(),
        ownerId: widget.args[3].toString(),
        platformId: getPlatformId(widget.args[1].name)));
    super.initState();
    flickMultiManager = FlickMultiManager();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildchatAppBar(context, widget.args),
      body: BlocListener<ConnectivityBloc, ConnectivityState>(
        listener: (context, state) {
          if (state.isReconnected) {
            context.read<SmChatBloc>().add(GetsinglechatdetailsEvent(
                conversationId: widget.args[2].toString(),
                ownerId: widget.args[3].toString(),
                platformId: getPlatformId(widget.args[1].name)));
          }
        },
        child: SafeArea(
          child: BlocBuilder<SmChatBloc, SmChatState>(
            builder: (context, state) {
              return Column(
                children: [
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20.0.w),
                      child: Column(
                        children: [
                          Expanded(
                            child: BlocBuilder<ConnectivityBloc, ConnectivityState>(
                              builder: (context, connectivityState) {
                                if (!connectivityState.isConnected && state.singleChatModel == null) {
                                  return ChatDetailShimmer();
                                } else if (state.issinglechatloding) {
                                  return ChatDetailShimmer();
                                } else if (state.singleChatModel == null) {
                                  return ExceptionWidget(
                                    imagePath: Assets.images.svg.exception.svgNoMassage.path,
                                    showButton: false,
                                    title: Lang.of(context).lbl_no_massage_found,
                                    subtitle: Lang.of(context).lbl_no_message,
                                  );
                                } else {
                                  return _buildMessageList(state.singleChatModel);
                                }
                              },
                            ),
                          ),
                          buildSizedBoxH(20.0),
                          if (state.aiGenerateMassageOrCommentModel?.result != null &&
                              (state.aiGenerateMassageOrCommentModel?.result.isNotEmpty ?? false))
                            _buildAIResponseSection(),
                          Align(alignment: Alignment.bottomCenter, child: _buildMessageTextField()),
                          Platform.isIOS ? buildSizedBoxH(30.0) : buildSizedBoxH(20.0),
                        ],
                      ),
                    ),
                  )
                ],
              );
            },
          ),
        ),
      ),

      // KeyboardVisibilityBuilder(
      //   builder: (context, isKeyboardVisible) {
      //     return Padding(
      //       padding: EdgeInsets.only(
      //         bottom: isKeyboardVisible ? 20.0 : 0.0,
      //       ),
      //       child: Column(
      //         children: [
      //           Expanded(
      //             child: BlocBuilder<SmChatBloc, SmChatState>(
      //               builder: (context, state) {
      //                 return Padding(
      //                   padding: EdgeInsets.symmetric(horizontal: 8.0.w),
      //                   child: state.issinglechatloding
      //                       ? const LoadingAnimationWidget()
      //                       : _buildMessageList(state.singleChatModel),
      //                 );
      //               },
      //             ),
      //           _buildMessageTextField(),
      //           ),
      //         ],
      //       ),
      //     );
      //   },
      // ),
    );
  }

  PreferredSizeWidget _buildchatAppBar(BuildContext context, dynamic args) {
    return CustomAppbar(
      leading: [
        InkWell(
          onTap: () {
            NavigatorService.goBack();
          },
          child: CustomImageView(
            imagePath: AssetConstants.pngBack,
            height: 16.0.h,
          ),
        ),
        buildSizedBoxW(20.w),
        InkWell(
          onTap: () {},
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              ClipRRect(
                clipBehavior: Clip.hardEdge,
                child: Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: CustomImageView(
                    imagePath: Assets.images.pngs.other.pngLogo.path,
                    height: 40.0.h,
                    width: 40.0.w,
                    fit: BoxFit.cover,
                    radius: BorderRadius.circular(100.r),
                  ),
                ),
              ),
              buildSizedBoxW(8),
              Text(args[0].toString(),
                  style:
                      Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700, fontSize: 16.0.sp))
            ],
          ),
        ),
      ],
    );
  }

  int getPlatformId(String platformName) {
    switch (platformName.toLowerCase()) {
      case 'facebook':
        return 1;
      case 'instagram':
        return 2;
      default:
        return 0;
    }
  }

  Widget _buildMessageList(SingleChatModel? singleChatModel) {
    final groupedMessages = _groupMessagesByDate(singleChatModel?.data);

    return ListView.builder(
      padding: EdgeInsets.zero,
      controller: _scrollController,
      itemCount: groupedMessages.length,
      reverse: true,
      shrinkWrap: true,
      itemBuilder: (context, index) {
        final date = groupedMessages.keys.elementAt(index);
        final messages = groupedMessages[date]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    date.toString(),
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontSize: 12.0.sp,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).customColors.black.withOpacity(0.5),
                        ),
                  ),
                ),
              ],
            ),
            ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              reverse: true,
              itemCount: messages.length,
              itemBuilder: (context, messageIndex) {
                return _buildChatBubble(messages[messageIndex]);
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildChatBubble(ChatData? message) {
    Logger.lOG(message?.attachments?.data?[0].imageData?.previewUrl);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: message?.checkMessage == int.tryParse('1') ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          Visibility(
            visible: message?.checkMessage != int.tryParse('1'),
            child: ClipRRect(
              clipBehavior: Clip.hardEdge,
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: CustomImageView(
                  imagePath: Assets.images.pngs.other.pngLogo.path,
                  height: 40.0.h,
                  width: 40.0.w,
                  fit: BoxFit.cover,
                  radius: BorderRadius.circular(100.r),
                ),
              ),
            ),
          ),
          Container(
              constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.75),
              padding: EdgeInsets.only(
                  left: 16.w, top: 10.h, bottom: 10.h, right: message?.checkMessage == int.tryParse("1") ? 22.w : 16.w),
              margin: EdgeInsets.fromLTRB(5.w, 0.h, 6.w, 2.h),
              decoration: BoxDecoration(
                color: message?.checkMessage == int.tryParse('1')
                    ? Theme.of(context).primaryColor
                    : ThemeData().customColors.messagecolor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(message?.checkMessage == int.tryParse("1") ? 10.r : 0.r),
                  topRight: Radius.circular(10.r),
                  bottomRight: Radius.circular(message?.checkMessage == int.tryParse('1') ? 0.r : 10.r),
                  bottomLeft: Radius.circular(10.r),
                ),
              ),
              child: message?.message == ""
                  ? message?.attachments?.data![0].imageData == null
                      ? FlickMultiPlayer(
                          key: ObjectKey(message?.attachments?.data![0].videoData?.previewUrl),
                          url: message?.attachments?.data![0].videoData?.previewUrl ?? '',
                          flickMultiManager: flickMultiManager,
                          image: AssetConstants.pngUserReomve,
                        )
                      : CustomImageView(
                          imagePath: message?.attachments?.data?[0].imageData?.previewUrl,
                          height: 150,
                          radius: BorderRadius.circular(8.0.r),
                        )
                  :

                  //  message.type == 'image'
                  //     ? InkWell(
                  //         onTap: () {
                  //           FocusScope.of(context).unfocus();
                  //           // PersistentNavBarNavigator.pushNewScreen(context,
                  //           //     screen: FlowkarImagePreview(
                  //           //       imagepath:
                  //           //           '${APIEndPoints.mainbaseURL}/${message.message}',
                  //           //     ));
                  //         },
                  //         child: CustomImageView(
                  //           imagePath:
                  //               '${APIConfig.mainbaseURL}/${message.message}',
                  // height: 250.0.h,
                  // width: 150.0.w,
                  // radius: BorderRadius.circular(4.0.r),
                  //           fit: BoxFit.cover,
                  //         ),
                  //       )
                  //     : message.type == 'custom'
                  //         ? SizedBox.shrink()
                  //         : message.message!.isUrl
                  //             ? LinkPreview(
                  //                 url: message.message ?? '',
                  //               )
                  // :
                  Text(
                      message?.message ?? '',
                      textAlign: TextAlign.start,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: message?.checkMessage == int.tryParse('1')
                              ? ThemeData().customColors.white
                              : ThemeData().customColors.black,
                          fontSize: 16.0.sp,
                          fontWeight: FontWeight.w500),
                    )),
        ],
      ),
    );
  }

  Map<String, List<ChatData>> _groupMessagesByDate(List<ChatData>? messages) {
    if (messages == null || messages.isEmpty) return {};

    // Sort messages in descending order (newest first)
    messages
        .sort((a, b) => DateTime.parse(b.createdTime.toString()).compareTo(DateTime.parse(a.createdTime.toString())));

    final Map<String, List<ChatData>> groupedMessages = {};

    for (var message in messages) {
      final dateTime = DateTime.parse(message.createdTime.toString().split("+").first);
      final adjustedDateTime = dateTime.add(Duration(hours: 5, minutes: 30));
      final formattedDate = adjustedDateTime.formatForChatMessage();

      groupedMessages.putIfAbsent(formattedDate, () => []).add(message);
    }

    return groupedMessages;
  }

  // Widget _buildMessageTextField() {
  //   return Visibility(
  //     child: SizedBox(
  //       height: 59.0.h,
  //       child: FlowkarTextFormField(
  //         filled: true,
  //         fillColor: Theme.of(context).customColors.white,
  //         controller: chatController,
  //         hintText: 'Enter Message',
  //         context: context,
  //         contentPadding: EdgeInsets.all(16.0),
  //         onChanged: (inputText) {
  //           // context.read<ChatBloc>().add(TypingSocketEvent(
  //           //       userId: widget.args[0].userId,
  //           //       isTyping: "1",
  //           //     ));
  //           _inputText.value = inputText;
  //         },
  //         // prefix: ValueListenableBuilder<String>(
  //         //   valueListenable: _inputText,
  //         //   builder: (_, inputTextValue, __) {
  //         //     if (inputTextValue.isEmpty) {
  //         //       return Padding(
  //         //         padding: const EdgeInsets.all(14.0),
  //         //         child: CustomImageView(
  //         //           color: ThemeData().customColors.white,
  //         //           height: 25.h,
  //         //           width: 25.w,
  //         //           imagePath: '',
  //         //           onTap: () => _onIconPressed(ImageSource.camera),
  //         //         ),
  //         //       );
  //         //     } else {
  //         //       return IconButton(
  //         //         icon: CustomImageView(
  //         //           color: ThemeData().customColors.white,
  //         //           height: 12.0.h,
  //         //           width: 12.0.w,
  //         //           imagePath: '',
  //         //         ),
  //         //         onPressed: () {
  //         //           state.chatController?.clear();
  //         //           _inputText.value = '';
  //         //         },
  //         //       );
  //         //     }
  //         //   },
  //         // ),
  //         suffixIcon: Row(
  //           mainAxisSize: MainAxisSize.min,
  //           children: [
  //             InkWell(
  //               onTap: () {
  //                 Logger.lOG('send message');
  //                 // Get the last message from the chat message list
  //                 if (state.chatMessageList.isNotEmpty) {
  //                   final lastMessage = state.chatMessageList.first; // first because list is reversed
  //                   Logger.lOG('Last message: ${lastMessage.message}');
  //                   Logger.lOG('Last message type: ${lastMessage.type}');
  //                   Logger.lOG('Last message sent by: ${lastMessage.sentBy}');
  //                   Logger.lOG('Last message created at: ${lastMessage.createdAt}');

  //                   // Print the complete last message object
  //                   print('Complete last message: $lastMessage');
  //                 } else {
  //                   Logger.lOG('No messages found in chat');
  //                 }
  //               },
  //               child: CircleAvatar(
  //                 radius: 15.r,
  //                 backgroundColor: Theme.of(context).primaryColor,
  //                 child:
  //                     // state.isAIGeneratingloading
  //                     //     ? CupertinoActivityIndicator(
  //                     //         color: Colors.white,
  //                     //       )
  //                     //     :
  //                     Icon(Icons.auto_fix_high_rounded, color: Colors.white, size: 20),
  //               ),
  //             ),
  //             ValueListenableBuilder<String>(
  //               valueListenable: _inputText,
  //               builder: (_, inputTextValue, __) {
  //                 if (inputTextValue.isNotEmpty) {
  //                   return InkWell(
  //                       onTap: () {
  //                         final messageText = chatController.text.trim();
  //                         if (messageText.isNotEmpty) {
  //                           if (getPlatformId(widget.args[1].name) == 2) {
  //                             context.read<SmChatBloc>().add(SendInstaMessageEvent(
  //                                   touserId: widget.args[4].toString(),
  //                                   fromuserId: widget.args[3].toString(),
  //                                   messageType: 1,
  //                                   message: chatController.text,
  //                                   file: null,
  //                                 ));
  //                           } else {
  //                             context.read<SmChatBloc>().add(SendFacebookMessageEvent(
  //                                   touserId: widget.args[4].toString(),
  //                                   fromuserId: widget.args[3].toString(),
  //                                   messageType: 1,
  //                                   message: chatController.text,
  //                                   file: null,
  //                                 ));
  //                           }

  //                           setState(() {});
  //                         }
  //                         chatController.clear();
  //                         _inputText.value = '';
  //                         FocusScope.of(context).requestFocus(FocusNode());
  //                       },
  //                       child: CustomImageView(
  //                         imagePath: Assets.images.icons.other.icSend.path,
  //                         margin: EdgeInsets.all(12),
  //                         height: 10,
  //                       )

  //                       // Icon(
  //                       //   Icons.send_rounded,
  //                       //   color: Theme.of(context).primaryColor,
  //                       // )
  //                       // Text(
  //                       //   // "Lang.of(context).lbl_send",
  //                       //   style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold),
  //                       // ),
  //                       );
  //                 } else {
  //                   // context.read<ChatBloc>().add(TypingSocketEvent(
  //                   //       userId: widget.args[0].userId,
  //                   //       isTyping: "0",
  //                   //     ));
  //                   return SizedBox.shrink();
  //                   // Row(
  //                   //   mainAxisAlignment: MainAxisAlignment.end,
  //                   //   mainAxisSize: MainAxisSize.min,
  //                   //   children: [
  //                   //     Padding(
  //                   //       padding: EdgeInsets.only(
  //                   //           top: 18.0.h, bottom: 18.0.h, right: 8.0.w),
  //                   //       child: CustomImageView(
  //                   //         color: ThemeData().customColors.white,
  //                   //         imagePath: '',
  //                   //         height: 20.h,
  //                   //         width: 20.w,
  //                   //         onTap: () =>
  //                   //             _onIconPressed(ImageSource.gallery),
  //                   //       ),
  //                   //     ),
  //                   //     // Padding(
  //                   //     //   padding: EdgeInsets.only(
  //                   //     //       top: 15.0.h, bottom: 15.0.h, right: 15.0.w),
  //                   //     //   child: CustomImageView(
  //                   //     //       height: 25.h,
  //                   //     //       width: 25.0.w,
  //                   //     //       imagePath:
  //                   //     //           Assets.images.icons.icMicrophone.path,
  //                   //     //       onTap: () => _recordOrStop()),
  //                   //     // ),
  //                   //   ],
  //                   // );
  //                 }
  //               },
  //             ),
  //           ],
  //         ),
  //       ),
  //     ),
  //   );
  // }
  // Widget to show AI generated response
  Widget _buildAIResponseSection() {
    return BlocBuilder<SmChatBloc, SmChatState>(
      builder: (context, state) {
        return Container(
          margin: EdgeInsets.only(bottom: 10.0.h),
          padding: EdgeInsets.symmetric(horizontal: 12.0.w, vertical: 12.0.h),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Theme.of(context).primaryColor.withOpacity(0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.auto_fix_high_rounded, color: Theme.of(context).primaryColor, size: 16),
                  buildSizedBoxW(8),
                  Text(
                    'AI Generated Replay Massage',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).primaryColor,
                        ),
                  ),
                ],
              ),
              buildSizedBoxH(8),
              Text(
                state.aiGenerateMassageOrCommentModel?.result ?? "",
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              buildSizedBoxH(8),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () {
                      // Clear AI response
                      context.read<SmChatBloc>().add(ClearAIGeneratedMassageEvent());
                    },
                    child: Text(
                      'Dismiss',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).primaryColor,
                          ),
                    ),
                  ),
                  buildSizedBoxW(8),
                  ElevatedButton(
                    onPressed: () {
                      final rawAiResponse = state.aiGenerateMassageOrCommentModel?.result ?? "";
                      String cleanedResponse = '';

                      if (rawAiResponse.isNotEmpty) {
                        try {
                          cleanedResponse = json.decode(rawAiResponse) as String;
                        } catch (e) {
                          // જો JSON decoding fail થાય, fallback to raw text
                          cleanedResponse = rawAiResponse;
                        }
                      }

                      if (state.isAIGeneratingMassageloading) {
                        _inputText.value = '';
                      } else {
                        chatController.text = cleanedResponse;
                        _inputText.value = cleanedResponse;
                        setState(() {});
                      }

                      context.read<SmChatBloc>().add(ClearAIGeneratedMassageEvent());
                      // final rawAiResponse = state.aiGenerateMassageOrCommentModel?.result ?? "";
                      // final cleanedResponse = rawAiResponse.isNotEmpty ? json.decode(rawAiResponse) : "";
                      // // Use AI response
                      // if (state.isAIGeneratingMassageloading) {
                      //   _inputText.value = '';
                      // } else {
                      //   chatController.text = cleanedResponse;
                      //   _inputText.value = cleanedResponse;
                      //   setState(() {});
                      // }

                      // Clear AI response
                      // context.read<SmChatBloc>().add(ClearAIGeneratedMassageEvent());
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: Text('Apply'),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMessageTextField() {
    return Visibility(
      child: SizedBox(
        height: 59.0.h,
        child: FlowkarTextFormField(
          filled: true,
          fillColor: Theme.of(context).customColors.white,
          controller: chatController,
          hintText: 'Enter Message',
          context: context,
          contentPadding: EdgeInsets.all(16.0),
          onChanged: (inputText) {
            _inputText.value = inputText;
            if (chatController.text.isEmpty) {
              setState(() {
                context.read<SmChatBloc>().add(ClearAIGeneratedMassageEvent());

                chatController.clear();
              });
            }
          },
          suffixIcon: Padding(
            padding: EdgeInsets.only(left: 6.w),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // AI Button - Updated with API call functionality
                BlocBuilder<SmChatBloc, SmChatState>(
                  builder: (context, state) {
                    // Only update text field when AI generates response AND it's not loading
                    // if (!state.isAIGeneratingMassageloading &&
                    //     state.aiGenerateMassageOrCommentModel?.result != null &&
                    //     state.aiGenerateMassageOrCommentModel!.result!.isNotEmpty) {
                    //   // Use post frame callback to avoid setState during build
                    //   WidgetsBinding.instance.addPostFrameCallback((_) {
                    //     if (chatController.text != state.aiGenerateMassageOrCommentModel!.result!) {
                    //       chatController.text = state.aiGenerateMassageOrCommentModel!.result!;
                    //       _inputText.value = chatController.text;
                    //     }
                    //   });
                    // }

                    return ValueListenableBuilder<String>(
                      valueListenable: _inputText,
                      builder: (_, inputTextValue, __) {
                        // Get last message from same user (not current user)
                        ChatData? lastSameUserMessage;
                        if (state.singleChatModel?.data?.isNotEmpty ?? false) {
                          // Find the last message where checkMessage != 1 (not sent by current user)
                          lastSameUserMessage = state.singleChatModel!.data!
                                  .where((message) => message.checkMessage != int.tryParse('1'))
                                  .isNotEmpty
                              ? state.singleChatModel!.data!
                                  .where((message) => message.checkMessage != int.tryParse('1'))
                                  .first
                              : null;
                        }

                        // Check if last message is media/attachment/link
                        bool isLastMessageMedia = false;
                        if (lastSameUserMessage != null) {
                          // Check if message is empty (means it's attachment only)
                          bool isAttachmentOnly = lastSameUserMessage.message?.isEmpty ?? true;

                          // Check if message has attachments (image/video)
                          bool hasAttachments = lastSameUserMessage.attachments?.data?.isNotEmpty ?? false;

                          // Check if message is a URL/link
                          bool isLink = false;
                          if (lastSameUserMessage.message?.isNotEmpty ?? false) {
                            String message = lastSameUserMessage.message!;
                            isLink = message.contains('http://') ||
                                message.contains('https://') ||
                                message.contains('www.') ||
                                RegExp(r'^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}').hasMatch(message);
                          }

                          // Message is media if it's attachment only, has attachments, or is a link
                          isLastMessageMedia = isAttachmentOnly || hasAttachments || isLink;
                        }

                        // AI button should be enabled when:
                        // 1. Last message exists and is text type, OR
                        // 2. Last message doesn't exist or is not text type BUT text field has content
                        bool isAIButtonEnabled = (lastSameUserMessage != null && !isLastMessageMedia) ||
                            ((isLastMessageMedia || lastSameUserMessage == null) && inputTextValue.trim().isNotEmpty);

                        return InkWell(
                          onTap: !isAIButtonEnabled
                              ? null
                              : () {
                                  String messageToPass = '';

                                  // If last message exists and is text type, pass that message
                                  if (lastSameUserMessage != null && !isLastMessageMedia) {
                                    messageToPass = lastSameUserMessage.message ?? '';
                                    Logger.lOG('Passing last same user text message: $messageToPass');
                                  }
                                  // If last message doesn't exist or is not text type, pass text field content
                                  else if ((isLastMessageMedia || lastSameUserMessage == null) &&
                                      inputTextValue.trim().isNotEmpty) {
                                    messageToPass = inputTextValue.trim();
                                    Logger.lOG('Passing text field content: $messageToPass');
                                  }

                                  // Call AI generate message event with the determined message
                                  if (messageToPass.isNotEmpty) {
                                    context
                                        .read<SmChatBloc>()
                                        .add(AIgenerateMassageEvent(context: context, massageText: messageToPass));
                                  }
                                },
                          child: CircleAvatar(
                            radius: 15.r,
                            backgroundColor: !isAIButtonEnabled
                                ? Theme.of(context).primaryColor.withOpacity(0.5)
                                : Theme.of(context).primaryColor,
                            child: state.isAIGeneratingMassageloading
                                ? CupertinoActivityIndicator(
                                    color: Colors.white,
                                  )
                                : Icon(Icons.auto_fix_high_rounded,
                                    color: !isAIButtonEnabled ? Colors.white.withOpacity(0.5) : Colors.white, size: 20),
                          ),
                        );
                      },
                    );
                  },
                ),

                // Send Button
                ValueListenableBuilder<String>(
                  valueListenable: _inputText,
                  builder: (_, inputTextValue, __) {
                    if (inputTextValue.isNotEmpty) {
                      return InkWell(
                        onTap: () {
                          final messageText = chatController.text.trim();
                          if (messageText.isNotEmpty) {
                            if (getPlatformId(widget.args[1].name) == 2) {
                              context.read<SmChatBloc>().add(SendInstaMessageEvent(
                                    touserId: widget.args[4].toString(),
                                    fromuserId: widget.args[3].toString(),
                                    messageType: 1,
                                    message: chatController.text,
                                    file: null,
                                  ));
                            } else {
                              context.read<SmChatBloc>().add(SendFacebookMessageEvent(
                                    touserId: widget.args[4].toString(),
                                    fromuserId: widget.args[3].toString(),
                                    messageType: 1,
                                    message: chatController.text,
                                    file: null,
                                  ));
                            }

                            context.read<SmChatBloc>().add(ClearAIGeneratedMassageEvent());

                            chatController.clear();

                            setState(() {});
                          }
                          chatController.clear();
                          _inputText.value = '';
                          FocusScope.of(context).requestFocus(FocusNode());
                        },
                        child: CustomImageView(
                          imagePath: Assets.images.icons.other.icSend.path,
                          margin: EdgeInsets.all(12),
                          height: 22.0.h,
                        ),
                      );
                    } else {
                      return SizedBox.shrink();
                    }
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
