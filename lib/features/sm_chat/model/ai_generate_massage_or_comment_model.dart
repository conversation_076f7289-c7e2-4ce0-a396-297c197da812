AiGenerateMassageOrComment deserializeAiGenerateMassageOrComment(Map<String, dynamic> json) =>
    AiGenerateMassageOrComment.fromJson(json);

class AiGenerateMassageOrComment {
  final bool status;
  final String message;
  final String result;

  AiGenerateMassageOrComment({
    required this.status,
    required this.message,
    required this.result,
  });

  // Factory constructor to create from JSON
  factory AiGenerateMassageOrComment.fromJson(Map<String, dynamic> json) {
    return AiGenerateMassageOrComment(
      status: json['status'] as bool,
      message: json['message'] as String,
      result: json['result'] as String,
    );
  }

  // Convert object to JSON
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'result': result,
    };
  }

  // copyWith method
  AiGenerateMassageOrComment copyWith({
    bool? status,
    String? message,
    String? result,
  }) {
    return AiGenerateMassageOrComment(
      status: status ?? this.status,
      message: message ?? this.message,
      result: result ?? this.result,
    );
  }
}
