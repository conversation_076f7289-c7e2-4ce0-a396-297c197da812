import 'package:flowkar/core/utils/logger.dart';

TelegramSandMassageModel deserializeTelegramSandMassageModel(Map<String, dynamic> json) =>
    TelegramSandMassageModel.fromJson(json);

class TelegramSandMassageModel {
  final bool status;
  final String message;
  final TelegramMessageData data;

  TelegramSandMassageModel({
    required this.status,
    required this.message,
    required this.data,
  });

  TelegramSandMassageModel copyWith({
    bool? status,
    String? message,
    TelegramMessageData? data,
  }) {
    return TelegramSandMassageModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  factory TelegramSandMassageModel.fromJson(Map<String, dynamic> json) {
    return TelegramSandMassageModel(
      status: json['status'],
      message: json['message'],
      data: TelegramMessageData.from<PERSON>son(json['data']),
    );
  }

  Map<String, dynamic> to<PERSON>son() {
    return {
      'status': status,
      'message': message,
      'data': data.toJson(),
    };
  }
}

class TelegramMessageData {
  final int id;
  final DateTime date;
  final String text;
  final int fromId;
  final int toId;
  final bool isOutgoing;
  final int? replyToMsgId;
  final String? mediaType;
  final dynamic mediaInfo;

  TelegramMessageData({
    required this.id,
    required this.date,
    required this.text,
    required this.fromId,
    required this.toId,
    required this.isOutgoing,
    this.replyToMsgId,
    this.mediaType,
    this.mediaInfo,
  });

  TelegramMessageData copyWith({
    int? id,
    DateTime? date,
    String? text,
    int? fromId,
    int? toId,
    bool? isOutgoing,
    int? replyToMsgId,
    String? mediaType,
    dynamic mediaInfo,
  }) {
    return TelegramMessageData(
      id: id ?? this.id,
      date: date ?? this.date,
      text: text ?? this.text,
      fromId: fromId ?? this.fromId,
      toId: toId ?? this.toId,
      isOutgoing: isOutgoing ?? this.isOutgoing,
      replyToMsgId: replyToMsgId ?? this.replyToMsgId,
      mediaType: mediaType ?? this.mediaType,
      mediaInfo: mediaInfo ?? this.mediaInfo,
    );
  }

  factory TelegramMessageData.fromJson(Map<String, dynamic> json) {
    try {
      return TelegramMessageData(
        id: (json['id'] as num?)?.toInt() ?? 0,
        date: json['date'] != null ? DateTime.tryParse(json['date'].toString()) ?? DateTime.now() : DateTime.now(),
        text: json['text']?.toString() ?? '',
        fromId: (json['from_id'] as num?)?.toInt() ?? 0,
        toId: (json['to_id'] as num?)?.toInt() ?? 0,
        isOutgoing: json['is_outgoing'] == true,
        replyToMsgId: (json['reply_to_msg_id'] as num?)?.toInt(),
        mediaType: json['media_type']?.toString(),
        mediaInfo: json['media_info'],
      );
    } catch (e) {
      // Log the error or handle it as needed
      Logger.lOG('Error parsing TelegramMessageData: $e');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'text': text,
      'from_id': fromId,
      'to_id': toId,
      'is_outgoing': isOutgoing,
      'reply_to_msg_id': replyToMsgId,
      'media_type': mediaType,
      'media_info': mediaInfo,
    };
  }
}
