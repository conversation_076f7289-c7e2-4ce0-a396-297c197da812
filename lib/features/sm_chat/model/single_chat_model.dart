SingleChatModel deserializeSingleChatModel(Map<String, dynamic> json) =>
    SingleChatModel.fromJson(json);

class SingleChatModel {
  bool? status;
  String? message;
  List<ChatData>? data;

  SingleChatModel({this.status, this.message, this.data});

  SingleChatModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <ChatData>[];
      json['data'].forEach((v) {
        data!.add(ChatData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  SingleChatModel copyWith(
      {bool? status, String? message, List<ChatData>? data}) {
    return SingleChatModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

class ChatData {
  String? id;
  String? createdTime;
  From? from;
  To? to;
  String? message;
  Attachments? attachments;
  int? checkMessage;

  ChatData(
      {this.id,
      this.createdTime,
      this.from,
      this.to,
      this.message,
      this.attachments,
      this.checkMessage});

  ChatData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createdTime = json['created_time'];
    from = json['from'] != null ? From.fromJson(json['from']) : null;
    to = json['to'] != null ? To.fromJson(json['to']) : null;
    message = json['message'];
    attachments = json['attachments'] != null
        ? Attachments.fromJson(json['attachments'])
        : null;
    checkMessage = json['check_message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['created_time'] = createdTime;
    if (from != null) {
      data['from'] = from!.toJson();
    }
    if (to != null) {
      data['to'] = to!.toJson();
    }
    data['message'] = message;
    if (attachments != null) {
      data['attachments'] = attachments!.toJson();
    }
    data['check_message'] = checkMessage;
    return data;
  }

  ChatData copyWith(
      {String? id,
      String? createdTime,
      From? from,
      To? to,
      String? message,
      Attachments? attachments,
      int? checkMessage}) {
    return ChatData(
      id: id ?? this.id,
      createdTime: createdTime ?? this.createdTime,
      from: from ?? this.from,
      to: to ?? this.to,
      message: message ?? this.message,
      attachments: attachments ?? this.attachments,
      checkMessage: checkMessage ?? this.checkMessage,
    );
  }
}

class From {
  String? username;
  String? id;

  From({this.username, this.id});

  From.fromJson(Map<String, dynamic> json) {
    username = json['username'];
    id = json['id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['username'] = username;
    data['id'] = id;
    return data;
  }
}

class To {
  List<ChatData>? data;

  To({this.data});

  To.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <ChatData>[];
      json['data'].forEach((v) {
        data!.add(ChatData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Attachments {
  List<AttachmentsData>? data;
  Paging? paging;

  Attachments({this.data, this.paging});

  Attachments.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <AttachmentsData>[];
      json['data'].forEach((v) {
        data!.add(AttachmentsData.fromJson(v));
      });
    }
    paging = json['paging'] != null ? Paging.fromJson(json['paging']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (paging != null) {
      data['paging'] = paging!.toJson();
    }
    return data;
  }
}

class AttachmentsData {
  ImageData? imageData;
  VideoData? videoData;

  AttachmentsData({this.imageData, this.videoData});

  AttachmentsData.fromJson(Map<String, dynamic> json) {
    imageData = json['image_data'] != null
        ? ImageData.fromJson(json['image_data'])
        : null;
    videoData = json['video_data'] != null
        ? VideoData.fromJson(json['video_data'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (imageData != null) {
      data['image_data'] = imageData!.toJson();
    }
    if (videoData != null) {
      data['video_data'] = videoData!.toJson();
    }
    return data;
  }
}

class ImageData {
  int? width;
  int? height;
  int? maxWidth;
  int? maxHeight;
  String? url;
  String? previewUrl;

  ImageData(
      {this.width,
      this.height,
      this.maxWidth,
      this.maxHeight,
      this.url,
      this.previewUrl});

  ImageData.fromJson(Map<String, dynamic> json) {
    width = json['width'];
    height = json['height'];
    maxWidth = json['max_width'];
    maxHeight = json['max_height'];
    url = json['url'];
    previewUrl = json['preview_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['width'] = width;
    data['height'] = height;
    data['max_width'] = maxWidth;
    data['max_height'] = maxHeight;
    data['url'] = url;
    data['preview_url'] = previewUrl;
    return data;
  }
}

class VideoData {
  int? width;
  int? height;
  String? url;
  String? previewUrl;

  VideoData({this.width, this.height, this.url, this.previewUrl});

  VideoData.fromJson(Map<String, dynamic> json) {
    width = json['width'];
    height = json['height'];
    url = json['url'];
    previewUrl = json['preview_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['width'] = width;
    data['height'] = height;
    data['url'] = url;
    data['preview_url'] = previewUrl;
    return data;
  }
}

class Paging {
  Cursors? cursors;

  Paging({this.cursors});

  Paging.fromJson(Map<String, dynamic> json) {
    cursors =
        json['cursors'] != null ? Cursors.fromJson(json['cursors']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (cursors != null) {
      data['cursors'] = cursors!.toJson();
    }
    return data;
  }
}

class Cursors {
  String? before;
  String? after;

  Cursors({this.before, this.after});

  Cursors.fromJson(Map<String, dynamic> json) {
    before = json['before'];
    after = json['after'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['before'] = before;
    data['after'] = after;
    return data;
  }
}
