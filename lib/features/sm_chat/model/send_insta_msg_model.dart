SendinstagreamMessageModel deserializeSendinstagreamMessageModel(
        Map<String, dynamic> json) =>
    SendinstagreamMessageModel.fromJson(json);

class SendinstagreamMessageModel {
  bool? status;
  String? message;
  InstaData? data;

  SendinstagreamMessageModel({this.status, this.message, this.data});

  SendinstagreamMessageModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    data = json['data'] != null ? InstaData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class InstaData {
  String? recipientId;
  String? messageId;

  InstaData({this.recipientId, this.messageId});

  InstaData.fromJson(Map<String, dynamic> json) {
    recipientId = json['recipient_id'];
    messageId = json['message_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['recipient_id'] = recipientId;
    data['message_id'] = messageId;
    return data;
  }
}
