SendfacebookMessageModel deserializeSendfacebookMessageModel(
        Map<String, dynamic> json) =>
    SendfacebookMessageModel.fromJson(json);

class SendfacebookMessageModel {
  bool? status;
  String? message;
  FacebookData? data;

  SendfacebookMessageModel({this.status, this.message, this.data});

  SendfacebookMessageModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    data = json['data'] != null ? FacebookData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class FacebookData {
  String? recipientId;
  String? messageId;

  FacebookData({this.recipientId, this.messageId});

  FacebookData.fromJson(Map<String, dynamic> json) {
    recipientId = json['recipient_id'];
    messageId = json['message_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['recipient_id'] = recipientId;
    data['message_id'] = messageId;
    return data;
  }
}
