TelegramUserListModel deserializeTelegramUserListModel(Map<String, dynamic> json) =>
    TelegramUserListModel.fromJson(json);

class TelegramUserListModel {
  final bool status;
  final String message;
  final List<DialogData> data;
  final int count;

  TelegramUserListModel({
    required this.status,
    required this.message,
    required this.data,
    required this.count,
  });

  TelegramUserListModel copyWith({
    bool? status,
    String? message,
    List<DialogData>? data,
    int? count,
  }) {
    return TelegramUserListModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
      count: count ?? this.count,
    );
  }

  factory TelegramUserListModel.fromJson(Map<String, dynamic> json) {
    return TelegramUserListModel(
      status: json['status'] as bool,
      message: json['message'] as String,
      data: (json['data'] as List<dynamic>).map((e) => DialogData.fromJson(e as Map<String, dynamic>)).toList(),
      count: json['count'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data.map((e) => e.toJson()).toList(),
      'count': count,
    };
  }
}

class DialogData {
  final int id;
  final String name;
  final String? username;
  final String type;
  final int unreadCount;
  final DateTime lastMessageDate;
  final bool isPinned;
  final LastMessage? lastMessage;

  DialogData({
    required this.id,
    required this.name,
    this.username,
    required this.type,
    required this.unreadCount,
    required this.lastMessageDate,
    required this.isPinned,
    this.lastMessage,
  });

  DialogData copyWith({
    int? id,
    String? name,
    String? username,
    String? type,
    int? unreadCount,
    DateTime? lastMessageDate,
    bool? isPinned,
    LastMessage? lastMessage,
  }) {
    return DialogData(
      id: id ?? this.id,
      name: name ?? this.name,
      username: username ?? this.username,
      type: type ?? this.type,
      unreadCount: unreadCount ?? this.unreadCount,
      lastMessageDate: lastMessageDate ?? this.lastMessageDate,
      isPinned: isPinned ?? this.isPinned,
      lastMessage: lastMessage ?? this.lastMessage,
    );
  }

  factory DialogData.fromJson(Map<String, dynamic> json) {
    return DialogData(
      id: json['id'] as int,
      name: json['name'] as String,
      username: json['username'] as String?,
      type: json['type'] as String,
      unreadCount: json['unread_count'] as int,
      lastMessageDate: DateTime.parse(json['last_message_date'] as String),
      isPinned: json['is_pinned'] as bool,
      lastMessage:
          json['last_message'] != null ? LastMessage.fromJson(json['last_message'] as Map<String, dynamic>) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'username': username,
      'type': type,
      'unread_count': unreadCount,
      'last_message_date': lastMessageDate.toIso8601String(),
      'is_pinned': isPinned,
      'last_message': lastMessage?.toJson(),
    };
  }
}

class LastMessage {
  final String text;
  final DateTime date;
  final int? fromId;

  LastMessage({
    required this.text,
    required this.date,
    this.fromId,
  });

  LastMessage copyWith({
    String? text,
    DateTime? date,
    int? fromId,
  }) {
    return LastMessage(
      text: text ?? this.text,
      date: date ?? this.date,
      fromId: fromId ?? this.fromId,
    );
  }

  factory LastMessage.fromJson(Map<String, dynamic> json) {
    return LastMessage(
      text: json['text'] as String,
      date: DateTime.parse(json['date'] as String),
      fromId: json['from_id'] != null ? json['from_id'] as int : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'date': date.toIso8601String(),
      'from_id': fromId,
    };
  }
}
