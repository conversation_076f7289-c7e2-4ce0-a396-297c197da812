ChatMessageListModel deserializeChatMessageListModel(Map<String, dynamic> json) => ChatMessageListModel.fromJson(json);

// class ChatMessageListModel {
//   int? count;
//   String? next;
//   String? previous;
//   Results? results;

//   ChatMessageListModel({this.count, this.next, this.previous, this.results});

//   ChatMessageListModel.fromJson(Map<String, dynamic> json) {
//     count = json['count'];
//     next = json['next'];
//     previous = json['previous'];
//     results =
//         json['results'] != null ? Results.fromJson(json['results']) : null;
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['count'] = count;
//     data['next'] = next;
//     data['previous'] = previous;
//     if (results != null) {
//       data['results'] = results!.toJson();
//     }
//     return data;
//   }
// }

// class Results {
//   bool? status;
//   String? message;
//   List<ChatMessageData>? data;

//   Results({this.status, this.message, this.data});

//   Results.fromJson(Map<String, dynamic> json) {
//     status = json['status'];
//     message = json['message'];
//     if (json['data'] != null) {
//       data = <ChatMessageData>[];
//       json['data'].forEach((v) {
//         data!.add(ChatMessageData.fromJson(v));
//       });
//     }
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['status'] = status;
//     data['message'] = message;
//     if (this.data != null) {
//       data['data'] = this.data!.map((v) => v.toJson()).toList();
//     }
//     return data;
//   }
// }

// class ChatMessageData {
//   int? id;
//   String? message;
//   String? type;
//   bool isRead;
//   String? createdAt;
//   int? sentBy;

//   ChatMessageData({
//     this.id,
//     this.message,
//     this.type,
//     this.isRead = false,
//     this.createdAt,
//     this.sentBy,
//   });

//   factory ChatMessageData.fromJson(Map<String, dynamic> json) {
//     return ChatMessageData(
//       id: json['id'],
//       message: json['message'],
//       type: json['type'],
//       isRead: json['is_read'],
//       createdAt: json['created_at'],
//       sentBy: json['sent_by'],
//     );
//   }

//   factory ChatMessageData.fromMap(Map<String, dynamic> map) {
//     return ChatMessageData(
//       id: map['id'],
//       message: map['message'],
//       type: map['type'],
//       isRead: map['is_read'],
//       createdAt: map['created_at'],
//       sentBy: map['sent_by'],
//     );
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     data['message'] = message;
//     data['type'] = type;
//     data['is_read'] = isRead;
//     data['created_at'] = createdAt;
//     data['sent_by'] = sentBy;
//     return data;
//   }
// }

class ChatMessageListModel {
  int? count;
  String? next;
  String? previous;
  ChatResults? results;

  ChatMessageListModel({this.count, this.next, this.previous, this.results});

  ChatMessageListModel.fromJson(Map<String, dynamic> json) {
    count = json['count'];
    next = json['next'];
    previous = json['previous'];
    results = json['results'] != null ? ChatResults.fromJson(json['results']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['count'] = count;
    data['next'] = next;
    data['previous'] = previous;
    if (results != null) {
      data['results'] = results!.toJson();
    }
    return data;
  }
}

class ChatResults {
  final bool status;
  final String message;
  final List<ChatMessageData> data;

  ChatResults({
    required this.status,
    required this.message,
    required this.data,
  });

  ChatResults copyWith({
    bool? status,
    String? message,
    List<ChatMessageData>? data,
  }) {
    return ChatResults(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  factory ChatResults.fromJson(Map<String, dynamic> json) {
    return ChatResults(
      status: json['status'],
      message: json['message'],
      data: (json['data'] as List).map((e) => ChatMessageData.fromJson(e)).toList(),
    );
  }
  factory ChatResults.fromMap(Map<String, dynamic> map) {
    return ChatResults(
      status: map['status'],
      message: map['message'],
      data: (map['data'] as List).map((e) => ChatMessageData.fromMap(e)).toList(),
    );
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['data'] = this.data.map((v) => v.toJson()).toList();
    return data;
  }
}

class ChatMessageData {
  int? id;
  String? message;
  String? type;
  bool isRead;
  String? createdAt;
  int? sentBy;
  int? postId;
  String? postMediaUrl;
  List<String>? file;
  String? title;
  String? description;
  String? postError;
  PostUser? postUser;

  ChatMessageData({
    this.id,
    this.message,
    this.file = const [],
    this.type,
    this.isRead = false,
    this.createdAt,
    this.sentBy,
    this.postId,
    this.postMediaUrl,
    this.title,
    this.description,
    this.postError,
    this.postUser,
  });

  ChatMessageData copyWith({
    int? id,
    String? message,
    String? type,
    bool? isRead,
    String? createdAt,
    int? sentBy,
    int? postId,
    String? postMediaUrl,
    List<String>? file,
    String? title,
    String? description,
    String? postError,
    PostUser? postUser,
  }) {
    return ChatMessageData(
      id: id ?? this.id,
      message: message ?? this.message,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      sentBy: sentBy ?? this.sentBy,
      postId: postId ?? this.postId,
      postMediaUrl: postMediaUrl ?? this.postMediaUrl,
      file: file ?? this.file,
      title: title ?? this.title,
      description: description ?? this.description,
      postError: postError ?? this.postError,
      postUser: postUser ?? this.postUser,
    );
  }

  factory ChatMessageData.fromJson(Map<String, dynamic> json) {
    return ChatMessageData(
      id: json['id'],
      type: json['type'],
      isRead: json['is_read'],
      createdAt: json['created_at'],
      file: json['file'] != null ? List<String>.from(json['file']) : null,
      sentBy: json['sent_by'],
      message: json['message'],
      postId: json['post_id'],
      postMediaUrl: json['post_media_url'],
      title: json['title'],
      description: json['description'],
      postError: json['post_error'],
      postUser: json['post_user'] != null ? PostUser.fromJson(json['post_user']) : null,
    );
  }
  factory ChatMessageData.fromMap(Map<String, dynamic> map) {
    return ChatMessageData(
      id: map['id'],
      message: map['message'],
      type: map['type'],
      isRead: map['is_read'],
      file: map['file'] != null ? List<String>.from(map['file']) : null,
      createdAt: map['created_at'],
      sentBy: map['sent_by'],
      postId: map['post_id'],
      postMediaUrl: map['post_media_url'],
      title: map['title'],
      description: map['description'],
      postError: map['post_error'],
      postUser: map['post_user'] != null ? PostUser.fromJson(map['post_user']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['message'] = message;
    data['type'] = type;
    data['is_read'] = isRead;
    data['created_at'] = createdAt;
    data['sent_by'] = sentBy;
    // data['file'] = List<String>.from(file!);

    data['post_id'] = postId;
    data['post_media_url'] = postMediaUrl;
    data['title'] = title;
    data['description'] = description;
    data['post_error'] = postError;
    if (postUser != null) {
      data['post_user'] = postUser!.toJson();
    }

    return data;
  }
}

class PostUser {
  final int id;
  final String username;
  final String name;
  final String profileImage;

  PostUser({
    required this.id,
    required this.username,
    required this.name,
    required this.profileImage,
  });

  PostUser copyWith({
    int? id,
    String? username,
    String? name,
    String? profileImage,
  }) {
    return PostUser(
      id: id ?? this.id,
      username: username ?? this.username,
      name: name ?? this.name,
      profileImage: profileImage ?? this.profileImage,
    );
  }

  factory PostUser.fromJson(Map<String, dynamic> json) {
    return PostUser(
      id: json['id'],
      username: json['username'],
      name: json['name'],
      profileImage: json['profile_image'] ?? '',
    );
  }
  factory PostUser.fromMap(Map<String, dynamic> map) {
    return PostUser(
      id: map['id'],
      username: map['username'],
      name: map['name'],
      profileImage: map['profile_image'] ?? '',
    );
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['username'] = username;
    data['name'] = name;
    data['profile_image'] = profileImage;
    return data;
  }
}
