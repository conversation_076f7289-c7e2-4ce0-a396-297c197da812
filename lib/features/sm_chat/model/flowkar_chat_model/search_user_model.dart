class SearchUserModel {
  bool? status;
  String? message;
  List<SearchUserData>? data;

  SearchUserModel({this.status, this.message, this.data});

  SearchUserModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <SearchUserData>[];
      json['data'].forEach((v) {
        data!.add(SearchUserData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SearchUserData {
  int? userId;
  String? name;
  String? userName;
  String? profileImage;

  SearchUserData({this.userId, this.name, this.userName, this.profileImage});

  SearchUserData.fromJson(Map<String, dynamic> json) {
    userId = json['id'];
    name = json['name'];
    userName = json['username'];
    profileImage = json['profile'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = userId;
    data['name'] = name;
    data['username'] = userName;
    data['profile'] = profileImage;
    return data;
  }
}
