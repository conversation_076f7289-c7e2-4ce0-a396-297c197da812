SmchatListModel deserializeSmchatListModel(Map<String, dynamic> json) => SmchatListModel.fromJson(json);

class SmchatListModel {
  bool? status;
  String? message;
  SmchatData? data;

  SmchatListModel({this.status, this.message, this.data});

  SmchatListModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    data = json['data'] != null ? SmchatData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class SmchatData {
  List<Facebook>? facebook;
  List<Instagram>? instagram;

  SmchatData({this.facebook, this.instagram});

  SmchatData.fromJson(Map<String, dynamic> json) {
    if (json['facebook'] != null) {
      facebook = <Facebook>[];
      json['facebook'].forEach((v) {
        facebook!.add(new Facebook.fromJson(v));
      });
    }
    if (json['instagram'] != null) {
      instagram = <Instagram>[];
      json['instagram'].forEach((v) {
        instagram!.add(new Instagram.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (facebook != null) {
      data['facebook'] = facebook!.map((v) => v.toJson()).toList();
    }
    if (instagram != null) {
      data['instagram'] = instagram!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Facebook {
  String? name;
  String? conversationId;
  String? ownerId;
  String? chattingUserId;
  String? latestMessage;

  Facebook({this.name, this.conversationId, this.ownerId, this.chattingUserId, this.latestMessage});

  Facebook.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    conversationId = json['conversation_id'];
    ownerId = json['owner_id'];
    chattingUserId = json['chatting_user_id'];
    latestMessage = json['last_message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['conversation_id'] = conversationId;
    data['owner_id'] = ownerId;
    data['chatting_user_id'] = chattingUserId;
    data['last_message'] = latestMessage;
    return data;
  }
}

class Instagram {
  String? name;
  String? conversationId;
  String? ownerId;
  String? chattingUserId;
  String? latestMessage;

  Instagram({this.name, this.conversationId, this.ownerId, this.chattingUserId, this.latestMessage});

  Instagram.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    conversationId = json['conversation_id'];
    ownerId = json['owner_id'];
    chattingUserId = json['chatting_user_id'];
    latestMessage = json['last_message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['conversation_id'] = conversationId;
    data['owner_id'] = ownerId;
    data['chatting_user_id'] = chattingUserId;
    data['last_message'] = latestMessage;
    return data;
  }
}
