TelegramUserChatListModel deserializeTelegramUserChatListModel(Map<String, dynamic> json) =>
    TelegramUserChatListModel.fromJson(json);

class TelegramUserChatListModel {
  final bool status;
  final String message;
  final String targetUserId;
  final List<TelegramMessage> data;
  final int totalCount;
  final String note;

  TelegramUserChatListModel({
    required this.status,
    required this.message,
    required this.targetUserId,
    required this.data,
    required this.totalCount,
    required this.note,
  });

  TelegramUserChatListModel copyWith({
    bool? status,
    String? message,
    String? targetUserId,
    List<TelegramMessage>? data,
    int? totalCount,
    String? note,
  }) {
    return TelegramUserChatListModel(
      status: status ?? this.status,
      message: message ?? this.message,
      targetUserId: targetUserId ?? this.targetUserId,
      data: data ?? this.data,
      totalCount: totalCount ?? this.totalCount,
      note: note ?? this.note,
    );
  }

  factory TelegramUserChatListModel.fromJson(Map<String, dynamic> json) {
    return TelegramUserChatListModel(
      status: json['status'],
      message: json['message'],
      targetUserId: json['target_user_id'],
      data: (json['data'] as List<dynamic>).map((e) => TelegramMessage.fromJson(e)).toList(),
      totalCount: json['total_count'],
      note: json['note'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'target_user_id': targetUserId,
      'data': data.map((e) => e.toJson()).toList(),
      'total_count': totalCount,
      'note': note,
    };
  }
}

class TelegramMessage {
  final int? id;
  final DateTime date;
  final String? text;
  final int? fromId;
  final int? toId;
  final bool? isOutgoing;
  final int? replyToMsgId;
  final String? mediaType;
  final dynamic mediaInfo;

  TelegramMessage({
    this.id,
    required this.date,
    this.text,
    this.fromId,
    this.toId,
    this.isOutgoing,
    this.replyToMsgId,
    this.mediaType,
    this.mediaInfo,
  });

  TelegramMessage copyWith({
    int? id,
    DateTime? date,
    String? text,
    int? fromId,
    int? toId,
    bool? isOutgoing,
    int? replyToMsgId,
    String? mediaType,
    dynamic mediaInfo,
  }) {
    return TelegramMessage(
      id: id ?? this.id,
      date: date ?? this.date,
      text: text ?? this.text,
      fromId: fromId ?? this.fromId,
      toId: toId ?? this.toId,
      isOutgoing: isOutgoing ?? this.isOutgoing,
      replyToMsgId: replyToMsgId ?? this.replyToMsgId,
      mediaType: mediaType ?? this.mediaType,
      mediaInfo: mediaInfo ?? this.mediaInfo,
    );
  }

  factory TelegramMessage.fromJson(Map<String, dynamic> json) {
    return TelegramMessage(
      id: json['id'],
      date: DateTime.parse(json['date']),
      text: json['text'],
      fromId: json['from_id'],
      toId: json['to_id'],
      isOutgoing: json['is_outgoing'],
      replyToMsgId: json['reply_to_msg_id'],
      mediaType: json['media_type'],
      mediaInfo: json['media_info'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'text': text,
      'from_id': fromId,
      'to_id': toId,
      'is_outgoing': isOutgoing,
      'reply_to_msg_id': replyToMsgId,
      'media_type': mediaType,
      'media_info': mediaInfo,
    };
  }
}
