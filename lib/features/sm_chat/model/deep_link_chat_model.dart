DeepLinkChatModel deserializeDeepLinkChatModel(Map<String, dynamic> json) => DeepLinkChatModel.fromJson(json);

class DeepLinkChatModel {
  final bool status;
  final String message;
  final UserData userData;

  DeepLinkChatModel({
    required this.status,
    required this.message,
    required this.userData,
  });

  DeepLinkChatModel copyWith({
    bool? status,
    String? message,
    UserData? userData,
  }) {
    return DeepLinkChatModel(
      status: status ?? this.status,
      message: message ?? this.message,
      userData: userData ?? this.userData,
    );
  }

  factory DeepLinkChatModel.fromJson(Map<String, dynamic> json) {
    return DeepLinkChatModel(
      status: json['status'],
      message: json['message'],
      userData: UserData.fromJson(json['user_data']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'user_data': userData.toJson(),
    };
  }
}

class UserData {
  final int userId;
  final String username;
  final String name;
  final String profileImage;

  UserData({
    required this.userId,
    required this.username,
    required this.name,
    required this.profileImage,
  });

  UserData copyWith({
    int? userId,
    String? username,
    String? name,
    String? profileImage,
  }) {
    return UserData(
      userId: userId ?? this.userId,
      username: username ?? this.username,
      name: name ?? this.name,
      profileImage: profileImage ?? this.profileImage,
    );
  }

  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
      userId: json['user_id'],
      username: json['username'],
      name: json['name'],
      profileImage: json['profile_image'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'username': username,
      'name': name,
      'profile_image': profileImage,
    };
  }
}
