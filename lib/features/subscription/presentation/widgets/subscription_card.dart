import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/subscription/model/subscription_plan_model.dart';

class SubscriptionCard extends StatelessWidget {
  final String title;
  final bool isActive;
  final bool isOptin;
  final List<PlanFeature> features;

  const SubscriptionCard({
    super.key,
    required this.title,
    required this.isActive,
    required this.features,
    required this.isOptin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      margin: EdgeInsets.all(16.0),
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            offset: Offset(0, 4),
            color: Colors.black.withOpacity(0.25),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 8.h),
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).primaryColor,
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
              ),
              Container(
                width: 82.w,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomRight,
                    end: Alignment.topLeft,
                    colors: [
                      Color(0XFF563D39),
                      Color(0XFFBC857D),
                    ],
                  ),
                ),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    CustomImageView(
                      height: 21.h,
                      width: 21.w,
                      imagePath: Assets.images.svg.subscription.svgStars.path,
                      margin: EdgeInsets.symmetric(vertical: 12.h, horizontal: 32.w),
                    ),
                    title == "FREE"
                        ? SizedBox.shrink()
                        : Text(
                            isOptin ? Lang.current.lbl_active : Lang.current.lbl_upgrade,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Theme.of(context).customColors.white,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                          )
                  ],
                ),
              ),
            ],
          ),
          Divider(color: Theme.of(context).customColors.authsubtitlecolor, height: 1),
          Stack(
            alignment: Alignment.centerRight,
            children: [
              Container(
                height: 159.h,
                width: 82.w,
                decoration: BoxDecoration(
                  color: Theme.of(context).customColors.authsubtitlecolor.withOpacity(0.3),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 14.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: features
                      .map(
                        (feature) => Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Padding(
                              padding: EdgeInsets.symmetric(vertical: 5.h),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    feature.name,
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          color: Theme.of(context).customColors.black,
                                          fontSize: 14.sp,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              width: 82.w,
                              child: CustomImageView(
                                height: 22.0.h,
                                width: 22.0.w,
                                fit: BoxFit.contain,
                                imagePath: feature.isAvailable
                                    ? Assets.images.pngs.subscription.pngIsActive.path
                                    : Assets.images.pngs.subscription.pngIsUnactive.path,
                              ),
                            ),
                          ],
                        ),
                      )
                      .toList(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
