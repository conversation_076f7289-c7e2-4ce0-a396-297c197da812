import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/subscription/model/subscription_plan_model.dart';
import 'package:flowkar/features/subscription/presentation/widgets/subscription_card.dart';
import 'package:flowkar/features/survey_form/bloc/survey_bloc.dart';
import 'package:flowkar/core/helpers/vibration_helper.dart';

class SubscriptionPlanScreen extends StatefulWidget {
  const SubscriptionPlanScreen({super.key});

  static Widget builder(BuildContext context) {
    return SubscriptionPlanScreen();
  }

  @override
  State<SubscriptionPlanScreen> createState() => _SubscriptionPlanScreenState();
}

List<SubscriptionPlan> plans = [
  SubscriptionPlan(
    title: "FREE",
    isActive: true,
    features: [
      PlanFeature(name: "1 Post in 24h", isAvailable: true),
      PlanFeature(name: "Analytics Reports", isAvailable: false),
      PlanFeature(name: "Social Media Chat", isAvailable: false),
      PlanFeature(name: "Single Brand Access", isAvailable: true),
      PlanFeature(name: "User Role management", isAvailable: false),
    ],
  ),
  SubscriptionPlan(
    title: "PREMIUM",
    isActive: false,
    features: [
      PlanFeature(name: "Unlimited Posts", isAvailable: true),
      PlanFeature(name: "Analytics Reports", isAvailable: true),
      PlanFeature(name: "Social Media Chat", isAvailable: true),
      PlanFeature(name: "Multiple Brands Access", isAvailable: true),
      PlanFeature(name: "User Role management", isAvailable: true),
    ],
  ),
];

class _SubscriptionPlanScreenState extends State<SubscriptionPlanScreen> {
  late PageController _pageController = PageController(viewportFraction: 1, keepPage: true);
  int _currentPage = 0;
  bool isOptin = false;

  @override
  void initState() {
    isOptin = Prefobj.preferences?.get(Prefkeys.ISOPTIN) ?? false;
    _pageController = PageController(viewportFraction: 1, keepPage: true, initialPage: isOptin ? 1 : 0);
    _currentPage = isOptin ? 1 : 0;

    super.initState();
    _pageController.addListener(() {
      int next = _pageController.page!.round();
      if (_currentPage != next) {
        setState(() {
          _currentPage = next;
        });
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // setState(() {
    //   isOptin = Prefobj.preferences?.get(Prefkeys.ISOPTIN) ?? false;
    // });

    return Scaffold(
      appBar: _buildSubscriptionAppBar(),
      body: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          CustomImageView(
            width: MediaQuery.of(context).size.width,
            imagePath: Assets.images.svg.subscription.svgSubBottomBg.path,
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              CustomImageView(
                height: 260.h,
                width: 268.w,
                imagePath: Assets.images.svg.subscription.svgSubscription.path,
              ),
              buildSizedBoxH(10),
              _buildDetailsText(),
              _buildSubscriptionPlans(),
              buildSizedBoxH(34),
              _currentPage == 1
                  ? _buildOptInButton()
                  // : SizedBox.shrink(),
                  : Text(
                      "Please swipe right to explore Plans",
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).customColors.white,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                          ),
                    ),
              buildSizedBoxH(8.0),
            ],
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildSubscriptionAppBar() {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          Lang.current.lbl_subscription_plan,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  Widget _buildDetailsText() {
    return Column(
      children: [
        Text(
          Lang.current.lbl_empower_your_growth,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontSize: 18.sp,
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w700,
              ),
        ),
        buildSizedBoxH(12),
        Container(
          height: 3.0.h,
          width: 118.0.w,
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(10.0.r),
          ),
        ),
      ],
    );
  }

  Widget _buildSubscriptionPlans() {
    return SizedBox(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 237.h,
            child: PageView.builder(
              controller: _pageController,
              itemCount: plans.length,
              itemBuilder: (context, index) {
                return SubscriptionCard(
                  title: plans[index].title,
                  isActive: plans[index].isActive,
                  features: plans[index].features,
                  isOptin: isOptin,
                );
              },
            ),
          ),
          buildSizedBoxH(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              plans.length,
              (index) => AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                margin: EdgeInsets.symmetric(horizontal: 4.w),
                height: 4.h,
                width: _currentPage == index ? 16.w : 4.w,
                decoration: BoxDecoration(
                  color: Theme.of(context).customColors.white,
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
            ),
          ),
          buildSizedBoxH(16),
        ],
      ),
    );
  }

  Widget _buildOptInButton() {
    isOptin = Prefobj.preferences?.get(Prefkeys.ISOPTIN) ?? false;
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        BlocBuilder<SurveyBloc, SurveyState>(
          builder: (context, state) {
            return CustomElevatedButton(
              height: 50.h,
              width: 159.w,
              isLoading: state.updateSurveyLoading,
              isDisabled: state.updateSurveyLoading,
              text: (state.updateSurveyModel?.isOptIn ?? isOptin) ? Lang.current.lbl_opted_in : Lang.current.lbl_opt_in,
              buttonTextStyle: Theme.of(context).textTheme.labelLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 16.0.sp,
                    color: Theme.of(context).primaryColor,
                  ),
              buttonStyle: ButtonStyle(
                  backgroundColor: WidgetStatePropertyAll(Theme.of(context).customColors.white),
                  shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.0.r),
                  ))),
              onPressed: (state.updateSurveyModel?.isOptIn ?? isOptin)
                  ? () {}
                  : () {
                      VibrationHelper.singleShortBuzz();
                      showDialog(
                        context: context,
                        builder: (ctx) {
                          return CustomAlertDialog(
                              width: 380.w,
                              isredius: true,
                              imageheight: 45.h,
                              imagewidth: 45.w,
                              fit: BoxFit.contain,
                              imagePath: Assets.images.svg.homeFeed.svgSubscription.path,
                              title: Lang.of(ctx).lbl_subscription,
                              subtitle: "",
                              onConfirmButtonPressed: () async {
                                NavigatorService.goBack();
                                context.read<SurveyBloc>().add(UpdateSurveyAPI(isoptin: true, context: context));
                                setState(() {});
                                VibrationHelper.singleShortBuzz();
                              },
                              confirmButtonText: "Yes",
                              cancelButtonText: "No",
                              isLoading: false,
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    'Do you want to apply for our',
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        fontWeight: FontWeight.w400,
                                        fontSize: 14.sp,
                                        color: Theme.of(context).iconTheme.color),
                                    textAlign: TextAlign.center,
                                  ),
                                  SizedBox(height: 5),

                                  // Highlighted text
                                  Text(
                                    '3 Months Free Premium Subscription',
                                    textAlign: TextAlign.center,
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          fontWeight: FontWeight.w600,
                                          fontSize: 14.sp,
                                          color: Theme.of(context).iconTheme.color,
                                        ),
                                  ),
                                  SizedBox(height: 5),

                                  // Highlighted text
                                  Text(
                                    'which will let you use all Premium features.',
                                    textAlign: TextAlign.center,
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          fontWeight: FontWeight.w400,
                                          fontSize: 14.sp,
                                          color: Theme.of(context).iconTheme.color,
                                        ),
                                  ),
                                  SizedBox(height: 16),
                                ],
                              ));
                        },
                      );
                    },
            );
          },
        ),
      ],
    );
  }
}
