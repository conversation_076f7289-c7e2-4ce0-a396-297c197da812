import 'dart:async';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/deep_link_post/deep_link_post.dart';
import 'package:flowkar/features/notification/bloc/notification_bloc.dart';
import 'package:flowkar/features/notification/model/notification_list_model.dart';
import 'package:flowkar/features/notification/widget/get_post_by_id_screen.dart';
import 'package:flowkar/features/notification/widget/notification_screen_shimmer.dart';
import 'package:flowkar/features/survey_form/bloc/survey_bloc.dart';
import 'package:flowkar/features/widgets/common/get_user_profile_by_id/get_user_profile_by_id.dart';
import 'package:flowkar/features/widgets/custom/custom_conditional_scroll_physics.dart';
import 'package:flutter/cupertino.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:intl/intl.dart';

class NotificationPage extends StatefulWidget {
  const NotificationPage({super.key});

  static Widget builder(BuildContext context) {
    return const NotificationPage();
  }

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends State<NotificationPage> {
  late ScrollController _scrollController;

  String currentUserId = '';

  Map<String, bool> showButtons = {};

  @override
  void initState() {
    super.initState();
    currentUserId = Prefobj.preferences!.get(Prefkeys.USER_ID).toString();
    final state = context.read<NotificationBloc>().state;
    if (state.notification.isNotEmpty) {
      state.notification.clear();
    }
    context.read<NotificationBloc>().add(const GetNotificationEvent(page: 1));
    _scrollController = ScrollController()..addListener(_scrollListener);
  }

  Future<void> _refreshFeed() async {
    final state = context.read<NotificationBloc>().state;
    state.notification.clear();
    context.read<NotificationBloc>().add(const GetNotificationEvent(page: 1));
  }

  void _scrollListener() {
    final state = context.read<NotificationBloc>().state;
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent && !state.isLoadingMore) {
      context.read<NotificationBloc>().add(GetNotificationEvent(page: state.page + 1));
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(right: 2.w, left: 2.w),
      child: Scaffold(
        appBar: CustomAppbar(
          hasLeadingIcon: true,
          height: 18.h,
          leading: [
            InkWell(
              onTap: () {
                context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
                PersistentNavBarNavigator.pop(context);
              },
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: CustomImageView(
                  imagePath: Assets.images.svg.authentication.icBackArrow.path,
                  height: 16.h,
                ),
              ),
            ),
            buildSizedBoxW(20.w),
            Text(
              Lang.of(context).lbl_notification,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 18.sp,
                  ),
            ),
          ],
        ),
        body: Padding(
          padding: EdgeInsets.only(right: 3.w, left: 3.w),
          child: BlocListener<ConnectivityBloc, ConnectivityState>(
            listener: (context, connectivityState) {
              if (connectivityState.isReconnected) {
                context.read<NotificationBloc>().add(const GetNotificationEvent(page: 1));
              }
            },
            child: BlocBuilder<NotificationBloc, NotificationState>(
              builder: (context, state) {
                if (state.isloding) {
                  return NotificationPageShimmer();
                }

                // Compute filtered notifications here
                final filteredNotifications = state.notification.where((notif) {
                  if (notif.type == "user_invited") {
                    return notif.invitedUsers != null && notif.invitedUsers!.isNotEmpty;
                  }
                  return true; // Keep all non-"user_invited" notifications
                }).toList();

                return LiquidPullToRefresh(
                  color: Theme.of(context).primaryColor.withOpacity(0.5),
                  showChildOpacityTransition: false,
                  backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
                  onRefresh: _refreshFeed,
                  child: filteredNotifications.isEmpty
                      ? _buildNoNotification()
                      : ListView(
                          controller: _scrollController,
                          physics: ConditionalAlwaysScrollPhysics(controller: _scrollController),
                          children: [
                            _buildNotificationList(filteredNotifications, state),
                            if (state.isLoadingMore)
                              Padding(
                                padding: EdgeInsets.symmetric(vertical: 16.h),
                                child: Center(
                                  child: CupertinoActivityIndicator(
                                    color: Theme.of(context).colorScheme.primary,
                                  ),
                                ),
                              ),
                          ],
                        ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationList(List<NotificationData> filteredNotifications, NotificationState state) {
    List<NotificationModel> notifications = filteredNotifications.map((notif) {
      return NotificationModel(
        text: notif.message,
        date: DateTime.parse(notif.createdAt),
        profileImage: notif.user?.profileImage ?? '',
        originalData: notif,
      );
    }).toList();

    final grouped = groupNotificationsByDate(notifications);
    final nonEmptyGroups = grouped.entries.where((e) => e.value.isNotEmpty).toList();

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: nonEmptyGroups.length,
      itemBuilder: (context, index) {
        final group = nonEmptyGroups[index];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              child: Text(
                group.key,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 14.sp,
                    ),
              ),
            ),
            ...group.value.map((notification) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 12.0),
                child: _buildNotificationItem(state, index, notification.originalData),
              );
            }),
          ],
        );
      },
    );
  }

  Widget _buildNoNotification() {
    return ListView(
      physics: AlwaysScrollableScrollPhysics(),
      children: [
        buildSizedBoxH(MediaQuery.of(context).size.height / 10),
        ExceptionWidget(
          imagePath: AssetConstants.pngNoNotification,
          title: Lang.of(context).lbl_no_new_notification,
          subtitle: Lang.of(context).lbl_notification_subtitle,
          showButton: true,
          buttonText: Lang.of(context).lbl_go_to_home,
          onButtonPressed: () {
            context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
            PersistentNavBarNavigator.pop(context);
          },
        ),
      ],
    );
  }

  Widget _buildNotificationItem(NotificationState state, int index, NotificationData notif) {
    // final notif = state.notification[index];
    final String notificationId = notif.id.toString();

    // Initialize the show/hide state if not already set
    if (!showButtons.containsKey(notificationId)) {
      showButtons[notificationId] = true; // By default, buttons are visible
    }
    // DateTime dateTime =
    //     DateTime.parse(notif.createdAt ?? DateTime.now().toIso8601String()).add(DateTime.now().timeZoneOffset);

    return Dismissible(
      key: Key(notificationId.toString()),
      direction: DismissDirection.endToStart, // Swipe direction
      background: Container(
        decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withOpacity(0.3), borderRadius: BorderRadius.circular(4.0.r)),
        child: Align(
          alignment: Alignment.centerRight,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                CustomImageView(
                  imagePath: Assets.images.svg.setting.svgDeleteAccount.path,
                  height: 20.0.h,
                  // color: Theme.of(context).customColors.white,
                ),
                buildSizedBoxW(10),
                Text("Delete")
              ],
            ),
          ),
        ),
      ),
      confirmDismiss: (direction) {
        return showDialog(
          context: context,
          builder: (ctx) {
            bool isLoading = false;
            return StatefulBuilder(builder: (ctx, setState) {
              return CustomAlertDialog(
                  imagePath: Assets.images.pngs.pngNoPost.path,
                  title: Lang.of(context).lbl_delete_notification,
                  subtitle: "",
                  onConfirmButtonPressed: () async {
                    setState(() {
                      isLoading = true;
                    });
                    if (!mounted) return;
                    context
                        .read<NotificationBloc>()
                        .add(DeleteNotificationEvent(context, notificationId: int.parse(notificationId)));
                    if (!mounted) return;
                    FocusScope.of(context).requestFocus(FocusNode());
                  },
                  confirmButtonText: Lang.of(ctx).lbl_delete,
                  isLoading: isLoading);
            });
          },
        );
      },
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        child: Column(
          children: [
            InkWell(
              onTap: () {
                if (notif.type == "liked_post" || notif.type == "tag") {
                  PersistentNavBarNavigator.pushNewScreen(context,
                      screen: GetPostByIdScreen(
                        postId: notif.post?.id.toString() ?? '',
                        isTextPost: (notif.post?.postFiles.isEmpty ?? true) ? true : false,
                        isNotificationPost: true,
                      ));
                } else if (notif.type == "liked_comment" ||
                    notif.type == "comment_post" ||
                    notif.type == "reply_comment") {
                  PersistentNavBarNavigator.pushNewScreen(context,
                      screen: DeepLinkPostScreen(
                        postId: notif.post?.id.toString() ?? '',
                        showCommentBottomSheet: true,
                        isNotificationPost: true,
                      ));
                } else {
                  PersistentNavBarNavigator.pushNewScreen(context,
                      screen: GetUserProfileById(userId: notif.user?.userId, stackonScreen: true));
                }
              },
              child: BlocBuilder<NotificationBloc, NotificationState>(
                builder: (context, state) {
                  return Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(14),
                                  child: CustomImageView(
                                    height: 50,
                                    width: 50,
                                    fit: BoxFit.cover,
                                    radius: BorderRadius.circular(30),
                                    alignment: Alignment.center,
                                    imagePath: (notif.user?.profileImage.isEmpty ?? true)
                                        ? ((notif.invitedUsers?.first.profileImage.isEmpty ?? true)
                                            ? AssetConstants.pngUserReomve
                                            : notif.invitedUsers!.first.profileImage)
                                        : notif.user!.profileImage,
                                  ),
                                ),
                                buildSizedBoxW(8),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      RichText(
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        text: TextSpan(
                                          children: [
                                            TextSpan(
                                              text: notif.message.split(" ").first,
                                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                            ),
                                            TextSpan(
                                              text: ' ${notif.message.split(" ").skip(1).join(" ")}',
                                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      buildSizedBoxH(5),
                                      Text(
                                        getTimeAgoFromUTC(notif.createdAt.toString()),
                                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                              fontSize: 12.sp,
                                              fontWeight: FontWeight.w400,
                                            ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          if (notif.invitedUsers != null &&
                              notif.invitedUsers!.isNotEmpty &&
                              notif.invitedUsers!.first.isAccepted == false)
                            if (notif.type == "user_invited")
                              IconButton(
                                onPressed: () {
                                  setState(() {
                                    showButtons[notificationId] = !(showButtons[notificationId] ?? false);
                                  });
                                },
                                icon: Icon(showButtons[notificationId] ?? false
                                    ? Icons.keyboard_arrow_up_rounded
                                    : Icons.keyboard_arrow_down_rounded),
                              )
                        ],
                      ),
                      if (notif.invitedUsers != null &&
                          notif.invitedUsers!.isNotEmpty &&
                          notif.invitedUsers!.first.isAccepted == false)
                        if (notif.type == "user_invited" && showButtons[notificationId]!)
                          Column(
                            children: [
                              buildSizedBoxH(8.0),
                              _buildAcceptDeclineButtons(context, notif.invitedUsers?.first, state, index),
                              buildSizedBoxH(8.0),
                            ],
                          )
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Map<String, List<NotificationModel>> groupNotificationsByDate(List<NotificationModel> notifications) {
    final now = DateTime.now();
    DateTime today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final last7Days = today.subtract(const Duration(days: 7));
    final last30Days = today.subtract(const Duration(days: 30));

    final grouped = {
      Lang.of(context).lbl_new: <NotificationModel>[],
      Lang.of(context).lbl_yesterday: <NotificationModel>[],
      Lang.of(context).lbl_last_7_day: <NotificationModel>[],
      Lang.of(context).lbl_last_30_days: <NotificationModel>[],
      Lang.of(context).lbl_Older: <NotificationModel>[],
    };

    for (var notif in notifications) {
      final utcDateTime = DateTime.parse(notif.date.toString()).toUtc();
      // Device local time
      DateTime localDateTime = utcDateTime.toLocal();
      String date = DateFormat("yyyy/MM/dd").format(today);
      String dates = DateFormat("yyyy/MM/dd").format(notif.date.toLocal());
      DateTime tempDate = DateFormat("yyyy/MM/dd").parse(date);
      DateTime tempDate2 = DateFormat("yyyy/MM/dd").parse(dates);
      localDateTime = tempDate2;
      today = tempDate;

      if (localDateTime == today) {
        grouped[Lang.of(context).lbl_new]?.add(notif);
      } else if (localDateTime == yesterday) {
        grouped[Lang.of(context).lbl_yesterday]?.add(notif);
      } else if (localDateTime.isAfter(last7Days)) {
        grouped[Lang.of(context).lbl_last_7_day]?.add(notif);
      } else if (localDateTime.isAfter(last30Days)) {
        grouped[Lang.of(context).lbl_last_30_days]?.add(notif);
      } else {
        grouped[Lang.of(context).lbl_Older]?.add(notif);
      }
    }

    return grouped;
  }

  Widget _buildAcceptDeclineButtons(
      BuildContext context, InvitedUser? invitedUser, NotificationState state, int index) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        buildSizedBoxW(58.0),
        Expanded(
          child: CustomElevatedButton(
            height: 40.h,
            brderRadius: 10.r,
            isLoading: state.decliningInviteIds.contains(invitedUser?.id),
            isDisabled: state.approvingInviteIds.isNotEmpty || state.decliningInviteIds.isNotEmpty,
            onPressed: () {
              if (!mounted) return;
              context.read<NotificationBloc>().add(DeclineInviteEvent(context, declineId: invitedUser?.id ?? 0));
            },
            text: "Decline",
            buttonStyle: ButtonStyle(
              padding: WidgetStateProperty.all(EdgeInsets.zero),
              backgroundColor: WidgetStateProperty.all(Theme.of(context).customColors.white),
              shape: WidgetStatePropertyAll(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.r),
                  side: BorderSide(color: Theme.of(context).customColors.primaryColor),
                ),
              ),
            ),
            buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).customColors.primaryColor,
                ),
          ),
        ),
        buildSizedBoxW(20.0),
        Expanded(
          child: CustomElevatedButton(
            height: 40.h,
            brderRadius: 10.r,
            isLoading: state.approvingInviteIds.contains(invitedUser?.id),
            isDisabled: state.approvingInviteIds.isNotEmpty || state.decliningInviteIds.isNotEmpty,
            onPressed: () {
              context.read<NotificationBloc>().add(ApproveInviteEvent(context, inviteId: invitedUser?.id ?? 0));
            },
            text: "Accept",
            buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).customColors.white,
                ),
          ),
        ),
        buildSizedBoxW(20.0),
      ],
    );
  }
}

class NotificationModel {
  final String text;
  final DateTime date;
  final String profileImage;
  final NotificationData originalData;

  NotificationModel({required this.text, required this.date, required this.profileImage, required this.originalData});
}

String getTimeAgoFromUTC(String utcString) {
  try {
    final utcDateTime = DateTime.parse(utcString).toUtc();
    // Device local time
    final localDateTime = utcDateTime.toLocal();

    final now = DateTime.now();
    final difference = now.difference(localDateTime);

    // Check if date is today
    final bool isToday =
        localDateTime.year == now.year && localDateTime.month == now.month && localDateTime.day == now.day;

    if (difference.isNegative) return "Just now";

    // Show only seconds, minutes, hours for today's data
    if (isToday) {
      if (difference.inSeconds < 60) return "${difference.inSeconds} seconds ago";
      if (difference.inMinutes < 60) return "${difference.inMinutes} minutes ago";
      if (difference.inHours < 24) return "${difference.inHours} hours ago";
      return "Today";
    } else {
      // For older data, use the original format
      if (difference.inDays < 7) return "${difference.inDays} days ago";
      if (difference.inDays < 30) return "${(difference.inDays / 7).floor()} weeks ago";
      if (difference.inDays < 365) return "${(difference.inDays / 30).floor()} months ago";
      return "${(difference.inDays / 365).floor()} years ago";
    }
  } catch (e) {
    return "Invalid time";
  }
}
