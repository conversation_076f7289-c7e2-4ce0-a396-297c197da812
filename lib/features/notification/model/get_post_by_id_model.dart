GetPostbyIdModel deserializeGetPostbyIdModel(Map<String, dynamic> json) => GetPostbyIdModel.fromJson(json);

class GetPostbyIdModel {
  int? count;
  String? next;
  String? previous;
  Results? results;

  GetPostbyIdModel({this.count, this.next, this.previous, this.results});

  GetPostbyIdModel.fromJson(Map<String, dynamic> json) {
    count = json['count'];
    next = json['next'];
    previous = json['previous'];
    results = json['results'] != null ? Results.fromJson(json['results']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['count'] = count;
    data['next'] = next;
    data['previous'] = previous;
    if (results != null) {
      data['results'] = results!.toJson();
    }
    return data;
  }
}

class Results {
  bool? status;
  String? message;
  List<GetNotificationPostData>? data;

  Results({this.status, this.message, this.data});

  Results.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <GetNotificationPostData>[];
      json['data'].forEach((v) {
        data!.add(GetNotificationPostData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class GetNotificationPostData {
  int? id;
  String? title;
  String? description;
  String? location;
  int? likes;
  int? dislikes;
  int? commentsCount;
  String? createdAt;
  List<String>? files;
  int? height;
  int? width;
  User? user;
  bool? isLiked;
  bool? isSaved;
  String? latestComment;

  GetNotificationPostData(
      {this.id,
      this.title,
      this.description,
      this.location,
      this.likes,
      this.dislikes,
      this.commentsCount,
      this.createdAt,
      this.files,
      this.height,
      this.width,
      this.user,
      this.isLiked,
      this.isSaved,
      this.latestComment});

  GetNotificationPostData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    description = json['description'];
    location = json['location'];
    likes = json['likes'];
    dislikes = json['dislikes'];
    commentsCount = json['comments_count'];
    createdAt = json['created_at'];
    files = json['files'].cast<String>();
    height = json['height'];
    width = json['width'];
    user = json['user'] != null ? User.fromJson(json['user']) : null;
    isLiked = json['is_liked'];
    isSaved = json['is_saved'];
    latestComment = json['latest_comment'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['description'] = description;
    data['location'] = location;
    data['likes'] = likes;
    data['dislikes'] = dislikes;
    data['comments_count'] = commentsCount;
    data['created_at'] = createdAt;
    data['files'] = files;
    data['height'] = height;
    data['width'] = width;
    if (user != null) {
      data['user'] = user!.toJson();
    }
    data['is_liked'] = isLiked;
    data['is_saved'] = isSaved;
    data['latest_comment'] = latestComment;
    return data;
  }

  GetNotificationPostData copyWith({
    int? id,
    String? title,
    String? description,
    String? location,
    int? likes,
    int? dislikes,
    int? commentsCount,
    String? createdAt,
    List<String>? files,
    int? height,
    int? width,
    User? user,
    bool? isLiked,
    bool? isSaved,
    String? latestComment,
  }) {
    return GetNotificationPostData(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      likes: likes ?? this.likes,
      dislikes: dislikes ?? this.dislikes,
      commentsCount: commentsCount ?? this.commentsCount,
      createdAt: createdAt ?? this.createdAt,
      files: files ?? this.files,
      height: height ?? this.height,
      width: width ?? this.width,
      user: user ?? this.user,
      isLiked: isLiked ?? this.isLiked,
      isSaved: isSaved ?? this.isSaved,
      latestComment: latestComment ?? this.latestComment,
    );
  }
}

class User {
  int? userId;
  String? username;
  String? name;
  String? profileImage;

  User({this.userId, this.username, this.name, this.profileImage});

  User.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'];
    username = json['username'];
    name = json['name'];
    profileImage = json['profile_image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['user_id'] = userId;
    data['username'] = username;
    data['name'] = name;
    data['profile_image'] = profileImage;
    return data;
  }
}
