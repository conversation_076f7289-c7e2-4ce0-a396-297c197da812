// import 'package:json_annotation/json_annotation.dart';

// part 'notification_list_model.g.dart';

GetNotificationListModel deserializeGetNotificationListModel(Map<String, dynamic> json) =>
    GetNotificationListModel.fromJson(json);

class GetNotificationListModel {
  final int count;
  final String? next;
  final String? previous;
  final NotificationResults results;

  GetNotificationListModel({
    required this.count,
    this.next,
    this.previous,
    required this.results,
  });

  factory GetNotificationListModel.fromJson(Map<String, dynamic> json) {
    return GetNotificationListModel(
      count: json['count'],
      next: json['next'],
      previous: json['previous'],
      results: NotificationResults.fromJson(json['results']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'count': count,
      'next': next,
      'previous': previous,
      'results': results.toJson(),
    };
  }

  GetNotificationListModel copyWith({
    int? count,
    String? next,
    String? previous,
    NotificationResults? results,
  }) {
    return GetNotificationListModel(
      count: count ?? this.count,
      next: next ?? this.next,
      previous: previous ?? this.previous,
      results: results ?? this.results,
    );
  }
}

class NotificationResults {
  final bool status;
  final String message;
  final List<NotificationData> data;

  NotificationResults({
    required this.status,
    required this.message,
    required this.data,
  });

  factory NotificationResults.fromJson(Map<String, dynamic> json) {
    return NotificationResults(
      status: json['status'],
      message: json['message'],
      data: List<NotificationData>.from(
        json['data'].map((x) => NotificationData.fromJson(x)),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data.map((x) => x.toJson()).toList(),
    };
  }

  NotificationResults copyWith({
    bool? status,
    String? message,
    List<NotificationData>? data,
  }) {
    return NotificationResults(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

class NotificationData {
  final int id;
  final String type;
  final String title;
  final String message;
  final String createdAt;
  final List<InvitedUser>? invitedUsers;
  final PostUser? user;
  final PostDetail? post;

  NotificationData({
    required this.id,
    required this.type,
    required this.title,
    required this.message,
    required this.createdAt,
    this.invitedUsers,
    this.user,
    this.post,
  });

  factory NotificationData.fromJson(Map<String, dynamic> json) {
    return NotificationData(
      id: json['id'],
      type: json['type'],
      title: json['title'],
      message: json['message'],
      createdAt: json['created_at'],
      invitedUsers: json['invited_users'] != null
          ? List<InvitedUser>.from(json['invited_users'].map((x) => InvitedUser.fromJson(x)))
          : null,
      user: json['user'] != null ? PostUser.fromJson(json['user']) : null,
      post: json['post'] != null ? PostDetail.fromJson(json['post']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'title': title,
      'message': message,
      'created_at': createdAt,
      'invited_users': invitedUsers?.map((x) => x.toJson()).toList(),
      'user': user?.toJson(),
      'post': post?.toJson(),
    };
  }

  NotificationData copyWith({
    int? id,
    String? type,
    String? title,
    String? message,
    String? createdAt,
    List<InvitedUser>? invitedUsers,
    PostUser? user,
    PostDetail? post,
  }) {
    return NotificationData(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      message: message ?? this.message,
      createdAt: createdAt ?? this.createdAt,
      invitedUsers: invitedUsers ?? this.invitedUsers,
      user: user ?? this.user,
      post: post ?? this.post,
    );
  }
}

class InvitedUser {
  final int id;
  final String username;
  final String profileImage;
  final bool isAccepted;

  InvitedUser({
    required this.id,
    required this.username,
    required this.profileImage,
    required this.isAccepted,
  });

  factory InvitedUser.fromJson(Map<String, dynamic> json) {
    return InvitedUser(
      id: json['id'],
      username: json['username'],
      profileImage: json['profile_image'],
      isAccepted: json['is_accepted'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'profile_image': profileImage,
      'is_accepted': isAccepted,
    };
  }

  InvitedUser copyWith({
    int? id,
    String? username,
    String? profileImage,
    bool? isAccepted,
  }) {
    return InvitedUser(
      id: id ?? this.id,
      username: username ?? this.username,
      profileImage: profileImage ?? this.profileImage,
      isAccepted: isAccepted ?? this.isAccepted,
    );
  }
}

class PostUser {
  final int userId;
  final String profileImage;

  PostUser({
    required this.userId,
    required this.profileImage,
  });

  factory PostUser.fromJson(Map<String, dynamic> json) {
    return PostUser(
      userId: json['user_id'],
      profileImage: json['profile_image'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'profile_image': profileImage,
    };
  }

  PostUser copyWith({
    int? userId,
    String? profileImage,
  }) {
    return PostUser(
      userId: userId ?? this.userId,
      profileImage: profileImage ?? this.profileImage,
    );
  }
}

class PostDetail {
  final int id;
  final List<String> postFiles;

  PostDetail({
    required this.id,
    required this.postFiles,
  });

  factory PostDetail.fromJson(Map<String, dynamic> json) {
    return PostDetail(
      id: json['id'],
      postFiles: List<String>.from(json['post_files']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'post_files': postFiles,
    };
  }

  PostDetail copyWith({
    int? id,
    List<String>? postFiles,
  }) {
    return PostDetail(
      id: id ?? this.id,
      postFiles: postFiles ?? this.postFiles,
    );
  }
}
