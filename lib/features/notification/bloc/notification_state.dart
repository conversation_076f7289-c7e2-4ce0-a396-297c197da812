part of 'notification_bloc.dart';

class NotificationState extends Equatable {
  final bool isloding;
  final GetNotificationListModel? getNotificationListModel;
  final List<NotificationData> notification;
  final bool deleteNotificationLoading;
  final bool isLoadingMore;
  final ScrollController? scrollController;
  final bool hasMore;
  final int page;
  final bool allFetch;
  final bool isUserManageLoading;
  final bool approveInviteLoading;
  final bool declineInviteLoading;
  final Set<int> approvingInviteIds;
  final Set<int> decliningInviteIds;

  const NotificationState({
    this.getNotificationListModel,
    this.isloding = false,
    this.notification = const [],
    this.deleteNotificationLoading = false,
    this.isLoadingMore = false,
    this.scrollController,
    this.hasMore = false,
    this.page = 1,
    this.allFetch = false,
    this.isUserManageLoading = false,
    this.approveInviteLoading = false,
    this.declineInviteLoading = false,
    this.approvingInviteIds = const {},
    this.decliningInviteIds = const {},
  });

  @override
  List<Object?> get props => [
        getNotificationListModel,
        isloding,
        notification,
        deleteNotificationLoading,
        isLoadingMore,
        scrollController,
        hasMore,
        page,
        allFetch,
        isUserManageLoading,
        approveInviteLoading,
        declineInviteLoading,
        approvingInviteIds,
        decliningInviteIds,
      ];

  NotificationState copyWith({
    GetNotificationListModel? getNotificationListModel,
    bool? isloding,
    List<NotificationData>? notification,
    bool? deleteNotificationLoading,
    bool? isLoadingMore,
    ScrollController? scrollController,
    bool? hasMore,
    int? page,
    bool? allFetch,
    bool? isUserManageLoading,
    bool? approveInviteLoading,
    bool? declineInviteLoading,
    Set<int>? approvingInviteIds,
    Set<int>? decliningInviteIds,
  }) {
    return NotificationState(
      notification: notification ?? this.notification,
      deleteNotificationLoading: deleteNotificationLoading ?? this.deleteNotificationLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      scrollController: scrollController ?? this.scrollController,
      hasMore: hasMore ?? this.hasMore,
      page: page ?? this.page,
      allFetch: allFetch ?? this.allFetch,
      isloding: isloding ?? this.isloding,
      getNotificationListModel: getNotificationListModel ?? this.getNotificationListModel,
      isUserManageLoading: isUserManageLoading ?? this.isUserManageLoading,
      approveInviteLoading: approveInviteLoading ?? this.approveInviteLoading,
      declineInviteLoading: declineInviteLoading ?? this.declineInviteLoading,
      approvingInviteIds: approvingInviteIds ?? this.approvingInviteIds,
      decliningInviteIds: decliningInviteIds ?? this.decliningInviteIds,
    );
  }
}
