part of 'notification_bloc.dart';

sealed class NotificationEvent extends Equatable {
  const NotificationEvent();

  @override
  List<Object> get props => [];
}

class GetNotificationinitialEvent extends NotificationEvent {
  @override
  List<Object> get props => [];
}

class GetNotificationEvent extends NotificationEvent {
  final int page;

  const GetNotificationEvent({required this.page});

  @override
  List<Object> get props => [page];
}

class ApproveInviteEvent extends NotificationEvent {
  final BuildContext context;
  final int inviteId;

  const ApproveInviteEvent(this.context, {required this.inviteId});

  @override
  List<Object> get props => [inviteId];
}

class DeclineInviteEvent extends NotificationEvent {
  final BuildContext context;
  final int declineId;

  const DeclineInviteEvent(this.context, {required this.declineId});

  @override
  List<Object> get props => [declineId];
}

class DeleteNotificationEvent extends NotificationEvent {
  final BuildContext context;
  final int notificationId;

  const DeleteNotificationEvent(this.context, {required this.notificationId});

  @override
  List<Object> get props => [context, notificationId];
}
