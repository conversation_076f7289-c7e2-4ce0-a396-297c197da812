import 'dart:async';

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/notification/model/notification_list_model.dart';

part 'notification_event.dart';
part 'notification_state.dart';

class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  ApiClient apiClient = ApiClient(Dio());
  NotificationBloc() : super(const NotificationState()) {
    on<GetNotificationinitialEvent>(_onInitialize);
    on<GetNotificationEvent>(_onGetNotificationApi);
    on<ApproveInviteEvent>(_onApproveInvite);
    on<DeclineInviteEvent>(_onDeclineInvite);
    on<DeleteNotificationEvent>(_onDeleteNotification);
  }
  FutureOr<void> _onInitialize(GetNotificationinitialEvent event, Emitter<NotificationState> emit) {
    emit(state.copyWith(
      allFetch: false,
      notification: [],
      hasMore: true,
      isLoadingMore: true,
      isloding: false,
      scrollController: ScrollController(),
    ));
  }

  _onGetNotificationApi(GetNotificationEvent event, Emitter<NotificationState> emit) async {
    if (event.page == 1) {
      emit(state.copyWith(isloding: true, notification: []));
    } else {
      emit(state.copyWith(isLoadingMore: true));
    }
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final result = await apiClient.getNotificationListApi(loginuserId, event.page, brandId.toString());
      if (result.results.status == true) {
        state.notification.addAll(result.results.data);
        Logger.lOG("TOTAL NOTIFICATION DATA LENGTH : ${state.notification.length}");

        emit(state.copyWith(
            notification: state.notification,
            page: event.page,
            allFetch: false,
            isloding: false,
            getNotificationListModel: result,
            isLoadingMore: false));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(
          isLoadingMore: false,
          allFetch: true,
          isUserManageLoading: false,
        ));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(
          allFetch: false,
          isLoadingMore: false,
          isUserManageLoading: false,
        ));
      });
    }
  }

  Future<void> _onApproveInvite(ApproveInviteEvent event, Emitter<NotificationState> emit) async {
    Set<int> newApproving = Set<int>.from(state.approvingInviteIds)..add(event.inviteId);
    emit(state.copyWith(approveInviteLoading: true, approvingInviteIds: newApproving));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final result =
          await apiClient.approveInvite(logInUserId: loginuserId, inviteId: event.inviteId, brand: brandId.toString());

      if (result.status == true) {
        event.context.read<NotificationBloc>().add(GetNotificationEvent(page: 1));

        emit(state.copyWith(approveInviteLoading: false));
      } else {
        emit(state.copyWith(approveInviteLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(approveInviteLoading: false));
      Logger.lOG("_onApproveInvite Error: $error");
      emit(state.copyWith(approveInviteLoading: false));
    } finally {
      Set<int> newApproving = Set<int>.from(state.approvingInviteIds)..remove(event.inviteId);
      emit(state.copyWith(approvingInviteIds: newApproving));
    }
  }

  Future<void> _onDeclineInvite(DeclineInviteEvent event, Emitter<NotificationState> emit) async {
    Set<int> newDeclining = Set<int>.from(state.decliningInviteIds)..add(event.declineId);
    emit(state.copyWith(declineInviteLoading: true, decliningInviteIds: newDeclining));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final result = await apiClient.declineInvite(
          logInUserId: loginuserId, declineId: event.declineId, brand: brandId.toString());

      if (result.status == true) {
        event.context.read<NotificationBloc>().add(GetNotificationEvent(page: 1));

        emit(state.copyWith(declineInviteLoading: false));
      } else {
        emit(state.copyWith(declineInviteLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(declineInviteLoading: false));
      Logger.lOG("_onDeclineInvite Error: $error");
      emit(state.copyWith(declineInviteLoading: false));
    } finally {
      Set<int> newDeclining = Set<int>.from(state.decliningInviteIds)..remove(event.declineId);
      emit(state.copyWith(decliningInviteIds: newDeclining));
    }
  }

  Future<void> _onDeleteNotification(DeleteNotificationEvent event, Emitter<NotificationState> emit) async {
    emit(state.copyWith(deleteNotificationLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final result = await apiClient.deleteNotification(
          logInUserId: loginuserId, notificationId: event.notificationId, brand: brandId.toString());

      if (result.status == true) {
        event.context.read<NotificationBloc>().add(GetNotificationEvent(page: 1));
        NavigatorService.goBack();
        emit(state.copyWith(deleteNotificationLoading: false));
      }
    } catch (error) {
      Logger.lOG("_onDeclineInvite Error: $error");
      emit(state.copyWith(deleteNotificationLoading: false));
    }
  }
}
