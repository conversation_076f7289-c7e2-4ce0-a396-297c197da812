import 'package:flowkar/core/utils/env.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:permission_asker/permission_asker.dart';

Future<void> initializeOneSignalNotification() async {
  try {
    OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
    OneSignal.initialize(oneSignalAppId);
    await OneSignal.Notifications.requestPermission(true);
    await getSubscriptionId();

    OneSignal.Notifications.addForegroundWillDisplayListener((event) {
      // Logger.lOG('NOTIFICATION WILL DISPLAY LISTENER CALLED WITH: ${event.notification.jsonRepresentation()}');
      event.notification.display();
    });

    OneSignal.Notifications.addClickListener((openedResult) {
      String? screen;
      // Logger.lOG('NOTIFICATION OPENED LISTENER CALLED WITH: ${openedResult.notification.jsonRepresentation()}');
      final data = openedResult.notification.additionalData;
      screen = data?['screen'];
      if (screen != null) {
        NavigatorService.navigatorKey.currentState?.pushNamed(screen);
      }
    });
  } catch (e) {
    Logger.lOG("Error initializing OneSignal: $e");
  }
}

Future<void> getSubscriptionId() async {
  OneSignal.Notifications.addPermissionObserver(
    (permission) {
      Logger.lOG("permission");
    },
  );
  if (await Permission.notification.isGranted) {
    String? externalId = await OneSignal.User.getExternalId();
    OneSignal.login(externalId.toString());
    Logger.lOG("PushSubscription Id: ${OneSignal.User.pushSubscription.id}");
    Prefobj.preferences?.put(Prefkeys.SUBSCRIPTIONID, OneSignal.User.pushSubscription.id ?? '');
  }
}
