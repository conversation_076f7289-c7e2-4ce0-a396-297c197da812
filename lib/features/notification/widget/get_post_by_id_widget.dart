import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/comment_bottom_sheet_widget.dart';
import 'package:flowkar/features/notification/model/get_post_by_id_model.dart';
import 'package:flowkar/features/widgets/custom/custom_conditional_scroll_physics.dart';
// import 'package:flowkar/features/upload_post/model/get_post_by_id_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';

class GetUserPostbyIdScreen extends StatefulWidget {
  final List<GetNotificationPostData>? userPosts;
  final int? initialIndex;
  final bool? isTextPost;
  final bool? isNotificationPost;
  const GetUserPostbyIdScreen(
      {super.key, this.userPosts, this.initialIndex, this.isTextPost, this.isNotificationPost = false});

  @override
  State<GetUserPostbyIdScreen> createState() => _GetUserPostbyIdScreenState();
}

class _GetUserPostbyIdScreenState extends State<GetUserPostbyIdScreen> with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themestate) {
          return BlocBuilder<HomeFeedBloc, HomeFeedState>(
            builder: (context, state) {
              return Scaffold(
                appBar: CustomAppbar(
                  hasLeadingIcon: true,
                  height: 18.h,
                  leading: [
                    IconButton(
                      icon: CustomImageView(
                        imagePath: Assets.images.svg.authentication.icBackArrow.path,
                        height: 16.h,
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                    buildSizedBoxW(20.w),
                    Text(
                      Lang.of(context).lbl_post,
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
                    ),
                  ],
                ),
                body: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                        child: state.isloding ? const LoadingAnimationWidget() : _buildPostFeed(state, themestate)),
                    BlocBuilder<ConnectivityBloc, ConnectivityState>(
                      builder: (context, connectivityState) {
                        return Visibility(
                          visible: state.isLoadingMore && connectivityState.isConnected,
                          child: SizedBox(
                            height: 50.h,
                            child:
                                Center(child: CupertinoActivityIndicator(color: Theme.of(context).colorScheme.primary)),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildPostFeed(HomeFeedState state, ThemeState themestate) {
    return BlocListener<ConnectivityBloc, ConnectivityState>(
      listener: (context, connectivityState) {
        if (connectivityState.isReconnected) {
          PersistentNavBarNavigator.pop(context);
          if (state.getpostbyIdData.isNotEmpty) {
            context
                .read<HomeFeedBloc>()
                .add(GetNotificationPostEvent(postId: state.getpostbyIdData.first.id.toString()));
          }
        }
      },
      child: LiquidPullToRefresh(
        color: Theme.of(context).primaryColor.withOpacity(0.5),
        showChildOpacityTransition: false,
        backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
        onRefresh: () async {
          return await Future.delayed(const Duration(seconds: 0), () {
            context
                .read<HomeFeedBloc>()
                .add(GetNotificationPostEvent(postId: state.getpostbyIdData.first.id.toString()));
          });
        },
        child: BlocBuilder<HomeFeedBloc, HomeFeedState>(
          builder: (context, state) {
            return BlocBuilder<ConnectivityBloc, ConnectivityState>(
              builder: (context, connectivityState) {
                if (!connectivityState.isConnected) {
                  return HomeFeedShimmer();
                } else if (state.fetchPostLoading) {
                  return HomeFeedShimmer();
                } else if (state.getpostbyIdData.isEmpty) {
                  return ListView(
                    physics: AlwaysScrollableScrollPhysics(),
                    children: [
                      buildSizedBoxH(MediaQuery.of(context).size.height / 6),
                      ExceptionWidget(
                        imagePath: Assets.images.svg.exception.svgNodatafound.path,
                        showButton: false,
                        title: Lang.of(context).lbl_no_data_found,
                        subtitle: Lang.of(context).lbl_no_post,
                      ),
                    ],
                  );
                } else {
                  final post = state.getpostbyIdData.first;
                  return ListView(
                    controller: _scrollController,
                    physics: ConditionalScrollPhysics(controller: _scrollController),
                    children: [
                      buildSizedBoxH(8),
                      PostWidget(
                        width: post.width ?? 1024,
                        height: post.height ?? 1024,
                        userpost: false,
                        isTextPost: widget.isTextPost ?? false,
                        userByIDpost: false,
                        userByIDvideo: false,
                        userVideo: false,
                        state: context.read<HomeFeedBloc>().state,
                        latestcomments: post.latestComment.toString(),
                        index: 0,
                        userId: post.user?.userId,
                        postId: post.id ?? 0,
                        profileImage: post.user?.profileImage ?? "",
                        name: post.user?.name ?? "",
                        username: post.user?.username ?? "",
                        postMedia: post.files ?? [],
                        thumbnailImage: [],
                        taggedIn: [],
                        title: "${post.title == "''" || (post.title?.isEmpty ?? true) ? '' : post.title}",
                        caption:
                            "${post.title == "''" || (post.title?.isEmpty ?? true) ? '' : post.title}${(post.description?.isEmpty ?? true) ? '' : post.title == "''" || (post.title?.isEmpty ?? true) ? post.description : "\n${post.description}"}",
                        likes: post.likes.toString(),
                        comments: post.commentsCount.toString(),
                        postTime: post.createdAt ?? "",
                        isLiked: post.isLiked ?? false,
                        isSaved: post.isSaved ?? false,
                        screenType: "Shared Post",
                        reelScreenType: 'Notification',
                        isprofilrpostDelete: false,
                        isNotificationPost: widget.isNotificationPost ?? false,
                        doubleTap: () {
                          if (post.isLiked == false) {
                            context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: post.id ?? 0));
                          }
                        },
                        likeonTap: () {
                          context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: post.id ?? 0));
                        },
                        commentonTap: () {
                          showModalBottomSheet(
                            context: context,
                            useRootNavigator: true,
                            isScrollControlled: true,
                            builder: (context) => CommentsBottomSheet(postId: post.id ?? 0),
                          );
                        },
                        shareonTap: () {},
                        saveonTap: () {},
                      ),
                      buildSizedBoxH(16),
                    ],
                  );
                }
              },
            );
          },
        ),
      ),
    );
  }

  void showCommentsBottomSheet(BuildContext context, String postId) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return CommentsBottomSheet(postId: int.parse(postId));
      },
    );
  }
}
