import 'dart:async';

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/notification/widget/get_post_by_id_widget.dart';

class GetPostByIdScreen extends StatefulWidget {
  final String postId;
  final bool? isTextPost;
  final bool? isNotificationPost;
  const GetPostByIdScreen({super.key, required this.postId, this.isTextPost, this.isNotificationPost = false});

  @override
  State<GetPostByIdScreen> createState() => _GetPostByIdScreenState();
}

class _GetPostByIdScreenState extends State<GetPostByIdScreen> {
  @override
  void initState() {
    super.initState();
    final state = context.read<HomeFeedBloc>().state;
    if (state.getpostbyIdData.isNotEmpty) {
      state.getpostbyIdData.clear();
    }
    scheduleMicrotask(() => context.read<HomeFeedBloc>().add(GetNotificationPostEvent(postId: widget.postId)));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeFeedBloc, HomeFeedState>(
      builder: (context, state) {
        return GetUserPostbyIdScreen(
          initialIndex: 0,
          userPosts: state.getpostbyIdData,
          isTextPost: widget.isTextPost ?? false,
          isNotificationPost: widget.isNotificationPost ?? false,
        );
      },
    );
  }
}
