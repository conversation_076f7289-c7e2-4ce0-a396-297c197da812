import 'package:flowkar/core/utils/exports.dart';

class NotificationPageShimmer extends StatelessWidget {
  const NotificationPageShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.only(bottom: 50.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Last 7 Days Header
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.0.w, vertical: 8.0.h),
                child: Container(
                  width: 80.w,
                  height: 18.h,
                  decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(4.r)),
                ),
              ),

              // Notification items for Last 7 Days
              ..._buildShimmerNotificationItems(5),

              buildSizedBoxH(20.h),

              // Last 30 Days Header
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.0.w, vertical: 8.0.h),
                child: Container(
                  width: 100.w,
                  height: 18.h,
                  decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(4.r)),
                ),
              ),

              // Notification items for Last 30 Days
              ..._buildShimmerNotificationItems(3),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildShimmerNotificationItems(int count) {
    return List.generate(
      count,
      (index) => Padding(
        padding: EdgeInsets.only(bottom: 12.0),
        child: _buildShimmerNotificationItem(),
      ),
    );
  }

  Widget _buildShimmerNotificationItem() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.0.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile image placeholder
          Container(
            width: 50.0,
            height: 50.0,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
          ),
          buildSizedBoxW(8.0.w),

          // Notification content placeholder
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User name and action placeholder
                Container(
                  height: 16.h,
                  width: double.infinity,
                  decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(4.r)),
                ),
                buildSizedBoxH(5.h),

                // Timestamp placeholder
                Container(
                  height: 12.h,
                  width: 80.w,
                  decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(4.r)),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
