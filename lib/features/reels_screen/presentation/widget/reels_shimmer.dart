// ignore_for_file: deprecated_member_use

import 'package:flowkar/core/utils/exports.dart';

class ReelShimmer extends StatelessWidget {
  const ReelShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).customColors.white.withOpacity(0.3),
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Shimmer.fromColors(
            baseColor: Color(0xFF101010).withOpacity(0.7),
            highlightColor: Colors.grey.shade900,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Theme.of(context).customColors.black,
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Shimmer.fromColors(
                                baseColor: Color(0xFF101010),
                                highlightColor: Color(0xFF222222),
                                child: Container(
                                  height: 32.h,
                                  width: 32.w,
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).customColors.black.withOpacity(0.5),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              ),
                              buildSizedBoxW(12),
                              Shimmer.fromColors(
                                baseColor: Color(0xFF101010),
                                highlightColor: Color(0xFF222222),
                                child: Container(
                                  height: 14.h,
                                  width: 100.w,
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).customColors.black.withOpacity(0.5),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          buildSizedBoxH(12),
                          Shimmer.fromColors(
                            baseColor: Color(0xFF101010),
                            highlightColor: Color(0xFF222222),
                            child: Container(
                              height: 13.h,
                              width: 200.w,
                              decoration: BoxDecoration(
                                color: Theme.of(context).customColors.black.withOpacity(0.5),
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ),
                          buildSizedBoxH(16),
                        ],
                      ),
                    ),
                    buildSizedBoxW(30),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        _buildShimmerAction(context),
                        buildSizedBoxH(16),
                        _buildShimmerAction(context),
                        buildSizedBoxH(16),
                        _buildShimmerAction(context),
                        buildSizedBoxH(24),
                        _buildShimmerAction(context),
                        buildSizedBoxH(16),
                        buildSizedBoxH(60),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerAction(BuildContext context) {
    return Column(
      children: [
        Shimmer.fromColors(
          baseColor: Color(0xFF101010),
          highlightColor: Color(0xFF222222),
          child: Container(
            height: 26.h,
            width: 26.w,
            decoration: BoxDecoration(
              color: Theme.of(context).customColors.black.withOpacity(0.5),
              shape: BoxShape.circle,
            ),
          ),
        ),
        buildSizedBoxH(4),
        Shimmer.fromColors(
          baseColor: Color(0xFF101010),
          highlightColor: Color(0xFF222222),
          child: Container(
            height: 14.h,
            width: 20.w,
            decoration: BoxDecoration(
              color: Theme.of(context).customColors.black.withOpacity(0.5),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
      ],
    );
  }
}
