part of 'social_connec_bloc.dart';

sealed class SocialConnectEvent extends Equatable {
  const SocialConnectEvent();

  @override
  List<Object> get props => [];
}

class SocialConnectInitial extends SocialConnectEvent {
  @override
  List<Object> get props => [];
}

// Linkdin
class LinkdinconnectApiEvent extends SocialConnectEvent {
  final BuildContext context;

  const LinkdinconnectApiEvent({required this.context});
}

class LinkdinDisconnectApiEvent extends SocialConnectEvent {
  const LinkdinDisconnectApiEvent();
}

// Vimeo
class VimeoconnectApiEvent extends SocialConnectEvent {
  final BuildContext context;
  final bool didLaunchUrl;

  const VimeoconnectApiEvent({required this.context, required this.didLaunchUrl});
}

class VimeoDisconnectApiEvent extends SocialConnectEvent {
  const VimeoDisconnectApiEvent();
}

class SocialConnectCheckApiEvent extends SocialConnectEvent {
  const SocialConnectCheckApiEvent();
}

// Pintrest
class PinterstconnectApiEvent extends SocialConnectEvent {
  final BuildContext context;
  final bool didLaunchUrl;

  const PinterstconnectApiEvent({
    required this.context,
    required this.didLaunchUrl,
  });
}

class PintrestDisconnectApiEvent extends SocialConnectEvent {
  const PintrestDisconnectApiEvent();
}

// Reddit
class RedditconnectApiEvent extends SocialConnectEvent {
  final BuildContext context;
  final bool didLaunchUrl;

  const RedditconnectApiEvent({required this.context, required this.didLaunchUrl});
}

class RedditDisconnectApiEvent extends SocialConnectEvent {
  const RedditDisconnectApiEvent();
}

class ConnectTumblrApiEvent extends SocialConnectEvent {
  const ConnectTumblrApiEvent({
    required this.oauthtoken,
    required this.oauthtokensecret,
    required this.oauthverifier,
  });
  final String oauthtoken;
  final String oauthtokensecret;
  final String oauthverifier;

  @override
  List<Object> get props => [];
}

class SocialconnectTumblrApiEvent extends SocialConnectEvent {
  const SocialconnectTumblrApiEvent({required this.context, required this.platForm});
  final String platForm;
  final BuildContext context;
  @override
  List<Object> get props => [context, platForm];
}

class TumblrDisconnectApiEvent extends SocialConnectEvent {
  const TumblrDisconnectApiEvent();
}

// Instagram
class InstagramconnectApiEvent extends SocialConnectEvent {
  final BuildContext context;
  final bool didLaunchUrl;

  const InstagramconnectApiEvent({required this.context, required this.didLaunchUrl});
}

class InstagramDisconnectApiEvent extends SocialConnectEvent {
  const InstagramDisconnectApiEvent();
}

// Facebook
class FacebookconnectApiEvent extends SocialConnectEvent {
  final BuildContext context;
  final bool didLaunchUrl;

  const FacebookconnectApiEvent({required this.context, required this.didLaunchUrl});
}

class FacebookDisconnectApiEvent extends SocialConnectEvent {
  const FacebookDisconnectApiEvent();
}

// Thread
class ThreadconnectApiEvent extends SocialConnectEvent {
  final BuildContext context;
  final bool didLaunchUrl;

  const ThreadconnectApiEvent({required this.context, required this.didLaunchUrl});
}

class ThreadDisconnectApiEvent extends SocialConnectEvent {
  const ThreadDisconnectApiEvent();
}

// Youtube
class YoutubeconnectApiEvent extends SocialConnectEvent {
  final BuildContext context;
  final bool didLaunchUrl;

  const YoutubeconnectApiEvent({required this.context, required this.didLaunchUrl});
}

class YoutubeDisconnectApiEvent extends SocialConnectEvent {
  const YoutubeDisconnectApiEvent();
}

// Tiktok
class TiktokconnectApiEvent extends SocialConnectEvent {
  final BuildContext context;
  final bool didLaunchUrl;

  const TiktokconnectApiEvent({required this.context, required this.didLaunchUrl});
}

class TiktokDisconnectApiEvent extends SocialConnectEvent {
  const TiktokDisconnectApiEvent();
}

// X- (Twitter)
class XconnectApiEvent extends SocialConnectEvent {
  final BuildContext context;
  final bool didLaunchUrl;

  const XconnectApiEvent({required this.context, required this.didLaunchUrl});
}

class XDisconnectApiEvent extends SocialConnectEvent {
  const XDisconnectApiEvent();
}

class PurchaseXApiEvent extends SocialConnectEvent {
  const PurchaseXApiEvent();
}

class ShareProfileApiEvent extends SocialConnectEvent {
  const ShareProfileApiEvent();
}

class ThreadProfileDataApiEvent extends SocialConnectEvent {
  const ThreadProfileDataApiEvent();
}

class DynamicplatformApiEvent extends SocialConnectEvent {
  const DynamicplatformApiEvent();
}

class CountrySelected extends SocialConnectEvent {
  final Country country;

  const CountrySelected(this.country);
}

class PhoneNumberChanged extends SocialConnectEvent {
  final String phoneNumber;

  const PhoneNumberChanged(this.phoneNumber);
}

//Telegram
class TelegramconnectSendCodeApiEvent extends SocialConnectEvent {
  final BuildContext context;
  final String mobileNumber;
  final bool isresendOtp;
  const TelegramconnectSendCodeApiEvent({required this.context, required this.mobileNumber, required this.isresendOtp});
}

class TelegramconnectSignInApiEvent extends SocialConnectEvent {
  final BuildContext context;
  final String mobileNumber;
  final String code;
  final String phoneCodeHash;
  final bool didLaunchUrl;
  const TelegramconnectSignInApiEvent(
      {required this.context,
      required this.mobileNumber,
      required this.code,
      required this.phoneCodeHash,
      required this.didLaunchUrl});
}

class StartTimerEvent extends SocialConnectEvent {}

class TickEvent extends SocialConnectEvent {
  final int remainingTime;
  const TickEvent(this.remainingTime);
}

class TelegramDisconnectApiEvent extends SocialConnectEvent {
  const TelegramDisconnectApiEvent();
}

class ClearTelegramSendCodeModelEvent extends SocialConnectEvent {}

// Mastodon
class MastodonconnectApiEvent extends SocialConnectEvent {
  final BuildContext context;
  final bool didLaunchUrl;

  const MastodonconnectApiEvent({required this.context, required this.didLaunchUrl});
}

class MastodonDisconnectApiEvent extends SocialConnectEvent {
  const MastodonDisconnectApiEvent();
}

class ShowPhoneSectionEvent extends SocialConnectEvent {
  final bool isPhonesection;
  const ShowPhoneSectionEvent({required this.isPhonesection});
}
