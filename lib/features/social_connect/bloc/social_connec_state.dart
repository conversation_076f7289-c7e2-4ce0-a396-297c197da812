part of 'social_connec_bloc.dart';

class SocialConnectState extends Equatable {
  const SocialConnectState({
    this.socialConnectCheck,
    this.socialDisconnectModel,
    this.socialConnectModel,
    this.applaunchurL = false,
    this.issetsocialConnectLoading = false,
    this.isLinkedInConnectLoading = false,
    this.isVimeoConnectLoading = false,
    this.linkedinConnectError,
    this.vimeoConnectError,
    this.isConnectLoading = false,
    this.socialConnectError,
    this.isPintrestConnectLoading = false,
    this.pintrestConnectError,
    this.isReditConnectLoading = false,
    this.redditConnectError,
    this.isTumblrLoading = false,
    this.tumblrConnectError,
    this.tumblrSocialMediaModel,
    this.isInstagramConnectLoading = false,
    this.instagramConnectError,
    this.isFacebookConnectLoading = false,
    this.isThreadConnectLoading = false,
    this.isYoutubeConnectLoading = false,
    this.isTiktokConnectLoading = false,
    this.isShareProfileLoading = false,
    this.shareProfileModel,
    this.instaProfileModel,
    this.getanalyticsLoading = false,
    this.platformstatus,
    this.userstatus,
    this.isXConnectLoading = false,
    this.isPurchaseXLoading = false,
    required this.selectedCountry,
    this.phoneNumber = '',
    this.isTelegramSendCodeLoading = false,
    this.telegramSendCodeModel,
    this.telegramSignInModel,
    this.isTelegramSignInLoading = false,
    this.remainingTime = 0,
    this.isResendEnabled = false,
    this.isMastodonConnectLoading = false,
    this.isPhonesection = false,
  });

  final SocialConnectCheck? socialConnectCheck;
  final SocialDisconnectModel? socialDisconnectModel;
  final SocialConnectModel? socialConnectModel;
  final bool applaunchurL;
  final bool issetsocialConnectLoading;
  final bool isLinkedInConnectLoading;
  final String? linkedinConnectError;
  final bool isVimeoConnectLoading;
  final String? vimeoConnectError;
  final bool isConnectLoading;
  final String? socialConnectError;
  final bool isPintrestConnectLoading;
  final String? pintrestConnectError;
  final bool isReditConnectLoading;
  final String? redditConnectError;
  final bool isTumblrLoading;
  final String? tumblrConnectError;
  final ThumblrSocialMediaModel? tumblrSocialMediaModel;
  final bool isInstagramConnectLoading;
  final String? instagramConnectError;
  final bool isFacebookConnectLoading;
  final bool isThreadConnectLoading;
  final bool isYoutubeConnectLoading;
  final bool isTiktokConnectLoading;
  final bool isShareProfileLoading;
  final ShareProfileModel? shareProfileModel;
  final ThreadProfileModel? instaProfileModel;
  final bool getanalyticsLoading;
  final PlatformStatus? platformstatus;
  final UserStatus? userstatus;
  final bool isXConnectLoading;
  final bool isPurchaseXLoading;
  final Country selectedCountry;
  final String phoneNumber;
  final bool isTelegramSendCodeLoading;
  final TelegramSendCodeModel? telegramSendCodeModel;
  final bool isTelegramSignInLoading;
  final TelegramSignInModel? telegramSignInModel;
  final int remainingTime;
  final bool isResendEnabled;
  final bool isMastodonConnectLoading;
  final bool isPhonesection;

  @override
  List<Object?> get props => [
        socialConnectCheck,
        socialDisconnectModel,
        socialConnectModel,
        applaunchurL,
        issetsocialConnectLoading,
        isLinkedInConnectLoading,
        isVimeoConnectLoading,
        linkedinConnectError,
        vimeoConnectError,
        isConnectLoading,
        socialConnectError,
        isPintrestConnectLoading,
        pintrestConnectError,
        isReditConnectLoading,
        redditConnectError,
        isTumblrLoading,
        tumblrConnectError,
        tumblrSocialMediaModel,
        isInstagramConnectLoading,
        instagramConnectError,
        isFacebookConnectLoading,
        isThreadConnectLoading,
        isYoutubeConnectLoading,
        isTiktokConnectLoading,
        isShareProfileLoading,
        shareProfileModel,
        instaProfileModel,
        getanalyticsLoading,
        platformstatus,
        userstatus,
        isXConnectLoading,
        isPurchaseXLoading,
        selectedCountry,
        phoneNumber,
        isTelegramSendCodeLoading,
        telegramSendCodeModel,
        isTelegramSignInLoading,
        telegramSignInModel,
        remainingTime,
        isResendEnabled,
        isMastodonConnectLoading,
        isPhonesection,
      ];
  SocialConnectState copyWith({
    SocialConnectCheck? socialConnectCheck,
    SocialDisconnectModel? socialDisconnectModel,
    SocialConnectModel? socialConnectModel,
    bool? applaunchurL,
    bool? issetsocialConnectLoading,
    bool? isLinkedInConnectLoading,
    bool? isVimeoConnectLoading,
    String? linkedinConnectError,
    String? vimeoConnectError,
    bool? isConnectLoading,
    String? socialConnectError,
    bool? isPintrestConnectLoading,
    String? pintrestConnectError,
    bool? isReditConnectLoading,
    String? redditConnectError,
    bool? isTumblrLoading,
    String? tumblrConnectError,
    ThumblrSocialMediaModel? tumblrSocialMediaModel,
    bool? isInstagramConnectLoading,
    String? instagramConnectError,
    bool? isFacebookConnectLoading,
    bool? isThreadConnectLoading,
    bool? isYoutubeConnectLoading,
    bool? isTiktokConnectLoading,
    bool? isShareProfileLoading,
    ShareProfileModel? shareProfileModel,
    ThreadProfileModel? instaProfileModel,
    bool? getanalyticsLoading,
    PlatformStatus? platformstatus,
    UserStatus? userstatus,
    bool? isXConnectLoading,
    bool? isPurchaseXLoading,
    Country? selectedCountry,
    String? phoneNumber,
    bool? isTelegramSendCodeLoading,
    TelegramSendCodeModel? telegramSendCodeModel,
    bool? isTelegramSignInLoading,
    TelegramSignInModel? telegramSignInModel,
    int? remainingTime,
    bool? isResendEnabled,
    bool? isMastodonConnectLoading,
    bool? isPhonesection,
  }) {
    return SocialConnectState(
      socialConnectCheck: socialConnectCheck ?? this.socialConnectCheck,
      socialDisconnectModel: socialDisconnectModel ?? this.socialDisconnectModel,
      socialConnectModel: socialConnectModel ?? this.socialConnectModel,
      applaunchurL: applaunchurL ?? this.applaunchurL,
      issetsocialConnectLoading: issetsocialConnectLoading ?? this.issetsocialConnectLoading,
      isLinkedInConnectLoading: isLinkedInConnectLoading ?? this.isLinkedInConnectLoading,
      isVimeoConnectLoading: isVimeoConnectLoading ?? this.isVimeoConnectLoading,
      linkedinConnectError: linkedinConnectError ?? this.linkedinConnectError,
      vimeoConnectError: vimeoConnectError ?? this.vimeoConnectError,
      isConnectLoading: isConnectLoading ?? this.isConnectLoading,
      socialConnectError: socialConnectError ?? this.socialConnectError,
      isPintrestConnectLoading: isPintrestConnectLoading ?? this.isPintrestConnectLoading,
      pintrestConnectError: pintrestConnectError ?? this.pintrestConnectError,
      isReditConnectLoading: isReditConnectLoading ?? this.isReditConnectLoading,
      redditConnectError: redditConnectError ?? this.redditConnectError,
      isTumblrLoading: isTumblrLoading ?? this.isTumblrLoading,
      tumblrConnectError: tumblrConnectError ?? this.tumblrConnectError,
      tumblrSocialMediaModel: tumblrSocialMediaModel ?? this.tumblrSocialMediaModel,
      isInstagramConnectLoading: isInstagramConnectLoading ?? this.isInstagramConnectLoading,
      instagramConnectError: instagramConnectError ?? this.instagramConnectError,
      isFacebookConnectLoading: isFacebookConnectLoading ?? this.isFacebookConnectLoading,
      isThreadConnectLoading: isThreadConnectLoading ?? this.isThreadConnectLoading,
      isYoutubeConnectLoading: isYoutubeConnectLoading ?? this.isYoutubeConnectLoading,
      isTiktokConnectLoading: isTiktokConnectLoading ?? this.isTiktokConnectLoading,
      isShareProfileLoading: isShareProfileLoading ?? this.isShareProfileLoading,
      shareProfileModel: shareProfileModel ?? this.shareProfileModel,
      instaProfileModel: instaProfileModel ?? this.instaProfileModel,
      getanalyticsLoading: getanalyticsLoading ?? this.getanalyticsLoading,
      platformstatus: platformstatus ?? this.platformstatus,
      userstatus: userstatus ?? this.userstatus,
      isXConnectLoading: isXConnectLoading ?? this.isXConnectLoading,
      isPurchaseXLoading: isPurchaseXLoading ?? this.isPurchaseXLoading,
      selectedCountry: selectedCountry ?? this.selectedCountry,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      isTelegramSendCodeLoading: isTelegramSendCodeLoading ?? this.isTelegramSendCodeLoading,
      telegramSendCodeModel: telegramSendCodeModel ?? this.telegramSendCodeModel,
      isTelegramSignInLoading: isTelegramSignInLoading ?? this.isTelegramSignInLoading,
      telegramSignInModel: telegramSignInModel ?? this.telegramSignInModel,
      remainingTime: remainingTime ?? this.remainingTime,
      isResendEnabled: isResendEnabled ?? this.isResendEnabled,
      isMastodonConnectLoading: isMastodonConnectLoading ?? this.isMastodonConnectLoading,
      isPhonesection: isPhonesection ?? this.isPhonesection,
    );
  }

  int getRequiredLength() {
    String example = selectedCountry.example;
    String cleanedExample = example.replaceAll(RegExp(r'[^0-9]'), '');
    return cleanedExample.length;
  }
}
