ThreadProfileModel deserializeThreadProfileModel(Map<String, dynamic> json) => ThreadProfileModel.fromJson(json);

class ThreadProfileModel {
  final bool status;
  final UserData? data;

  ThreadProfileModel({required this.status, this.data});

  factory ThreadProfileModel.fromJson(Map<String, dynamic> json) {
    return ThreadProfileModel(
      status: json['status'] ?? false,
      data: json['data'] != null ? UserData.fromJson(json['data']['data']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'data': data?.toJson(),
    };
  }
}

class UserData {
  final String username;
  final String name;
  final String followersCount;
  final String followsCount;
  final String profileImage;
  final PostData? post;

  UserData({
    required this.username,
    required this.name,
    required this.followersCount,
    required this.followsCount,
    required this.profileImage,
    this.post,
  });

  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
      username: json['username'] ?? '',
      name: json['name'] ?? '',
      followersCount: json['followers_count'] ?? '0',
      followsCount: json['follows_count'] ?? '0',
      profileImage: json['profile_image'] ?? '',
      post: json['post'] != null ? PostData.fromJson(json['post']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'name': name,
      'followers_count': followersCount,
      'follows_count': followsCount,
      'profile_image': profileImage,
      'post': post?.toJson(),
    };
  }
}

class PostData {
  final String likes;
  final String comment;
  final String mediaData;

  PostData({
    required this.likes,
    required this.comment,
    required this.mediaData,
  });

  factory PostData.fromJson(Map<String, dynamic> json) {
    return PostData(
      likes: json['likes'] ?? '0',
      comment: json['comment'] ?? '0',
      mediaData: json['media_data'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'likes': likes,
      'comment': comment,
      'media_data': mediaData,
    };
  }
}
