import 'package:json_annotation/json_annotation.dart';

part 'thumblr_social_media_model.g.dart';

ThumblrSocialMediaModel deserializeThumblrSocialMediaModel(
        Map<String, dynamic> json) =>
    ThumblrSocialMediaModel.fromJson(json);

@JsonSerializable()
class ThumblrSocialMediaModel {
  bool? status;
  @JsonKey(name: 'authorization_url')
  String? authorizationUrl;
  @JsonKey(name: 'oauth_token')
  String? oauthToken;
  @JsonKey(name: 'oauth_token_secret')
  String? oauthTokenSecret;

  ThumblrSocialMediaModel({
    this.status,
    this.authorizationUrl,
    this.oauthToken,
    this.oauthTokenSecret,
  });

  /// Factory constructor for creating a new instance from a map.
  factory ThumblrSocialMediaModel.fromJson(Map<String, dynamic> json) =>
      _$ThumblrSocialMediaModelFromJson(json);

  /// Converts this object into a map.
  Map<String, dynamic> toJson() => _$ThumblrSocialMediaModelToJson(this);
}
