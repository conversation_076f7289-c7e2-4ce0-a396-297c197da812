import 'package:json_annotation/json_annotation.dart';

part 'share_profile_model.g.dart';

ShareProfileModel deserializeShareProfileModel(Map<String, dynamic> json) =>
    ShareProfileModel.fromJson(json);

@JsonSerializable()
class ShareProfileModel {
  final bool status;
  final Profile profile;

  ShareProfileModel({
    required this.status,
    required this.profile,
  });

  factory ShareProfileModel.fromJson(Map<String, dynamic> json) =>
      _$ShareProfileModelFromJson(json);

  Map<String, dynamic> toJson() => _$ShareProfileModelToJson(this);
}

@JsonSerializable()
class Profile {
  final String username;
  final String name;
  @JsonKey(name: 'profile_picture')
  final String? profilePicture;
  @JsonKey(name: 'social_links')
  final List<SocialLink> socialLinks;

  Profile({
    required this.username,
    required this.name,
    this.profilePicture,
    required this.socialLinks,
  });

  factory Profile.fromJson(Map<String, dynamic> json) =>
      _$Profile<PERSON>romJson(json);

  Map<String, dynamic> toJson() => _$ProfileToJson(this);
}

@JsonSerializable()
class SocialLink {
  final String platform;
  final String url;
  final String name;
  final String username;
  @JsonKey(name: 'profile_image')
  final String? profileImage;
  @JsonKey(name: 'platform_status')
  final String? platformStatus;
  @JsonKey(name: 'user_status')
  final bool? userStatus;

  SocialLink(
      {required this.platform,
      required this.url,
      required this.name,
      required this.username,
      this.profileImage,
      this.platformStatus,
      this.userStatus});

  factory SocialLink.fromJson(Map<String, dynamic> json) =>
      _$SocialLinkFromJson(json);

  Map<String, dynamic> toJson() => _$SocialLinkToJson(this);
}
