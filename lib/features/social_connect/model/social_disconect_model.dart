import 'package:json_annotation/json_annotation.dart';

part 'social_disconect_model.g.dart';

SocialDisconnectModel deserializeSocialDisconnectModel(
        Map<String, dynamic> json) =>
    SocialDisconnectModel.fromJson(json);

@JsonSerializable()
class SocialDisconnectModel {
  final bool status;
  final String message;
  @JsonKey(name: 'Connect')
  final bool connect;

  SocialDisconnectModel({
    required this.status,
    required this.message,
    required this.connect,
  });

  factory SocialDisconnectModel.fromJson(Map<String, dynamic> json) =>
      _$SocialDisconnectModelFromJson(json);

  Map<String, dynamic> toJson() => _$SocialDisconnectModelToJson(this);
}
