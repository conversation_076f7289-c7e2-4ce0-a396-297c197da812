TelegramSendCodeModel deserializeTelegramSendCodeModel(Map<String, dynamic> json) =>
    TelegramSendCodeModel.fromJson(json);

class TelegramSendCodeModel {
  final bool status;
  final String message;
  final String phoneCodeHash;

  TelegramSendCodeModel({
    required this.status,
    required this.message,
    required this.phoneCodeHash,
  });

  factory TelegramSendCodeModel.fromJson(Map<String, dynamic> json) {
    return TelegramSendCodeModel(
      status: json['status'] as bool,
      message: json['message'] as String,
      phoneCodeHash: json['phone_code_hash'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'phone_code_hash': phoneCodeHash,
    };
  }

  TelegramSendCodeModel copyWith({
    bool? status,
    String? message,
    String? phoneCodeHash,
  }) {
    return TelegramSendCodeModel(
      status: status ?? this.status,
      message: message ?? this.message,
      phoneCodeHash: phoneCodeHash ?? this.phoneCodeHash,
    );
  }
}
