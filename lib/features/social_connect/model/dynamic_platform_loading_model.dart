DynamicPlatformModel deserializeDynamicPlatformModel(
        Map<String, dynamic> json) =>
    DynamicPlatformModel.fromJson(json);

class DynamicPlatformModel {
  bool? status;
  Data? data;

  DynamicPlatformModel({this.status, this.data});

  DynamicPlatformModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }

  DynamicPlatformModel copyWith({bool? status, Data? data}) {
    return DynamicPlatformModel(
      status: status ?? this.status,
      data: data ?? this.data,
    );
  }
}

class Data {
  PlatformStatus? platformStatus;
  UserStatus? userStatus;

  Data({this.platformStatus, this.userStatus});

  Data.fromJson(Map<String, dynamic> json) {
    platformStatus = json['platform_status'] != null
        ? PlatformStatus.fromJson(json['platform_status'])
        : null;
    userStatus = json['user_status'] != null
        ? UserStatus.fromJson(json['user_status'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (platformStatus != null) {
      data['platform_status'] = platformStatus!.toJson();
    }
    if (userStatus != null) {
      data['user_status'] = userStatus!.toJson();
    }
    return data;
  }

  Data copyWith({PlatformStatus? platformStatus, UserStatus? userStatus}) {
    return Data(
      platformStatus: platformStatus ?? this.platformStatus,
      userStatus: userStatus ?? this.userStatus,
    );
  }
}

class PlatformStatus {
  String? facebook;
  String? instagram;
  String? thread;
  String? linkedin;
  String? pintrest;
  String? tumblr;
  String? reddit;
  String? youtube;
  String? tiktok;

  PlatformStatus(
      {this.facebook,
      this.instagram,
      this.thread,
      this.linkedin,
      this.pintrest,
      this.tumblr,
      this.reddit,
      this.youtube,
      this.tiktok});

  PlatformStatus.fromJson(Map<String, dynamic> json) {
    facebook = json['facebook'];
    instagram = json['instagram'];
    thread = json['thread'];
    linkedin = json['linkedin'];
    pintrest = json['pintrest'];
    tumblr = json['tumblr'];
    reddit = json['Reddit'];
    youtube = json['Youtube'];
    tiktok = json['tiktok'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['facebook'] = facebook;
    data['instagram'] = instagram;
    data['thread'] = thread;
    data['linkedin'] = linkedin;
    data['pintrest'] = pintrest;
    data['tumblr'] = tumblr;
    data['Reddit'] = reddit;
    data['Youtube'] = youtube;
    data['tiktok'] = tiktok;
    return data;
  }

  PlatformStatus copyWith({
    String? facebook,
    String? instagram,
    String? thread,
    String? linkedin,
    String? pintrest,
    String? tumblr,
    String? reddit,
    String? youtube,
    String? tiktok,
  }) {
    return PlatformStatus(
      facebook: facebook ?? this.facebook,
      instagram: instagram ?? this.instagram,
      thread: thread ?? this.thread,
      linkedin: linkedin ?? this.linkedin,
      pintrest: pintrest ?? this.pintrest,
      tumblr: tumblr ?? this.tumblr,
      reddit: reddit ?? this.reddit,
      youtube: youtube ?? this.youtube,
      tiktok: tiktok ?? this.tiktok,
    );
  }
}

class UserStatus {
  bool? facebook;
  bool? instagram;
  bool? linkedin;
  bool? pintrest;
  bool? tumblr;
  bool? reddit;
  bool? youtube;
  bool? tiktok;

  UserStatus(
      {this.facebook,
      this.instagram,
      this.linkedin,
      this.pintrest,
      this.tumblr,
      this.reddit,
      this.youtube,
      this.tiktok});

  UserStatus.fromJson(Map<String, dynamic> json) {
    facebook = json['facebook'];
    instagram = json['instagram'];
    linkedin = json['linkedin'];
    pintrest = json['pintrest'];
    tumblr = json['tumblr'];
    reddit = json['Reddit'];
    youtube = json['Youtube'];
    tiktok = json['tiktok'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['facebook'] = facebook;
    data['instagram'] = instagram;
    data['linkedin'] = linkedin;
    data['pintrest'] = pintrest;
    data['tumblr'] = tumblr;
    data['Reddit'] = reddit;
    data['Youtube'] = youtube;
    data['tiktok'] = tiktok;
    return data;
  }

  UserStatus copyWith({
    bool? facebook,
    bool? instagram,
    bool? linkedin,
    bool? pintrest,
    bool? tumblr,
    bool? reddit,
    bool? youtube,
    bool? tiktok,
  }) {
    return UserStatus(
      facebook: facebook ?? this.facebook,
      instagram: instagram ?? this.instagram,
      linkedin: linkedin ?? this.linkedin,
      pintrest: pintrest ?? this.pintrest,
      tumblr: tumblr ?? this.tumblr,
      reddit: reddit ?? this.reddit,
      youtube: youtube ?? this.youtube,
      tiktok: tiktok ?? this.tiktok,
    );
  }
}
