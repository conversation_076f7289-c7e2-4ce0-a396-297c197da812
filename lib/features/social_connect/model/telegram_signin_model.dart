TelegramSignInModel deserializeTelegramSignInModel(Map<String, dynamic> json) => TelegramSignInModel.fromJson(json);

class TelegramSignInModel {
  final bool status;
  final String message;
  final UserInfo userInfo;

  TelegramSignInModel({
    required this.status,
    required this.message,
    required this.userInfo,
  });

  factory TelegramSignInModel.fromJson(Map<String, dynamic> json) {
    return TelegramSignInModel(
      status: json['status'] as bool,
      message: json['message'] as String,
      userInfo: UserInfo.fromJson(json['user_info'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'user_info': userInfo.toJson(),
    };
  }

  TelegramSignInModel copyWith({
    bool? status,
    String? message,
    UserInfo? userInfo,
  }) {
    return TelegramSignInModel(
      status: status ?? this.status,
      message: message ?? this.message,
      userInfo: userInfo ?? this.userInfo,
    );
  }
}

class UserInfo {
  final int userId;
  final String? username;
  final String firstName;
  final String? lastName;
  final String phone;

  UserInfo({
    required this.userId,
    this.username,
    required this.firstName,
    this.lastName,
    required this.phone,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      userId: json['user_id'] as int,
      username: json['username'] as String?,
      firstName: json['first_name'] as String,
      lastName: json['last_name'] as String?,
      phone: json['phone'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'username': username,
      'first_name': firstName,
      'last_name': lastName,
      'phone': phone,
    };
  }

  UserInfo copyWith({
    int? userId,
    String? username,
    String? firstName,
    String? lastName,
    String? phone,
  }) {
    return UserInfo(
      userId: userId ?? this.userId,
      username: username ?? this.username,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phone: phone ?? this.phone,
    );
  }
}
