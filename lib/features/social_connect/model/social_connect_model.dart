import 'package:json_annotation/json_annotation.dart';

part 'social_connect_model.g.dart';

SocialConnectModel deserializeSocialConnectModel(Map<String, dynamic> json) =>
    SocialConnectModel.fromJson(json);

@JsonSerializable()
class SocialConnectModel {
  final bool status;
  final String message;
  final String? url;

  SocialConnectModel({
    required this.status,
    required this.message,
    this.url,
  });

  /// A factory constructor for creating a new instance from a JSON map.
  factory SocialConnectModel.fromJson(Map<String, dynamic> json) =>
      _$SocialConnectModelFromJson(json);

  /// A method to convert the instance into a JSON map.
  Map<String, dynamic> toJson() => _$SocialConnectModelToJson(this);
}
