// ignore_for_file: non_constant_identifier_names

import 'package:json_annotation/json_annotation.dart';

part 'social_connect_check.g.dart';

SocialConnectCheck deserializeSocialConnectCheck(Map<String, dynamic> json) => SocialConnectCheck.fromJson(json);

@JsonSerializable()
class SocialConnectCheck {
  final bool status;
  final String message;
  final SocialConnectData data;

  SocialConnectCheck({
    required this.status,
    required this.message,
    required this.data,
  });

  /// Factory method to generate a `SocialConnectCheck` object from JSON.
  factory SocialConnectCheck.fromJson(Map<String, dynamic> json) => _$SocialConnectCheckFromJson(json);

  /// Method to convert `SocialConnectCheck` object to JSON.
  Map<String, dynamic> toJson() => _$SocialConnectCheckToJson(this);
}

@JsonSerializable()
class SocialConnectData {
  @JsonKey(name: 'is_any_auth')
  final bool isAnyAuth;
  final bool Facebook;
  final bool Instagram;
  final bool x;
  final bool YouTube;
  final bool LinkedIn;
  final bool Pinterest;
  final bool tiktok;
  final bool threads;
  final bool Dailymotion;
  final bool tumblr;
  final bool Vimeo;
  @JsonKey(name: 'telegram')
  final bool telegram;
  @JsonKey(name: 'mastodon')
  final bool mastodon;
  final bool reddit;

  SocialConnectData({
    required this.isAnyAuth,
    required this.Facebook,
    required this.Instagram,
    required this.x,
    required this.YouTube,
    required this.LinkedIn,
    required this.Pinterest,
    required this.tiktok,
    required this.threads,
    required this.Dailymotion,
    required this.tumblr,
    required this.Vimeo,
    required this.telegram,
    required this.mastodon,
    required this.reddit,
  });

  /// Factory method to generate a `SocialConnectData` object from JSON.
  factory SocialConnectData.fromJson(Map<String, dynamic> json) => _$SocialConnectDataFromJson(json);

  /// Method to convert `SocialConnectData` object to JSON.
  Map<String, dynamic> toJson() => _$SocialConnectDataToJson(this);
}
