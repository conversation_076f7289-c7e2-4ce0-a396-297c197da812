import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_manager.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_player.dart';
import 'package:flowkar/features/social_connect/bloc/social_connec_bloc.dart';
// import 'package:flowkar/features/user_post/presentation/widget/flick_multi_manager.dart';
// import 'package:flowkar/features/user_post/presentation/widget/flick_multi_player.dart';

class Anyliticsview extends StatefulWidget {
  const Anyliticsview({super.key});

  static Widget builder(BuildContext context) {
    return Anyliticsview();
  }

  @override
  State<Anyliticsview> createState() => _AnyliticsviewState();
}

class _AnyliticsviewState extends State<Anyliticsview> {
  @override
  void initState() {
    context.read<SocialConnecBloc>().add(ThreadProfileDataApiEvent());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: BlocBuilder<SocialConnecBloc, SocialConnectState>(
        builder: (context, state) {
          if (state.getanalyticsLoading) {
            return LoadingAnimationWidget();
          }
          return SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.0.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildAnyliticsAppBar(context),
                  buildSizedBoxH(8),
                  _buildProfileInfo(state, context),
                  _buildRecentPostsHeader(),
                  _buildRecentPost(state, context),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAnyliticsAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      alignment: Alignment.center,
      title: 'Thread',
      textStyle: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
    );
  }

  Widget _buildProfileInfo(SocialConnectState state, BuildContext context) {
    return Row(
      children: [
        CircleAvatar(
          radius: 40,
          backgroundColor: Colors.grey[200],
          child: CircleAvatar(
            radius: 38,
            backgroundImage: NetworkImage(
              state.instaProfileModel?.data?.profileImage.toString() ?? Assets.images.pngs.other.pngPlaceholder.path,
            ),
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                state.instaProfileModel?.data?.name.toString() ?? 'Tester',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold,
                    ),
              ),
              Text(
                state.instaProfileModel?.data?.username.toString() ?? '@tester',
                style: TextStyle(color: Colors.grey, fontSize: 16.sp),
              ),
            ],
          ),
        ),
        CustomImageView(
          imagePath: Assets.images.icons.social.icThread.path,
          fit: BoxFit.cover,
        ),
      ],
    );
  }

  Widget _buildRecentPostsHeader() {
    return Padding(
      padding: EdgeInsets.all(16.0),
      child: Text(
        'Recent uploaded Post',
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildRecentPost(SocialConnectState state, BuildContext context) {
    final mediaData = state.instaProfileModel?.data?.post?.mediaData.toString() ?? '';
    bool isVideoUrl(String url) {
      final videoExtensions = ['.mp4', '.mov', '.avi', '.wmv', '.flv', '.mkv'];
      return videoExtensions.any((ext) => url.toLowerCase().contains(ext));
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          isVideoUrl(mediaData)
              ? FlickMultiPlayer(
                  url: mediaData,
                  flickMultiManager: FlickMultiManager(),
                  image: Assets.images.icons.social.icThread.path,
                )
              : _buildImagePost(mediaData),
          SizedBox(height: 8.h),
        ],
      ),
    );
  }

  Widget _buildImagePost(String mediaData) {
    return Container(
      height: 300.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        image: DecorationImage(
          image: NetworkImage(mediaData),
          fit: BoxFit.cover,
        ),
      ),
    );
  }
}
