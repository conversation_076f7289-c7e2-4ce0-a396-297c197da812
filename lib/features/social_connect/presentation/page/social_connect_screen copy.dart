import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/social_connect/bloc/social_connec_bloc.dart';
import 'package:flowkar/features/social_connect/model/share_profile_model.dart';
import 'package:flowkar/features/social_connect/presentation/widget/social_connect_shimmer.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

class SocialConnectPage extends StatefulWidget {
  final bool? stackonScreen;
  final bool? backBTN;
  final void Function()? oncallBack;
  const SocialConnectPage({super.key, this.stackonScreen, this.oncallBack, this.backBTN});

  static Widget builder(BuildContext context) {
    final List args = ModalRoute.of(context)?.settings.arguments as List;

    return SocialConnectPage(
      stackonScreen: args[0],
    );
  }

  @override
  State<SocialConnectPage> createState() => _SocialConnectPageState();
}

class _SocialConnectPageState extends State<SocialConnectPage> with WidgetsBindingObserver {
  bool _didLaunchUrl = false;

  @override
  void initState() {
    context.read<SocialConnecBloc>().add(DynamicplatformApiEvent());
    if (widget.stackonScreen == true) {
      context.read<SocialConnecBloc>().add(ShareProfileApiEvent());
    }
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed && _didLaunchUrl) {
      _didLaunchUrl = false;
      context.read<SocialConnecBloc>().add(SocialConnectCheckApiEvent());
      context.read<SocialConnecBloc>().add(ShareProfileApiEvent());
      Logger.lOG('WebView was closed, toast shown.');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<SocialConnecBloc, SocialConnectState>(
        builder: (context, state) {
          if (state.isShareProfileLoading) {
            return SocialConnectShimmer(
              useShimmerAppBar: false,
            );
          }
          _didLaunchUrl = state.applaunchurL;
          final shareProfileModel = state.shareProfileModel;

          final socialMediaPlatforms = [
            SocialPlatform(
              icon: Assets.images.icons.social.v.path,
              imageUrl: shareProfileModel?.profile.socialLinks
                      .firstWhere(
                        (link) => link.platform == 'vimeo',
                        orElse: () => SocialLink(
                          platform: 'vimeo',
                          profileImage: Assets.images.icons.social.v.path,
                          name: 'vimeo',
                          url: '',
                          username: '',
                        ),
                      )
                      .profileImage ??
                  Assets.images.icons.social.v.path,
              title: socialPlatformsStatus.value['VIMEO']!
                  ? shareProfileModel?.profile.socialLinks
                          .firstWhere(
                            (link) => link.platform == 'vimeo',
                            orElse: () => SocialLink(
                              platform: 'vimeo',
                              profileImage: Assets.images.icons.social.v.path,
                              name: 'vimeo',
                              url: '',
                              username: Lang.of(context).lbl_vimeo,
                            ),
                          )
                          .username ??
                      Lang.of(context).lbl_vimeo
                  : Lang.of(context).lbl_vimeo,
              onTap: (context) {
                final isConnected = Prefobj.preferences?.get(Prefkeys.VIMEO) == true;
                context.read<SocialConnecBloc>().add(isConnected
                    ? VimeoDisconnectApiEvent()
                    : VimeoconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
              },
              isConnected: socialPlatformsStatus.value['VIMEO']!,
              isDisabled: state.isVimeoConnectLoading,
              isLoading: state.isVimeoConnectLoading,
            ),
            SocialPlatform(
                icon: Assets.images.icons.social.facebook.path,
                imageUrl: shareProfileModel?.profile.socialLinks
                        .firstWhere(
                          (link) => link.platform == 'facebook',
                          orElse: () => SocialLink(
                            platform: Lang.of(context).lbl_facebook,
                            profileImage: Assets.images.icons.social.facebook.path,
                            name: Lang.of(context).lbl_facebook,
                            url: '',
                            username: '',
                          ),
                        )
                        .profileImage ??
                    Assets.images.icons.social.facebook.path,
                title: socialPlatformsStatus.value['FACEBOOK']!
                    ? shareProfileModel?.profile.socialLinks
                            .firstWhere(
                              (link) => link.platform == 'facebook',
                              orElse: () => SocialLink(
                                platform: Lang.of(context).lbl_facebook,
                                profileImage: Assets.images.icons.social.facebook.path,
                                name: Lang.of(context).lbl_facebook,
                                url: '',
                                username: Lang.of(context).lbl_facebook,
                              ),
                            )
                            .username ??
                        Lang.of(context).lbl_facebook
                    : Lang.of(context).lbl_facebook,
                onTap: (context) {
                  final isConnected = Prefobj.preferences?.get(Prefkeys.FACEBOOK) == true;
                  context.read<SocialConnecBloc>().add(isConnected
                      ? FacebookDisconnectApiEvent()
                      : FacebookconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
                },
                isConnected: socialPlatformsStatus.value['FACEBOOK']!,
                isDisabled: state.isFacebookConnectLoading,
                isLoading: state.isFacebookConnectLoading,
                comingsoon: false),
            SocialPlatform(
                icon: Assets.images.icons.social.insta.path,
                imageUrl: shareProfileModel?.profile.socialLinks
                        .firstWhere(
                          (link) => link.platform == 'instagram',
                          orElse: () => SocialLink(
                            platform: 'instagram',
                            profileImage: Assets.images.icons.social.insta.path,
                            name: 'instagram',
                            url: '',
                            username: '',
                          ),
                        )
                        .profileImage ??
                    Assets.images.icons.social.insta.path,
                title: socialPlatformsStatus.value['INSTAGRAM']!
                    ? shareProfileModel?.profile.socialLinks
                            .firstWhere(
                              (link) => link.platform == 'instagram',
                              orElse: () => SocialLink(
                                platform: Lang.of(context).lbl_instagram,
                                profileImage: Assets.images.icons.social.insta.path,
                                name: Lang.of(context).lbl_instagram,
                                url: '',
                                username: Lang.of(context).lbl_instagram,
                              ),
                            )
                            .username ??
                        Lang.of(context).lbl_instagram
                    : Lang.of(context).lbl_instagram,
                onTap: (context) {
                  final isConnected = Prefobj.preferences?.get(Prefkeys.INSTAGRAM) == true;
                  context.read<SocialConnecBloc>().add(isConnected
                      ? InstagramDisconnectApiEvent()
                      : InstagramconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
                },
                isConnected: socialPlatformsStatus.value['INSTAGRAM'] ?? false,
                isDisabled: state.isInstagramConnectLoading,
                isLoading: state.isInstagramConnectLoading,
                comingsoon: false),
            SocialPlatform(
                icon: Assets.images.icons.social.icThread.path,
                imageUrl: shareProfileModel?.profile.socialLinks
                        .firstWhere(
                          (link) => link.platform == 'thread',
                          orElse: () => SocialLink(
                            platform: 'thread',
                            profileImage: Assets.images.icons.social.icThread.path,
                            name: 'thread',
                            url: '',
                            username: '',
                          ),
                        )
                        .profileImage ??
                    Assets.images.icons.social.icThread.path,
                title: socialPlatformsStatus.value['THREAD']!
                    ? shareProfileModel?.profile.socialLinks
                            .firstWhere(
                              (link) => link.platform == 'thread',
                              orElse: () => SocialLink(
                                platform: Lang.of(context).lbl_thread,
                                profileImage: Assets.images.icons.social.icThread.path,
                                name: Lang.of(context).lbl_thread,
                                url: '',
                                username: Lang.of(context).lbl_thread,
                              ),
                            )
                            .username ??
                        Lang.of(context).lbl_thread
                    : Lang.of(context).lbl_thread,
                onTap: (context) {
                  final isConnected = Prefobj.preferences?.get(Prefkeys.THREAD) == true;
                  context.read<SocialConnecBloc>().add(isConnected
                      ? ThreadDisconnectApiEvent()
                      : ThreadconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
                },
                isConnected: socialPlatformsStatus.value['THREAD'] ?? false,
                isDisabled: state.isThreadConnectLoading,
                isLoading: state.isThreadConnectLoading,
                comingsoon: false),
            SocialPlatform(
              icon: Assets.images.icons.social.linkedin.path,
              imageUrl: shareProfileModel?.profile.socialLinks
                      .firstWhere(
                        (link) => link.platform == 'linkedin',
                        orElse: () => SocialLink(
                          platform: 'linkedin',
                          profileImage: Assets.images.icons.social.linkedin.path,
                          name: 'linkedin',
                          url: '',
                          username: '',
                        ),
                      )
                      .profileImage ??
                  Assets.images.icons.social.linkedin.path,
              title: socialPlatformsStatus.value['LINKEDIN']!
                  ? shareProfileModel?.profile.socialLinks
                          .firstWhere(
                            (link) => link.platform == 'linkedin',
                            orElse: () => SocialLink(
                              platform: Lang.of(context).lbl_linkedIn,
                              profileImage: Assets.images.icons.social.linkedin.path,
                              name: Lang.of(context).lbl_linkedIn,
                              url: '',
                              username: Lang.of(context).lbl_linkedIn,
                            ),
                          )
                          .username ??
                      Lang.of(context).lbl_linkedIn
                  : Lang.of(context).lbl_linkedIn,
              onTap: (context) {
                final isConnected = Prefobj.preferences?.get(Prefkeys.LINKEDIN) == true;
                context
                    .read<SocialConnecBloc>()
                    .add(isConnected ? LinkdinDisconnectApiEvent() : LinkdinconnectApiEvent(context: context));
              },
              isConnected: socialPlatformsStatus.value['LINKEDIN']!,
              isDisabled: state.isLinkedInConnectLoading,
              isLoading: state.isLinkedInConnectLoading,
            ),
            SocialPlatform(
              icon: Assets.images.icons.social.pintrest.path,
              imageUrl: shareProfileModel?.profile.socialLinks
                      .firstWhere(
                        (link) => link.platform == 'pinterest',
                        orElse: () => SocialLink(
                          platform: 'pinterest',
                          profileImage: Assets.images.icons.social.pintrest.path,
                          name: 'pinterest',
                          url: '',
                          username: '',
                        ),
                      )
                      .profileImage ??
                  Assets.images.icons.social.pintrest.path,
              title: socialPlatformsStatus.value['PINTEREST']!
                  ? shareProfileModel?.profile.socialLinks
                          .firstWhere(
                            (link) => link.platform == 'pintrest',
                            orElse: () => SocialLink(
                              platform: Lang.of(context).lbl_pintrest,
                              profileImage: Assets.images.icons.social.pintrest.path,
                              name: Lang.of(context).lbl_pintrest,
                              url: '',
                              username: Lang.of(context).lbl_pintrest,
                            ),
                          )
                          .username ??
                      Lang.of(context).lbl_pintrest
                  : Lang.of(context).lbl_pintrest,
              onTap: (context) {
                final isConnected = Prefobj.preferences?.get(Prefkeys.PINTEREST) == true;
                context.read<SocialConnecBloc>().add(isConnected
                    ? PintrestDisconnectApiEvent()
                    : PinterstconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
              },
              isConnected: socialPlatformsStatus.value['PINTEREST']!,
              isDisabled: state.isPintrestConnectLoading,
              isLoading: state.isPintrestConnectLoading,
            ),
            SocialPlatform(
                icon: Assets.images.pngs.socialConnect.icTumblr.path,
                imageUrl: shareProfileModel?.profile.socialLinks
                        .firstWhere(
                          (link) => link.platform == 'tumblr',
                          orElse: () => SocialLink(
                            platform: 'tumblr',
                            profileImage: Assets.images.icons.social.icTumblr.path,
                            name: 'tumblr',
                            url: '',
                            username: '',
                          ),
                        )
                        .profileImage ??
                    Assets.images.pngs.socialConnect.icTumblr.path,
                title: socialPlatformsStatus.value['TUMBLR']!
                    ? shareProfileModel?.profile.socialLinks
                            .firstWhere(
                              (link) => link.platform == 'tumblr',
                              orElse: () => SocialLink(
                                platform: Lang.of(context).lbl_tumblr,
                                profileImage: Assets.images.icons.social.icTumblr.path,
                                name: Lang.of(context).lbl_tumblr,
                                url: '',
                                username: Lang.of(context).lbl_tumblr,
                              ),
                            )
                            .username ??
                        Lang.of(context).lbl_tumblr
                    : Lang.of(context).lbl_tumblr,
                onTap: (context) {
                  final isConnected = Prefobj.preferences?.get(Prefkeys.TUMBLR) == true;
                  context.read<SocialConnecBloc>().add(isConnected
                      ? TumblrDisconnectApiEvent()
                      : SocialconnectTumblrApiEvent(
                          context: context,
                          platForm: 'Tumblr',
                        ));
                },
                isConnected: socialPlatformsStatus.value['TUMBLR'] ?? false,
                isDisabled: state.isTumblrLoading,
                isLoading: state.isTumblrLoading,
                comingsoon: true),
            SocialPlatform(
                icon: Assets.images.pngs.socialConnect.pngReddit.path,
                imageUrl: shareProfileModel?.profile.socialLinks
                        .firstWhere(
                          (link) => link.platform == 'Reddit',
                          orElse: () => SocialLink(
                            platform: 'Reddit',
                            profileImage: Assets.images.pngs.socialConnect.pngReddit.path,
                            name: 'Reddit',
                            url: '',
                            username: '',
                          ),
                        )
                        .profileImage ??
                    Assets.images.pngs.socialConnect.pngReddit.path,
                title: socialPlatformsStatus.value['REDDIT']!
                    ? shareProfileModel?.profile.socialLinks
                            .firstWhere(
                              (link) => link.platform == 'Reddit',
                              orElse: () => SocialLink(
                                platform: Lang.of(context).lbl_reddit,
                                profileImage: Assets.images.pngs.socialConnect.pngReddit.path,
                                name: Lang.of(context).lbl_reddit,
                                url: '',
                                username: Lang.of(context).lbl_reddit,
                              ),
                            )
                            .username ??
                        Lang.of(context).lbl_reddit
                    : Lang.of(context).lbl_reddit,
                onTap: (context) {
                  final isConnected = Prefobj.preferences?.get(Prefkeys.REDDIT) == true;
                  context.read<SocialConnecBloc>().add(isConnected
                      ? RedditDisconnectApiEvent()
                      : RedditconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
                },
                isConnected: socialPlatformsStatus.value['REDDIT'] ?? false,
                isDisabled: state.isReditConnectLoading,
                isLoading: state.isReditConnectLoading,
                comingsoon: true),
            SocialPlatform(
                icon: Assets.images.icons.social.icYoutube.path,
                imageUrl: shareProfileModel?.profile.socialLinks
                        .firstWhere(
                          (link) => link.platform == 'Youtube',
                          orElse: () => SocialLink(
                            platform: 'Youtube',
                            profileImage: Assets.images.icons.social.icYoutube.path,
                            name: 'Youtube',
                            url: '',
                            username: '',
                          ),
                        )
                        .profileImage ??
                    Assets.images.icons.social.icYoutube.path,
                title: shareProfileModel?.profile.socialLinks
                        .firstWhere(
                          (link) => link.platform == 'Youtube',
                          orElse: () => SocialLink(
                            platform: Lang.of(context).lbl_youtube,
                            profileImage: Assets.images.icons.social.icYoutube.path,
                            name: Lang.of(context).lbl_youtube,
                            url: '',
                            username: Lang.of(context).lbl_youtube,
                          ),
                        )
                        .username ??
                    Lang.of(context).lbl_youtube,
                onTap: (context) {},
                isConnected: false,
                isDisabled: state.isYoutubeConnectLoading,
                isLoading: state.isYoutubeConnectLoading,
                comingsoon: true),
            SocialPlatform(
                icon: Assets.images.icons.social.tiktok.path,
                imageUrl: shareProfileModel?.profile.socialLinks
                        .firstWhere(
                          (link) => link.platform == 'tiktok',
                          orElse: () => SocialLink(
                            platform: Lang.of(context).lbl_tiktok,
                            profileImage: Assets.images.icons.social.tiktok.path,
                            name: Lang.of(context).lbl_tiktok,
                            url: '',
                            username: '',
                          ),
                        )
                        .profileImage ??
                    Assets.images.icons.social.tiktok.path,
                title: socialPlatformsStatus.value['TIKTOK']!
                    ? shareProfileModel?.profile.socialLinks
                            .firstWhere(
                              (link) => link.platform == 'tiktok',
                              orElse: () => SocialLink(
                                platform: Lang.of(context).lbl_tiktok,
                                profileImage: Assets.images.icons.social.tiktok.path,
                                name: Lang.of(context).lbl_tiktok,
                                url: '',
                                username: Lang.of(context).lbl_tiktok,
                              ),
                            )
                            .username ??
                        Lang.of(context).lbl_tiktok
                    : Lang.of(context).lbl_tiktok,
                onTap: (context) {
                  final isConnected = Prefobj.preferences?.get(Prefkeys.TIKTOK) ?? false;
                  context.read<SocialConnecBloc>().add(isConnected
                      ? TiktokDisconnectApiEvent()
                      : TiktokconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
                },
                isConnected: socialPlatformsStatus.value['TIKTOK']!,
                isDisabled: state.isTiktokConnectLoading,
                isLoading: state.isTiktokConnectLoading,
                comingsoon: false),
          ];
          bool isSkipConditionCheck = socialMediaPlatforms.any((platform) => platform.isConnected);

          return PopScope(
            onPopInvoked: (didPop) {
              widget.oncallBack!();
            },
            child: Column(
              children: [
                buildSizedBoxH(8.h),
                widget.stackonScreen == true
                    ? _buildSocialAppBar()
                    : _buildSocialConnectBar(context, isSkipConditionCheck),
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        buildSizedBoxH(40.0),
                        _buildSocialConnectGrideView(socialMediaPlatforms),
                        widget.stackonScreen == true ? buildSizedBoxH(80.0) : buildSizedBoxH(10.0),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildSocialAppBar() {
    return CustomAppbar(
      title: "Social Connect",
      hasLeadingIcon: widget.backBTN ?? true,
      onLeadingTap: () {
        NavigatorService.goBack();
        widget.oncallBack!();
      },
      leadingImagePath: Assets.images.svg.authentication.icBackArrow.path,
      height: 18.h,
      alignment: Alignment.center,
      textStyle: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
    );
  }

  Widget _buildSocialConnectBar(BuildContext context, bool isSkipConditionCheck) {
    return CustomAppbar(
      // title: Lang.of(context).lbl_connect_account,
      title: 'Connect Account',
      hasLeadingIcon: false,
      actions: [
        InkWell(
          onTap: () {
            NavigatorService.pushAndRemoveUntil(AppRoutes.bottomNavBar);
            Prefobj.preferences?.put(Prefkeys.SKIPSOCIALCONNECT, true);
          },
          child: Text(
            isSkipConditionCheck ? 'Next' : 'Skip',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
          ),
        ),
      ],
    );
  }

  Widget _buildSocialConnectGrideView(List<SocialPlatform> socialMediaPlatforms) {
    return AnimationLimiter(
      child: GridView.builder(
        physics: const ScrollPhysics(),
        padding: EdgeInsets.symmetric(
          horizontal: 14.w,
        ),
        shrinkWrap: true,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 1,
          crossAxisSpacing: 14,
          mainAxisSpacing: 10,
        ),
        itemCount: socialMediaPlatforms.length,
        itemBuilder: (context, index) {
          final platform = socialMediaPlatforms[index];
          return AnimationConfiguration.staggeredGrid(
            position: index,
            duration: const Duration(milliseconds: 800),
            columnCount: 2,
            child: SlideAnimation(
              verticalOffset: 100.0,
              child: FadeInAnimation(
                child: _buildSocialConnectContainer(
                  context: context,
                  onPressed: platform.onTap != null ? () => platform.onTap!(context) : null,
                  imagePath: platform.icon,
                  text: platform.title,
                  isConnected: platform.isConnected,
                  isDisabled: platform.isDisabled,
                  isLoading: platform.isLoading,
                  comingsoon: platform.comingsoon,
                  profileImage: platform.imageUrl,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSocialConnectContainer({
    required BuildContext context,
    String? imagePath,
    String? profileImage,
    String? text,
    Function()? onPressed,
    required bool isConnected,
    required bool isDisabled,
    required bool isLoading,
    bool? comingsoon,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.socialContainer,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Stack(
        children: [
          isConnected
              ? Positioned(
                  top: 12.h,
                  right: 12.w,
                  child: CustomImageView(
                    height: 25.h,
                    width: 25.w,
                    alignment: Alignment.topRight,
                    imagePath: imagePath,
                  ),
                )
              : SizedBox.shrink(),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: EdgeInsets.only(left: 18.w, top: 18.h),
                width: 46.w,
                height: 46.h,
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.r),
                  color: Colors.white,
                ),
                child: isConnected
                    ? Container(
                        decoration: BoxDecoration(borderRadius: BorderRadius.circular(12.r)),
                        child: Image.network(profileImage.toString()))
                    : CustomImageView(imagePath: imagePath),
              ),
              Spacer(),
              _buildSocialConnectTitle(context, text),
              Spacer(),
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                  ),
                  child: CustomElevatedButton(
                    margin: EdgeInsets.only(bottom: 8.h),
                    onPressed: onPressed,
                    isLoading: isLoading,
                    isDisabled: comingsoon! ? true : isDisabled,
                    color: isConnected ? Colors.white : Theme.of(context).primaryColor,
                    buttonStyle: ButtonStyle(
                      backgroundColor: WidgetStatePropertyAll(
                        isConnected ? Theme.of(context).primaryColor : Theme.of(context).customColors.socialButton,
                      ),
                    ),
                    decoration: const BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                          blurRadius: 8,
                          color: Colors.black12,
                          offset: Offset(0, 0),
                        ),
                      ],
                    ),
                    text: comingsoon
                        ? "Coming Soon"
                        : isConnected
                            ? "Connected"
                            : "Connect",
                    buttonTextStyle: Theme.of(context).textTheme.labelLarge?.copyWith(
                          fontWeight: FontWeight.w700,
                          fontSize: comingsoon ? 15.sp : 16.0.sp,
                          color: isConnected ? Colors.white : Theme.of(context).primaryColor,
                        ),
                    height: 40.h,
                  ),
                ),
              ),
              Visibility(
                visible: isConnected && imagePath == Assets.images.icons.social.icThread.path,
                child: Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: CustomElevatedButton(
                      margin: EdgeInsets.only(bottom: 6.h),
                      onPressed: () {
                        NavigatorService.pushNamed(AppRoutes.anyliticsview);
                      },
                      isLoading: isLoading,
                      color: Theme.of(context).primaryColor,
                      buttonStyle: ButtonStyle(
                        backgroundColor: WidgetStatePropertyAll(
                          Theme.of(context).customColors.socialButton,
                        ),
                      ),
                      text: "View Details",
                      buttonTextStyle: Theme.of(context).textTheme.labelLarge?.copyWith(
                            fontWeight: FontWeight.w700,
                            fontSize: 16.0.sp,
                            color: Theme.of(context).primaryColor,
                          ),
                      height: 40.h,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSocialConnectTitle(BuildContext context, String? text) {
    return Padding(
      padding: EdgeInsets.only(left: 20.w),
      child: Text(
        text!,
        textAlign: TextAlign.center,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 16.sp),
      ),
    );
  }
}

class SocialPlatform {
  final String icon;
  final String title;
  final Function(BuildContext context)? onTap;
  final bool isConnected;
  final bool isDisabled;
  final bool isLoading;
  final bool comingsoon;
  final String imageUrl;

  SocialPlatform({
    required this.icon,
    required this.title,
    this.onTap,
    required this.isConnected,
    required this.isDisabled,
    required this.isLoading,
    this.comingsoon = false,
    required this.imageUrl,
  });
}
