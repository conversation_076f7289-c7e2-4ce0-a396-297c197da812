// ignore_for_file: deprecated_member_use, unnecessary_null_comparison
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/social_connect/bloc/social_connec_bloc.dart';
import 'package:flowkar/features/social_connect/presentation/widget/social_connect_shimmer.dart';
import 'package:flowkar/features/social_connect/presentation/widget/talegram_bottomsheet.dart';
import 'package:url_launcher/url_launcher.dart';

class SocialConnectPage extends StatefulWidget {
  final bool? stackonScreen;
  final bool? backBTN;
  final void Function()? oncallBack;
  const SocialConnectPage({super.key, this.stackonScreen, this.oncallBack, this.backBTN});

  static Widget builder(BuildContext context) {
    final List args = ModalRoute.of(context)?.settings.arguments as List;

    return SocialConnectPage(
      stackonScreen: args[0],
    );
  }

  @override
  State<SocialConnectPage> createState() => _SocialConnectPageState();
}

class _SocialConnectPageState extends State<SocialConnectPage> with WidgetsBindingObserver {
  bool _didLaunchUrl = false;
  final List<SocialPlatform> socialMediaPlatforms = [];
  int brandId = 0;

  @override
  void initState() {
    brandId = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;
    // context.read<UserManagementBloc>().add(GetBrandsAPI());
    context.read<SocialConnecBloc>().add(ShareProfileApiEvent());

    if (widget.stackonScreen == true) {}
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed && _didLaunchUrl) {
      _didLaunchUrl = false;
      context.read<SocialConnecBloc>().add(SocialConnectCheckApiEvent());
      context.read<SocialConnecBloc>().add(ShareProfileApiEvent());
    }
  }

  @override
  Widget build(BuildContext context) {
    brandId = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;
    return Scaffold(
      body: BlocBuilder<SocialConnecBloc, SocialConnectState>(
        builder: (context, state) {
          brandId = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;
          if (state.isShareProfileLoading) {
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 0.w),
              child: SocialConnectShimmer(
                useShimmerAppBar: widget.stackonScreen == true ? true : false,
              ),
            );
          }
          _didLaunchUrl = state.applaunchurL;
          final shareProfileModel = state.shareProfileModel;

          if (shareProfileModel?.profile.socialLinks != null) {
            socialMediaPlatforms.clear();

            // Create two separate lists
            List<SocialPlatform> regularPlatforms = [];
            List<SocialPlatform> comingSoonPlatforms = [];

            for (var link in shareProfileModel!.profile.socialLinks) {
              Logger.lOG("${link.platform}--- ${link.profileImage}");

              // Skip platforms with status '3' - don't show them
              if (link.platformStatus == '3') {
                continue; // Skip this iteration and don't add this platform
              }

              // Create the SocialPlatform object
              final platform = SocialPlatform(
                  icon: getplatformimgurL(link.platform, link.profileImage) ?? '',
                  imageUrl: getPlatformImage(link.platform, link.profileImage) ?? '',
                  title: link.username.isEmpty || link.username == ""
                      ? link.platform[0].toUpperCase() + link.platform.substring(1)
                      : link.username,
                  onTap: (c) {
                    String getPlatformSubtitle(String platform) {
                      switch (platform.toLowerCase()) {
                        case 'instagram':
                          return "To connect Instagram, your account must be set to a Business or Professional type; otherwise, authentication will not be successful. Additionally, if you want to use messaging and analytics features, your Instagram account must be linked to a Facebook.";
                        case 'facebook':
                          return "To connect Facebook, your account must be linked to at least one Facebook Page; otherwise, authentication will not be successful. Additionally, only one Page can be connected per brand—if multiple Pages are selected, the first one will be connected automatically.";
                        case 'thread':
                          return "To connect Threads, your account must be a Business or Professional account; otherwise, authentication will fail.";
                        case 'youtube':
                          return "To connect YouTube, your account must have an active channel; otherwise, authentication will not be successful.";
                        case 'tiktok':
                          return " To connect and upload content on TikTok, your device must be located in a country where access is unrestricted; otherwise, authentication will fail.";
                        case 'x':
                          return "Please Contect us.";
                        // case 'x':
                        //   return "Connect your X account to experience premium services of Flowkar.";
                        default:
                          return "Connect your $platform account to share your profile.";
                      }
                    }

                    String getPlatformSubtitleForDisconnect(String platform) {
                      switch (platform.toLowerCase()) {
                        // case 'instagram':
                        //   return "Are you sure you want to disconnect this platform from your account?";
                        // case 'facebook':
                        //   return "Are you sure you want to disconnect this platform from your account?";
                        // case 'thread':
                        //   return "Are you sure you want to disconnect this platform from your account?";
                        // case 'youtube':
                        //   return "Are you sure you want to disconnect this platform from your account?";
                        // case 'tiktok':
                        //   return "Are you sure you want to disconnect this platform from your account?";

                        // case 'x':
                        //   return "Are you sure you want to disconnect this platform from your account?";
                        default:
                          return "Are you sure you want to disconnect this platform from your account?";
                      }
                    }

                    bool isConnected = socialPlatformsStatus.value[link.platform.toUpperCase()] ?? false;
                    if (!isConnected) {
                      showDialog(
                          context: c,
                          barrierDismissible: isPlatformLoading(link.platform, state),
                          builder: (context) {
                            return BlocBuilder<SocialConnecBloc, SocialConnectState>(
                              builder: (context, cocialConnectState) {
                                return IgnorePointer(
                                  ignoring: isPlatformLoading(link.platform, cocialConnectState),
                                  child: CustomAlertDialog(
                                    imagePath: getplatformimgurL(link.platform, link.profileImage),
                                    title: link.platform == 'x'
                                        ? "Please Subscribe to X"
                                        : "Connect ${link.platform[0].toUpperCase()}${link.platform.substring(1)}",
                                    subtitle: getPlatformSubtitle(link.platform),
                                    onConfirmButtonPressed: () {
                                      handlePlatformOnTap(link.platform);
                                    },
                                    confirmButtonText: 'Connect',
                                    isLoading: isPlatformLoading(link.platform, cocialConnectState),
                                  ),
                                );
                              },
                            );
                          });
                    } else {
                      //
                      // If already connected, directly handle the tap without showing dialog
                      showDialog(
                          context: c,
                          barrierDismissible: isPlatformLoading(link.platform, state),
                          builder: (context) {
                            return BlocBuilder<SocialConnecBloc, SocialConnectState>(
                              builder: (context, discounted) {
                                return IgnorePointer(
                                  ignoring: isPlatformLoading(link.platform, discounted),
                                  child: CustomAlertDialog(
                                    imagePath: getplatformimgurL(link.platform, link.profileImage),
                                    title: "Disconnect ${link.platform[0].toUpperCase()}${link.platform.substring(1)}",
                                    subtitle: getPlatformSubtitleForDisconnect(link.platform),
                                    onConfirmButtonPressed: () {
                                      handlePlatformOnTap(link.platform);

                                      (isConnected) ? NavigatorService.goBack() : null;
                                    },
                                    confirmButtonText: 'Disconnect',
                                    isLoading: isPlatformLoading(link.platform, discounted),
                                  ),
                                );
                              },
                            );
                          });
                    }
                  },
                  colors: link.platformStatus == '2'
                      ? Theme.of(context).customColors.socialContainer
                      : getplatformColor(link.platform, link.profileImage) ??
                          Theme.of(context).customColors.socialContainer,
                  isConnected: socialPlatformsStatus.value[link.platform.toUpperCase()] ?? false,
                  isDisabled: isPlatformLoading(link.platform, state),
                  isLoading: isPlatformLoading(link.platform, state),
                  comingsoon: link.platformStatus == '2');

              // Add to appropriate list based on comingsoon status
              if (link.platformStatus == '2') {
                comingSoonPlatforms.add(platform);
              } else {
                regularPlatforms.add(platform);
              }
            }

            // Add regular platforms first, then coming soon platforms
            socialMediaPlatforms.addAll(regularPlatforms);
            socialMediaPlatforms.addAll(comingSoonPlatforms);
          }
          bool isSkipConditionCheck = socialMediaPlatforms.any((platform) => platform.isConnected);

          return PopScope(
            onPopInvoked: (didPop) {
              widget.oncallBack!();
            },
            child: Column(
              children: [
                buildSizedBoxH(8.h),
                widget.stackonScreen == true
                    ? _buildSocialAppBar()
                    : _buildSocialConnectBar(context, isSkipConditionCheck),
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        buildSizedBoxH(8.0),
                        // _buildSocialConnectGrideView(socialMediaPlatforms),
                        _buildSocialConnectListView(socialMediaPlatforms),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildSocialAppBar() {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            NavigatorService.goBack();
            widget.oncallBack!();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          "Social Connect",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
      // actions: [
      //   ValueListenableBuilder<String>(
      //     valueListenable: profileImageNotifier,
      //     builder: (context, imagePath, child) {
      //       return Container(
      //         height: 30.h,
      //         width: 30.w,
      //         padding: (imagePath == AssetConstants.pngUser) ? EdgeInsets.all(8) : EdgeInsets.zero,
      //         child: CustomImageView(
      //           radius: (imagePath == AssetConstants.pngUser) ? null : BorderRadius.circular(100.0.r),
      //           fit: (imagePath == AssetConstants.pngUser) ? BoxFit.contain : BoxFit.cover,
      //           imagePath: imagePath,
      //           alignment: ((imagePath == AssetConstants.pngUser) && imagePath.isEmpty) ? Alignment.center : null,
      //         ),
      //       );
      //     },
      //   ),
      // ],
    );
  }

  Widget _buildSocialConnectBar(BuildContext context, bool isSkipConditionCheck) {
    return CustomAppbar(
      height: 18.h,
      leading: [
        Text(
          "Social Connect",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
      actions: [
        InkWell(
          onTap: () {
            NavigatorService.pushAndRemoveUntil(AppRoutes.bottomNavBar);
            Prefobj.preferences?.put(Prefkeys.SKIPSOCIALCONNECT, true);
          },
          child: Text(
            isSkipConditionCheck ? 'Next' : 'Skip',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
          ),
        ),
      ],
    );
  }

  Widget _buildSocialConnectListView(List<SocialPlatform> socialMediaPlatforms) {
    return ListView.builder(
      physics: const ScrollPhysics(),
      shrinkWrap: true,
      itemCount: socialMediaPlatforms.length,
      padding: EdgeInsets.zero,
      itemBuilder: (context, index) {
        final platform = socialMediaPlatforms[index];

        return IgnorePointer(
          ignoring: platform.isLoading,
          child: AbsorbPointer(
            absorbing: platform.isLoading,
            child: Stack(
              children: [
                _buildSocialCard(platform),
                platform.comingsoon
                    ? Align(
                        alignment: Alignment.centerRight,
                        child: Padding(
                          padding: EdgeInsets.only(right: 28.w, top: 28.h),
                          child: Opacity(
                            opacity: 0.5,
                            child: Text(
                              "Coming Soon",
                              style: Theme.of(context)
                                  .textTheme
                                  .titleLarge
                                  ?.copyWith(fontWeight: FontWeight.w600, fontSize: 16.sp),
                            ),
                          ),
                        ),
                      )
                    : SizedBox.shrink(),
                platform.comingsoon
                    ? SizedBox.shrink()
                    : Align(
                        alignment: Alignment.topRight,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Container(
                              margin: EdgeInsets.only(right: 10.w, top: 0.h),
                              padding: EdgeInsets.symmetric(horizontal: 1.w, vertical: 1.h),
                              // color: Colors.red,
                              child: InkWell(
                                onTap: platform.onTap != null ? () => platform.onTap!(context) : null,
                                child: Align(
                                  alignment: Alignment.topRight,
                                  child: Container(
                                    // margin: EdgeInsets.symmetric(horizontal: 10.w, vertical: 0.h),
                                    width: 17.w,
                                    height: 17.h,
                                    padding: EdgeInsets.symmetric(horizontal: 1.w, vertical: 1.h),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(100.r),
                                      color: Theme.of(context).primaryColor,
                                      border: Border.all(
                                        width: 1.2.w,
                                        color: Theme.of(context).customColors.white,
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Color(0xff000000).withOpacity(0.25),
                                          blurRadius: 6,
                                          spreadRadius: 0,
                                          offset: const Offset(0, 0),
                                        ),
                                      ],
                                    ),
                                    child: CustomImageView(
                                        margin: EdgeInsets.all(platform.isConnected ? 1.5 : 0),
                                        imagePath: platform.isConnected
                                            ? Assets.images.icons.social.svgConnectSM.path
                                            : Assets.images.icons.social.svgDisconnectSM.path),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSocialCard(SocialPlatform platform) {
    return Container(
      height: 70.h,
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
        color: Theme.of(context).customColors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0xff063336).withOpacity(0.1),
            blurRadius: 16,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      margin: EdgeInsets.only(left: 14.w, right: 14.w, bottom: 17.h, top: 4.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Stack(
                  children: [
                    Container(
                      width: 34.w,
                      padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
                      decoration: BoxDecoration(
                        color: platform.colors,
                        boxShadow: [
                          BoxShadow(
                            color: Color(0xff000000).withOpacity(0.25),
                            blurRadius: 6,
                            spreadRadius: 0,
                            offset: const Offset(4, 0),
                          ),
                        ],
                      ),
                    ),
                    Align(
                      alignment: Alignment.center,
                      child: Container(
                        margin: EdgeInsets.only(left: 10.w),
                        width: 46.w,
                        height: 46.h,
                        padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(100.r),
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color: Color(0xff000000).withOpacity(0.25),
                              blurRadius: 6,
                              spreadRadius: 0,
                              offset: const Offset(4, 0),
                            ),
                          ],
                        ),
                        child: Opacity(
                          opacity: platform.comingsoon ? 0.5 : 1,
                          child: CustomImageView(
                            imagePath: platform.icon,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                Expanded(
                  child: Opacity(
                      opacity: platform.comingsoon ? 0.5 : 1, child: _buildSocialConnectTitle(context, platform.title)),
                ),
              ],
            ),
          ),
          buildSizedBoxW(10.w),
          platform.isLoading
              ? LoadingAnimationWidget(
                  height: 50.h,
                  width: 50.w,
                )
              :

              // if (platform.isLoading)

              platform.isConnected
                  ? Container(
                      margin: EdgeInsets.only(right: 12.w),
                      width: 38.w,
                      height: 38.h,
                      padding: EdgeInsets.symmetric(horizontal: 1.5.w, vertical: 1.5.h),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(100.r),
                        border: Border.all(
                          width: 2.w,
                          color: Theme.of(context).primaryColor.withOpacity(0.2),
                        ),
                        color: Theme.of(context).customColors.white,
                      ),
                      child: Container(
                          decoration: BoxDecoration(borderRadius: BorderRadius.circular(12.r)),
                          child: platform.imageUrl == null || platform.imageUrl.isEmpty
                              ? CustomImageView(
                                  radius: BorderRadius.circular(100.r),
                                  imagePath: AssetConstants.pngPlaceholder,
                                  fit: BoxFit.cover,
                                )
                              : ClipRRect(
                                  borderRadius: BorderRadius.circular(100.r),
                                  child: Image.network(
                                    platform.imageUrl.toString(),
                                    fit: BoxFit.cover,
                                  ),
                                )),
                    )
                  : SizedBox.shrink()
        ],
      ),
    );
  }

  // ignore: unused_element
  Widget _buildSocialConnectContainer({
    required BuildContext context,
    String? imagePath,
    String? profileImage,
    String? text,
    Function()? onPressed,
    required bool isConnected,
    required bool isDisabled,
    required bool isLoading,
    bool? comingsoon,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.socialContainer,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Stack(
        children: [
          isConnected
              ? Positioned(
                  top: 12.h,
                  right: 12.w,
                  child: CustomImageView(
                    height: 25.h,
                    width: 25.w,
                    alignment: Alignment.topRight,
                    imagePath: imagePath,
                  ),
                )
              : SizedBox.shrink(),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: EdgeInsets.only(left: 18.w, top: 18.h),
                width: 46.w,
                height: 46.h,
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.r),
                  color: Colors.white,
                ),
                child: isConnected
                    ? Container(
                        decoration: BoxDecoration(borderRadius: BorderRadius.circular(12.r)),
                        child: profileImage == null || profileImage.isEmpty
                            ? CustomImageView(
                                radius: BorderRadius.circular(6.r),
                                imagePath: AssetConstants.pngUserReomve,
                                fit: BoxFit.cover,
                              )
                            : ClipRRect(
                                borderRadius: BorderRadius.circular(6.r),
                                child: Image.network(
                                  profileImage.toString(),
                                  fit: BoxFit.cover,
                                ),
                              ))
                    : CustomImageView(imagePath: imagePath),
              ),
              Spacer(),
              _buildSocialConnectTitle(context, text),
              Spacer(),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                ),
                child: CustomElevatedButton(
                    margin: EdgeInsets.only(bottom: 14.h),
                    onPressed: onPressed,
                    isLoading: isLoading,
                    isDisabled: comingsoon! ? true : isDisabled,
                    color: isConnected ? Colors.white : Theme.of(context).primaryColor,
                    buttonStyle: ButtonStyle(
                      shape: WidgetStatePropertyAll(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.0.r),
                        ),
                      ),
                      backgroundColor: WidgetStatePropertyAll(
                        isConnected ? Theme.of(context).primaryColor : Theme.of(context).customColors.socialButton,
                      ),
                    ),
                    decoration: const BoxDecoration(
                        boxShadow: [BoxShadow(blurRadius: 5, color: Colors.black12, offset: Offset(0, 0))]),
                    text: comingsoon
                        ? "Coming Soon"
                        : isConnected
                            ? "Connected"
                            : "Connect",
                    buttonTextStyle: Theme.of(context).textTheme.labelLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                        fontSize: comingsoon ? 14.sp : 16.0.sp,
                        color: isConnected ? Colors.white : Theme.of(context).primaryColor),
                    height: 40.h),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSocialConnectTitle(BuildContext context, String? text) {
    return Padding(
      padding: EdgeInsets.only(left: 10.w),
      child: Text(
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
        text ?? "",
        textAlign: TextAlign.start,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600, fontSize: 16.sp),
      ),
    );
  }

  Future<void> handlePlatformOnTap(String platform) async {
    switch (platform.toLowerCase()) {
      case 'vimeo':
        final isConnected = Prefobj.preferences?.get(Prefkeys.VIMEO) == true;
        context.read<SocialConnecBloc>().add(isConnected
            ? VimeoDisconnectApiEvent()
            : VimeoconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
        setState(() {});
        break;

      case 'instagram':
        final isConnected = Prefobj.preferences?.get(Prefkeys.INSTAGRAM) == true;
        context.read<SocialConnecBloc>().add(isConnected
            ? InstagramDisconnectApiEvent()
            : InstagramconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
        break;

      case 'linkedin':
        final isConnected = Prefobj.preferences?.get(Prefkeys.LINKEDIN) == true;
        context
            .read<SocialConnecBloc>()
            .add(isConnected ? LinkdinDisconnectApiEvent() : LinkdinconnectApiEvent(context: context));

        break;

      case 'pinterest':
        final isConnected = Prefobj.preferences?.get(Prefkeys.PINTEREST) == true;
        context.read<SocialConnecBloc>().add(isConnected
            ? PintrestDisconnectApiEvent()
            : PinterstconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
        break;

      case 'reddit':
        final isConnected = Prefobj.preferences?.get(Prefkeys.REDDIT) == true;
        context.read<SocialConnecBloc>().add(isConnected
            ? RedditDisconnectApiEvent()
            : RedditconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
        break;

      case 'tumblr':
        final isConnected = Prefobj.preferences?.get(Prefkeys.TUMBLR) == true;
        context.read<SocialConnecBloc>().add(isConnected
            ? TumblrDisconnectApiEvent()
            : SocialconnectTumblrApiEvent(
                context: context,
                platForm: 'Tumblr',
              ));

        break;

      case 'thread':
        final isConnected = Prefobj.preferences?.get(Prefkeys.THREAD) == true;
        context.read<SocialConnecBloc>().add(isConnected
            ? ThreadDisconnectApiEvent()
            : ThreadconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
        break;

      case 'facebook':
        final isConnected = Prefobj.preferences?.get(Prefkeys.FACEBOOK) == true ? true : false;
        context.read<SocialConnecBloc>().add(isConnected
            ? FacebookDisconnectApiEvent()
            : FacebookconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
        break;
      case 'tiktok':
        final isConnected = Prefobj.preferences?.get(Prefkeys.TIKTOK) == true;
        context.read<SocialConnecBloc>().add(isConnected
            ? TiktokDisconnectApiEvent()
            : TiktokconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
        break;

      case 'youtube':
        final isConnected = Prefobj.preferences?.get(Prefkeys.YOUTUBE) == true;
        context.read<SocialConnecBloc>().add(isConnected
            ? YoutubeDisconnectApiEvent()
            : YoutubeconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
        break;
      case 'x':
        NavigatorService.goBack();
        const phoneNumber = "9023371032";

        final Uri launchUri = Uri(
          scheme: 'tel',
          path: phoneNumber,
        );
        await launchUrl(launchUri);
        // final isConnected = Prefobj.preferences?.get(Prefkeys.X) == true;
        // context
        //     .read<SocialConnecBloc>()
        //     .add(isConnected ? XDisconnectApiEvent() : XconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
        break;
      case 'blusky':
        final isConnected = Prefobj.preferences?.get(Prefkeys.YOUTUBE) == true;
        context.read<SocialConnecBloc>().add(isConnected
            ? YoutubeDisconnectApiEvent()
            : YoutubeconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
        break;
      case 'telegram':
        final isConnected = Prefobj.preferences?.get(Prefkeys.TELEGRAM) == true;
        if (!isConnected) {
          NavigatorService.goBack();
          // context.read<SocialConnecBloc>().add(ClearTelegramSendCodeModelEvent());

          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            useSafeArea: true,
            backgroundColor: Colors.transparent,
            builder: (BuildContext context) {
              return TelegramConnectBottomSheet();
            },
          );
        } else {
          context.read<SocialConnecBloc>().add(TelegramDisconnectApiEvent());
        }

        // context.read<SocialConnecBloc>().add(isConnected
        //     ? YoutubeDisconnectApiEvent()
        //     : YoutubeconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
        break;
      case 'mastodon':
        final isConnected = Prefobj.preferences?.get(Prefkeys.MASTODON) == true;
        context.read<SocialConnecBloc>().add(isConnected
            ? MastodonDisconnectApiEvent()
            : MastodonconnectApiEvent(context: context, didLaunchUrl: _didLaunchUrl));
        break;

      default:
        break;
    }
  }

  String? getPlatformImage(String platform, String? profileImage) {
    if (profileImage != null && profileImage.isNotEmpty || profileImage != "") {
      return profileImage;
    }

    switch (platform.toLowerCase()) {
      case 'vimeo':
        return "";
      case 'instagram':
        return '';
      case 'linkedin':
        return '';
      case 'pinterest':
        return '';
      case 'reddit':
        return '';
      case 'tumblr':
        return '';
      case 'thread':
        return '';
      case 'facebook':
        return '';
      case 'tiktok':
        return '';
      case 'youtube':
        return '';
      case 'x':
        return '';
      case 'blusky':
        return '';
      case 'telegram':
        return '';
      case 'mastodon':
        return '';
      default:
        return Assets.images.pngs.flowkar.path;
    }
  }

  String? getplatformimgurL(String platform, String? profileImage) {
    switch (platform.toLowerCase()) {
      case 'vimeo':
        return Assets.images.icons.social.v.path;
      case 'instagram':
        return Assets.images.icons.social.insta.path;
      case 'linkedin':
        return Assets.images.icons.social.linkedin.path;
      case 'pinterest':
        return Assets.images.icons.social.icPintrest.path;
      case 'reddit':
        return Assets.images.icons.social.icReddit.path;
      case 'tumblr':
        return Assets.images.icons.social.icTumblr.path;
      case 'thread':
        return Assets.images.icons.social.icThread.path;
      case 'facebook':
        return Assets.images.icons.social.facebook.path;
      case 'tiktok':
        return Assets.images.icons.social.icTictok.path;
      case 'youtube':
        return Assets.images.icons.social.icYoutube.path;
      case 'x':
        return Assets.images.icons.social.twitter.path;
      case 'blusky':
        return Assets.images.icons.social.svgBluesky.path;
      case 'telegram':
        return Assets.images.icons.social.svgTelegram.path;
      case 'mastodon':
        return Assets.images.icons.social.svgMastodon.path;
      default:
        return Assets.images.pngs.flowkar.path;
    }
  }

  Color? getplatformColor(String platform, String? profileImage) {
    switch (platform.toLowerCase()) {
      case 'vimeo':
        return Color(0xff86C9EF);
      case 'instagram':
        return Color(0xffDD2A7B);
      case 'linkedin':
        return Color(0xff1275B1);
      case 'pinterest':
        return Color(0xffE60019);
      case 'reddit':
        return Color(0xffFC471E);
      case 'tumblr':
        return Color(0xff36465D);
      case 'thread':
        return Color(0xff000000);
      case 'facebook':
        return Color(0xff0866FF);
      case 'tiktok':
        return Color(0xff000000);
      case 'youtube':
        return Color(0xffFF0302);
      case 'x':
        return Color(0xff000000);
      case 'blusky':
        return Color(0xff0085FF);
      case 'telegram':
        return Color(0xff0088cc);
      case 'mastodon':
        return Color(0xff3088D4);
      default:
        return Theme.of(context).customColors.socialContainer;
    }
  }

  bool isPlatformLoading(
    String platform,
    SocialConnectState state,
  ) {
    switch (platform.toLowerCase()) {
      case 'vimeo':
        return state.isVimeoConnectLoading;
      case 'instagram':
        return state.isInstagramConnectLoading;
      case 'linkedin':
        return state.isLinkedInConnectLoading;
      case 'pinterest':
        return state.isPintrestConnectLoading;
      case 'reddit':
        return state.isReditConnectLoading;
      case 'tumblr':
        return state.isTumblrLoading;
      case 'thread':
        return state.isThreadConnectLoading;
      case 'facebook':
        return state.isFacebookConnectLoading;
      case 'tiktok':
        return state.isTiktokConnectLoading;
      case 'youtube':
        return state.isYoutubeConnectLoading;
      case 'x':
        return state.isXConnectLoading;
      case 'blusky':
        return state.isYoutubeConnectLoading;
      case 'telegram':
        return state.isTelegramSignInLoading;
      case 'mastodon':
        return state.isMastodonConnectLoading;
      default:
        return false;
    }
  }
}

class SocialPlatform {
  final String icon;
  final String title;
  final Function(BuildContext context)? onTap;
  final bool isConnected;
  final bool isDisabled;
  final bool isLoading;
  final bool comingsoon;
  final String imageUrl;
  final Color colors;

  SocialPlatform({
    required this.icon,
    required this.title,
    this.onTap,
    required this.isConnected,
    required this.isDisabled,
    required this.isLoading,
    this.comingsoon = false,
    required this.imageUrl,
    required this.colors,
  });
}
