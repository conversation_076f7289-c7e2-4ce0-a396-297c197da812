import 'package:flowkar/core/utils/exports.dart';
import 'package:country_picker/country_picker.dart';
import 'package:flowkar/features/social_connect/bloc/social_connec_bloc.dart';

class TelegramConnectBottomSheet extends StatefulWidget {
  const TelegramConnectBottomSheet({super.key});

  @override
  State<TelegramConnectBottomSheet> createState() => _TelegramConnectBottomSheetState();
}

class _TelegramConnectBottomSheetState extends State<TelegramConnectBottomSheet> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _otpController = TextEditingController();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // Local state variables
  // bool _isOtpSent = false;
  // bool _showPhoneSection = true;

  @override
  void initState() {
    context.read<SocialConnecBloc>().add(ShowPhoneSectionEvent(isPhonesection: false));

    super.initState();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final double screenHeight = MediaQuery.of(context).size.height;

    return AnimatedPadding(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOut,
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: screenHeight * 0.82,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: EdgeInsets.symmetric(vertical: 8),
              height: 4,
              width: 40,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: _buildBody(keyboardHeight),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.only(top: 8.h, bottom: 16.h, left: 16.w, right: 16.w),
      child: Row(
        children: [
          // IconButton(
          //   onPressed: () => Navigator.of(context).pop(),
          //   icon: Icon(Icons.arrow_back, color: Colors.black87),
          // ),
          InkWell(
            onTap: () {
              FocusScope.of(context).unfocus();
              NavigatorService.goBack();
            },
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: CustomImageView(
                imagePath: Assets.images.svg.authentication.icBackArrow.path,
                height: 16.h,
              ),
            ),
          ),

          // const Spacer(),
          // buildSizedBoxW(20.w),
          // Text("Telegram",
          //     style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp)),
          // const Spacer(),
        ],
      ),
    );
  }

  Widget _buildBody(double keyboardHeight) {
    return BlocListener<SocialConnecBloc, SocialConnectState>(
      listener: (context, state) {
        // // Listen for OTP sent success
        // if (state.telegramSendCodeModel != null && state.telegramSendCodeModel?.status == true) {

        // }
      },
      child: BlocBuilder<SocialConnecBloc, SocialConnectState>(
        builder: (context, state) {
          return Form(
            key: formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // SizedBox(height: 8.h),
                _buildLogo(),
                SizedBox(height: 32.h),
                _buildTitle(),
                SizedBox(height: 16.h),
                _buildDescription(state),

                // Mobile number display with Edit button
                _buildMobileNumberSection(),

                SizedBox(height: 40.h),

                // Show country selector only when showing phone section
                if (!state.isPhonesection) ...[
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      'Country',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                    ),
                  ),
                  buildSizedBoxH(8.0),
                  GestureDetector(
                    onTap: () {
                      _selectCountry(context);
                    },
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          Text(
                            state.selectedCountry.flagEmoji,
                            style: TextStyle(fontSize: 20),
                          ),
                          buildSizedBoxW(12.0),
                          Expanded(
                            child: Text(
                              state.selectedCountry.name,
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: Colors.black87,
                              ),
                            ),
                          ),
                          Icon(Icons.keyboard_arrow_down_rounded, color: Colors.black54),
                        ],
                      ),
                    ),
                  ),
                  buildSizedBoxH(24.0),
                ],

                // Show OTP section or Phone section based on local state
                state.isPhonesection ? _buildOtpSection() : _buildPhoneSection(keyboardHeight),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildMobileNumberSection() {
    return BlocBuilder<SocialConnecBloc, SocialConnectState>(
      builder: (context, state) {
        // Only show mobile number section when OTP has been sent
        if (state.isPhonesection) {
          return Column(
            children: [
              SizedBox(height: 10.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '+${state.selectedCountry.phoneCode} ${_phoneController.text}',
                    style: TextStyle(fontSize: 16.sp, color: Colors.black54, height: 1.4),
                    textAlign: TextAlign.center,
                  ),
                  if (state.isPhonesection) ...[
                    SizedBox(width: 12.w),
                    GestureDetector(
                      onTap: () {
                        _editMobileNumber(context);
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                        decoration: BoxDecoration(
                          // ignore: deprecated_member_use
                          color: Color(0xFF2AABEE).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20.r),
                          border: Border.all(color: Color(0xFF2AABEE), width: 1),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.edit,
                              size: 16.sp,
                              color: Color(0xFF2AABEE),
                            ),
                            SizedBox(width: 4.w),
                            Text(
                              'Edit',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Color(0xFF2AABEE),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          );
        }
        return SizedBox.shrink();
      },
    );
  }

  void _editMobileNumber(BuildContext context) {
    setState(() {
      // Clear OTP controller
      _otpController.clear();

      // Reset the UI state flags
      // _isOtpSent = false;
      // _showPhoneSection = true;
      context.read<SocialConnecBloc>().add(ClearTelegramSendCodeModelEvent());
      context.read<SocialConnecBloc>().add(ShowPhoneSectionEvent(isPhonesection: false));
    });

    // Add event to clear telegram send code model
  }

  Widget _buildLogo() {
    return Container(
      width: 120.w,
      height: 120.w,
      decoration: const BoxDecoration(
        color: Color(0xFF2AABEE),
        shape: BoxShape.circle,
      ),
      child: CustomImageView(
        imagePath: Assets.images.icons.social.svgTelegram.path,
        color: Colors.white,
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      'Telegram',
      style: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.w600, color: Colors.black87),
    );
  }

  Widget _buildDescription(SocialConnectState state) {
    return Text(
      state.isPhonesection
          ? 'We have sent you a code on Telegram. Please check your Telegram app in your device.'
          : 'Please confirm your country code and enter your phone number.',
      textAlign: TextAlign.center,
      style: TextStyle(fontSize: 16.sp, color: Colors.black54, height: 1.4),
    );
  }

  Widget _buildPhoneSection(double keyboardHeight) {
    return BlocBuilder<SocialConnecBloc, SocialConnectState>(
      builder: (context, state) {
        final int requiredLength = state.getRequiredLength();
        return Column(
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Mobile Number',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.black54,
                ),
              ),
            ),
            buildSizedBoxH(8.0),
            _buildPhoneInput(context, requiredLength),
            SizedBox(height: 60.h),
            _buildSendOTPButton(state),
          ],
        );
      },
    );
  }

  Widget _buildSendOTPButton(SocialConnectState state) {
    return CustomElevatedButton(
      width: double.infinity,
      height: 56.w,
      isLoading: state.isTelegramSendCodeLoading,
      buttonStyle: ElevatedButton.styleFrom(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0.r),
        ),
        backgroundColor: state.isTelegramSendCodeLoading ? Color.fromARGB(255, 126, 206, 249) : Color(0xFF2AABEE),
        padding: EdgeInsets.zero,
        textStyle: Theme.of(context).textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w700),
      ),
      text: "Send OTP",
      onPressed: () async {
        if (formKey.currentState?.validate() ?? false) {
          FocusScope.of(context).unfocus();
          context.read<SocialConnecBloc>().add(TelegramconnectSendCodeApiEvent(
              context: context,
              mobileNumber: "+${state.selectedCountry.phoneCode}${_phoneController.text}",
              isresendOtp: false));
          setState(() {
            // _isOtpSent = true;
            // _showPhoneSection = false;
          });
        }
      },
    );
  }

  Widget _buildOtpSection() {
    return BlocBuilder<SocialConnecBloc, SocialConnectState>(
      builder: (context, state) {
        return Column(
          children: [
            // SizedBox(height: 10.h),

            // OTP Input Field
            Pinput(
              controller: _otpController,
              length: 5,
              defaultPinTheme: _pinTheme(Colors.grey[300]!, Colors.black87),
              focusedPinTheme: _pinTheme(const Color(0xFF2AABEE), Colors.black87),
              submittedPinTheme: _pinTheme(const Color(0xFF2AABEE), Colors.white, filled: true),
              onCompleted: (pin) {
                // Auto verify when OTP is complete
                _verifyOTP(state, pin);
              },
              onChanged: (value) {
                setState(() {
                  // This will trigger rebuild to enable/disable verify button
                });
              },
            ),
            SizedBox(height: 10.h),
            _buildResendSection(state),
            SizedBox(height: 60.h),

            // Verify OTP Button
            _buildVerifyOTPButton(state),
            // SizedBox(height: 20.h),
          ],
        );
      },
    );
  }

  Widget _buildVerifyOTPButton(SocialConnectState state) {
    bool isOtpEmpty = _otpController.text.trim().isEmpty || _otpController.text.length != 5;

    return Padding(
      padding: EdgeInsets.only(top: 20.h),
      child: CustomElevatedButton(
        width: double.infinity,
        height: 56.w,
        isLoading: state.isTelegramSignInLoading,
        buttonStyle: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0.r),
          ),
          backgroundColor: isOtpEmpty ? Color.fromARGB(255, 126, 206, 249) : Color(0xFF2AABEE),
          padding: EdgeInsets.zero,
          textStyle: Theme.of(context).textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w700),
        ),
        text: "Verify OTP",
        onPressed: isOtpEmpty
            ? null
            : () async {
                _verifyOTP(state, _otpController.text);
              },
      ),
    );
  }

  Widget _buildResendSection(SocialConnectState state) {
    if (state.isResendEnabled) {
      return TextButton(
        onPressed: () {
          // Resend OTP
          context.read<SocialConnecBloc>().add(TelegramconnectSendCodeApiEvent(
              context: context,
              mobileNumber: "+${state.selectedCountry.phoneCode}${_phoneController.text}",
              isresendOtp: true));
        },
        child: Text(
          'Resend Code',
          style: TextStyle(fontSize: 16.sp, color: const Color(0xFF2AABEE)),
        ),
      );
    } else {
      return Text(
        'Resend code in ${state.remainingTime}s',
        style: TextStyle(fontSize: 16.sp, color: Colors.grey[600]),
      );
    }
  }

  void _verifyOTP(SocialConnectState state, String otp) {
    if (otp.length == 5) {
      // Call the telegram sign in API
      context.read<SocialConnecBloc>().add(TelegramconnectSignInApiEvent(
          context: context,
          mobileNumber: "+${state.selectedCountry.phoneCode}${_phoneController.text}",
          code: otp,
          phoneCodeHash: state.telegramSendCodeModel?.phoneCodeHash ?? '',
          didLaunchUrl: false));
    }
  }

  void _selectCountry(BuildContext context) {
    showCountryPicker(
      context: context,
      showPhoneCode: true,
      countryListTheme: _buildCountryListTheme(context),
      useSafeArea: true,
      searchAutofocus: false,
      moveAlongWithKeyboard: true,
      onSelect: (Country country) {
        context.read<SocialConnecBloc>().add(CountrySelected(country));
      },
    );
  }

  CountryListThemeData _buildCountryListTheme(BuildContext context) {
    return CountryListThemeData(
      borderRadius: BorderRadius.circular(20.r),
      bottomSheetHeight: MediaQuery.of(context).size.height * 0.8,
      textStyle: Theme.of(
        context,
      ).textTheme.bodyLarge?.copyWith(fontSize: 16.sp, color: Theme.of(context).customColors.black),
      searchTextStyle: Theme.of(
        context,
      ).textTheme.bodyLarge?.copyWith(fontSize: 16.sp, color: Theme.of(context).customColors.black),
      inputDecoration: InputDecoration(
        hintText: "Search Country",
        hintStyle: Theme.of(context)
            .textTheme
            .bodyMedium
            ?.copyWith(fontSize: 16.5.sp, color: Theme.of(context).customColors.black.withValues(alpha: 0.5)),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(14.r),
          borderSide: BorderSide(color: Theme.of(context).customColors.greydivider),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(14.r),
          borderSide: BorderSide(color: Theme.of(context).customColors.greydivider),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(10.r)),
          borderSide: BorderSide(color: Theme.of(context).customColors.greydivider, width: 1.5),
        ),
        prefixIcon: CustomImageView(imagePath: AssetConstants.icSearch, margin: EdgeInsets.all(12.0)),
      ),
    );
  }

  Widget _buildPhoneInput(BuildContext context, int requiredLength) {
    return BlocBuilder<SocialConnecBloc, SocialConnectState>(
      builder: (context, state) {
        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: FlowkarTextFormField(
                borderDecoration: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(10.r)),
                  borderSide: BorderSide(color: Theme.of(context).customColors.greydivider, width: 1.5),
                ),
                prefixIcon: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8.0.w),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      buildSizedBoxW(5.w),
                      Text(
                        state.selectedCountry.flagEmoji,
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontSize: 16.sp, color: Theme.of(context).customColors.black, fontWeight: FontWeight.bold),
                      ),
                      Text(
                        " +${state.selectedCountry.phoneCode}",
                        textAlign: TextAlign.center,
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium
                            ?.copyWith(fontSize: 16.sp, color: Colors.black87, fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                ),
                filled: true,
                contentPadding: EdgeInsets.all(15),
                context: context,
                textInputType: TextInputType.number,
                textInputAction: TextInputAction.done,
                hintText: "Enter Mobile Number",
                hintStyle: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.copyWith(fontSize: 16.5.sp, color: Theme.of(context).customColors.black.withValues(alpha: 0.5)),
                controller: _phoneController,
                inputFormatters: [FilteringTextInputFormatter.deny(RegExp(r'[^\d+]'))],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return "enter mobile number";
                  }
                  if (value.length != requiredLength) {
                    return '$requiredLength digits required';
                  }
                  return null;
                },
              ),
            ),
          ],
        );
      },
    );
  }

  PinTheme _pinTheme(Color borderColor, Color textColor, {bool filled = false}) {
    return PinTheme(
      width: 50.w,
      height: 60.h,
      textStyle: TextStyle(fontSize: 20.sp, color: textColor, fontWeight: FontWeight.w600),
      decoration: BoxDecoration(
        color: filled ? borderColor : null,
        border: Border.all(color: borderColor, width: filled ? 0 : 2),
        borderRadius: BorderRadius.circular(8.r),
      ),
    );
  }
}
