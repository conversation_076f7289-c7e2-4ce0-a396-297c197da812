import 'dart:convert';
import 'dart:developer';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/social_connect/bloc/social_connec_bloc.dart';
import 'package:webview_flutter/webview_flutter.dart';

class SocialConnectWebView extends StatefulWidget {
  final String initialUrl;
  final String socialPlatform;

  static Widget builder(BuildContext context) {
    final List args = ModalRoute.of(context)?.settings.arguments as List;
    return SocialConnectWebView(
      initialUrl: args[0],
      socialPlatform: args[1],
    );
  }

  const SocialConnectWebView({
    super.key,
    required this.initialUrl,
    required this.socialPlatform,
  });

  @override
  State<SocialConnectWebView> createState() => _SocialConnectWebViewState();
}

class _SocialConnectWebViewState extends State<SocialConnectWebView> {
  late final WebViewController controller;

  @override
  void initState() {
    controller = WebViewController()
      ..clearCache()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (String url) async {
            try {
              dynamic htmlContent = await controller.runJavaScriptReturningResult(
                  'document.querySelector("pre") ? document.querySelector("pre").textContent : "{}";');

              String content = htmlContent.toString();

              content = content.startsWith('"') && content.endsWith('"')
                  ? content.substring(1, content.length - 1).replaceAll(r'\"', '"')
                  : content;

              log('HTML Content: $content');

              Map<String, dynamic> jsonObject = jsonDecode(content);

              bool status = jsonObject['status'] ?? false;

              String? oauthverifier = jsonObject['token_verifier'];

              log('Status: $status');

              switch (widget.socialPlatform.toUpperCase()) {
                case 'TUMBLR':
                  context.read<SocialConnecBloc>().add(ConnectTumblrApiEvent(
                      oauthtoken: Prefobj.preferences!.get(Prefkeys.OAUTH_TUMBLER),
                      oauthtokensecret: Prefobj.preferences!.get(Prefkeys.OAUTH_SECRET_TUMBLER),
                      oauthverifier: oauthverifier ?? ''));
                  break;

                default:
                  break;
              }

              if (status) {
                NavigatorService.goBack();
                // NavigatorService.pushAndRemoveUntil(AppRoutes.bottomNavBar);
              }
            } catch (e) {
              log('Error parsing JSON: $e');
            }
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.initialUrl));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
        statusBarColor: Color(0xff001935),
        statusBarIconBrightness: Brightness.light,
      ),
      child: WillPopScope(
        onWillPop: () async {
          // NavigatorService.pushAndRemoveUntil(AppRoutes.bottomNavBar);
          NavigatorService.goBack();

          return true;
        },
        child: Scaffold(
          body: SafeArea(
            child: WebViewWidget(
              controller: controller,
              gestureRecognizers: {
                Factory(() => EagerGestureRecognizer()),
              },
            ),
          ),
        ),
      ),
    );
  }
}
