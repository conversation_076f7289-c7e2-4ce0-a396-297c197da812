import 'package:flowkar/core/utils/exports.dart';

class SocialConnectShimmer extends StatefulWidget {
  final bool useShimmerAppBar;
  const SocialConnectShimmer({super.key, required this.useShimmerAppBar});

  @override
  // ignore: library_private_types_in_public_api
  _SocialConnectShimmerState createState() => _SocialConnectShimmerState();
}

class _SocialConnectShimmerState extends State<SocialConnectShimmer> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themestate) {
        return Scaffold(
          appBar: widget.useShimmerAppBar ? _buildSocialAppBar(context) : _buildSocialConnectBar(context),
          body: _buildShimmerEffect(themestate),
        );
      },
    );
  }
}

PreferredSizeWidget _buildSocialAppBar(BuildContext context) {
  return CustomAppbar(
    hasLeadingIcon: true,
    height: 18.h,
    leading: [
      InkWell(
        onTap: () {
          NavigatorService.goBack();
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: CustomImageView(
            imagePath: Assets.images.svg.authentication.icBackArrow.path,
            height: 16.h,
          ),
        ),
      ),
      buildSizedBoxW(20.w),
      Text(
        "Social Connect",
        style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
      ),
    ],
  );
}

PreferredSizeWidget _buildSocialConnectBar(
  BuildContext context,
) {
  return CustomAppbar(
    title: 'Connect Account',
    hasLeadingIcon: false,
    actions: [
      InkWell(
        onTap: () {
          NavigatorService.pushAndRemoveUntil(AppRoutes.bottomNavBar);
          Prefobj.preferences?.put(Prefkeys.SKIPSOCIALCONNECT, true);
        },
        child: Text(
          'Skip',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
      ),
    ],
  );
}

Widget _buildShimmerEffect(ThemeState themestate) {
  return Shimmer.fromColors(
    baseColor: themestate.isDarkThemeOn ? Colors.grey[900]! : Colors.grey[300]!,
    highlightColor: themestate.isDarkThemeOn ? Colors.grey[800]! : Colors.grey[100]!,
    child: ListView.builder(
      shrinkWrap: true,
      padding: EdgeInsets.symmetric(vertical: 0.h, horizontal: 8.w),
      itemCount: 10,
      itemBuilder: (context, index) {
        return Container(
          margin: EdgeInsets.only(left: 14.w, right: 14.w, bottom: 17.h, top: 4.h),
          height: 70.h,
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(8.r),
          ),
        );
      },
    ),
  );
}
