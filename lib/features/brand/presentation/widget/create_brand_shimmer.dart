import 'package:flowkar/core/utils/exports.dart';

class CreateBrandScreenShimmer extends StatefulWidget {
  const CreateBrandScreenShimmer({super.key});

  @override
  State<CreateBrandScreenShimmer> createState() => _CreateBrandScreenShimmerState();
}

class _CreateBrandScreenShimmerState extends State<CreateBrandScreenShimmer> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        leading: [
          Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              height: 14.h,
              width: 100.w,
              color: Colors.white,
            ),
          )
        ],
      ),
      body: InkWell(
        focusColor: Colors.transparent,
        onTap: () {
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              children: [
                buildSizedBoxH(20.0),
                Container(
                  width: 135.w,
                  height: 135.h,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                ),
                buildSizedBoxH(40.0),
                Container(
                  height: 50.h,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                ),
                buildSizedBoxH(20.0),
                Container(
                  height: 50.h,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                ),
                buildSizedBoxH(20.0),
                Container(
                  height: 50.h,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                ),
                buildSizedBoxH(75),
                Container(
                  height: 55.h,
                  width: 159.w,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
