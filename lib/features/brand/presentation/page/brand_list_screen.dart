import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/authentication/bloc/auth_bloc.dart';
import 'package:flowkar/features/authentication/model/get_brands_model.dart';
import 'package:flowkar/features/brand/presentation/page/create_brand.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/user_management/bloc/user_management_bloc.dart';
import 'package:flowkar/features/user_management/widget/user_management_screen_shimmer.dart';

class BrandListScreen extends StatefulWidget {
  const BrandListScreen({super.key});

  static Widget builder(BuildContext context) {
    return BrandListScreen();
  }

  @override
  State<BrandListScreen> createState() => _BrandListScreenState();
}

class _BrandListScreenState extends State<BrandListScreen> {
  int brandId = 0;

  @override
  void initState() {
    brandId = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;
    // context.read<UserManagementBloc>().add(GetBrandsAPI());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    brandId = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;
    return Scaffold(
      appBar: _buildBrandListAppBar(context),
      body: BlocListener<ConnectivityBloc, ConnectivityState>(
        listener: (context, connectivityState) {
          if (connectivityState.isReconnected) {
            brandId = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;
            // context.read<UserManagementBloc>().add(GetBrandsAPI());
          }
        },
        child: BlocBuilder<UserManagementBloc, UserManagementState>(
          builder: (context, state) {
            brandId = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;
            return BlocBuilder<ConnectivityBloc, ConnectivityState>(
              builder: (context, connectivityState) {
                brandId = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;
                if (!connectivityState.isConnected && (state.getBrandsModel?.data.isEmpty ?? true)) {
                  return ManageUserShimmer(height: 70.h);
                } else if (state.brandLoading) {
                  return ManageUserShimmer(height: 70.h);
                } else if ((state.getBrandsModel?.data.isEmpty ?? true)) {
                  return ListView(
                    physics: AlwaysScrollableScrollPhysics(),
                    children: [
                      buildSizedBoxH(MediaQuery.of(context).size.height / 6),
                      ExceptionWidget(
                        imagePath: Assets.images.svg.other.svgNodatafound.path,
                        showButton: false,
                        title: Lang.of(context).lbl_data_not_found,
                        subtitle: Lang.of(context).lbl_we_could_not,
                      ),
                    ],
                  );
                } else {
                  brandId = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;
                  final brands = state.getBrandsModel?.data ?? [];
                  final myBrands = brands.where((b) => b.isInvited == false).toList();
                  final invitedBrands = brands.where((b) => b.isInvited == true).toList();
                  return ListView(
                    children: [
                      if (myBrands.isNotEmpty) ...[
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "My Brands",
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ),
                        ),
                        buildSizedBoxH(8),
                        Column(
                          children: List.generate(
                            myBrands.length,
                            (index) {
                              final brand = myBrands[index];
                              return _buildBrandCard(brand, index);
                            },
                          ),
                        ),
                      ],
                      if (invitedBrands.isNotEmpty) ...[
                        buildSizedBoxH(20),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "Invited Brands",
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ),
                        ),
                        buildSizedBoxH(8),
                        Column(
                          children: List.generate(
                            invitedBrands.length,
                            (index) {
                              final brand = invitedBrands[index];
                              return _buildBrandCard(brand, index);
                            },
                          ),
                        ),
                      ],
                    ],
                  );
                  // ListView.separated(
                  //   padding: EdgeInsets.symmetric(horizontal: 20.w),
                  //   itemCount: brands.length,
                  //   itemBuilder: (context, index) {
                  //     return _buildBrandCard(brands[index], index);
                  //   },
                  //   separatorBuilder: (BuildContext context, int index) {
                  //     return buildSizedBoxH(20);
                  //   },
                  // );
                }
              },
            );
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          PersistentNavBarNavigator.pushNewScreen(context, screen: CreateBrandScreen(isNewBrand: true)).then((value) {
            if (value == true) {
              setState(() {});
            }
          });
        },
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(100.r)),
        child: Icon(Icons.add),
      ),
    );
  }

  PreferredSizeWidget _buildBrandListAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            // context.read<UserManagementBloc>().add(GetBrandsAPI());
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          Lang.of(context).lbl_my_brands,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  Widget _buildBrandCard(BrandData brand, int index) {
    return BlocBuilder<UserManagementBloc, UserManagementState>(
      builder: (context, state) {
        return Container(
          height: 70.h,
          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 14.h),
          margin: EdgeInsets.symmetric(horizontal: 23.w, vertical: 6.h),
          decoration: BoxDecoration(
            color: Theme.of(context).customColors.white,
            borderRadius: BorderRadius.circular(10.r),
            boxShadow: [
              BoxShadow(
                color: Color(0XFF063336).withOpacity(0.10),
                offset: Offset(0, 2),
                blurRadius: 8.r,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    Stack(
                      children: [
                        Container(
                          height: 42.h,
                          width: 42.w,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(color: Theme.of(context).customColors.black, width: 0.6.w),
                          ),
                          child: ClipRRect(
                            clipBehavior: Clip.antiAlias,
                            borderRadius: BorderRadius.circular(100.r),
                            child: CustomImageView(
                              height: 42.h,
                              width: 42.w,
                              fit: BoxFit.cover,
                              imagePath: (state.getBrandsModel?.data[index].logo ?? '').isEmpty
                                  ? Assets.images.icons.other.icFlowkar.path
                                  : "${APIConfig.mainbaseURL}${state.getBrandsModel?.data[index].logo}",
                            ),
                          ),
                        ),
                        if (brand.id == brandId)
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: Container(
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.check_circle_rounded,
                                color: Theme.of(context).primaryColor,
                                size: 16,
                              ),
                            ),
                          ),
                      ],
                    ),
                    buildSizedBoxW(12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            state.getBrandsModel?.data[index].name ?? '',
                            overflow: TextOverflow.ellipsis,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontSize: 16.sp,
                                  color: Theme.of(context).customColors.black,
                                  fontWeight: FontWeight.w500,
                                ),
                          ),
                          if (state.getBrandsModel?.data[index].domain != null &&
                              state.getBrandsModel!.data[index].domain.isNotEmpty)
                            Text(
                              state.getBrandsModel?.data[index].domain ?? '',
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontSize: 12.sp,
                                    color: Theme.of(context).customColors.subHeadingcolor,
                                    fontWeight: FontWeight.w400,
                                  ),
                            ),
                        ],
                      ),
                    ),
                    buildSizedBoxW(12),
                  ],
                ),
              ),
              if (state.getBrandsModel?.data[index].isInvited == false)
                IconButton(
                  onPressed: () {
                    showModalBottomSheet(
                      useRootNavigator: true,
                      context: context,
                      builder: (context) {
                        return Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.vertical(
                              top: Radius.circular(40.0.r),
                            ),
                          ),
                          padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 12.w),
                          child: StatefulBuilder(
                            builder: (context, setState) => Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  height: 3.h,
                                  width: 40.w,
                                  decoration: BoxDecoration(
                                    color: Color(0xffE9EBEA),
                                    borderRadius: BorderRadius.circular(100.r),
                                  ),
                                ),
                                buildSizedBoxH(8.h),
                                _buildBrandOptionCard(
                                  icon: Assets.images.svg.homeFeed.svgEdit.path,
                                  name: Lang.current.lbl_edit,
                                  onTap: () {
                                    NavigatorService.goBack();
                                    PersistentNavBarNavigator.pushNewScreen(context,
                                        screen: CreateBrandScreen(
                                            brandId: state.getBrandsModel?.data[index].id, isNewBrand: false));
                                  },
                                ),
                                // buildSizedBoxH(20),
                                _buildBrandOptionCard(
                                  icon: Assets.images.svg.setting.svgDeleteAccount.path,
                                  name: Lang.current.lbl_delete,
                                  onTap: () {
                                    NavigatorService.goBack();
                                    showDeleteBrandPopup(context, state.getBrandsModel?.data[index].id ?? 0, index);
                                  },
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    );
                  },
                  icon: CustomImageView(imagePath: Assets.images.svg.other.svgMoreHoriz.path),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBrandOptionCard({String? icon, String? name, Function()? onTap}) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 6.h),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(30.r),
      ),
      child: ListTile(
        minTileHeight: 60.h,
        leading: CustomImageView(
          height: 20.h,
          width: 20.w,
          imagePath: icon ?? '',
          fit: BoxFit.contain,
        ),
        title: Text(
          name ?? "",
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 16.sp),
        ),
        onTap: onTap,
      ),
    );
  }

  void showDeleteBrandPopup(BuildContext context, int brandId, int index) {
    showDialog(
      context: context,
      builder: (ctx) {
        return BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            return StatefulBuilder(builder: (context, setState) {
              return CustomAlertDialog(
                imagePath: Assets.images.svg.setting.svgDailogDeleteAccount.path,
                title: "Confirm Brand Deletion",
                subtitle: Lang.of(context).msg_delete_brand_subtitle,
                onConfirmButtonPressed: () async {
                  context.read<AuthBloc>().add(DeleteBrandsByIdAPI(context: context, brandId: brandId, index: index));
                },
                confirmButtonText: Lang.of(context).lbl_delete,
                cancelButtonText: Lang.of(context).lbl_Cancel,
                isLoading: state.idDeletebrandbyIdLoading,
              );
            });
          },
        );
      },
    );
  }
}
