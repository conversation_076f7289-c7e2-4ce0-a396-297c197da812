import 'dart:developer';
import 'dart:io';

import 'package:flowkar/core/utils/exports.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flowkar/features/authentication/bloc/auth_bloc.dart';
import 'package:flowkar/features/brand/presentation/widget/create_brand_shimmer.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';

class CreateBrandScreen extends StatefulWidget {
  final int? brandId;
  final bool? isNewBrand;

  const CreateBrandScreen({super.key, this.brandId, this.isNewBrand});
  static Widget builder(BuildContext context) {
    final List args = ModalRoute.of(context)?.settings.arguments as List;

    return CreateBrandScreen(
      brandId: args[0],
      isNewBrand: args[1],
    );
  }

  @override
  State<CreateBrandScreen> createState() => _CreateBrandScreenState();
}

class _CreateBrandScreenState extends State<CreateBrandScreen> {
  File? _selectedImage;
  File? _selectedBrandImage;
  String? _brandImage;
  TextEditingController brandNameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController domainController = TextEditingController();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  String? _imageError;

  bool isStart = true;
  bool isLoading = true;

  Future<void> _pickImage() async {
    final pickedFile = await ImagePicker().pickImage(
      source: ImageSource.gallery,
      requestFullMetadata: true,
    );

    if (pickedFile != null) {
      final isGif = pickedFile.mimeType == 'image/gif' || pickedFile.path.toLowerCase().endsWith('.gif');

      if (isGif) {
        setState(() {
          _imageError = 'GIF images are not allowed';
          _selectedImage = null;
          _brandImage = "";
        });
      } else {
        setState(() {
          _selectedImage = File(pickedFile.path);
          _brandImage = "";
          _imageError = null;
        });
      }
    }
  }
  // Future<void> _pickImage() async {
  //   final pickedFile = await ImagePicker().pickImage(
  //     source: ImageSource.gallery,
  //   );
  //   if (pickedFile != null) {
  //     setState(() {
  //       _selectedImage = File(pickedFile.path);
  //       _brandImage = "";
  //       _imageError = null;
  //     });
  //   }
  // }

  @override
  void initState() {
    super.initState();
    _initializeData();
    isStart = true;

    Logger.lOG("Brand Id ${widget.brandId}");
    if (widget.isNewBrand == false) {
      isLoading = true;
      // context.read<AuthBloc>().add(GetBrandsByBrIDAPI(context: context, brandId: widget.brandId ?? 0));
    } else {
      isLoading = false;
    }
  }

  void _initializeData() {
    _selectedImage = null;
    _selectedBrandImage = null;
    _brandImage = null;
    brandNameController.clear();
    emailController.clear();
    domainController.clear();
  }

  Future<void> _downloadAndSaveImage(String url) async {
    try {
      final response = await Dio().get(
        url,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200) {
        final Directory tempDir = await getTemporaryDirectory();
        final File file = File("${tempDir.path}_image.png");
        await file.writeAsBytes(response.data);

        setState(() {
          _selectedBrandImage = file;
        });
      } else {
        Logger.lOG("Failed to load image, Status Code: ${response.statusCode}");
      }
    } catch (e) {
      Logger.lOG("Error downloading image: $e");
    }
  }

  @override
  void dispose() {
    isStart = true;
    isLoading = false;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        if (isStart) {
          if (widget.isNewBrand == false && state.getBrandsModel?.data.isNotEmpty == true) {
            isLoading = true;
            final brand = state.getBrandsModel!.data.firstWhere(
              (b) => b.id == widget.brandId,
              // orElse: () => BrandData(id: 0, name: '', email: '', domain: '', logo: ''),
            );
            _brandImage = '${APIConfig.mainbaseURL}${brand.logo}';
            brandNameController.text = brand.name;
            emailController.text = brand.email;
            domainController.text = brand.domain;
            if (_brandImage != null && _brandImage!.isNotEmpty) {
              _downloadAndSaveImage(_brandImage ?? "");
            }

            log("brand.logo ===== ${APIConfig.mainbaseURL}${brand.logo}");
            log("_selectedBrandImage ===== ${_selectedBrandImage?.path ?? 'No image selected'}");

            isStart = false;
            isLoading = false;
          }
          isLoading = false;
        }
      },

      builder: (context, state) {
        if (state.idGetbrandbyIdLoading) {
          return CreateBrandScreenShimmer();
        }
        if (isLoading) {
          return CreateBrandScreenShimmer();
        }

        return Scaffold(
          appBar: _buildBrandRegistrationAppBar(context),
          body: InkWell(
            focusColor: Colors.transparent,
            onTap: () {
              FocusManager.instance.primaryFocus?.unfocus();
            },
            child: Padding(
              padding: EdgeInsets.only(bottom: Platform.isIOS ? 0 : MediaQuery.of(context).viewPadding.bottom),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        child: Form(
                          key: formKey,
                          child: Column(
                            children: [
                              buildSizedBoxH(20.0),
                              _buildLogoUploadSection(),
                              buildSizedBoxH(40.0),
                              _buildBrandNameField(context, brandNameController),
                              buildSizedBoxH(20.0),
                              _buildBrandEmailField(context, emailController),
                              buildSizedBoxH(20.0),
                              _buildBrandOfficialDomainField(context, domainController),
                              buildSizedBoxH(75),
                              _submitButton(),
                              buildSizedBoxH(16.0),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
      // ),
    );
  }

  PreferredSizeWidget _buildBrandRegistrationAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
            _selectedImage = null;
            brandNameController.clear();
            emailController.clear();
            domainController.clear();
            setState(() {});
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          widget.isNewBrand == true ? "Create Brand" : "Edit Brand",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  Widget _buildLogoUploadSection() {
    return Column(
      children: [
        Center(
          child: DottedBorder(
            borderType: BorderType.RRect,
            radius: Radius.circular(16.r),
            padding: EdgeInsets.zero,
            color: _imageError != null ? Theme.of(context).colorScheme.error : Theme.of(context).primaryColor,
            dashPattern: const [10, 8],
            strokeWidth: 2,
            child: GestureDetector(
              onTap: _pickImage,
              child: Container(
                width: 135.w,
                height: 135.h,
                decoration: BoxDecoration(
                  color: Theme.of(context).customColors.selectImageBgColor,
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16.r),
                  child: _brandImage != null && _brandImage!.isNotEmpty
                      ? CustomImageView(
                          fit: BoxFit.cover,
                          imagePath: _brandImage,
                        )
                      : _selectedImage != null
                          ? Image.file(
                              File(_selectedImage!.path),
                              width: 220.w,
                              height: 100.h,
                              fit: BoxFit.cover,
                            )
                          : Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  width: 37.w,
                                  height: 37.h,
                                  child: CustomImageView(
                                      imagePath: Assets.images.svg.authentication.svgUploadLogo.path,
                                      fit: BoxFit.contain),
                                ),
                                SizedBox(height: 8.h),
                                Text(
                                  'Select Image',
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        fontSize: 15.sp,
                                        fontWeight: FontWeight.w500,
                                      ),
                                ),
                              ],
                            ),
                ),
              ),
            ),
          ),
        ),
        if (_imageError != null)
          Padding(
            padding: EdgeInsets.only(top: 8.0),
            child: Center(
              child: Text(
                textAlign: TextAlign.center,
                _imageError ?? "",
                style: TextStyle(color: Theme.of(context).colorScheme.error, fontSize: 12.sp),
              ),
            ),
          ),
        buildSizedBoxH(10.0),
        Text(
          "Upload Your Logo*",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w400,
                fontSize: 16.sp,
              ),
        ),
      ],
    );
  }

  Widget _buildBrandNameField(
    BuildContext context,
    TextEditingController brandNameController,
  ) {
    return FlowkarTextFormField(
      context: context,
      labelText: "Brand Name",
      textInputType: TextInputType.emailAddress,
      controller: brandNameController,
      filled: true,
      validator: (p0) => AppValidations.validateBrandName(p0),
      textInputAction: TextInputAction.next,
    );
  }

  Widget _buildBrandEmailField(
    BuildContext context,
    TextEditingController emailController,
  ) {
    return FlowkarTextFormField(
      context: context,
      labelText: "Email",
      textInputType: TextInputType.emailAddress,
      controller: emailController,
      filled: true,
      validator: (p0) => AppValidations.validateEmail(p0),
      textInputAction: TextInputAction.next,
    );
  }

  Widget _buildBrandOfficialDomainField(
    BuildContext context,
    TextEditingController domainController,
  ) {
    return FlowkarTextFormField(
      context: context,
      labelText: "Official Domain",
      textInputType: TextInputType.emailAddress,
      controller: domainController,
      filled: true,
      textInputAction: TextInputAction.done,
    );
  }

  Widget _submitButton() {
    return CustomElevatedButton(
      width: 159.w,
      text: widget.isNewBrand == false ? 'Update' : "Submit",
      brderRadius: 10.r,
      isLoading: context.read<AuthBloc>().state.iseditbrandLiading,
      isDisabled: context.read<AuthBloc>().state.iseditbrandLiading,
      iconSpacing: widget.isNewBrand == false ? 20.w : 0,
      onPressed: () {
        if (_selectedImage == null && (_brandImage == null || _brandImage!.isEmpty)) {
          // setState(() {
          _imageError = "Please upload your brand logo.";
          // });
        } else {
          // setState(() {
          _imageError = null;
          // });
        }

        if (formKey.currentState!.validate()) {
          if (_imageError == null) {
            FocusScope.of(context).requestFocus(FocusNode());
            if (widget.isNewBrand == true) {
              context.read<AuthBloc>().add(RegisterBrandsAPI(
                  email: emailController.text.trim(),
                  name: brandNameController.text.trim(),
                  domain: domainController.text.isNotEmpty ? domainController.text : "",
                  filepath: _selectedImage!,
                  context: context));
            } else {
              context.read<AuthBloc>().add(EditBrandAPI(
                  email: emailController.text.trim(),
                  name: brandNameController.text.trim(),
                  brandid: widget.brandId ?? 0,
                  domain: domainController.text.isNotEmpty ? domainController.text : "",
                  filepath: _selectedImage ?? _selectedBrandImage ?? (throw ArgumentError("Filepath cannot be null")),
                  context: context));

              int brannd = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;
              if (brannd == widget.brandId) {
                brandNameNotifier.value = brandNameController.text.trim();
              }
            }
          }
        }
      },
    );
  }
}
