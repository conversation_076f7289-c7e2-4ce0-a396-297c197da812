import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/discover/bloc/discover_bloc.dart';
import 'package:flowkar/features/user_management/page/list_of_roles_screen.dart';

class SearchUserManagementScreen extends StatefulWidget {
  const SearchUserManagementScreen({super.key});
  static Widget builder(BuildContext context) {
    return SearchUserManagementScreen();
  }

  @override
  State<SearchUserManagementScreen> createState() => _SearchUserManagementScreenState();
}

class _SearchUserManagementScreenState extends State<SearchUserManagementScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    final state = context.read<DiscoverBloc>().state;
    _searchController.clear();

    if (state.inviteesearchuserList != null) {
      List newList = List.from(state.inviteesearchuserList!);
      newList.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvoked: (didPop) {
        final state = context.read<DiscoverBloc>().state;
        _searchController.clear();
        state.inviteesearchuserList?.clear();
        FocusScope.of(context).unfocus();
      },
      child: BlocBuilder<DiscoverBloc, DiscoverState>(
        builder: (context, state) {
          return Scaffold(
            appBar: _buildSearchUserManageAppBar(context),
            body: InkWell(
              focusColor: Colors.transparent,
              onTap: () {
                FocusManager.instance.primaryFocus?.unfocus();
              },
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Column(
                  children: [
                    _buildSearchBar(context, state),
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            _buildAccountsList(state),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildSearchUserManageAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            final state = context.read<DiscoverBloc>().state;
            _searchController.clear();
            state.inviteesearchuserList?.clear();
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          "Users",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  Widget _buildSearchBar(BuildContext context, DiscoverState state) {
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: _searchController,
      builder: (context, value, child) {
        return FlowkarTextFormField(
          hint: Lang.of(context).lbl_search,
          context: context,
          controller: _searchController,
          prefixIcon: CustomImageView(
            imagePath: AssetConstants.icSearch,
            height: 20.0.h,
            margin: EdgeInsets.all(16.0),
          ),
          onChanged: (value) {
            context.read<DiscoverBloc>().add(InviteeSearchUserListEvent(searchtext: value));
          },
          fillColor: ThemeData().customColors.fillcolor,
          borderDecoration: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(10.r)),
            borderSide: BorderSide.none,
          ),
          filled: true,
          contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
          suffixIcon: value.text.isNotEmpty
              ? CustomImageView(
                  imagePath: AssetConstants.icClose,
                  height: 15.0.h,
                  margin: EdgeInsets.all(16.0),
                  onTap: () {
                    _searchController.clear();
                    state.inviteesearchuserList?.clear();
                    setState(() {});
                  },
                )
              : null,
        );
      },
    );
  }

  Widget _buildAccountsList(DiscoverState state) {
    if (state.inviteesearchuserList == null || state.inviteesearchuserList!.isEmpty) {
      return Padding(
        padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 8),
        child: ExceptionWidget(
          imagePath: AssetConstants.pngNoResultFound,
          title: "Please invite this user to Flowkar",
          subtitle: "",
          showButton: false,
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: ScrollPhysics(),
      padding: EdgeInsets.only(top: 8.h),
      itemCount: state.inviteesearchuserList?.length ?? 0,
      itemBuilder: (context, index) {
        final item = state.inviteesearchuserList?[index];
        return InkWell(
          onTap: () async {
            Logger.lOG("User ID --- ${item?.userId}");
            // int brandId = await Prefobj.preferences?.get(Prefkeys.BRANDID).to;

            // persistentN
            // NavigatorService.pushNamed(AppRoutes.listOfBrandScreen, arguments: [item?.userId ?? 0]);
            PersistentNavBarNavigator.pushNewScreen(context,
                screen: ListOfRolesScreen(
                  // brandId: [brandId],
                  userId: item?.userId ?? 0,
                )
                // screen: LostOfBrandScreen(userId: item?.userId ?? 0),
                );
            // PersistentNavBarNavigator.pushNewScreen(context,
            //     screen: LostOfBrandScreen(
            //       userId: item?.userId ?? 0,
            //     ));
            // NavigatorService.pushNamed(AppRoutes.addUserRollScreen);
          },
          child: _buildaccountListTile(
              context,
              item?.profileImage == ""
                  ? AssetConstants.pngUserReomve
                  : "${APIConfig.mainbaseURL}${item?.profileImage ?? ''}",
              item?.name ?? '',
              item?.userName ?? '',
              item?.userId),
        );
      },
    );
  }

  Widget _buildaccountListTile(BuildContext context, String imagepath, String title, String subtitle, int? id) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.0.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 50.h,
                width: 50.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100.r),
                  border: Border.all(
                    color: Theme.of(context).primaryColor,
                    width: 2.w,
                  ),
                ),
                child: ClipRRect(
                  clipBehavior: Clip.hardEdge,
                  child: CustomImageView(
                    margin: const EdgeInsets.all(2.5),
                    radius: BorderRadius.circular(40.r),
                    fit: BoxFit.cover,
                    imagePath: imagepath,
                    alignment: Alignment.center,
                  ),
                ),
              ),
              buildSizedBoxW(8),
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge!
                        .copyWith(fontSize: 15.sp, fontWeight: FontWeight.w600, color: Theme.of(context).primaryColor),
                  ),
                  buildSizedBoxH(2),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        fontSize: 12.4.sp, fontWeight: FontWeight.w400, color: ThemeData().customColors.greylite),
                  ),
                ],
              )
            ],
          ),
        ],
      ),
    );
  }
}
