import 'dart:io';

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/user_management/bloc/user_management_bloc.dart';
import 'package:flowkar/features/user_management/page/add_user_roll_screen.dart';
import 'package:flowkar/features/user_management/widget/user_management_screen_shimmer.dart';

class ListOfRolesScreen extends StatefulWidget {
  final int userId;
  // final List<int> brandId;
  const ListOfRolesScreen({
    super.key,
    required this.userId,
    // required this.brandId,
  });

  @override
  State<ListOfRolesScreen> createState() => _ListOfRolesScreenState();
}

class _ListOfRolesScreenState extends State<ListOfRolesScreen> {
  int? selectedRoleId;
  int brandId = 0;
  @override
  void initState() {
    super.initState();
    context.read<UserManagementBloc>().add(GetUserRoleAPIEvent());
    brandId = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? 0;
  }

  void toggleRoleSelection(int roleId) {
    setState(() {
      selectedRoleId = (selectedRoleId == roleId) ? null : roleId;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildSearchUserManageAppBar(context),
      body: Padding(
        padding: EdgeInsets.only(bottom: Platform.isIOS ? 0 : MediaQuery.of(context).viewPadding.bottom),
        child: BlocBuilder<UserManagementBloc, UserManagementState>(
          builder: (context, state) {
            return state.getUserRolesLoading
                ? ManageUserShimmer(height: 50.h)
                : (state.getUserRolesModel?.data?.isEmpty ?? true)
                    ? _buildNoUserRoleFound()
                    : Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  buildSizedBoxH(8.0),
                                  Text(
                                    "Roles",
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(fontSize: 16.sp, fontWeight: FontWeight.w600),
                                  ),
                                  buildSizedBoxH(10.0),
                                  Expanded(
                                    child: ListView.builder(
                                      itemCount: state.getUserRolesModel?.data?.length,
                                      itemBuilder: (context, index) {
                                        return _buildAccountListTile(
                                          context,
                                          roleName: '${state.getUserRolesModel?.data?[index].roleName}',
                                          roleId: state.getUserRolesModel?.data?[index].id ?? 0,
                                          roleDescription: state.getUserRolesModel?.data?[index].roleDescription,
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            CustomElevatedButton(
                              margin: EdgeInsets.only(bottom: 16.0.h),
                              alignment: Alignment.bottomCenter,
                              isLoading: state.inviteUserLoading,
                              isDisabled: selectedRoleId == null || state.inviteUserLoading,
                              onPressed: () {
                                if (selectedRoleId != null) {
                                  context.read<UserManagementBloc>().add(InviteUserAPIEvent(
                                        userId: widget.userId,
                                        brandId: [brandId],
                                        roleId: selectedRoleId ?? 0,
                                      ));
                                } else {
                                  toastification.show(
                                    type: ToastificationType.error,
                                    showProgressBar: false,
                                    description: Text('Please select at least one role before moving forward.'),
                                    autoCloseDuration: const Duration(seconds: 3),
                                  );
                                }
                              },
                              text: "Done",
                            ),
                          ],
                        ),
                      );
          },
        ),
      ),
    );
  }

  Widget _buildNoUserRoleFound() {
    return ExceptionWidget(
      imagePath: Assets.images.svg.other.svgNodatafound.path,
      showButton: true,
      title: 'No User Roles Found',
      subtitle: 'Currently You Have No Roles Added Please Add One.',
      buttonText: "Add Role",
      onButtonPressed: () {
        // PersistentNavBarNavigator.pushNewScreen(context, screen: BrandRegistrationScreen(brandId: brandID ?? 0, isNewBrand: true), withNavBar: false);
        PersistentNavBarNavigator.pushNewScreen(context, screen: AddUserListScreen(), withNavBar: false);
      },
    );
  }

  PreferredSizeWidget _buildSearchUserManageAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          "List Of Roles",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
      actions: [
        CustomElevatedButton(
          width: 100.w,
          height: 40.h,
          isLoading: false,
          text: "+ Add Roles",
          fontSize: 13.sp,
          brderRadius: 8.r,
          onPressed: () {
            PersistentNavBarNavigator.pushNewScreen(context, screen: AddUserListScreen());
          },
        ),
      ],
    );
  }

  Widget _buildAccountListTile(
    BuildContext context, {
    required String roleName,
    required int roleId,
    required Map<String, String>? roleDescription, // RoleDescription? Nullable
  }) {
    bool isSelected = selectedRoleId == roleId;

    return InkWell(
      onTap: () => toggleRoleSelection(roleId),
      onLongPress: () {
        if (roleDescription != null) {
          _showRoleDescriptionDialog(context, roleDescription); // Convert to Map<String, dynamic>
        }
      },
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 1.0.h),
        child: Container(
          decoration: BoxDecoration(
            // color: Colors.red,
            border: Border.all(color: isSelected ? Theme.of(context).primaryColor : Colors.transparent, width: 1.0),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
                  child: Text(
                    roleName,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontSize: 15.sp,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).primaryColor,
                        ),
                  ),
                ),
              ),
              buildSizedBoxW(16),
              Icon(
                isSelected ? Icons.check_box_rounded : Icons.check_box_outline_blank_rounded,
                color: Theme.of(context).primaryColor,
              ),
              buildSizedBoxW(12),
            ],
          ),
        ),
      ),
    );
  }

  void _showRoleDescriptionDialog(BuildContext context, Map<String, dynamic> roleDescription) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            "Role Description",
            style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold),
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView(
              shrinkWrap: true,
              children: roleDescription.entries.toList().asMap().entries.map((entryWithIndex) {
                int index = entryWithIndex.key + 1;
                var entry = entryWithIndex.value;

                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Row(
                    children: [
                      Text("$index : ",
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500)),
                      Text(entry.value.toString(),
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(color: Theme.of(context).primaryColor)),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text("Close", style: TextStyle(color: Theme.of(context).primaryColor)),
            ),
          ],
        );
      },
    );
  }
}
