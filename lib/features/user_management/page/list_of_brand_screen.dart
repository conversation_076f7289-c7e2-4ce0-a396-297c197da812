import 'dart:io';

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/user_management/bloc/user_management_bloc.dart';
import 'package:flowkar/features/user_management/widget/user_management_screen_shimmer.dart';

class LostOfBrandScreen extends StatefulWidget {
  final int userId;
  const LostOfBrandScreen({super.key, required this.userId});
  static Widget builder(BuildContext context) {
    final List args = ModalRoute.of(context)?.settings.arguments as List;

    return LostOfBrandScreen(
      userId: args[0],
    );
  }

  @override
  State<LostOfBrandScreen> createState() => _LostOfBrandScreenState();
}

class _LostOfBrandScreenState extends State<LostOfBrandScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<int> selectedBrandIds = [];

  @override
  void initState() {
    super.initState();
    // context.read<UserManagementBloc>().add(GetBrandsAPI());
  }

  void toggleBrandSelection(int brandId) {
    setState(() {
      if (selectedBrandIds.contains(brandId)) {
        selectedBrandIds.remove(brandId);
      } else {
        selectedBrandIds.add(brandId);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    Logger.lOG(
      widget.userId,
    );
    return Scaffold(
      appBar: _buildSearchUserManageAppBar(context),
      body: Padding(
        padding: EdgeInsets.only(bottom: Platform.isIOS ? 0 : MediaQuery.of(context).viewPadding.bottom),
        child: BlocBuilder<UserManagementBloc, UserManagementState>(
          builder: (context, state) {
            return InkWell(
              focusColor: Colors.transparent,
              onTap: () {
                FocusManager.instance.primaryFocus?.unfocus();
              },
              child: state.brandLoading
                  ? ManageUserShimmer(height: 50.h)
                  : Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildSearchBar(context),
                                buildSizedBoxH(18.0),
                                Text(
                                  "Brand",
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(fontSize: 16.sp, fontWeight: FontWeight.w600),
                                ),
                                buildSizedBoxH(10.0),
                                Expanded(
                                  child: ListView.builder(
                                    itemCount: state.getBrandsModel?.data.length ?? 0,
                                    itemBuilder: (context, index) {
                                      return _buildaccountListTile(
                                        context,
                                        AssetConstants.pngPlaceholder,
                                        '${state.getBrandsModel?.data[index].name}',
                                        '${state.getBrandsModel?.data[index].logo}',
                                        state.getBrandsModel?.data[index].id ?? 0,
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                          CustomElevatedButton(
                            margin: EdgeInsets.only(bottom: 16.0.h),
                            alignment: Alignment.bottomCenter,
                            onPressed: () {
                              if (selectedBrandIds.isEmpty) {
                                Logger.lOG("No brand selected");
                                toastification.show(
                                  type: ToastificationType.error,
                                  showProgressBar: false,
                                  title: Text('Please select a brand.'),
                                  autoCloseDuration: const Duration(seconds: 3),
                                );
                                return;
                              }

                              Logger.lOG("Selected Brands: ${selectedBrandIds.length}");
                              Logger.lOG("Selected Brands: $selectedBrandIds");
                              Logger.lOG("User Id : ${widget.userId}");
                              // NavigatorService.pushNamed(AppRoutes.listOfRolesScreen, arguments: [
                              //   selectedBrandIds,
                              //   widget.userId,
                              // ]);
                              // PersistentNavBarNavigator.pushNewScreen(context,
                              //     screen: ListOfRolesScreen(brandId: selectedBrandIds, userId: widget.userId));
                            },
                            text: "Done",
                          ),
                        ],
                      ),
                    ),
            );
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildSearchUserManageAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          "List Of Brand",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: _searchController,
      builder: (context, value, child) {
        return FlowkarTextFormField(
          hint: Lang.of(context).lbl_search,
          context: context,
          controller: _searchController,
          prefixIcon: CustomImageView(
            imagePath: AssetConstants.icSearch,
            height: 20.0.h,
            margin: EdgeInsets.all(16.0),
          ),
          onChanged: (value) {},
          fillColor: ThemeData().customColors.fillcolor,
          borderDecoration: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(10.r)),
            borderSide: BorderSide.none,
          ),
          filled: true,
          contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
          suffixIcon: value.text.isNotEmpty
              ? CustomImageView(
                  imagePath: AssetConstants.icClose,
                  height: 15.0.h,
                  margin: EdgeInsets.all(16.0),
                  onTap: () {
                    _searchController.clear();
                  },
                )
              : null,
        );
      },
    );
  }

  Widget _buildaccountListTile(
    BuildContext context,
    String imagepath,
    String title,
    String brandlogo,
    int brandId,
  ) {
    bool isSelected = selectedBrandIds.contains(brandId);

    return InkWell(
      onTap: () => toggleBrandSelection(brandId),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 4.0.h),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? Theme.of(context).primaryColor : Colors.transparent,
              width: 1.0,
            ),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    ClipRRect(
                      clipBehavior: Clip.hardEdge,
                      child: Padding(
                        padding: const EdgeInsets.all(4.0),
                        child: CustomImageView(
                          radius: BorderRadius.circular(40.r),
                          height: 48.0.h,
                          width: 50.0.w,
                          fit: BoxFit.cover,
                          imagePath: brandlogo.isEmpty ? imagepath : "${APIConfig.mainbaseURL}$brandlogo",
                          alignment: Alignment.center,
                        ),
                      ),
                    ),
                    buildSizedBoxW(8),
                    Expanded(
                      child: Text(
                        overflow: TextOverflow.ellipsis,
                        title,
                        style: Theme.of(context).textTheme.titleLarge!.copyWith(
                              fontSize: 15.sp,
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).primaryColor,
                            ),
                      ),
                    ),
                  ],
                ),
              ),
              isSelected
                  ? Padding(
                      padding: EdgeInsets.only(right: 12.w),
                      child: Icon(
                        isSelected ? Icons.check_box_rounded : Icons.check_box_outline_blank_rounded,
                        color: isSelected ? Theme.of(context).primaryColor : Theme.of(context).primaryColor,
                      ),
                    )
                  : SizedBox.shrink(),
            ],
          ),
        ),
      ),
    );
  }
}
