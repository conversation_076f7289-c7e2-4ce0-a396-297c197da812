// ignore_for_file: use_build_context_synchronously

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/authentication/model/get_brands_model.dart';
import 'package:flowkar/features/user_management/model/get_invited_user_model.dart';
import 'package:flowkar/features/user_management/model/get_invitee_user_model.dart';
import 'package:flowkar/features/user_management/model/get_user_roles_model.dart';
import 'package:flowkar/features/user_management/model/user_role_request_model.dart';

part 'user_management_event.dart';
part 'user_management_state.dart';

class UserManagementBloc extends Bloc<UserManagementEvent, UserManagementState> {
  ApiClient apiClient = ApiClient(Dio());
  UserManagementBloc(super.initialState) {
    // on<GetBrandsAPI>(_getallBrandApi);
    on<GetInvitedUserAPIEvent>(_getInvitedUser);
    on<GetInviteeUserAPIEvent>(_getInviteeUser);
    on<InviteUserAPIEvent>(_inviteeUser);
    on<GetUserRoleAPIEvent>(_getUserRole);
    on<CreateUserRoleEvent>(_onCreateUserRole);

    on<ApproveInviteUserRoleEvent>(_onApproveInviteUserRole);
    on<DeclineInviteUserRoleEvent>(_onDeclineInviteUserRole);
    on<RevokeInviteUserRoleEvent>(_onRevokeInviteUserRole);
  }

  Future<void> getallBrandApi(DeclineInviteUserRoleEvent event, Emitter<UserManagementState> emit) async {
    emit(state.copyWith(brandLoading: true));
    try {
      String loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? '';
      final result = await apiClient.getBrands(
        logInUserId: loginUserId,
      );

      if (result.status == true) {
        emit(state.copyWith(getBrandsModel: result, brandLoading: false));
      } else {
        emit(state.copyWith(brandLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(brandLoading: false));
      Logger.lOG("_getallBrandApi Error: $error");
    }
  }

  Future<void> _getInvitedUser(GetInvitedUserAPIEvent event, Emitter<UserManagementState> emit) async {
    emit(state.copyWith(getInvitedUserLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      String? brandId = Prefobj.preferences?.get(Prefkeys.BRANDID)?.toString() ?? '';
      final result = await apiClient.getInvitedUserAPI(
        logInUserId: loginuserId,
        brandid: brandId.toString(),
      );

      if (result.status == true) {
        emit(state.copyWith(getInvitedUserModel: result, getInvitedUserLoading: false));
      } else {
        emit(state.copyWith(getInvitedUserLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(getInvitedUserLoading: false));
      Logger.lOG("_getInvitedUser Error: $error");
    }
  }

  Future<void> _getInviteeUser(GetInviteeUserAPIEvent event, Emitter<UserManagementState> emit) async {
    emit(state.copyWith(getInviteeUserLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      String? brandId = Prefobj.preferences?.get(Prefkeys.BRANDID)?.toString() ?? '';
      final result = await apiClient.getInviteeUserAPI(
        logInUserId: loginuserId,
        brandid: brandId.toString(),
      );

      if (result.status == true) {
        Logger.lOG("Get Invitee Users length ${result.data?.length}");
        Logger.lOG("Get Invitee Users ${result.data}");
        emit(state.copyWith(getInviteeUserModel: result, getInviteeUserLoading: false));
      } else {
        emit(state.copyWith(getInviteeUserLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(getInviteeUserLoading: false));
      Logger.lOG("_getInviteeUser Error: $error");
    }
  }

  Future<void> _inviteeUser(InviteUserAPIEvent event, Emitter<UserManagementState> emit) async {
    emit(state.copyWith(inviteUserLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      String subscriptionId = Prefobj.preferences?.get(Prefkeys.SUBSCRIPTION);
      String? brandId = Prefobj.preferences?.get(Prefkeys.BRANDID)?.toString() ?? '';
      final body = {
        "user_id": event.userId,
        "brands_list": event.brandId,
        "role_id": event.roleId,
      };
      final result = await apiClient.inviteUser(loginuserId, brandId.toString(), subscriptionId.toString(), body);

      if (result.status == true) {
        Logger.lOG("Invitee Users Message ${result.message}");
        Logger.lOG("Invitee Users Status ${result.status}");

        NavigatorService.goBack();
        NavigatorService.goBack();
        NavigatorService.goBack();
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            result.message ?? "",
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );

        add(GetInvitedUserAPIEvent());
        add(GetInviteeUserAPIEvent());

        emit(state.copyWith(inviteUserLoading: false, inviteUserSuccess: true));
      } else if (result.status == false) {
        toastification.show(
          type: ToastificationType.info,
          showProgressBar: false,
          title: Text(
            result.message ?? "",
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        emit(state.copyWith(inviteUserLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(inviteUserLoading: false));
      // handleError(error);
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.info,
                  showProgressBar: false,
                  // title: Text(
                  //   error.response?.data['message'] ?? error.message ?? '',
                  //   style: GoogleFonts.montserrat(
                  //     fontSize: 12.0.sp,
                  //     fontWeight: FontWeight.w500,
                  //   ),
                  // ),
                  description: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
      Logger.lOG("InviteeUser Error: $error");
    }
  }

  Future<void> _getUserRole(GetUserRoleAPIEvent event, Emitter<UserManagementState> emit) async {
    emit(state.copyWith(getUserRolesLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      String? brandId = Prefobj.preferences?.get(Prefkeys.BRANDID)?.toString() ?? '';
      final result = await apiClient.getUserRoles(
        logInUserId: loginuserId,
        brandid: brandId.toString(),
      );

      if (result.status == true) {
        Logger.lOG("Get Invitee Users length ${result.data?.length}");
        Logger.lOG("Get Invitee Users ${result.data}");

        emit(state.copyWith(getUserRolesModel: result, getUserRolesLoading: false));
      } else {
        emit(state.copyWith(getUserRolesLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(getUserRolesLoading: false));
      Logger.lOG("_getUserRole Error: $error");
    }
  }

  Future<void> _onCreateUserRole(CreateUserRoleEvent event, Emitter<UserManagementState> emit) async {
    emit(state.copyWith(getUserRolesLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      String? brandId = Prefobj.preferences?.get(Prefkeys.BRANDID)?.toString() ?? '';
      final result = await apiClient.createUserRole(loginuserId, brandId.toString(), event.roleData.toJson());

      if (result.status == true) {
        Logger.lOG("Get Invitee Users length ${result.status}");
        Logger.lOG("Get Invitee Users ${result.message}");
        await _getUserRole(GetUserRoleAPIEvent(), emit);
        NavigatorService.goBack();
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            result.message ?? "",
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );

        emit(state.copyWith(getUserRolesLoading: false));
      } else {
        toastification.show(
          type: ToastificationType.info,
          showProgressBar: false,
          title: Text(
            result.message ?? "",
            style: GoogleFonts.montserrat(fontSize: 12.0.sp, fontWeight: FontWeight.w500),
          ),
        );
        emit(state.copyWith(getUserRolesLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(getUserRolesLoading: false));
      Logger.lOG("_onCreateUserRole Error: $error");
      emit(state.copyWith(getUserRolesLoading: false));
    }
  }

  Future<void> _onApproveInviteUserRole(ApproveInviteUserRoleEvent event, Emitter<UserManagementState> emit) async {
    Set<int> newDeclining = Set<int>.from(state.approvingInviteUserRoleIds)..add(event.inviteId);
    emit(state.copyWith(approveInviteUserRoleLoading: true, approvingInviteUserRoleIds: newDeclining));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final result =
          await apiClient.approveInvite(logInUserId: loginuserId, inviteId: event.inviteId, brand: brandId.toString());

      if (result.status == true) {
        event.context.read<UserManagementBloc>().add(GetInviteeUserAPIEvent());

        emit(state.copyWith(approveInviteUserRoleLoading: false));
      } else {
        emit(state.copyWith(approveInviteUserRoleLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(approveInviteUserRoleLoading: false));
      Logger.lOG("_onApproveInviteUserRole Error: $error");
      emit(state.copyWith(approveInviteUserRoleLoading: false));
    } finally {
      Set<int> newDeclining = Set<int>.from(state.approvingInviteUserRoleIds)..remove(event.inviteId);
      emit(state.copyWith(approvingInviteUserRoleIds: newDeclining));
    }
  }

  Future<void> _onDeclineInviteUserRole(DeclineInviteUserRoleEvent event, Emitter<UserManagementState> emit) async {
    Set<int> newDeclining = Set<int>.from(state.decliningInviteUserRoleIds)..add(event.declineId);
    emit(state.copyWith(declineInviteUserRoleLoading: true, decliningInviteUserRoleIds: newDeclining));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final result = await apiClient.declineInvite(
          logInUserId: loginuserId, declineId: event.declineId, brand: brandId.toString());

      if (result.status == true) {
        event.context.read<UserManagementBloc>().add(GetInviteeUserAPIEvent());

        emit(state.copyWith(declineInviteUserRoleLoading: false));
      } else {
        emit(state.copyWith(declineInviteUserRoleLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(declineInviteUserRoleLoading: false));
      Logger.lOG("_onDeclineInviteUserRole Error: $error");
      emit(state.copyWith(declineInviteUserRoleLoading: false));
    } finally {
      Set<int> newDeclining = Set<int>.from(state.decliningInviteUserRoleIds)..remove(event.declineId);
      emit(state.copyWith(decliningInviteUserRoleIds: newDeclining));
    }
  }

  Future<void> _onRevokeInviteUserRole(RevokeInviteUserRoleEvent event, Emitter<UserManagementState> emit) async {
    Set<int> newDeclining = Set<int>.from(state.revokingInviteUserRoleIds)..add(event.inviteId);
    emit(state.copyWith(revokeInviteUserRoleLoading: true, revokingInviteUserRoleIds: newDeclining));

    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final result =
          await apiClient.revokeInvite(logInUserId: loginuserId, inviteId: event.inviteId, brand: brandId.toString());

      if (result.status == true) {
        event.context.read<UserManagementBloc>().add(GetInvitedUserAPIEvent());
        emit(state.copyWith(revokeInviteUserRoleLoading: false));
      } else {
        emit(state.copyWith(revokeInviteUserRoleLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(revokeInviteUserRoleLoading: false));
      Logger.lOG("_onRevokeInviteUserRole Error: $error");
      emit(state.copyWith(revokeInviteUserRoleLoading: false));
    } finally {
      Set<int> newDeclining = Set<int>.from(state.revokingInviteUserRoleIds)..remove(event.inviteId);
      emit(state.copyWith(revokingInviteUserRoleIds: newDeclining));
    }
  }
}
