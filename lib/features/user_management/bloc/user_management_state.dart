part of 'user_management_bloc.dart';

class UserManagementState extends Equatable {
  const UserManagementState({
    this.brandLoading = false,
    this.getBrandsModel,
    this.getInvitedUserLoading = false,
    this.getInvitedUserModel,
    this.getInviteeUserLoading = false,
    this.getInviteeUserModel,
    this.inviteUserLoading = false,
    this.getUserRolesLoading = false,
    this.getUserRolesModel,
    this.approveInviteUserRoleLoading = false,
    this.declineInviteUserRoleLoading = false,
    this.revokeInviteUserRoleLoading = false,
    this.approvingInviteUserRoleIds = const {},
    this.decliningInviteUserRoleIds = const {},
    this.revokingInviteUserRoleIds = const {},
    this.inviteUserSuccess = false,
  });

  // Get Brand
  final bool brandLoading;
  final GetBrandsModel? getBrandsModel;
  // User Management
  // get Invited User
  final bool getInvitedUserLoading;
  final GetInvitedUserModel? getInvitedUserModel;
  // get Invitee User
  final bool getInviteeUserLoading;
  final GetInviteeUserModel? getInviteeUserModel;

  // Invite User API
  final bool inviteUserLoading;
  // Get User Roles API
  final bool getUserRolesLoading;
  final GetUserRolesModel? getUserRolesModel;

  final bool approveInviteUserRoleLoading;
  final bool declineInviteUserRoleLoading;
  final bool revokeInviteUserRoleLoading;
  final Set<int> approvingInviteUserRoleIds;
  final Set<int> decliningInviteUserRoleIds;
  final Set<int> revokingInviteUserRoleIds;

  final bool inviteUserSuccess;

  @override
  List<Object?> get props => [
        brandLoading,
        getBrandsModel,
        getInvitedUserLoading,
        getInvitedUserModel,
        getInviteeUserLoading,
        getInviteeUserModel,
        inviteUserLoading,
        getUserRolesLoading,
        getUserRolesModel,
        approveInviteUserRoleLoading,
        declineInviteUserRoleLoading,
        revokeInviteUserRoleLoading,
        approvingInviteUserRoleIds,
        decliningInviteUserRoleIds,
        revokingInviteUserRoleIds,
        inviteUserSuccess,
      ];

  UserManagementState copyWith({
    bool? brandLoading,
    GetBrandsModel? getBrandsModel,
    bool? getInvitedUserLoading,
    GetInvitedUserModel? getInvitedUserModel,
    bool? getInviteeUserLoading,
    GetInviteeUserModel? getInviteeUserModel,
    bool? inviteUserLoading,
    bool? getUserRolesLoading,
    GetUserRolesModel? getUserRolesModel,
    bool? approveInviteUserRoleLoading,
    bool? declineInviteUserRoleLoading,
    bool? revokeInviteUserRoleLoading,
    Set<int>? approvingInviteUserRoleIds,
    Set<int>? decliningInviteUserRoleIds,
    Set<int>? revokingInviteUserRoleIds,
    bool? inviteUserSuccess,
  }) {
    return UserManagementState(
      brandLoading: brandLoading ?? this.brandLoading,
      getBrandsModel: getBrandsModel ?? this.getBrandsModel,
      getInvitedUserLoading: getInvitedUserLoading ?? this.getInvitedUserLoading,
      getInvitedUserModel: getInvitedUserModel ?? this.getInvitedUserModel,
      getInviteeUserLoading: getInviteeUserLoading ?? this.getInviteeUserLoading,
      getInviteeUserModel: getInviteeUserModel ?? this.getInviteeUserModel,
      inviteUserLoading: inviteUserLoading ?? this.inviteUserLoading,
      getUserRolesLoading: getUserRolesLoading ?? this.getUserRolesLoading,
      getUserRolesModel: getUserRolesModel ?? this.getUserRolesModel,
      approveInviteUserRoleLoading: approveInviteUserRoleLoading ?? this.approveInviteUserRoleLoading,
      declineInviteUserRoleLoading: declineInviteUserRoleLoading ?? this.declineInviteUserRoleLoading,
      revokeInviteUserRoleLoading: revokeInviteUserRoleLoading ?? this.revokeInviteUserRoleLoading,
      approvingInviteUserRoleIds: approvingInviteUserRoleIds ?? this.approvingInviteUserRoleIds,
      decliningInviteUserRoleIds: decliningInviteUserRoleIds ?? this.decliningInviteUserRoleIds,
      revokingInviteUserRoleIds: revokingInviteUserRoleIds ?? this.revokingInviteUserRoleIds,
      inviteUserSuccess: inviteUserSuccess ?? this.inviteUserSuccess,
    );
  }
}
