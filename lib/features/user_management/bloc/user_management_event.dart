part of 'user_management_bloc.dart';

class UserManagementEvent extends Equatable {
  const UserManagementEvent();

  @override
  List<Object> get props => [];
}

// class GetBrandsAPI extends UserManagementEvent {
//   const GetBrandsAPI();
//   @override
//   List<Object> get props => [];
// }

class GetInvitedUserAPIEvent extends UserManagementEvent {
  const GetInvitedUserAPIEvent();
  @override
  List<Object> get props => [];
}

class GetInviteeUserAPIEvent extends UserManagementEvent {
  const GetInviteeUserAPIEvent();
  @override
  List<Object> get props => [];
}

class InviteUserAPIEvent extends UserManagementEvent {
  final int userId;
  final int roleId;
  final List<int> brandId;
  const InviteUserAPIEvent({required this.userId, required this.roleId, required this.brandId});
  @override
  List<Object> get props => [userId, brandId];
}

class GetUserRoleAPIEvent extends UserManagementEvent {
  const GetUserRoleAPIEvent();
  @override
  List<Object> get props => [];
}

class CreateUserRoleEvent extends UserManagementEvent {
  final UserRoleRequestModel roleData;

  const CreateUserRoleEvent(this.roleData);
}

class ApproveInviteUserRoleEvent extends UserManagementEvent {
  final BuildContext context;
  final int inviteId;

  const ApproveInviteUserRoleEvent(this.context, {required this.inviteId});

  @override
  List<Object> get props => [inviteId];
}

class DeclineInviteUserRoleEvent extends UserManagementEvent {
  final BuildContext context;
  final int declineId;

  const DeclineInviteUserRoleEvent(this.context, {required this.declineId});

  @override
  List<Object> get props => [declineId];
}

class RevokeInviteUserRoleEvent extends UserManagementEvent {
  final BuildContext context;
  final int inviteId;

  const RevokeInviteUserRoleEvent(this.context, {required this.inviteId});

  @override
  List<Object> get props => [inviteId];
}
