import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/user_management/bloc/user_management_bloc.dart';
import 'package:flowkar/features/user_management/model/get_invited_user_model.dart';
import 'package:flowkar/features/user_management/model/get_invitee_user_model.dart';

class UserManageContainer extends StatelessWidget {
  final int tabIndex;
  final String email;
  final String imageUrl;
  final List<Map<String, String>> brandLogos;
  final int maxVisibleIcons;

  final GetInvitedUserData? invitedUserData;
  final GetInviteeUserData? inviteeUserData;

  const UserManageContainer({
    super.key,
    required this.email,
    required this.imageUrl,
    required this.brandLogos,
    this.maxVisibleIcons = 3,
    required this.tabIndex,
    this.invitedUserData,
    this.inviteeUserData,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserManagementBloc, UserManagementState>(
      builder: (context, state) {
        return Container(
          margin: EdgeInsets.symmetric(vertical: 8.h),
          padding: EdgeInsets.symmetric(vertical: 14.h),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(color: Colors.brown.withOpacity(0.2), width: 1.5),
          ),
          child: Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 10.w),
                    child: CustomImageView(
                      height: 35.h,
                      width: 35.w,
                      radius: BorderRadius.circular(50.r),
                      fit: BoxFit.cover,
                      imagePath: imageUrl,
                    ),
                  ),
                  Flexible(
                    child: Padding(
                      padding: EdgeInsets.only(left: 12.w),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (email.isNotEmpty)
                            Text(
                              textAlign: TextAlign.start,
                              email,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 12.sp),
                            ),
                          if (email.isNotEmpty) buildSizedBoxH(8.0),
                          _buildPlatformIcons(context),
                        ],
                      ),
                    ),
                  ),
                  // PopupMenuButton(
                  //   icon: Icon(
                  //     Icons.more_vert_rounded,
                  //     color: Theme.of(context).primaryColor.withOpacity(0.9),
                  //   ),
                  //   offset: Offset(-38.w, -30.h),
                  //   shadowColor: Theme.of(context).primaryColor.withOpacity(0.5),
                  //   itemBuilder: (context) => [
                  //     PopupMenuItem(
                  //       onTap: () {},
                  //       height: 30.h,
                  //       child: Row(
                  //         mainAxisAlignment: MainAxisAlignment.center,
                  //         children: [
                  //           CustomImageView(
                  //             height: 20.h,
                  //             imagePath: Assets.images.svg.homeFeed.svgDelete.path,
                  //             color: Theme.of(context).primaryColor,
                  //           ),
                  //           buildSizedBoxW(10.0),
                  //           Text(
                  //             "Delete",
                  //             style: Theme.of(context).textTheme.bodyMedium,
                  //           ),
                  //         ],
                  //       ),
                  //     ),
                  //   ],
                  // ),
                ],
              ),
              tabIndex == 0
                  ? _buildRevokeButtons(context, state, isRevoke: invitedUserData?.isAccepted ?? false)
                  : inviteeUserData?.isAccepted == true
                      ? SizedBox.shrink()
                      : _buildAcceptDeclineButtons(context, state, showButtons: true),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPlatformIcons(BuildContext context) {
    int extraIconsCount = brandLogos.length > maxVisibleIcons ? brandLogos.length - maxVisibleIcons : 0;

    return Align(
      alignment: Alignment.centerLeft,
      child: SizedBox(
        width: (16 + (maxVisibleIcons * 30)).w,
        height: 26.h,
        child: Stack(
          alignment: Alignment.centerLeft,
          children: [
            for (int i = 0; i < brandLogos.length; i++)
              if (i < maxVisibleIcons)
                Positioned(
                  left: (i * 20.0).w,
                  child: Container(
                    padding: EdgeInsets.all(1.8),
                    height: 26.h,
                    width: 26.h,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.black45),
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(50.r),
                    ),
                    child: Center(
                      child: CustomImageView(
                        height: 22.h,
                        width: 22.w,
                        fit: BoxFit.cover,
                        radius: BorderRadius.circular(100.r),
                        imagePath: brandLogos[i]['logo'] ?? '',
                      ),
                    ),
                  ),
                ),
            if (extraIconsCount > 0)
              Positioned(
                left: (maxVisibleIcons * 20.0).w,
                child: GestureDetector(
                  onTap: () => _showPlatformPopup(context),
                  child: Container(
                    padding: EdgeInsets.all(3),
                    height: 26.h,
                    width: 26.h,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.black45),
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(50.r),
                    ),
                    child: Text(
                      textAlign: TextAlign.center,
                      '+$extraIconsCount',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontSize: 11.sp,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showPlatformPopup(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text("Brands"),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: brandLogos
                .map((brand) => ListTile(
                      leading: CustomImageView(
                        imagePath: brand['logo'] ?? '',
                        width: 24.w,
                        height: 24.h,
                      ),
                      title: Text(brand['name'] ?? ''),
                    ))
                .toList(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            )
          ],
        );
      },
    );
  }

  Widget _buildAcceptDeclineButtons(BuildContext context, UserManagementState state, {bool showButtons = true}) {
    return showButtons
        ? Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.w),
            child: Column(
              children: [
                buildSizedBoxH(16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: CustomElevatedButton(
                        height: 40.h,
                        brderRadius: 10.r,
                        isLoading: state.decliningInviteUserRoleIds.contains(inviteeUserData?.id),
                        isDisabled:
                            state.decliningInviteUserRoleIds.isNotEmpty || state.approvingInviteUserRoleIds.isNotEmpty,
                        onPressed: () {
                          context
                              .read<UserManagementBloc>()
                              .add(DeclineInviteUserRoleEvent(context, declineId: inviteeUserData?.id ?? 0));
                        },
                        text: "Decline",
                        buttonStyle: ButtonStyle(
                          padding: WidgetStateProperty.all(EdgeInsets.zero),
                          backgroundColor: WidgetStateProperty.all(Theme.of(context).customColors.white),
                          shape: WidgetStatePropertyAll(
                            RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10.r),
                              side: BorderSide(color: Theme.of(context).customColors.primaryColor),
                            ),
                          ),
                        ),
                        buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).customColors.primaryColor,
                            ),
                      ),
                    ),
                    buildSizedBoxW(10.0),
                    Expanded(
                      child: CustomElevatedButton(
                        height: 40.h,
                        brderRadius: 10.r,
                        isLoading: state.approvingInviteUserRoleIds.contains(inviteeUserData?.id),
                        isDisabled:
                            state.decliningInviteUserRoleIds.isNotEmpty || state.approvingInviteUserRoleIds.isNotEmpty,
                        onPressed: () {
                          context
                              .read<UserManagementBloc>()
                              .add(ApproveInviteUserRoleEvent(context, inviteId: inviteeUserData?.id ?? 0));
                        },
                        text: "Accept",
                        buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).customColors.white,
                            ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          )
        : SizedBox.shrink();
  }

  Widget _buildRevokeButtons(BuildContext context, UserManagementState state, {bool isRevoke = true}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8.w),
      child: Column(
        children: [
          buildSizedBoxH(16),
          isRevoke
              ? CustomElevatedButton(
                  height: 40.h,
                  width: double.infinity,
                  brderRadius: 10.r,
                  isLoading: state.revokingInviteUserRoleIds.contains(inviteeUserData?.id),
                  isDisabled: state.revokingInviteUserRoleIds.isNotEmpty,
                  onPressed: () {
                    context
                        .read<UserManagementBloc>()
                        .add(RevokeInviteUserRoleEvent(context, inviteId: invitedUserData?.id ?? 0));
                  },
                  text: "Revoke",
                  buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).customColors.white,
                      ),
                )
              : CustomElevatedButton(
                  height: 40.h,
                  width: double.infinity,
                  brderRadius: 10.r,
                  isLoading: state.revokingInviteUserRoleIds.contains(inviteeUserData?.id),
                  isDisabled: state.revokingInviteUserRoleIds.isNotEmpty,
                  onPressed: () {},
                  text: "Pending",
                  buttonStyle: ButtonStyle(
                    padding: WidgetStateProperty.all(EdgeInsets.zero),
                    backgroundColor: WidgetStateProperty.all(Theme.of(context).customColors.white),
                    shape: WidgetStatePropertyAll(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10.r),
                        side: BorderSide(color: Theme.of(context).customColors.primaryColor),
                      ),
                    ),
                  ),
                  buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).customColors.primaryColor,
                      ),
                ),
        ],
      ),
    );
  }
}
