GetUserRolesModel deserializeGetUserRolesModel(Map<String, dynamic> json) => GetUserRolesModel.fromJson(json);

class GetUserRolesModel {
  bool? status;
  String? message;
  List<Data>? data;

  GetUserRolesModel({this.status, this.message, this.data});

  GetUserRolesModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  int? id;
  String? roleName;
  Map<String, String>? roleDescription;

  Data({this.id, this.roleName, this.roleDescription});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    roleName = json['role_name'];
    roleDescription =
        (json['role_description'] as Map<String, dynamic>?)?.map((key, value) => MapEntry(key, value.toString()));
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['role_name'] = roleName;
    if (roleDescription != null) {
      data['role_description'] = roleDescription;
    }
    return data;
  }
}
