GetInviteeUserModel deserializeGetInviteeUserModel(Map<String, dynamic> json) => GetInviteeUserModel.fromJson(json);

class GetInviteeUserModel {
  bool? status;
  String? message;
  List<GetInviteeUserData>? data;

  GetInviteeUserModel({this.status, this.message, this.data});

  GetInviteeUserModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <GetInviteeUserData>[];
      json['data'].forEach((v) {
        data!.add(GetInviteeUserData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class GetInviteeUserData {
  int? id;
  String? name;
  String? username;
  String? email;
  String? profileImage;
  bool? isAccepted;
  List<Brands>? brands;

  GetInviteeUserData({this.id, this.name, this.username, this.email, this.profileImage, this.isAccepted, this.brands});

  GetInviteeUserData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    username = json['username'];
    email = json['email'];
    profileImage = json['profile_image'];
    isAccepted = json['is_accepted'];
    if (json['brands'] != null) {
      brands = <Brands>[];
      json['brands'].forEach((v) {
        brands!.add(Brands.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['username'] = username;
    data['email'] = email;
    data['profile_image'] = profileImage;
    data['is_accepted'] = isAccepted;
    if (brands != null) {
      data['brands'] = brands!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Brands {
  int? id;
  String? name;
  String? logo;

  Brands({this.id, this.name, this.logo});

  Brands.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    logo = json['logo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['logo'] = logo;
    return data;
  }
}
