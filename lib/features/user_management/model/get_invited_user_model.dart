GetInvitedUserModel deserializeGetInvitedUserModel(Map<String, dynamic> json) => GetInvitedUserModel.fromJson(json);

class GetInvitedUserModel {
  bool? status;
  String? message;
  List<GetInvitedUserData>? data;

  GetInvitedUserModel({this.status, this.message, this.data});

  GetInvitedUserModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <GetInvitedUserData>[];
      json['data'].forEach((v) {
        data!.add(GetInvitedUserData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class GetInvitedUserData {
  int? id;
  String? name;
  String? username;
  String? email;
  String? profileImage;
  bool? isAccepted;
  List<Brands>? brands;

  GetInvitedUserData({this.id, this.name, this.username, this.email, this.profileImage, this.isAccepted, this.brands});

  GetInvitedUserData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    username = json['username'];
    email = json['email'];
    profileImage = json['profile_image'];
    isAccepted = json['is_accepted'];
    if (json['brands'] != null) {
      brands = <Brands>[];
      json['brands'].forEach((v) {
        brands!.add(Brands.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['username'] = username;
    data['email'] = email;
    data['profile_image'] = profileImage;
    data['is_accepted'] = isAccepted;
    if (brands != null) {
      data['brands'] = brands!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Brands {
  int? id;
  String? name;
  String? logo;

  Brands({this.id, this.name, this.logo});

  Brands.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    logo = json['logo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['logo'] = logo;
    return data;
  }
}
