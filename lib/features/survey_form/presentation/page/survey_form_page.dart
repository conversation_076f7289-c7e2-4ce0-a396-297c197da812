import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/survey_form/bloc/survey_bloc.dart';

class SurveyDialog extends StatefulWidget {
  const SurveyDialog({super.key});

  static Future<void> show(BuildContext context) {
    return showDialog(
      // barrierDismissible: false,
      context: context,
      builder: (context) => const SurveyDialog(),
    );
  }

  @override
  State<SurveyDialog> createState() => _SurveyDialogState();
}

class _SurveyDialogState extends State<SurveyDialog> {
  bool isUsingApp = false;
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  final TextEditingController nameController = TextEditingController();
  final TextEditingController appNameController = TextEditingController();
  final TextEditingController appDescriptionController = TextEditingController();

  @override
  initState() {
    super.initState();

    String name = Prefobj.preferences?.get(Prefkeys.USERNAME) ?? '';
    nameController.text = name;
  }

  @override
  void dispose() {
    nameController.dispose();
    appNameController.dispose();
    appDescriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SurveyBloc, SurveyState>(
      builder: (context, state) {
        return Form(
          key: formKey,
          child: Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: 400.w,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: Padding(
                padding: EdgeInsets.all(16.0.w),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // IconButton(onPressed: (){}, icon: Icon(Icons.close))
                    GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: Align(
                        alignment: Alignment.topRight,
                        child: Container(
                          padding: EdgeInsets.all(5),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(color: Theme.of(context).primaryColor, width: 1),
                          ),
                          child: Icon(
                            Icons.close_rounded,
                            size: 16.sp,
                          ),
                        ),
                      ),
                    ),
                    Flexible(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            _buildHeaderImage(),
                            _buildTitle(context),
                            buildSizedBoxH(20),
                            _buildTextField(
                              context,
                              "Your Name",
                              controller: nameController,
                              validator: AppValidations.validateUsername,
                            ),
                            buildSizedBoxH(20.0),
                            _buildRadioQuestion(context),
                            if (isUsingApp)
                              Column(
                                children: [
                                  buildSizedBoxH(10.0),
                                  _buildTextField(
                                    context,
                                    "App Name",
                                    controller: appNameController,
                                    validator: (value) => AppValidations.validateRequired(
                                      value,
                                      fieldName: 'App name',
                                    ),
                                  ),
                                  buildSizedBoxH(20.0),
                                  _buildTextField(
                                    context,
                                    "",
                                    controller: appDescriptionController,
                                    maxLines: 3,
                                    heint: "Tell us something about the app",
                                    validator: (value) => AppValidations.validateRequired(
                                      value,
                                      fieldName: 'Tell us something',
                                    ),
                                  ),
                                ],
                              ),
                            buildSizedBoxH(20.0),
                          ],
                        ),
                      ),
                    ),
                    Center(
                      child: CustomElevatedButton(
                        isDisabled: state.surveyLoading,
                        isLoading: state.surveyLoading,
                        text: "Submit",
                        onPressed: () {
                          if (formKey.currentState!.validate()) {
                            context.read<SurveyBloc>().add(
                                  SurveyformAPI(
                                      name: nameController.text.trim(),
                                      isappused: isUsingApp,
                                      appdiscription: appDescriptionController.text.trim(),
                                      appname: isUsingApp ? appNameController.text.trim() : "",
                                      isoptin: true,
                                      context: context),
                                );
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeaderImage() => Align(
        alignment: Alignment.center,
        child: CustomImageView(
          imagePath: Assets.images.svg.other.svgSurveyForm.path,
        ),
      );

  Widget _buildTitle(BuildContext context) => Align(
        alignment: Alignment.center,
        child: Text(
          "Survey",
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w800,
                fontSize: 18.sp,
              ),
        ),
      );

  Widget _buildTextField(
    BuildContext context,
    String label, {
    int maxLines = 1,
    String? heint,
    String? Function(String?)? validator,
    required TextEditingController controller,
  }) =>
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Text(
          //   label,
          //   style: Theme.of(context)
          //       .textTheme
          //       .bodyLarge
          //       ?.copyWith(fontSize: 14.sp),
          // ),
          // buildSizedBoxH(5.0),
          FlowkarTextFormField(
            labelText: label,
            hintText: heint,
            context: context,
            controller: controller,
            maxLines: maxLines,
            borderDecoration: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xffE0E0E0)),
              borderRadius: BorderRadius.circular(12.r),
            ),
            validator: validator,
          ),
        ],
      );

  Widget _buildRadioQuestion(BuildContext context) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Are you using a similar app?",
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontSize: 14.sp),
          ),
          _buildRadioOption("Yes", true),
          _buildRadioOption("No", false),
        ],
      );

  Widget _buildRadioOption(String text, bool value) => Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Radio(
            value: value,
            groupValue: isUsingApp,
            onChanged: (bool? newValue) {
              setState(() => isUsingApp = newValue ?? false);
            },
            activeColor: Colors.brown,
          ),
          Text(
            text,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontSize: 14.sp),
          ),
        ],
      );
}
