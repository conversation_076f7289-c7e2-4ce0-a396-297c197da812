import 'package:json_annotation/json_annotation.dart';

part 'survey_form_model.g.dart';

SurveyFormModel deserializeSurveyFormModel(Map<String, dynamic> json) =>
    SurveyFormModel.fromJson(json);

@JsonSerializable()
class SurveyFormModel {
  final bool? status;
  final String? message;
  @J<PERSON><PERSON>ey(name: 'is_opt_in')
  final bool? isOptIn;
  @JsonKey(name: 'user_status')
  final String? userStatus;

  SurveyFormModel({this.status, this.message, this.isOptIn, this.userStatus});

  factory SurveyFormModel.fromJson(Map<String, dynamic> json) =>
      _$SurveyFormModelFromJson(json);

  Map<String, dynamic> toJson() => _$SurveyFormModelToJson(this);
}
