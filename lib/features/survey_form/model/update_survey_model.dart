import 'package:json_annotation/json_annotation.dart';

part 'update_survey_model.g.dart';

UpdateSurveyModel deserializeUpdateSurveyModel(Map<String, dynamic> json) => UpdateSurveyModel.fromJson(json);

@JsonSerializable()
class UpdateSurveyModel {
  bool? status;
  String? message;
  @Json<PERSON>ey(name: 'is_opt_in')
  bool? isOptIn;

  UpdateSurveyModel({this.status, this.message, this.isOptIn});

  factory UpdateSurveyModel.fromJson(Map<String, dynamic> json) => _$UpdateSurveyModelFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateSurveyModelToJson(this);
}
