import 'package:json_annotation/json_annotation.dart';

part 'user_home_data_model.g.dart';

UserHomeDataModel deserializeUserHomeDataModel(Map<String, dynamic> json) => UserHomeDataModel.fromJson(json);

@JsonSerializable()
class UserHomeDataModel {
  bool? status;
  String? message;

  @Json<PERSON>ey(name: 'user_id')
  int? userId;

  @Json<PERSON>ey(name: 'subscription_id')
  int? subscriptionId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_beta_tester')
  bool? isbetatester;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_opt_in')
  bool? isOptIn;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_status')
  String? userStatus;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_type')
  String? usertype;

  @Json<PERSON>ey(name: 'username')
  String? username;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'email')
  String? email;

  UserHomeDataModel({
    this.status,
    this.message,
    this.userId,
    this.subscriptionId,
    this.isbetatester,
    this.isOptIn,
    this.userStatus,
    this.usertype,
    this.username,
    this.email,
  });

  factory UserHomeDataModel.fromJson(Map<String, dynamic> json) => _$UserHomeDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserHomeDataModelToJson(this);

  UserHomeDataModel copyWith({
    bool? status,
    String? message,
    int? userId,
    int? subscriptionId,
    bool? isbetatester,
    bool? isOptIn,
    String? userStatus,
    String? usertype,
    String? username,
    String? email,
  }) {
    return UserHomeDataModel(
      status: status ?? this.status,
      message: message ?? this.message,
      userId: userId ?? this.userId,
      subscriptionId: subscriptionId ?? this.subscriptionId,
      isbetatester: isbetatester ?? this.isbetatester,
      isOptIn: isOptIn ?? this.isOptIn,
      userStatus: userStatus ?? this.userStatus,
      usertype: usertype ?? this.usertype,
      username: username ?? this.username,
      email: email ?? this.email,
    );
  }
}
