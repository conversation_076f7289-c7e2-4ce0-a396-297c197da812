part of 'survey_bloc.dart';

class SurveyState extends Equatable {
  const SurveyState({
    this.surveyLoading = false,
    this.subscriptionLoading = false,
    this.surveyformModel,
    this.surveySuccess = false,
    this.userHomeDataModel,
    this.updateSurveyModel,
    this.updateSurveyLoading = false,
  });

  final bool surveyLoading;
  final bool subscriptionLoading;
  final SurveyFormModel? surveyformModel;
  final bool surveySuccess;
  final UserHomeDataModel? userHomeDataModel;
  final UpdateSurveyModel? updateSurveyModel;
  final bool updateSurveyLoading;

  @override
  List<Object?> get props => [
        surveyLoading,
        surveyformModel,
        subscriptionLoading,
        surveySuccess,
        userHomeDataModel,
        updateSurveyModel,
        updateSurveyLoading,
      ];

  SurveyState copyWith({
    bool? surveyLoading,
    SurveyFormModel? surveyformModel,
    bool? subscriptionLoading,
    bool? surveySuccess,
    bool? isOptInLoding,
    UserHomeDataModel? userHomeDataModel,
    UpdateSurveyModel? updateSurveyModel,
    bool? updateSurveyLoading,
  }) {
    return SurveyState(
      surveyLoading: surveyLoading ?? this.surveyLoading,
      surveyformModel: surveyformModel ?? this.surveyformModel,
      subscriptionLoading: subscriptionLoading ?? this.subscriptionLoading,
      surveySuccess: surveySuccess ?? this.surveySuccess,
      userHomeDataModel: userHomeDataModel ?? this.userHomeDataModel,
      updateSurveyModel: updateSurveyModel ?? this.updateSurveyModel,
      updateSurveyLoading: updateSurveyLoading ?? this.updateSurveyLoading,
    );
  }
}
