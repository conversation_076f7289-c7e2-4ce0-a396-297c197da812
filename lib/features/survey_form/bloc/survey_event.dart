part of 'survey_bloc.dart';

sealed class SurveyEvent extends Equatable {
  const SurveyEvent();

  @override
  List<Object> get props => [];
}

class SurveyformAPI extends SurveyEvent {
  final String name;
  final bool isappused;
  final String appdiscription;
  final String appname;
  final bool isoptin;
  final BuildContext? context;

  const SurveyformAPI(
      {required this.name,
      required this.isappused,
      required this.appdiscription,
      required this.appname,
      required this.isoptin,
      this.context});
  @override
  List<Object> get props => [
        name,
        isappused,
        appdiscription,
        appname,
        isoptin,
      ];
}

class UserHomeDataAPI extends SurveyEvent {
  final BuildContext? context;
  const UserHomeDataAPI({this.context});
  @override
  List<Object> get props => [];
}

class UpdateSurveyAPI extends SurveyEvent {
  final bool isoptin;
  final BuildContext? context;
  const UpdateSurveyAPI({required this.isoptin, this.context});
  @override
  List<Object> get props => [isoptin, if (context != null) context!];
}
