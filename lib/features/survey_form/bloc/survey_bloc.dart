import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/survey_form/model/survey_form_model.dart';
import 'package:flowkar/features/survey_form/model/update_survey_model.dart';
import 'package:flowkar/features/survey_form/model/user_home_data_model.dart';
part 'survey_event.dart';
part 'survey_state.dart';

class SurveyBloc extends Bloc<SurveyEvent, SurveyState> {
  ApiClient apiClient = ApiClient(Dio());
  SurveyBloc(super.initialState) {
    on<SurveyformAPI>(_sureveyFormApi);
    on<UserHomeDataAPI>(_getUserHomeDataApi);
    on<UpdateSurveyAPI>(_updatesurveyApi);
  }
  Future<void> _sureveyFormApi(SurveyformAPI event, Emitter<SurveyState> emit) async {
    emit(state.copyWith(surveyLoading: true));
    try {
      String loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.surveyFormApi(
          loginUserId,
          {
            "name": event.name,
            "is_app_used": event.isappused,
            "app_description": event.appdiscription,
            "app_name": event.appname,
            "is_opt_in": event.isoptin
          },
          brandId.toString());

      if (result.status == true) {
        await Prefobj.preferences?.put(Prefkeys.STATUS, result.userStatus);
        emit(state.copyWith(surveyformModel: result));
        emit(state.copyWith(surveyLoading: false));
        NavigatorService.goBack();
        await _getUserHomeDataApi(UserHomeDataAPI(context: event.context), emit);
      } else {
        emit(state.copyWith(surveyLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(surveyLoading: false));
      Logger.lOG("surveyFormApi Error: $error");
    }
  }

  Future<void> _getUserHomeDataApi(UserHomeDataAPI event, Emitter<SurveyState> emit) async {
    try {
      String loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.getuserHomeDataApi(
        logInUserId: loginUserId,
        brandid: brandId.toString(),
      );

      if (result.status == true) {
        // Batch storage operations to reduce I/O overhead
        await OptimizedStorage.batchPut({
          Prefkeys.SUBSCRIPTION: result.subscriptionId.toString(),
          Prefkeys.STATUS: result.userStatus,
          Prefkeys.USERTYPE: result.usertype,
          Prefkeys.USERNAME: result.username,
          Prefkeys.USEREMAIL: result.email,
          Prefkeys.ISOPTIN: result.isOptIn,
        });

        emit(state.copyWith(surveySuccess: result.isOptIn, userHomeDataModel: result));
        // if (result.isOptIn == false) {
        //   return Future.delayed(
        //     Duration(milliseconds: 500),
        //     () {
        //       // if (Navigator.canPop(event.context!)) {
        //       //   Navigator.pop(event.context!);
        //       // }

        //       isDialogShowing == true
        //           ? null
        //           : showDialog(
        //               context: event.context!,
        //               builder: (ctx) {
        //                 return CustomAlertDialog(
        //                     width: 380.w,
        //                     isredius: true,
        //                     imageheight: 45.h,
        //                     imagewidth: 45.w,
        //                     fit: BoxFit.contain,
        //                     imagePath: Assets.images.svg.homeFeed.svgSubscription.path,
        //                     title: Lang.of(ctx).lbl_subscription,
        //                     subtitle: "",
        //                     onConfirmButtonPressed: () async {
        //                       NavigatorService.goBack();

        //                       await _updatesurveyApi(UpdateSurveyAPI(isoptin: true, context: ctx), emit);
        //                     },
        //                     confirmButtonText: "Yes",
        //                     cancelButtonText: "No",
        //                     isLoading: false,
        //                     child: Column(
        //                       mainAxisSize: MainAxisSize.min,
        //                       children: [
        //                         Text(
        //                           'Do you want to apply for our',
        //                           style: Theme.of(event.context!).textTheme.bodyMedium?.copyWith(
        //                               fontWeight: FontWeight.w400,
        //                               fontSize: 14.sp,
        //                               color: Theme.of(event.context!).iconTheme.color),
        //                           textAlign: TextAlign.center,
        //                         ),
        //                         SizedBox(height: 5),

        //                         // Highlighted text
        //                         Text(
        //                           '3 Months Free Premium Subscription',
        //                           textAlign: TextAlign.center,
        //                           style: Theme.of(event.context!).textTheme.bodyMedium?.copyWith(
        //                                 fontWeight: FontWeight.w600,
        //                                 fontSize: 14.sp,
        //                                 color: Theme.of(event.context!).iconTheme.color,
        //                               ),
        //                         ),
        //                         SizedBox(height: 5),

        //                         // Highlighted text
        //                         Text(
        //                           'which will let you use all Premium features.',
        //                           textAlign: TextAlign.center,
        //                           style: Theme.of(event.context!).textTheme.bodyMedium?.copyWith(
        //                                 fontWeight: FontWeight.w400,
        //                                 fontSize: 14.sp,
        //                                 color: Theme.of(event.context!).iconTheme.color,
        //                               ),
        //                         ),
        //                         SizedBox(height: 16),
        //                       ],
        //                     ));
        //               },
        //             );
        //     },
        //   );
        // }
      }
    } catch (error) {
      Logger.lOG("_getUserHomeDataApi Error: $error");
    }
  }

  void showSubscriptionDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0),
          ),
          child: Container(
            padding: EdgeInsets.all(20),
            width: 300,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Subscription icon
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.card_membership,
                    size: 40,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: 20),

                // Title
                Text(
                  'Subscription',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 15),

                // Description
                Text(
                  'Do you want to apply for our',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: 5),

                // Highlighted text
                Text(
                  '3 Months Free Premium Subscription',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: 20),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // No button
                    Expanded(
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey.shade200,
                          foregroundColor: Colors.black,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        onPressed: () {
                          Navigator.of(context).pop();
                          context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
                        },
                        child: Padding(
                          padding: EdgeInsets.symmetric(vertical: 12),
                          child: Text('No'),
                        ),
                      ),
                    ),
                    SizedBox(width: 10),

                    // Yes button
                    Expanded(
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.black,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        onPressed: () {
                          // Handle subscription acceptance
                          Navigator.of(context).pop();
                          context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
                          // Add your subscription logic here
                        },
                        child: Padding(
                          padding: EdgeInsets.symmetric(vertical: 12),
                          child: Text('Yes'),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _updatesurveyApi(UpdateSurveyAPI event, Emitter<SurveyState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      String? brandId = Prefobj.preferences?.get(Prefkeys.BRANDID)?.toString() ?? '';

      emit(state.copyWith(updateSurveyLoading: true));

      final result = await apiClient.updatesurveyApi(loginuserId, brandId.toString(), {"is_opt_in": event.isoptin});

      if (result.status == true) {
        String loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? '';

        await Prefobj.preferences?.put(Prefkeys.ISOPTIN, result.isOptIn);
        int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

        final userHomeData = await apiClient.getuserHomeDataApi(logInUserId: loginUserId, brandid: brandId.toString());
        if (userHomeData.status == true) {
          // Batch storage operations for user home data
          await OptimizedStorage.batchPut({
            Prefkeys.SUBSCRIPTION: userHomeData.subscriptionId.toString(),
            Prefkeys.STATUS: userHomeData.userStatus,
            Prefkeys.USERNAME: userHomeData.username,
            Prefkeys.ISOPTIN: result.isOptIn,
          });

          Logger.lOG("USERNAME ${userHomeData.username}");
          Logger.lOG("STATUS ${userHomeData.userStatus}");
          Logger.lOG("SUBSCRIPTION ${userHomeData.subscriptionId}");
          Logger.lOG("ISOPTIN ${result.isOptIn}");
        }

        emit(state.copyWith(updateSurveyModel: result, userHomeDataModel: userHomeData, updateSurveyLoading: false));

        // await _getUserHomeDataApi(UserHomeDataAPI(context: event.context), emit);
      } else {
        Logger.lOG("surveyFormApi Error");
      }
      emit(state.copyWith(updateSurveyLoading: false));
    } catch (error) {
      Logger.lOG("surveyFormApi Error: $error");
    }
  }
}
