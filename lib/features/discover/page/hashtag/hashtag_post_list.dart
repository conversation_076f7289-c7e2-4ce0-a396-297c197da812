import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/discover/model/hashtag_post_model.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/comment_bottom_sheet_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class HashTagPostList extends StatefulWidget {
  final int? hashID;
  final List<HashtagPostData>? posts;
  final int? initialIndex;
  const HashTagPostList({super.key, this.hashID, this.posts, this.initialIndex});

  @override
  State<HashTagPostList> createState() => _HashTagPostListState();
}

class _HashTagPostListState extends State<HashTagPostList> {
  late ItemScrollController _itemScrollController;
  late ItemPositionsListener _itemPositionsListener;

  @override
  void initState() {
    super.initState();
    _itemScrollController = ItemScrollController();
    _itemPositionsListener = ItemPositionsListener.create();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        hasLeadingIcon: true,
        height: 18.h,
        leading: [
          InkWell(
            onTap: () {
              PersistentNavBarNavigator.pop(context);
            },
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: CustomImageView(
                imagePath: Assets.images.svg.authentication.icBackArrow.path,
                height: 16.h,
              ),
            ),
          ),
          buildSizedBoxW(20.w),
          Text(
            "Posts",
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
          ),
        ],
      ),
      body: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themestate) {
          return BlocBuilder<HomeFeedBloc, HomeFeedState>(
            builder: (context, state) {
              return Scaffold(
                body: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(child: _buildFlowkarFeed(state)),
                    BlocBuilder<ConnectivityBloc, ConnectivityState>(
                      builder: (context, connectivityState) {
                        return Visibility(
                          visible: state.isLoadingMore && connectivityState.isConnected,
                          child: SizedBox(
                            height: 50.h,
                            child:
                                Center(child: CupertinoActivityIndicator(color: Theme.of(context).colorScheme.primary)),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  Future<void> _refreshFeed() async {
    final state = context.read<HomeFeedBloc>().state;
    if (state.getProfilePost.isNotEmpty) {
      state.getProfilePost.clear();
    }
    context.read<HomeFeedBloc>().add(DiscoverHashtagPostListEvent(hashtagId: widget.hashID ?? 0));
  }

  Widget _buildFlowkarFeed(HomeFeedState state) {
    return LiquidPullToRefresh(
      color: Theme.of(context).primaryColor.withOpacity(0.5),
      showChildOpacityTransition: false,
      backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      onRefresh: _refreshFeed,
      child: state.isdiscoverloding
          ? LoadingAnimationWidget()
          : ScrollablePositionedList.builder(
              padding: EdgeInsets.symmetric(vertical: 8.0.h),
              itemCount: state.hashtagPostData.length,
              itemScrollController: _itemScrollController,
              itemPositionsListener: _itemPositionsListener,
              physics: const ClampingScrollPhysics(),
              itemBuilder: (context, index) {
                final postIndex = index;
                final post = state.hashtagPostData[postIndex];
                return PostWidget(
                  width: post.width ?? 0,
                  height: post.height ?? 0,
                  userByIDpost: false,
                  userByIDvideo: false,
                  userVideo: false,
                  userpost: false,
                  isPost: true,
                  state: state,
                  index: index,
                  userId: post.user?.userId ?? 0,
                  latestcomments: post.latestComment.toString(),
                  postId: post.id ?? 0,
                  profileImage: post.user?.profileImage.toString() ?? "",
                  name: post.user?.name ?? "",
                  username: post.user?.username ?? "",
                  postMedia: post.files ?? [],
                  thumbnailImage: [],
                  title: post.title ?? "",
                  caption:
                      "${post.title == "''" || post.title!.isEmpty ? '' : post.title ?? ''}${post.description == null || post.description!.isEmpty ? '' : "\n${post.description ?? ''}"}",
                  likes: post.likes.toString(),
                  comments: post.commentsCount.toString(),
                  postTime: post.createdAt ?? "",
                  isLiked: post.isLiked ?? false,
                  isSaved: post.isSaved ?? false,
                  taggedIn: post.taggedIn,
                  doubleTap: () {
                    if (post.isLiked == false) {
                      context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: post.id ?? 0));
                    }
                  },
                  likeonTap: () {
                    context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: post.id ?? 0));
                  },
                  commentonTap: () {
                    showModalBottomSheet(
                      context: context,
                      useRootNavigator: true,
                      isScrollControlled: true,
                      builder: (context) => CommentsBottomSheet(postId: post.id ?? 0),
                    );
                  },
                  shareonTap: () {},
                  saveonTap: () {
                    NavigatorService.goBack();
                    context.read<HomeFeedBloc>().add(SavedPostSocketEvent(
                          postId: post.id.toString(),
                        ));
                  },
                );
              },
            ),
    );
  }

  void showCommentsBottomSheet(BuildContext context, int postId) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return CommentsBottomSheet(
          postId: postId,
        );
      },
    );
  }
}
