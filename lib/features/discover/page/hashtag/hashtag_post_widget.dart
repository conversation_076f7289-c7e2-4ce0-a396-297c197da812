import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/discover/model/hashtag_search_model.dart';
import 'package:flowkar/features/discover/page/hashtag/hashtag_post_list.dart';
import 'package:flowkar/features/discover/widget/discover_page_shimmer.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class HashtagPostWidget extends StatefulWidget {
  final HashtagData? hashtagData;
  const HashtagPostWidget({super.key, required bool stackonScreen, required this.hashtagData});

  @override
  State<HashtagPostWidget> createState() => _HashtagPostWidgetState();
}

class _HashtagPostWidgetState extends State<HashtagPostWidget> {
  @override
  void initState() {
    context.read<HomeFeedBloc>().add(DiscoverHashtagPostListEvent(hashtagId: widget.hashtagData?.id ?? 0));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themestate) {
        return BlocBuilder<HomeFeedBloc, HomeFeedState>(
          builder: (context, state) {
            return NotificationListener<ScrollNotification>(
              onNotification: (ScrollNotification scrollInfo) {
                return false;
              },
              child: Scaffold(
                appBar: CustomAppbar(
                  hasLeadingIcon: true,
                  height: 18.h,
                  leading: [
                    InkWell(
                      onTap: () {
                        PersistentNavBarNavigator.pop(context);
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: CustomImageView(
                          imagePath: Assets.images.svg.authentication.icBackArrow.path,
                          height: 16.h,
                        ),
                      ),
                    ),
                    buildSizedBoxW(20.w),
                    Text(
                      widget.hashtagData?.name ?? "",
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
                    ),
                  ],
                ),
                body: BlocListener<ConnectivityBloc, ConnectivityState>(
                  listener: (context, state) {
                    if (state.isReconnected) {
                      context
                          .read<HomeFeedBloc>()
                          .add(DiscoverHashtagPostListEvent(hashtagId: widget.hashtagData?.id ?? 0));
                    }
                  },
                  child: Stack(
                    alignment: Alignment.bottomCenter,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
                        child: BlocBuilder<ConnectivityBloc, ConnectivityState>(
                          builder: (context, connectivityState) {
                            if (!connectivityState.isConnected && state.hashtagPostData.isEmpty) {
                              return DiscoverPageShimmer();
                            } else if (state.isloding) {
                              return DiscoverPageShimmer();
                            } else if (state.hashtagPostData.isEmpty) {
                              return ListView(
                                physics: AlwaysScrollableScrollPhysics(),
                                children: [
                                  buildSizedBoxH(MediaQuery.of(context).size.height / 6),
                                  ExceptionWidget(
                                    imagePath: Assets.images.pngs.exception.pngNoDataFound.path,
                                    showButton: false,
                                    title: Lang.of(context).lbl_data_not_found,
                                    subtitle: Lang.of(context).lbl_we_could_not,
                                  ),
                                ],
                              );
                            } else {
                              return StaggeredGridView.countBuilder(
                                padding: EdgeInsets.only(bottom: 20.h),
                                shrinkWrap: true,
                                crossAxisCount: 2,
                                mainAxisSpacing: 2.0,
                                crossAxisSpacing: 2.0,
                                staggeredTileBuilder: (int index) {
                                  return StaggeredTile.count(1, index.isEven ? 1.3 : 1);
                                },
                                itemCount: state.hashtagPostData.length,
                                itemBuilder: (context, index) {
                                  final filePath = (state.hashtagPostData[index].files?.first != null &&
                                          state.hashtagPostData[index].files!.first.isNotEmpty)
                                      ? state.hashtagPostData[index].files?.first
                                      : Assets.images.pngs.other.pngPlaceholder.path;

                                  return Container(
                                    clipBehavior: Clip.antiAlias,
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(18.0.r),
                                        border: Border.all(
                                            color: Theme.of(context).primaryColor.withOpacity(0.1), width: 2)),
                                    child: GestureDetector(
                                      onTap: () {
                                        PersistentNavBarNavigator.pushNewScreen(
                                          context,
                                          screen: HashTagPostList(
                                              hashID: widget.hashtagData?.id ?? 0,
                                              initialIndex: index,
                                              posts: state.hashtagPostData),
                                          withNavBar: false,
                                        );
                                      },
                                      child: isVideo(filePath ?? "")
                                          ? state.hashtagPostData[index].thumbailFiles!.isNotEmpty
                                              ? Stack(
                                                  fit: StackFit.expand,
                                                  children: [
                                                    CustomImageView(
                                                      width: double.infinity,
                                                      imagePath: state.hashtagPostData[index].thumbailFiles?.first,
                                                      fit: BoxFit.cover,
                                                      radius: BorderRadius.circular(16.0.r),
                                                    ),
                                                    Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: [
                                                        CustomImageView(
                                                          margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                                                          imagePath: Assets.images.svg.other.svgPlayIconWhite.path,
                                                          color: Colors.white70,
                                                          height: 30.h,
                                                          width: 30.w,
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                )
                                              : Container(
                                                  decoration: BoxDecoration(
                                                    color: Colors.grey.shade300,
                                                    image: DecorationImage(
                                                      image: AssetImage(Assets.images.pngs.other.pngPlaceholder.path),
                                                    ),
                                                    borderRadius: BorderRadius.circular(16.0.r),
                                                  ),
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Padding(
                                                        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                                                        child: CustomImageView(
                                                          height: 30.h,
                                                          width: 30.w,
                                                          imagePath: Assets.images.svg.other.icPlayVideo.path,
                                                          radius: BorderRadius.circular(16.0.r),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                )
                                          : CustomImageView(
                                              imagePath: filePath,
                                              fit: BoxFit.cover,
                                              radius: BorderRadius.circular(16.0.r),
                                            ),
                                    ),
                                  );
                                },
                              );
                            }
                          },
                        ),
                      ),
                      BlocBuilder<ConnectivityBloc, ConnectivityState>(
                        builder: (context, connectivityState) {
                          return Visibility(
                            visible: state.isLoadingMore && connectivityState.isConnected,
                            child: SizedBox(
                              height: 50.h,
                              child: Center(
                                  child: CupertinoActivityIndicator(color: Theme.of(context).colorScheme.primary)),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
