part of 'discover_bloc.dart';

abstract class DiscoverEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class DiscoverInitialEvent extends DiscoverEvent {
  @override
  List<Object> get props => [];
}

class SearchQueryChanged extends DiscoverEvent {
  final String query;
  SearchQueryChanged(this.query);

  @override
  List<Object?> get props => [query];
}

class TabChanged extends DiscoverEvent {
  final int index;
  TabChanged(this.index);

  @override
  List<Object?> get props => [index];
}

class ClearSearchHistory extends DiscoverEvent {}

class RemoveSearchItem extends DiscoverEvent {
  final int index;
  RemoveSearchItem(this.index);

  @override
  List<Object?> get props => [index];
}

class DiscoverSearchUserListEvent extends DiscoverEvent {
  final String searchtext;
  DiscoverSearchUserListEvent({required this.searchtext});
  @override
  List<Object> get props => [searchtext];
}

class InviteeSearchUserListEvent extends DiscoverEvent {
  final String searchtext;
  InviteeSearchUserListEvent({required this.searchtext});
  @override
  List<Object> get props => [searchtext];
}

class DiscoverHashtagListEvent extends DiscoverEvent {
  final String searchtext;
  DiscoverHashtagListEvent({required this.searchtext});
  @override
  List<Object> get props => [searchtext];
}
