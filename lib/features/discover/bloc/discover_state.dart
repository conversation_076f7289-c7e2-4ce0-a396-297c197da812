part of 'discover_bloc.dart';

class DiscoverState extends Equatable {
  final String searchQuery;
  final List<Map<String, dynamic>> searchHistory;
  final bool isLoadingMore;
  final bool isloding;
  final TextEditingController? searchController;
  final int selectedTabIndex;
  final List<SearchUserData>? searchuserList;
  final List<SearchUserData>? inviteesearchuserList;
  final List<HashtagData>? hashtagData;
  final HashtagPostModel? hashtagPostModel;
  final List<HashtagPostData> hashtagPostData;
  final bool isSearchLoading;

  const DiscoverState({
    this.searchQuery = '',
    this.inviteesearchuserList = const [],
    this.searchHistory = const [],
    this.selectedTabIndex = 0,
    this.searchuserList = const [],
    this.hashtagData = const [],
    this.isLoadingMore = false,
    this.isloding = false,
    this.searchController,
    this.hashtagPostModel,
    this.hashtagPostData = const [],
    this.isSearchLoading = false,
  });

  DiscoverState copyWith({
    String? searchQuery,
    List<Map<String, dynamic>>? searchHistory,
    int? selectedTabIndex,
    List<SearchUserData>? searchuserList,
    List<HashtagData>? hashtagData,
    TextEditingController? searchController,
    bool? isLoadingMore,
    bool? isloding,
    List<SearchUserData>? inviteesearchuserList,
    bool? hasMore,
    int? page,
    bool? allFetch,
    List<PostData>? posts,
    PostResponseModel? getAllPostModel,
    HashtagPostModel? hashtagPostModel,
    List<HashtagPostData>? hashtagPostData,
    bool? isSearchLoading,
  }) {
    return DiscoverState(
      searchQuery: searchQuery ?? this.searchQuery,
      searchHistory: searchHistory ?? this.searchHistory,
      selectedTabIndex: selectedTabIndex ?? this.selectedTabIndex,
      searchuserList: searchuserList ?? this.searchuserList,
      hashtagData: hashtagData ?? this.hashtagData,
      isloding: isloding ?? this.isloding,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      inviteesearchuserList: inviteesearchuserList ?? this.inviteesearchuserList,
      searchController: searchController ?? this.searchController,
      hashtagPostModel: hashtagPostModel ?? this.hashtagPostModel,
      hashtagPostData: hashtagPostData ?? this.hashtagPostData,
      isSearchLoading: isSearchLoading ?? this.isSearchLoading,
    );
  }

  @override
  List<Object?> get props => [
        searchQuery,
        searchHistory,
        selectedTabIndex,
        searchuserList,
        hashtagData,
        isLoadingMore,
        isloding,
        searchController,
        inviteesearchuserList,
        hashtagPostData,
        hashtagPostModel,
        isSearchLoading,
      ];
}
