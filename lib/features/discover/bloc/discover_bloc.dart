import 'dart:async';
import 'package:flowkar/core/socket/socket_service.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/discover/model/hashtag_post_model.dart';
import 'package:flowkar/features/discover/model/hashtag_search_model.dart';
import 'package:flowkar/features/discover/model/search_user_model.dart';
import 'package:flowkar/features/home_feed_screen/model/post_response_model.dart';
import 'package:stream_transform/stream_transform.dart';

part 'discover_event.dart';
part 'discover_state.dart';

const _duration = Duration(milliseconds: 300);
EventTransformer<Event> debounce<Event>(Duration duration) {
  return (events, mapper) => events.debounce(duration).switchMap(mapper);
}

class DiscoverBloc extends Bloc<DiscoverEvent, DiscoverState> {
  ApiClient apiClient = ApiClient(Dio());

  DiscoverBloc() : super(const DiscoverState()) {
    on<DiscoverInitialEvent>(_onInitialize);
    on<SearchQueryChanged>(_onSearchQueryChanged);
    on<TabChanged>(_onTabChanged);
    on<ClearSearchHistory>(_onClearSearchHistory);
    on<RemoveSearchItem>(_onRemoveSearchItem);
    on<DiscoverSearchUserListEvent>(_onUserSearchEvent, transformer: debounce(_duration));
    on<InviteeSearchUserListEvent>(_inviteeSearchUserListEvent, transformer: debounce(_duration));
    on<DiscoverHashtagListEvent>(_onhashtagearchEvent);
  }
  _onInitialize(DiscoverInitialEvent event, Emitter<DiscoverState> emit) {
    emit(state.copyWith(
      allFetch: false,
      searchuserList: [],
      posts: [],
      hasMore: true,
      isLoadingMore: false,
      isloding: false,
      hashtagData: [],
    ));
  }

  void _onSearchQueryChanged(SearchQueryChanged event, Emitter<DiscoverState> emit) {
    emit(state.copyWith(searchQuery: event.query));
  }

  void _onTabChanged(TabChanged event, Emitter<DiscoverState> emit) {
    emit(state.copyWith(selectedTabIndex: event.index));
  }

  void _onClearSearchHistory(ClearSearchHistory event, Emitter<DiscoverState> emit) {
    emit(state.copyWith(searchHistory: []));
  }

  void _onRemoveSearchItem(RemoveSearchItem event, Emitter<DiscoverState> emit) {
    final newHistory = List<Map<String, dynamic>>.from(state.searchHistory);
    newHistory.removeAt(event.index);
    emit(state.copyWith(searchHistory: newHistory));
  }

  _onUserSearchEvent(DiscoverSearchUserListEvent event, Emitter<DiscoverState> emit) async {
    emit(state.copyWith(isSearchLoading: true));
    try {
      final completer = Completer<Map<String, dynamic>>();
      SocketService.emit(APIConfig.searchuser, {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'search_text': event.searchtext,
      });
      bool isCompleted = false;

      SocketService.response(
        APIConfig.searchuser,
        (response) {
          if (!isCompleted) {
            completer.complete(response);
            isCompleted = true;
          }
        },
      );
      final response = await completer.future;
      if (response['data'] == null) {
        emit(state.copyWith(searchuserList: [], isSearchLoading: false));
      }
      final data = response['data'] as List<dynamic>;
      List<SearchUserData> allUsers = data.map((item) {
        if (item is Map<String, dynamic>) {
          return SearchUserData(
            userId: item['id'],
            name: item['name'],
            userName: item['username'],
            profileImage: item['profile'],
          );
        }
        throw Exception('Invalid data format');
      }).toList();
      final searchText = event.searchtext.toLowerCase().trim();

      if (searchText.isEmpty) {
      } else {
        final filteredUsers = allUsers.where((user) {
          final name = user.name?.toLowerCase().trim() ?? '';
          final username = user.userName?.toLowerCase().trim() ?? '';
          return name.contains(searchText) || username.contains(searchText);
        }).toList();
        Logger.lOG(filteredUsers);
        emit(state.copyWith(searchuserList: filteredUsers, isSearchLoading: false));
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isSearchLoading: false));
    }
  }

  _inviteeSearchUserListEvent(InviteeSearchUserListEvent event, Emitter<DiscoverState> emit) async {
    emit(state.copyWith(isSearchLoading: true));
    try {
      final completer = Completer<Map<String, dynamic>>();
      SocketService.emit(APIConfig.searchuser, {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'search_text': event.searchtext,
      });
      bool isCompleted = false;

      SocketService.response(
        APIConfig.searchuser,
        (response) {
          if (!isCompleted) {
            completer.complete(response);
            isCompleted = true;
          }
        },
      );
      final response = await completer.future;
      if (response['data'] == null) {
        emit(state.copyWith(inviteesearchuserList: [], isSearchLoading: false));
      }
      final data = response['data'] as List<dynamic>;
      List<SearchUserData> allUsers = data.map((item) {
        if (item is Map<String, dynamic>) {
          return SearchUserData(
            userId: item['id'],
            name: item['name'],
            userName: item['username'],
            profileImage: item['profile'],
          );
        }
        throw Exception('Invalid data format');
      }).toList();
      final searchText = event.searchtext.toLowerCase().trim();

      if (searchText.isEmpty) {
      } else {
        final filteredUsers = allUsers.where((user) {
          final name = user.name?.toLowerCase().trim() ?? '';
          final username = user.userName?.toLowerCase().trim() ?? '';
          return name.contains(searchText) || username.contains(searchText);
        }).toList();
        Logger.lOG(filteredUsers);
        emit(state.copyWith(inviteesearchuserList: filteredUsers, isSearchLoading: false));
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isSearchLoading: false));
    }
  }

  _onhashtagearchEvent(DiscoverHashtagListEvent event, Emitter<DiscoverState> emit) async {
    emit(state.copyWith(isSearchLoading: true));
    try {
      final completer = Completer<Map<String, dynamic>>();
      SocketService.emit(APIConfig.searchhashtag, {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'search_text': event.searchtext,
      });
      bool isCompleted = false;

      SocketService.response(
        APIConfig.searchhashtag,
        (response) {
          if (!isCompleted) {
            completer.complete(response);
            isCompleted = true;
          }
        },
      );
      final response = await completer.future;
      if (response['data'] == null) {
        emit(state.copyWith(hashtagData: [], isSearchLoading: false));
      }
      final data = response['data'] as List<dynamic>;
      List<HashtagData> allUsers = data.map((item) {
        if (item is Map<String, dynamic>) {
          return HashtagData(
            id: item['id'],
            name: item['name'],
            posts: item['posts'],
          );
        }
        throw Exception('Invalid data format');
      }).toList();
      final searchText = event.searchtext.toLowerCase();

      if (searchText.isEmpty) {
      } else {
        final filteredUsers = allUsers.where((user) {
          return user.name?.toLowerCase().contains(searchText) ?? false;
        }).toList();
        Logger.lOG(filteredUsers);
        emit(state.copyWith(hashtagData: filteredUsers, isSearchLoading: false));
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isSearchLoading: false));
    }
  }
}
