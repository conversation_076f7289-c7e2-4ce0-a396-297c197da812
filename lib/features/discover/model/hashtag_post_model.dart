HashtagPostModel deserializeHashtagPostModel(Map<String, dynamic> json) => HashtagPostModel.fromJson(json);

class HashtagPostModel {
  int? count;
  String? next;
  String? previous;
  Results? results;

  HashtagPostModel({this.count, this.next, this.previous, this.results});

  HashtagPostModel.fromJson(Map<String, dynamic> json) {
    count = json['count'];
    next = json['next'];
    previous = json['previous'];
    results = json['results'] != null ? Results.fromJson(json['results']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['count'] = count;
    data['next'] = next;
    data['previous'] = previous;
    if (results != null) {
      data['results'] = results!.toJson();
    }
    return data;
  }
}

class Results {
  bool? status;
  String? message;
  List<HashtagPostData>? data;

  Results({this.status, this.message, this.data});

  Results.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <HashtagPostData>[];
      json['data'].forEach((v) {
        data!.add(HashtagPostData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class HashtagPostData {
  int? id;
  String? title;
  String? description;
  String? location;
  int? likes;
  int? dislikes;
  int? commentsCount;
  List<dynamic>? taggedIn;
  String? createdAt;
  List<String>? files;
  int? width;
  int? height;
  List<String>? thumbailFiles;
  String? latestComment;
  User? user;
  bool? isLiked;
  bool? isSaved;

  HashtagPostData({
    this.id,
    this.title,
    this.description,
    this.location,
    this.likes,
    this.dislikes,
    this.commentsCount,
    this.taggedIn,
    this.createdAt,
    this.files,
    this.thumbailFiles,
    this.latestComment,
    this.user,
    this.isLiked,
    this.isSaved,
    this.width,
    this.height,
  });

  HashtagPostData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    description = json['description'];
    location = json['location'];
    likes = json['likes'];
    dislikes = json['dislikes'];
    commentsCount = json['comments_count'];
    taggedIn = json['tagged_in'] != null ? List<dynamic>.from(json['tagged_in']) : [];
    createdAt = json['created_at'];
    files = json['files'].cast<String>();
    width = json['width'];
    height = json['height'];
    thumbailFiles = json['thumbail_files'].cast<String>();
    latestComment = json['latest_comment'];
    user = json['user'] != null ? User.fromJson(json['user']) : null;
    isLiked = json['is_liked'];
    isSaved = json['is_saved'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['description'] = description;
    data['location'] = location;
    data['likes'] = likes;
    data['dislikes'] = dislikes;
    data['comments_count'] = commentsCount;
    data['tagged_in'] = taggedIn;
    data['created_at'] = createdAt;
    data['files'] = files;
    data['width'] = width;
    data['height'] = height;
    data['thumbail_files'] = thumbailFiles;
    data['latest_comment'] = latestComment;
    if (user != null) {
      data['user'] = user!.toJson();
    }
    data['is_liked'] = isLiked;
    data['is_saved'] = isSaved;
    return data;
  }

  HashtagPostData copyWith({
    int? id,
    String? title,
    String? description,
    String? location,
    int? likes,
    int? dislikes,
    int? commentsCount,
    List<dynamic>? taggedIn,
    String? createdAt,
    List<String>? files,
    int? width,
    int? height,
    List<String>? thumbailFiles,
    String? latestComment,
    User? user,
    bool? isLiked,
    bool? isSaved,
  }) {
    return HashtagPostData(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      likes: likes ?? this.likes,
      dislikes: dislikes ?? this.dislikes,
      commentsCount: commentsCount ?? this.commentsCount,
      taggedIn: taggedIn ?? this.taggedIn,
      createdAt: createdAt ?? this.createdAt,
      files: files ?? this.files,
      width: width ?? this.width,
      height: height ?? this.height,
      thumbailFiles: thumbailFiles ?? this.thumbailFiles,
      latestComment: latestComment ?? this.latestComment,
      user: user ?? this.user,
      isLiked: isLiked ?? this.isLiked,
      isSaved: isSaved ?? this.isSaved,
    );
  }
}

class User {
  int? userId;
  String? username;
  String? name;
  String? profileImage;

  User({this.userId, this.username, this.name, this.profileImage});

  User.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'];
    username = json['username'];
    name = json['name'];
    profileImage = json['profile_image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['user_id'] = userId;
    data['username'] = username;
    data['name'] = name;
    data['profile_image'] = profileImage;
    return data;
  }
}
