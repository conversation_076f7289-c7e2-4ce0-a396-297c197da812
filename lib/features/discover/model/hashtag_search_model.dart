class HashtagSearch {
  bool? status;
  String? message;
  List<HashtagData>? data;

  HashtagSearch({this.status, this.message, this.data});

  HashtagSearch.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <HashtagData>[];
      json['data'].forEach((v) {
        data!.add(HashtagData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class HashtagData {
  int? id;
  String? name;
  String? posts;

  HashtagData({this.id, this.name, this.posts});

  HashtagData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    posts = json['posts'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['posts'] = posts;
    return data;
  }
}
