import 'dart:io';

import 'package:flowkar/core/utils/exports.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class DiscoverPageShimmer extends StatelessWidget {
  const DiscoverPageShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        children: [
          // Grid shimmer
          Expanded(
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: StaggeredGridView.countBuilder(
                padding: EdgeInsets.only(bottom: Platform.isAndroid ? 70.h : 44.h),
                physics: Platform.isIOS ? AlwaysScrollableScrollPhysics() : BouncingScrollPhysics(),
                shrinkWrap: true,
                crossAxisCount: 2,
                mainAxisSpacing: 2.0,
                crossAxisSpacing: 2.0,
                staggeredTileBuilder: (int index) {
                  return StaggeredTile.count(1, index.isEven ? 1.3 : 1);
                },
                itemCount: 15,
                itemBuilder: (context, index) {
                  return Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(18),
                      border: Border.all(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        width: 2.w,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// For a more sophisticated shimmer that matches your layout exactly:
class AdvancedDiscoverPageShimmer extends StatelessWidget {
  const AdvancedDiscoverPageShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Search bar shimmer
            _buildSearchBarShimmer(),
            buildSizedBoxH(26),
            // Grid shimmer
            Expanded(
              child: _buildGridShimmer(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBarShimmer() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: 50.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          children: [
            buildSizedBoxW(16),
            Container(
              width: 20,
              height: 20,
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
            ),
            buildSizedBoxW(10),
            Expanded(
              child: Container(
                height: 20.h,
                color: Colors.white,
              ),
            ),
            buildSizedBoxW(16),
          ],
        ),
      ),
    );
  }

  Widget _buildGridShimmer(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: StaggeredGridView.countBuilder(
        padding: EdgeInsets.only(bottom: 20.h),
        shrinkWrap: true,
        crossAxisCount: 2,
        mainAxisSpacing: 2.0,
        crossAxisSpacing: 2.0,
        staggeredTileBuilder: (int index) {
          return StaggeredTile.count(1, index.isEven ? 1.3 : 1);
        },
        itemCount: 15,
        itemBuilder: (context, index) {
          return Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(18.r),
              border: Border.all(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                width: 2.w,
              ),
            ),
          );
        },
      ),
    );
  }
}

// Tab view shimmer for search results
class SearchResultsShimmer extends StatelessWidget {
  const SearchResultsShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        buildSizedBoxH(18),
        // Tab bar shimmer
        Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            height: 50.h,
            margin: EdgeInsets.symmetric(horizontal: 60.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
            ),
          ),
        ),
        buildSizedBoxH(13),
        // List items shimmer
        Expanded(
          child: Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: ListView.builder(
              itemCount: 10,
              itemBuilder: (context, index) {
                return Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.0.h),
                  child: Row(
                    children: [
                      Container(
                        width: 50.w,
                        height: 50.h,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                      ),
                      buildSizedBoxW(8),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            width: 120.w,
                            height: 16.h,
                            color: Colors.white,
                          ),
                          buildSizedBoxH(4),
                          Container(
                            width: 80.w,
                            height: 12.h,
                            color: Colors.white,
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
