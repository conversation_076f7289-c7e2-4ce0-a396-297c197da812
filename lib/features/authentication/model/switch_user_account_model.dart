SwitchUserAccountModel deserializeSwitchUserAccountModel(Map<String, dynamic> json) =>
    SwitchUserAccountModel.fromJson(json);

class SwitchUserAccountModel {
  final bool status;
  final String message;
  final UserData data;

  SwitchUserAccountModel({
    required this.status,
    required this.message,
    required this.data,
  });

  SwitchUserAccountModel copyWith({
    bool? status,
    String? message,
    UserData? data,
  }) {
    return SwitchUserAccountModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  factory SwitchUserAccountModel.fromJson(Map<String, dynamic> json) {
    return SwitchUserAccountModel(
      status: json['status'],
      message: json['message'],
      data: UserData.fromJson(json['data']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data.toJson(),
    };
  }
}

class UserData {
  final String userStatus;
  final int userId;
  // final int brandId;
  final String username;
  final String name;
  final String profileImage;
  final String token;
  final bool isAnyAuth;
  final bool facebook;
  final bool instagram;
  final bool x;
  final bool youTube;
  final bool linkedIn;
  final bool pinterest;
  final bool tiktok;
  final bool threads;
  final bool dailymotion;
  final bool twitter;
  final bool tumblr;
  final bool vimeo;
  final bool telegram;
  final bool mastodon;
  final bool reddit;
  final bool isAdmin;
  final Map<String, dynamic> subData;

  UserData({
    required this.userStatus,
    required this.userId,
    // required this.brandId,
    required this.username,
    required this.name,
    required this.profileImage,
    required this.token,
    required this.isAnyAuth,
    required this.facebook,
    required this.instagram,
    required this.x,
    required this.youTube,
    required this.linkedIn,
    required this.pinterest,
    required this.tiktok,
    required this.threads,
    required this.dailymotion,
    required this.twitter,
    required this.tumblr,
    required this.vimeo,
    required this.reddit,
    required this.telegram,
    required this.mastodon,
    required this.isAdmin,
    required this.subData,
  });

  UserData copyWith({
    String? userStatus,
    int? userId,
    // int? brandId,
    String? username,
    String? name,
    String? profileImage,
    String? token,
    bool? isAnyAuth,
    bool? facebook,
    bool? instagram,
    bool? x,
    bool? youTube,
    bool? linkedIn,
    bool? pinterest,
    bool? tiktok,
    bool? threads,
    bool? dailymotion,
    bool? twitter,
    bool? tumblr,
    bool? vimeo,
    bool? telegram,
    bool? mastodon,
    bool? reddit,
    bool? isAdmin,
    Map<String, dynamic>? subData,
  }) {
    return UserData(
      userStatus: userStatus ?? this.userStatus,
      userId: userId ?? this.userId,
      // brandId: brandId ?? this.brandId,
      username: username ?? this.username,
      name: name ?? this.name,
      profileImage: profileImage ?? this.profileImage,
      token: token ?? this.token,
      isAnyAuth: isAnyAuth ?? this.isAnyAuth,
      facebook: facebook ?? this.facebook,
      instagram: instagram ?? this.instagram,
      x: x ?? this.x,
      youTube: youTube ?? this.youTube,
      linkedIn: linkedIn ?? this.linkedIn,
      pinterest: pinterest ?? this.pinterest,
      tiktok: tiktok ?? this.tiktok,
      threads: threads ?? this.threads,
      dailymotion: dailymotion ?? this.dailymotion,
      twitter: twitter ?? this.twitter,
      tumblr: tumblr ?? this.tumblr,
      vimeo: vimeo ?? this.vimeo,
      telegram: telegram ?? this.telegram,
      mastodon: mastodon ?? this.mastodon,
      reddit: reddit ?? this.reddit,
      isAdmin: isAdmin ?? this.isAdmin,
      subData: subData ?? this.subData,
    );
  }

  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
      userStatus: json['user_status'],
      userId: json['user_id'],
      // brandId: json['brand_id'],
      username: json['username'],
      name: json['name'],
      profileImage: json['profile_image'],
      token: json['token'],
      isAnyAuth: json['is_any_auth'],
      facebook: json['Facebook'],
      instagram: json['Instagram'],
      x: json['x'],
      youTube: json['YouTube'],
      linkedIn: json['LinkedIn'],
      pinterest: json['Pinterest'],
      tiktok: json['tiktok'],
      threads: json['threads'],
      dailymotion: json['Dailymotion'],
      twitter: json['Twitter'],
      tumblr: json['tumblr'],
      vimeo: json['Vimeo'],
      telegram: json['telegram'],
      mastodon: json['mastodon'],
      reddit: json['reddit'],
      isAdmin: json['is_admin'],
      subData: Map<String, dynamic>.from(json['sub_data'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_status': userStatus,
      'user_id': userId,
      // 'brand_id': brandId,
      'username': username,
      'name': name,
      'profile_image': profileImage,
      'token': token,
      'is_any_auth': isAnyAuth,
      'Facebook': facebook,
      'Instagram': instagram,
      'x': x,
      'YouTube': youTube,
      'LinkedIn': linkedIn,
      'Pinterest': pinterest,
      'tiktok': tiktok,
      'threads': threads,
      'Dailymotion': dailymotion,
      'Twitter': twitter,
      'tumblr': tumblr,
      'Vimeo': vimeo,
      'reddit': reddit,
      'is_admin': isAdmin,
      'sub_data': subData,
    };
  }
}
