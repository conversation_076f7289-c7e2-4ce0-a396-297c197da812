import 'package:json_annotation/json_annotation.dart';

part 'select_user_and_industry_type.g.dart';

SelectUserAndIndustryType deserializeSelectUserAndIndustryType(Map<String, dynamic> json) =>
    SelectUserAndIndustryType.fromJson(json);

@JsonSerializable()
class SelectUserAndIndustryType {
  final bool status;
  final String message;
  @Json<PERSON>ey(name: 'user_status')
  final String userStatus;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'brand_id')
  final int brandid;

  SelectUserAndIndustryType(
      {required this.status, required this.message, required this.userStatus, required this.brandid});

  factory SelectUserAndIndustryType.fromJson(Map<String, dynamic> json) => _$SelectUserAndIndustryTypeFromJson(json);

  Map<String, dynamic> toJson() => _$SelectUserAndIndustryTypeToJson(this);
}
