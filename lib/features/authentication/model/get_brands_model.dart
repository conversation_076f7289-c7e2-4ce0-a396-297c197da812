import 'package:json_annotation/json_annotation.dart';

part 'get_brands_model.g.dart';

GetBrandsModel deserializeGetBrandsModel(Map<String, dynamic> json) => GetBrandsModel.fromJson(json);

@JsonSerializable()
class GetBrandsModel {
  final bool status;
  final String message;
  final List<BrandData> data;

  GetBrandsModel({
    required this.status,
    required this.message,
    required this.data,
  });

  factory GetBrandsModel.fromJson(Map<String, dynamic> json) => _$GetBrandsModelFromJson(json);
  Map<String, dynamic> toJson() => _$GetBrandsModelToJson(this);
  GetBrandsModel copyWith({
    bool? status,
    String? message,
    List<BrandData>? data,
  }) {
    return GetBrandsModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

@JsonSerializable()
class BrandData {
  final int id;
  final String name;
  final String email;
  final String domain;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_invited')
  final bool isInvited;

  final String logo;

  BrandData({
    required this.id,
    required this.name,
    required this.email,
    required this.domain,
    required this.isInvited,
    required this.logo,
  });

  factory BrandData.fromJson(Map<String, dynamic> json) => _$BrandDataFromJson(json);
  Map<String, dynamic> toJson() => _$BrandDataToJson(this);

  // CopyWith Method
  BrandData copyWith({
    int? id,
    String? name,
    String? email,
    String? domain,
    bool? isInvited,
    String? logo,
  }) {
    return BrandData(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      domain: domain ?? this.domain,
      isInvited: isInvited ?? this.isInvited,
      logo: logo ?? this.logo,
    );
  }
}
