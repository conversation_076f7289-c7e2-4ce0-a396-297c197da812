GetCurentBrandIdModel deserializeGetCurentBrandIdModel(Map<String, dynamic> json) => GetCurentBrandIdModel.fromJson(json);

class GetCurentBrandIdModel {
  bool? status;
  String? message;
  int? brandId;

  GetCurentBrandIdModel({this.status, this.message, this.brandId});

  GetCurentBrandIdModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    brandId = json['brand_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['brand_id'] = brandId;
    return data;
  }
}
