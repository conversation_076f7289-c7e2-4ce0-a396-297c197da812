import 'package:json_annotation/json_annotation.dart';

part 'edit_brand_by_id_model.g.dart';

EditBrandByIdModel deserializeEditBrandByIdModel(Map<String, dynamic> json) => EditBrandByIdModel.fromJson(json);

@JsonSerializable()
class EditBrandByIdModel {
  final bool status;
  final String message;
  @Json<PERSON>ey(name: 'user_status')
  final String userStatus;

  EditBrandByIdModel({
    required this.status,
    required this.message,
    required this.userStatus,
  });

  factory EditBrandByIdModel.fromJson(Map<String, dynamic> json) => _$EditBrandByIdModelFromJson(json);

  Map<String, dynamic> toJson() => _$EditBrandByIdModelToJson(this);
}
