import 'package:json_annotation/json_annotation.dart';

part 'reset_password_response_model.g.dart';

ResetPasswordResponseModel deserializeResetPasswordResponseModel(
        Map<String, dynamic> json) =>
    ResetPasswordResponseModel.fromJson(json);

Map<String, dynamic> serializeResetPasswordResponseModel(
        ResetPasswordResponseModel model) =>
    model.toJson();

@JsonSerializable()
class ResetPasswordResponseModel {
  bool? status;
  String? message;

  ResetPasswordResponseModel({this.status, this.message});

  factory ResetPasswordResponseModel.fromJson(Map<String, dynamic> json) =>
      _$ResetPasswordResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$ResetPasswordResponseModelToJson(this);
}
