import 'package:json_annotation/json_annotation.dart';

part 'forgot_password_response_model.g.dart';

ForgotPasswordResponseModel deserializeForgotPasswordResponseModel(Map<String, dynamic> json) =>
    ForgotPasswordResponseModel.fromJson(json);

Map<String, dynamic> serializeForgotPasswordResponseModel(ForgotPasswordResponseModel model) => model.toJson();

@JsonSerializable()
class ForgotPasswordResponseModel {
  bool? status;
  String? message;

  ForgotPasswordResponseModel({this.status, this.message});

  factory ForgotPasswordResponseModel.fromJson(Map<String, dynamic> json) =>
      _$ForgotPasswordResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$ForgotPasswordResponseModelToJson(this);
}
