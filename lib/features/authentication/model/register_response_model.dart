import 'package:json_annotation/json_annotation.dart';

part 'register_response_model.g.dart';

RegisterResponseModel deserializeRegisterResponseModel(Map<String, dynamic> json) =>
    RegisterResponseModel.fromJson(json);

Map<String, dynamic> serializeRegisterResponseModel(RegisterResponseModel model) => model.toJson();

@JsonSerializable()
class RegisterResponseModel {
  final bool status;
  final String message;
  final Data data;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'Facebook')
  final bool facebook;
  @<PERSON>son<PERSON><PERSON>(name: 'Instagram')
  final bool instagram;
  // @<PERSON><PERSON><PERSON><PERSON>(name: 'Twitter')
  // final bool twitter;
  @J<PERSON><PERSON><PERSON>(name: 'YouTube')
  final bool youtube;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'LinkedIn')
  final bool linkedIn;
  @Json<PERSON><PERSON>(name: 'Pinterest')
  final bool pinterest;
  final bool tiktok;
  final bool threads;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'Dailymotion')
  final bool dailymotion;
  final bool tumblr;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'Vimeo')
  final bool vimeo;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'telegram')
  final bool telegram;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'mastodon')
  final bool mastodon;
  @JsonKey(name: 'x')
  final bool x;
  final bool reddit;

  RegisterResponseModel({
    required this.status,
    required this.message,
    required this.data,
    required this.facebook,
    required this.instagram,
    // required this.twitter,
    required this.youtube,
    required this.linkedIn,
    required this.pinterest,
    required this.tiktok,
    required this.threads,
    required this.dailymotion,
    required this.tumblr,
    required this.vimeo,
    required this.telegram,
    required this.mastodon,
    required this.x,
    required this.reddit,
  });

  factory RegisterResponseModel.fromJson(Map<String, dynamic> json) => _$RegisterResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$RegisterResponseModelToJson(this);
}

@JsonSerializable()
class Data {
  @JsonKey(name: 'user_id')
  final int userId;
  @JsonKey(name: 'user_status')
  final String userstatus;
  final String username;
  final String name;
  final String email;
  final String phone;
  @JsonKey(name: 'profile_image')
  final String profileImage;
  final String token;

  Data({
    required this.userId,
    required this.userstatus,
    required this.username,
    required this.name,
    required this.email,
    required this.phone,
    required this.profileImage,
    required this.token,
  });

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

  Map<String, dynamic> toJson() => _$DataToJson(this);
}
