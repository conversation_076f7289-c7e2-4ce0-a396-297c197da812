import 'package:json_annotation/json_annotation.dart';

part 'delete_account_response.g.dart';

DeleteAccountResponse deserializeDeleteAccountResponse(
        Map<String, dynamic> json) =>
    DeleteAccountResponse.fromJson(json);

@JsonSerializable()
class DeleteAccountResponse {
  final bool status;
  final String message;

  DeleteAccountResponse({
    required this.status,
    required this.message,
  });

  /// Factory method to create an instance from a JSON map.
  factory DeleteAccountResponse.fromJson(Map<String, dynamic> json) =>
      _$DeleteAccountResponseFromJson(json);

  /// Method to convert an instance into a JSON map.
  Map<String, dynamic> toJson() => _$DeleteAccountResponseToJson(this);
}
