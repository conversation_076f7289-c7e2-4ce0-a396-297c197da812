GetUserAndIndustryType deserializeGetUserAndIndustryType(Map<String, dynamic> json) => GetUserAndIndustryType.fromJson(json);

class GetUserAndIndustryType {
  bool? status;
  String? message;
  Data? data;

  GetUserAndIndustryType({this.status, this.message, this.data});

  GetUserAndIndustryType.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  List<Industries>? industries;
  List<UserTypes>? userTypes;

  Data({this.industries, this.userTypes});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['industries'] != null) {
      industries = <Industries>[];
      json['industries'].forEach((v) {
        industries!.add(Industries.fromJson(v));
      });
    }
    if (json['user_types'] != null) {
      userTypes = <UserTypes>[];
      json['user_types'].forEach((v) {
        userTypes!.add(UserTypes.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (industries != null) {
      data['industries'] = industries!.map((v) => v.toJson()).toList();
    }
    if (userTypes != null) {
      data['user_types'] = userTypes!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Industries {
  int? id;
  String? industryName;

  Industries({this.id, this.industryName});

  Industries.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    industryName = json['industry_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['industry_name'] = industryName;
    return data;
  }
}

class UserTypes {
  int? id;
  String? typeName;

  UserTypes({this.id, this.typeName});

  UserTypes.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    typeName = json['type_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['type_name'] = typeName;
    return data;
  }
}
