RegisterBrandModel deserializeRegisterBrandModel(Map<String, dynamic> json) => RegisterBrandModel.fromJson(json);

class RegisterBrandModel {
  bool? status;
  String? message;
  int? brandId;

  RegisterBrandModel({this.status, this.message, this.brandId});

  RegisterBrandModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    brandId = json['brand_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['brand_id'] = brandId;
    return data;
  }
}
