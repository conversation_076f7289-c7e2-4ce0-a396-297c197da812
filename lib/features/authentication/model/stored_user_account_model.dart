import 'package:json_annotation/json_annotation.dart';
import 'package:collection/collection.dart';

part 'stored_user_account_model.g.dart';

/// Represents a single stored user account.
@JsonSerializable()
class StoredUserAccount {
  final int brandId;
  final String name;
  final String profileImage;
  final String username;
  final int userId;
  final String token;

  const StoredUserAccount({
    required this.brandId,
    required this.name,
    required this.profileImage,
    required this.username,
    required this.userId,
    required this.token,
  });

  factory StoredUserAccount.fromJson(Map<String, dynamic> json) => _$StoredUserAccountFromJson(json);

  Map<String, dynamic> toJson() => _$StoredUserAccountToJson(this);

  StoredUserAccount copyWith({
    int? brandId,
    String? name,
    String? profileImage,
    String? username,
    int? userId,
    String? token,
  }) {
    return StoredUserAccount(
      brandId: brandId ?? this.brandId,
      name: name ?? this.name,
      profileImage: profileImage ?? this.profileImage,
      username: username ?? this.username,
      userId: userId ?? this.userId,
      token: token ?? this.token,
    );
  }

  @override
  String toString() {
    return 'StoredUserAccount(brandId: $brandId, name: $name, profileImage: $profileImage, username: $username, userId: $userId, token: $token)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StoredUserAccount &&
        other.brandId == brandId &&
        other.name == name &&
        other.profileImage == profileImage &&
        other.username == username &&
        other.userId == userId &&
        other.token == token;
  }

  @override
  int get hashCode => Object.hash(
        brandId,
        name,
        profileImage,
        username,
        userId,
        token,
      );
}

/// Represents a list of stored user accounts with a selected one.
@JsonSerializable()
class StoredAccountsList {
  final List<StoredUserAccount> accounts;
  final int currentAccountIndex;

  const StoredAccountsList({
    required this.accounts,
    required this.currentAccountIndex,
  });

  factory StoredAccountsList.fromJson(Map<String, dynamic> json) => _$StoredAccountsListFromJson(json);

  Map<String, dynamic> toJson() => _$StoredAccountsListToJson(this);

  StoredAccountsList copyWith({
    List<StoredUserAccount>? accounts,
    int? currentAccountIndex,
  }) {
    return StoredAccountsList(
      accounts: accounts ?? this.accounts,
      currentAccountIndex: currentAccountIndex ?? this.currentAccountIndex,
    );
  }

  /// Returns the currently selected user account, or null if out of bounds.
  StoredUserAccount? get currentAccount {
    if (currentAccountIndex >= 0 && currentAccountIndex < accounts.length) {
      return accounts[currentAccountIndex];
    }
    return null;
  }

  @override
  String toString() {
    return 'StoredAccountsList(accounts: $accounts, currentAccountIndex: $currentAccountIndex)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StoredAccountsList &&
        const DeepCollectionEquality().equals(other.accounts, accounts) &&
        other.currentAccountIndex == currentAccountIndex;
  }

  @override
  int get hashCode => Object.hash(
        const DeepCollectionEquality().hash(accounts),
        currentAccountIndex,
      );
}
