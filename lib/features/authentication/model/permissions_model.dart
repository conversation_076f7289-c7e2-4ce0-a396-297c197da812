PermissionModel deserializePermissionModel(Map<String, dynamic> json) => PermissionModel.fromJson(json);

class PermissionModel {
  final bool status;
  final String message;
  final List<int> data;

  PermissionModel({
    required this.status,
    required this.message,
    required this.data,
  });

  /// Factory constructor to create object from JSON
  factory PermissionModel.fromJson(Map<String, dynamic> json) {
    return PermissionModel(
      status: json['status'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? List<int>.from(json['data']) : [],
    );
  }

  /// Convert object to JSON
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data,
    };
  }

  /// copyWith method
  PermissionModel copyWith({
    bool? status,
    String? message,
    List<int>? data,
  }) {
    return PermissionModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  @override
  String toString() {
    return 'PermissionModel(status: $status, message: $message, data: $data)';
  }
}
