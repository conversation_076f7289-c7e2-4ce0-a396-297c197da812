import 'package:json_annotation/json_annotation.dart';

part 'get_user_status.g.dart';

GetUserStatus deserializeGetUserStatus(Map<String, dynamic> json) =>
    GetUserStatus.fromJson(json);

@JsonSerializable()
class GetUserStatus {
  final bool status;
  final String message;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_status')
  final String userStatus;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'brand_id')
  final int brandId;
  final String username;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_any_auth')
  final bool isAnyAuth;

  GetUserStatus({
    required this.status,
    required this.message,
    required this.userStatus,
    required this.brandId,
    required this.username,
    required this.isAnyAuth,
  });

  factory GetUserStatus.fromJson(Map<String, dynamic> json) =>
      _$GetUserStatusFromJson(json);

  Map<String, dynamic> toJson() => _$GetUserStatusToJson(this);
}
