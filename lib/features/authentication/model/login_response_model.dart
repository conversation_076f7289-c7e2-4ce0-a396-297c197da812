import 'package:json_annotation/json_annotation.dart';

part 'login_response_model.g.dart';

LoginResponseModel deserializeLoginResponseModel(Map<String, dynamic> json) => LoginResponseModel.fromJson(json);

Map<String, dynamic> serializeLoginResponseModel(LoginResponseModel model) => model.toJson();

@JsonSerializable()
class LoginResponseModel {
  final bool status;
  final String message;
  @<PERSON>son<PERSON>ey(name: 'user_id')
  final int userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'brand_id')
  final int brandId;
  @J<PERSON><PERSON><PERSON>(name: 'user_status')
  final String userstatus;
  final String username;
  final String name;
  @J<PERSON><PERSON><PERSON>(name: 'profile_image')
  final String profileImage;
  final String token;
  @Json<PERSON>ey(name: 'is_any_auth')
  final bool isAnyAuth;
  @<PERSON>son<PERSON><PERSON>(name: 'Facebook')
  final bool facebook;
  @<PERSON>son<PERSON><PERSON>(name: 'Instagram')
  final bool instagram;
  // @<PERSON><PERSON><PERSON><PERSON>(name: 'Twitter')
  // final bool twitter;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'YouTube')
  final bool youtube;
  @J<PERSON><PERSON><PERSON>(name: 'LinkedIn')
  final bool linkedIn;
  @J<PERSON><PERSON>ey(name: 'Pinterest')
  final bool pinterest;
  final bool tiktok;
  final bool threads;
  @JsonKey(name: 'Dailymotion')
  final bool dailymotion;
  @JsonKey(name: 'x')
  final bool x;
  final bool tumblr;
  @JsonKey(name: 'Vimeo')
  final bool vimeo;
  @JsonKey(name: 'telegram')
  final bool telegram;
  @JsonKey(name: 'mastodon')
  final bool mastodon;
  final bool reddit;
  @JsonKey(name: 'is_admin')
  final bool isAdmin;

  LoginResponseModel({
    required this.status,
    required this.message,
    required this.userId,
    required this.brandId,
    required this.userstatus,
    required this.username,
    required this.name,
    required this.profileImage,
    required this.token,
    required this.isAnyAuth,
    required this.facebook,
    required this.instagram,
    // required this.twitter,
    required this.youtube,
    required this.linkedIn,
    required this.pinterest,
    required this.tiktok,
    required this.threads,
    required this.dailymotion,
    required this.x,
    required this.tumblr,
    required this.vimeo,
    required this.telegram,
    required this.mastodon,
    required this.reddit,
    required this.isAdmin,
  });

  factory LoginResponseModel.fromJson(Map<String, dynamic> json) => _$LoginResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginResponseModelToJson(this);
}
