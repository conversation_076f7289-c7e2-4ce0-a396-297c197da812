import 'package:json_annotation/json_annotation.dart';

part 'verify_otp_response_model.g.dart';

VerifyOtpResponseModel deserializeVerifyOtpResponseModel(
        Map<String, dynamic> json) =>
    VerifyOtpResponseModel.fromJson(json);

Map<String, dynamic> serializeVerifyOtpResponseModel(
        VerifyOtpResponseModel model) =>
    model.toJson();

@JsonSerializable()
class VerifyOtpResponseModel {
  bool? status;
  String? message;
  String? token;

  VerifyOtpResponseModel({this.status, this.message, this.token});

  factory VerifyOtpResponseModel.fromJson(Map<String, dynamic> json) =>
      _$VerifyOtpResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$VerifyOtpResponseModelToJson(this);
}
