part of 'auth_bloc.dart';

@immutable
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object> get props => [];
}

class AuthInitial extends AuthEvent {
  @override
  List<Object> get props => [];
}

class PasswordVisibilityEvent extends AuthEvent {
  const PasswordVisibilityEvent({required this.value});

  final bool value;

  @override
  List<Object> get props => [value];
}

class LogInEvent extends AuthEvent {
  final String email;
  final String password;
  final BuildContext? context;

  const LogInEvent({required this.email, required this.password, this.context});
}

class ChoosePasswordVisibilityEvent extends AuthEvent {
  const ChoosePasswordVisibilityEvent({required this.value});

  final bool value;

  @override
  List<Object> get props => [value];
}

class ConfirmPasswordVisibilityEventS extends AuthEvent {
  const ConfirmPasswordVisibilityEventS({required this.value});

  final bool value;

  @override
  List<Object> get props => [];
}

class SignUpAPIEvent extends AuthEvent {
  final String name;
  final String userName;
  final String email;
  final String confirmPassword;
  final String refferenceCode;
  const SignUpAPIEvent({
    required this.name,
    required this.userName,
    required this.email,
    required this.confirmPassword,
    required this.refferenceCode,
  });

  @override
  List<Object> get props => [name, userName, email, confirmPassword, refferenceCode];
}

class ForgotpasswordEvent extends AuthEvent {
  final String email;
  final BuildContext context;

  const ForgotpasswordEvent({required this.email, required this.context});
}

class StartOtpTimerEvent extends AuthEvent {}

class VerifyOtpEvent extends AuthEvent {
  final String email;
  final String userotp;

  const VerifyOtpEvent({
    required this.email,
    required this.userotp,
  });
}

class ResendOtpEvent extends AuthEvent {
  final String email;

  const ResendOtpEvent({required this.email});
}

class ResetPasswordEvent extends AuthEvent {
  final String newpassword;

  const ResetPasswordEvent({required this.newpassword});
}

class OneSignalIdApiEvent extends AuthEvent {
  final String onesignalId;

  const OneSignalIdApiEvent({required this.onesignalId});
}

class SelectUserAndIndustryType extends AuthEvent {
  final String industry;
  final String type;
  final bool isChangeUserType;

  const SelectUserAndIndustryType({required this.industry, required this.type, required this.isChangeUserType});
}

class GEtUserAndIndustryTypeAPI extends AuthEvent {
  const GEtUserAndIndustryTypeAPI();
}

class GetUserStatusApi extends AuthEvent {
  final BuildContext? context;
  const GetUserStatusApi({this.context});
}

class GetBrandsByBrIDAPI extends AuthEvent {
  final BuildContext? context;
  final int brandId;

  const GetBrandsByBrIDAPI({this.context, required this.brandId});
}

class DeleteBrandsByIdAPI extends AuthEvent {
  final BuildContext? context;
  final int brandId;
  final int index;

  const DeleteBrandsByIdAPI({this.context, required this.brandId, required this.index});
}

class EditBrandAPI extends AuthEvent {
  final BuildContext? context;
  final int brandid;
  final String name;
  final String email;
  final String? domain;
  final File filepath;

  const EditBrandAPI({
    this.context,
    required this.brandid,
    required this.name,
    required this.email,
    this.domain,
    required this.filepath,
  });
}

class RegisterBrandsAPI extends AuthEvent {
  final String name;
  final String email;
  final String? domain;
  final File filepath;
  final BuildContext? context;

  const RegisterBrandsAPI({
    required this.name,
    required this.email,
    this.domain,
    required this.filepath,
    this.context,
  });
}

class CurentBrandIDAPI extends AuthEvent {
  final int brandId;
  final BuildContext? context;

  const CurentBrandIDAPI({required this.brandId, this.context});
}

class LogOutAPI extends AuthEvent {
  const LogOutAPI();

  @override
  List<Object> get props => [];
}

class SwitchAccountEvent extends AuthEvent {
  final int brandId;
  final int accountIndex;
  final String brandName;
  final BuildContext context;

  const SwitchAccountEvent({
    required this.brandId,
    required this.accountIndex,
    required this.brandName,
    required this.context,
  });

  @override
  List<Object> get props => [brandId, accountIndex, brandName, context];
}

// Multi-account events
class AddAccountLoginEvent extends AuthEvent {
  final String email;
  final String password;
  final BuildContext? context;
  final bool isAddingAccount;

  const AddAccountLoginEvent({
    required this.email,
    required this.password,
    this.context,
    this.isAddingAccount = true,
  });

  @override
  List<Object> get props => [email, password, isAddingAccount];
}

// class SwitchToStoredAccountEvent extends AuthEvent {
//   final int accountIndex;
//   final BuildContext? context;

//   const SwitchToStoredAccountEvent({
//     required this.accountIndex,
//     this.context,
//   });

//   @override
//   List<Object> get props => [accountIndex];
// }

class LoadStoredAccountsEvent extends AuthEvent {
  const LoadStoredAccountsEvent();

  @override
  List<Object> get props => [];
}

class RemoveStoredAccountEvent extends AuthEvent {
  final int accountIndex;

  const RemoveStoredAccountEvent({
    required this.accountIndex,
  });

  @override
  List<Object> get props => [accountIndex];
}

class GetCurrentUserRoleEvent extends AuthEvent {
  const GetCurrentUserRoleEvent();
  @override
  List<Object> get props => [];
}
