part of 'auth_bloc.dart';

class AuthState extends Equatable {
  const AuthState({
    // signup_state

    this.loginResponseModel,
    this.isShowPassword = false,
    this.isLoginLoading = false,
    //signup_state

    this.registerResponseModel,
    this.isChooseShowPassword = false,
    this.isConfirmShowPassword = false,
    this.isSignUpLoading = false,

    //forgotPassword_state
    this.forgotPasswordResponseModel,
    this.isForgotPasswordLoading = false,
    this.verifyOtpResponseModel,
    this.isVerifyEmailLoading = false,
    this.remainingSeconds = 0,
    this.isResedOtpLoading = false,
    this.resetPasswordResponseModel,
    this.isResetPasswordLoading = false,
    // Select User Type And Industry Type
    this.isgetUserAndIndustryLoadong = false,
    this.isUploadLoadong = false,
    this.getUserAndIndustryType,
    this.getUserStatus,
    this.idGetbrandbyIdLoading = false,
    this.idDeletebrandbyIdLoading = false,
    this.getBrandsModel,
    this.iseditbrandLiading = false,
    this.editBrandByIdModel,
    this.isGetCurentbrandIDLoading = false,
    this.swichbranchLoading = false,
    this.getCurentBrandIdModel,
    this.logOutLoading = false,
    // switch user account
    this.switchUserAccountModel,
    this.isSwitchUserAccountLoading = false,
    // multi-account support
    this.storedAccountsList,
    this.isLoadingStoredAccounts = false,
    this.isAddingAccount = false,
    this.isSwitchingToStoredAccount = false,
    this.getCurrentUserRoleLoading = false,
    this.currentUserRoleModel,
  });
  // signup_state
  final LoginResponseModel? loginResponseModel;
  final bool isShowPassword;
  final bool isLoginLoading;

  //Signup_state
  final RegisterResponseModel? registerResponseModel;
  final bool isChooseShowPassword;
  final bool isConfirmShowPassword;
  final bool isSignUpLoading;

  //ForgotPassword_state
  final ForgotPasswordResponseModel? forgotPasswordResponseModel;
  final bool isForgotPasswordLoading;
  //
  final VerifyOtpResponseModel? verifyOtpResponseModel;
  final bool isVerifyEmailLoading;
  final int remainingSeconds;
  final bool isResedOtpLoading;
  //ForgotPasswords_state
  final ResetPasswordResponseModel? resetPasswordResponseModel;
  final bool isResetPasswordLoading;

  // Select User Type And Industry Type
  final bool isgetUserAndIndustryLoadong;
  final GetUserAndIndustryType? getUserAndIndustryType;
  final bool isUploadLoadong;
  final GetUserStatus? getUserStatus;

  // Get Brand By Id
  final bool idGetbrandbyIdLoading;
  final bool idDeletebrandbyIdLoading;
  final GetBrandsModel? getBrandsModel;
  // Edit Brand
  final bool iseditbrandLiading;
  final EditBrandByIdModel? editBrandByIdModel;

  final bool isGetCurentbrandIDLoading;
  final GetCurentBrandIdModel? getCurentBrandIdModel;
  final bool swichbranchLoading;

  final bool logOutLoading;
// switch user account
  final bool isSwitchUserAccountLoading;
  // signup_state
  final SwitchUserAccountModel? switchUserAccountModel;
  // multi-account support
  final StoredAccountsList? storedAccountsList;
  final bool isLoadingStoredAccounts;
  final bool isAddingAccount;
  final bool isSwitchingToStoredAccount;
  // Role Permission
  final bool getCurrentUserRoleLoading;
  final PermissionModel? currentUserRoleModel;
  @override
  List<Object?> get props => [
        // signup_state
        // loginEmailController,
        // loginPasswordController,
        loginResponseModel,
        isShowPassword,
        isLoginLoading,

        //signup_state

        registerResponseModel,
        isChooseShowPassword,
        isConfirmShowPassword,
        isSignUpLoading,
        //forgotPassword_state
        forgotPasswordResponseModel,
        isForgotPasswordLoading,
        isVerifyEmailLoading,
        verifyOtpResponseModel,
        remainingSeconds,
        isResedOtpLoading,
        resetPasswordResponseModel,
        isResetPasswordLoading,
        //selectUserTypeAndIndustryType
        isgetUserAndIndustryLoadong,
        getUserAndIndustryType,
        isUploadLoadong,
        getUserStatus,
        // get Brand by id
        idGetbrandbyIdLoading,
        idDeletebrandbyIdLoading,
        getBrandsModel,
        // edit brand
        iseditbrandLiading,
        editBrandByIdModel,
        isGetCurentbrandIDLoading,
        getCurentBrandIdModel,
        swichbranchLoading,
        logOutLoading,
        // switch user account
        isSwitchUserAccountLoading,
        switchUserAccountModel,
        // multi-account support
        storedAccountsList,
        isLoadingStoredAccounts,
        isAddingAccount,
        isSwitchingToStoredAccount,
        getCurrentUserRoleLoading,
        currentUserRoleModel,
      ];

  AuthState copyWith({
    // signup_state

    LoginResponseModel? loginResponseModel,
    bool? isShowPassword,
    bool? isLoginLoading,
    //signup_state

    RegisterResponseModel? registerResponseModel,
    bool? isChooseShowPassword,
    bool? isConfirmShowPassword,
    bool? isSignUpLoading,
    //forgotPassword_state
    ForgotPasswordResponseModel? forgotPasswordResponseModel,
    bool? isForgotPasswordLoading,
    VerifyOtpResponseModel? verifyOtpResponseModel,
    bool? isVerifyEmailLoading,
    int? remainingSeconds,
    bool? isResedOtpLoading,
    ResetPasswordResponseModel? resetPasswordResponseModel,
    bool? isResetPasswordLoading,
    bool? isgetUserAndIndustryLoadong,
    GetUserAndIndustryType? getUserAndIndustryType,
    GetUserStatus? getUserStatus,
    bool? isUploadLoadong,
    bool? idGetbrandbyIdLoading,
    bool? idDeletebrandbyIdLoading,
    GetBrandsModel? getBrandsModel,
    bool? iseditbrandLiading,
    EditBrandByIdModel? editBrandByIdModel,
    bool? isGetCurentbrandIDLoading,
    bool? swichbranchLoading,
    GetCurentBrandIdModel? getCurentBrandIdModel,
    bool? logOutLoading,
    // switch user account
    bool? isSwitchUserAccountLoading,
    SwitchUserAccountModel? switchUserAccountModel,
    // multi-account support
    StoredAccountsList? storedAccountsList,
    bool? isLoadingStoredAccounts,
    bool? isAddingAccount,
    bool? isSwitchingToStoredAccount,
    bool? getCurrentUserRoleLoading,
    PermissionModel? currentUserRoleModel,
  }) {
    return AuthState(
      // signup_state

      loginResponseModel: loginResponseModel ?? this.loginResponseModel,
      isShowPassword: isShowPassword ?? this.isShowPassword,
      isLoginLoading: isLoginLoading ?? this.isLoginLoading,
      //signup_state
      registerResponseModel: registerResponseModel ?? this.registerResponseModel,
      isChooseShowPassword: isChooseShowPassword ?? this.isChooseShowPassword,
      isConfirmShowPassword: isConfirmShowPassword ?? this.isConfirmShowPassword,
      isSignUpLoading: isSignUpLoading ?? this.isSignUpLoading,
      //forgotPassword_state
      forgotPasswordResponseModel: forgotPasswordResponseModel ?? this.forgotPasswordResponseModel,
      isForgotPasswordLoading: isForgotPasswordLoading ?? this.isForgotPasswordLoading,
      verifyOtpResponseModel: verifyOtpResponseModel ?? this.verifyOtpResponseModel,
      isVerifyEmailLoading: isVerifyEmailLoading ?? this.isVerifyEmailLoading,
      remainingSeconds: remainingSeconds ?? this.remainingSeconds,
      isResedOtpLoading: isResedOtpLoading ?? this.isResedOtpLoading,
      resetPasswordResponseModel: resetPasswordResponseModel ?? this.resetPasswordResponseModel,
      isResetPasswordLoading: isResetPasswordLoading ?? this.isResetPasswordLoading,
      isgetUserAndIndustryLoadong: isgetUserAndIndustryLoadong ?? this.isgetUserAndIndustryLoadong,
      getUserAndIndustryType: getUserAndIndustryType ?? this.getUserAndIndustryType,
      isUploadLoadong: isUploadLoadong ?? this.isUploadLoadong,
      getUserStatus: getUserStatus ?? this.getUserStatus,
      // Get Brand By Id
      idGetbrandbyIdLoading: idGetbrandbyIdLoading ?? this.idGetbrandbyIdLoading,
      idDeletebrandbyIdLoading: idDeletebrandbyIdLoading ?? this.idDeletebrandbyIdLoading,
      getBrandsModel: getBrandsModel ?? this.getBrandsModel,
      iseditbrandLiading: iseditbrandLiading ?? this.iseditbrandLiading,
      editBrandByIdModel: editBrandByIdModel ?? this.editBrandByIdModel,
      isGetCurentbrandIDLoading: isGetCurentbrandIDLoading ?? this.isGetCurentbrandIDLoading,
      swichbranchLoading: swichbranchLoading ?? this.swichbranchLoading,
      getCurentBrandIdModel: getCurentBrandIdModel ?? this.getCurentBrandIdModel,
      logOutLoading: logOutLoading ?? this.logOutLoading,
      // switch user account`
      isSwitchUserAccountLoading: isSwitchUserAccountLoading ?? this.isSwitchUserAccountLoading,
      switchUserAccountModel: switchUserAccountModel ?? this.switchUserAccountModel,
      // multi-account support
      storedAccountsList: storedAccountsList ?? this.storedAccountsList,
      isLoadingStoredAccounts: isLoadingStoredAccounts ?? this.isLoadingStoredAccounts,
      isAddingAccount: isAddingAccount ?? this.isAddingAccount,
      isSwitchingToStoredAccount: isSwitchingToStoredAccount ?? this.isSwitchingToStoredAccount,
      getCurrentUserRoleLoading: getCurrentUserRoleLoading ?? this.getCurrentUserRoleLoading,
      currentUserRoleModel: currentUserRoleModel ?? this.currentUserRoleModel,
    );
  }
}
