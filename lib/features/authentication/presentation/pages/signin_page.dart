import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/authentication/bloc/auth_bloc.dart';
import 'package:flowkar/features/widgets/common/flowkar_background/flowkar_background.dart';

class SigninPage extends StatefulWidget {
  final bool isAddingAccount;
  const SigninPage({super.key, this.isAddingAccount = false});

  static Widget builder(BuildContext context) {
    return const SigninPage();
  }

  @override
  State<SigninPage> createState() => _SigninPageState();
}

class _SigninPageState extends State<SigninPage> {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  // final FocusNode emailFocusNode = FocusNode();
  // final FocusNode passwordFocusNode = FocusNode();
  @override
  Widget build(BuildContext context) {
    Prefobj.preferences?.delete(Prefkeys.TMPAUTHTOKEN);

    return Scaffold(
        resizeToAvoidBottomInset: false,
        extendBodyBehindAppBar: true,
        appBar: widget.isAddingAccount
            ? CustomAppbar(
                leading: [
                  InkWell(
                    onTap: () {
                      FocusScope.of(context).unfocus();
                      NavigatorService.goBack();
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: CustomImageView(
                        imagePath: Assets.images.svg.authentication.icBackArrow.path,
                        height: 16.h,
                      ),
                    ),
                  ),
                ],
              )
            : null,
        body: BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            return Padding(
              padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewPadding.bottom),
              child: InkWell(
                focusColor: Colors.transparent,
                onTap: () {
                  FocusManager.instance.primaryFocus?.unfocus();
                },
                child: BackgroundImage(
                  imagePath: Assets.images.pngs.authentication.pngAuthBg1.path,
                  child: _buildInitialState(
                      context,
                      formKey,
                      emailController,
                      passwordController,
                      isPasswordVisible: state.isShowPassword,
                      state),
                ),
              ),
            );
          },
        ));
  }

  Widget _buildInitialState(
    BuildContext context,
    GlobalKey<FormState> formKey,
    TextEditingController emailController,
    TextEditingController passwordController,
    // FocusNode emailFocusNode,
    // FocusNode passwordFocusNode,
    AuthState state, {
    bool isPasswordVisible = false,
  }) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0.w, vertical: 16.h),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildTaglineText(context),
              Text(
                textAlign: TextAlign.center,
                "\n${Lang.of(context).lbl_create_an_account_and_start_exploring}",
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w400,
                      color: Theme.of(context).customColors.authsubtitlecolor,
                      fontSize: 14.sp,
                    ),
              ),
              buildSizedBoxH(60),
              AbsorbPointer(
                  absorbing: state.isLoginLoading,
                  child: _buildLoginForm(
                      context,
                      formKey,
                      emailController,
                      passwordController,
                      isPasswordVisible: isPasswordVisible,
                      state)),
              buildSizedBoxH(30),
            ],
          ),
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            if (!widget.isAddingAccount) _builddontHaveanaccount(context),
            buildSizedBoxH(20),
          ],
        )
      ],
    );
  }

  Widget _buildTaglineText(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: Lang.of(context).lbl_sign,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 34.sp,
                  ),
            ),
            TextSpan(
              text: Lang.of(context).lbl_in,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w400,
                    fontSize: 34.sp,
                  ),
            ),
          ],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildLoginForm(
    BuildContext context,
    GlobalKey<FormState> formKey,
    TextEditingController emailController,
    TextEditingController passwordController,
    // FocusNode emailFocusNode,
    // FocusNode passwordFocusNode,
    AuthState state, {
    bool isPasswordVisible = false,
  }) {
    return Form(
      key: formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildEmailField(context, emailController),
          buildSizedBoxH(20),
          _buildPasswordField(
            context,
            passwordController,
            // passwordFocusNode,
            isPasswordVisible,
          ),
          buildSizedBoxH(30),
          if (!widget.isAddingAccount) _buildForgotPassword(context),
          buildSizedBoxH(75),
          _buildSignInButton(context, formKey, emailController, passwordController, state),
        ],
      ),
    );
  }

  Widget _buildEmailField(
    BuildContext context,
    TextEditingController emailController,
    // FocusNode emailFocusNode,
    // FocusNode passwordFocusNode,
  ) {
    return FlowkarTextFormField(
      context: context,
      labelText: Lang.of(context).lbl_email,
      prefixIcon: CustomImageView(imagePath: AssetConstants.pngEmail, width: 23.0.w, margin: EdgeInsets.all(16.0)),
      textInputType: TextInputType.emailAddress,
      controller: emailController,
      filled: true,
      // focusNode: emailFocusNode,
      validator: AppValidations.validateEmail,
      textInputAction: TextInputAction.next,
      onFieldSubmitted: (_) {
        // FocusScope.of(context).requestFocus(passwordFocusNode);
      },
    );
  }

  Widget _buildPasswordField(
    BuildContext context,
    TextEditingController passwordController,
    // FocusNode passwordFocusNode,
    bool isPasswordVisible,
  ) {
    return FlowkarTextFormField(
      context: context,
      labelText: Lang.of(context).lbl_password,
      prefixIcon: CustomImageView(
        imagePath: AssetConstants.pngPassword,
        height: 24.0.h,
        margin: EdgeInsets.all(16.0),
      ),
      suffixIcon: CustomImageView(
        onTap: () => context.read<AuthBloc>().add(PasswordVisibilityEvent(value: !isPasswordVisible)),
        imagePath: isPasswordVisible ? AssetConstants.pngSeen : AssetConstants.pngHide,
        height: isPasswordVisible ? 19.91.h : 21.0.h,
        width: isPasswordVisible ? 19.91.h : 21.0.h,
        margin: EdgeInsets.all(16.0),
      ),
      filled: true,
      obscureText: !isPasswordVisible,
      controller: passwordController,
      // focusNode: passwordFocusNode,
      validator: (value) {
        return AppValidations.validateRequired(value, fieldName: Lang.of(context).lbl_password);
      },
    );
  }

  Widget _buildSignInButton(
    BuildContext context,
    GlobalKey<FormState> formKey,
    TextEditingController emailController,
    TextEditingController passwordController,
    AuthState state,
  ) {
    return CustomElevatedButton(
      width: 159.w,
      isDisabled: state.isLoginLoading,
      isLoading: state.isLoginLoading,
      text: Lang.of(context).lbl_signin,
      brderRadius: 10.r,
      iconSpacing: 20.w,
      // rightIcon: CustomImageView(
      //   imagePath: Assets.images.svg.authentication.icBackIcon.path,
      // ),
      onPressed: () async {
        if (formKey.currentState?.validate() ?? false) {
          FocusScope.of(context).requestFocus(FocusNode());
          // context
          //     .read<AuthBloc>()
          //     .add(LogInEvent(email: emailController.text, password: passwordController.text, context: context));
          if (widget.isAddingAccount) {
            // Use AddAccountLoginEvent for adding new account
            context.read<AuthBloc>().add(AddAccountLoginEvent(
                  email: emailController.text,
                  password: passwordController.text,
                  context: context,
                ));
          } else {
            // Use regular LogInEvent for normal login
            context.read<AuthBloc>().add(LogInEvent(
                  email: emailController.text,
                  password: passwordController.text,
                  context: context,
                ));
          }
        }
      },
    );
  }

  Widget _buildForgotPassword(BuildContext context) {
    return InkWell(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
        // NavigatorService.pushNamed(AppRoutes.forgotPasswordPage);
        PersistentNavBarNavigator.pushNewScreen(context,
            screen: const ForgotPasswordPage(
                // stackonScreen: true,
                ));
      },
      child: Text(
        Lang.of(context).lbl_forgotten_password,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w700,
              color: Theme.of(context).customColors.subHeadingcolor,
            ),
      ),
    );
  }

  Widget _builddontHaveanaccount(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: Lang.of(context).lbl_dont_have_an_account,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontSize: 14.0.sp, fontWeight: FontWeight.w400),
            ),
            TextSpan(
              text: " ${Lang.of(context).lbl_signup}",
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontSize: 14.0.sp, fontWeight: FontWeight.w700),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  // NavigatorService.pushNamed(AppRoutes.signupPage);
                  PersistentNavBarNavigator.pushNewScreen(context,
                      screen: const SignUpPage(
                          // stackonScreen: true,
                          ));
                },
            ),
          ],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
