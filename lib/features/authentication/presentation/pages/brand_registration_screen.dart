import 'dart:io';

import 'package:flowkar/core/utils/exports.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flowkar/features/authentication/bloc/auth_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';

class BrandRegistrationScreen extends StatefulWidget {
  final int? brandId;
  final bool? isNewBrand;

  const BrandRegistrationScreen({super.key, this.brandId, this.isNewBrand});
  static Widget builder(BuildContext context) {
    final List args = ModalRoute.of(context)?.settings.arguments as List;

    return BrandRegistrationScreen(
      brandId: args[0],
      isNewBrand: args[1],
    );
  }

  @override
  State<BrandRegistrationScreen> createState() => _BrandRegistrationScreenState();
}

class _BrandRegistrationScreenState extends State<BrandRegistrationScreen> {
  File? _selectedImage;
  TextEditingController brandNameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController domainController = TextEditingController();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  String? _imageError;

  Future<void> _pickImage() async {
    final pickedFile = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _selectedImage = File(pickedFile.path);
        _imageError = null;
      });
    }
  }

  @override
  void initState() {
    // context.read<AuthBloc>().add(GetBrandsByBrIDAPI(context: context, brandId: "2"));
    super.initState();
    Logger.lOG("Brand Id ${widget.brandId}");
    var state = context.read<AuthBloc>().state;

    brandNameController.text = state.getBrandsModel?.data.first.name ?? "";
    emailController.text = state.getBrandsModel?.data.first.email ?? "";
    domainController.text = state.getBrandsModel?.data.first.domain ?? "";

    String? logoUrl = state.getBrandsModel?.data.first.logo;
    if (logoUrl != null && logoUrl.isNotEmpty) {
      _downloadAndSaveImage('${APIConfig.mainbaseURL}$logoUrl');
    }
  }

  Future<void> _downloadAndSaveImage(String url) async {
    try {
      final response = await Dio().get(
        url,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200) {
        final Directory tempDir = await getTemporaryDirectory();
        final File file = File('${tempDir.path}/brand_logo.jpg');
        await file.writeAsBytes(response.data);

        setState(() {
          _selectedImage = file;
        });
      } else {
        Logger.lOG("Failed to load image, Status Code: ${response.statusCode}");
      }
    } catch (e) {
      Logger.lOG("Error downloading image: $e");
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildBrandRegistrationAppBar(context),
      body: InkWell(
        focusColor: Colors.transparent,
        onTap: () {
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: Padding(
          padding: EdgeInsets.only(bottom: Platform.isIOS ? 0 : MediaQuery.of(context).viewPadding.bottom),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Form(
                      key: formKey,
                      child: Column(
                        children: [
                          buildSizedBoxH(20.0),
                          _buildLogoUploadSection(),
                          buildSizedBoxH(40.0),
                          _buildBrandNameField(context, brandNameController),
                          buildSizedBoxH(20.0),
                          _buildBrandEmailField(context, emailController),
                          buildSizedBoxH(20.0),
                          _buildBrandOfficialDomainField(context, domainController),
                          buildSizedBoxH(75),
                          _submitButton(),
                          buildSizedBoxH(16.0),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildBrandRegistrationAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          widget.isNewBrand == true ? "Create Brand" : "Brand Registration",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  Widget _buildLogoUploadSection() {
    return Column(
      children: [
        Center(
          child: DottedBorder(
            borderType: BorderType.RRect,
            radius: Radius.circular(16.r),
            padding: EdgeInsets.zero,
            color: _imageError != null ? Theme.of(context).colorScheme.error : Theme.of(context).primaryColor,
            dashPattern: const [10, 8],
            strokeWidth: 2,
            child: GestureDetector(
              onTap: _pickImage,
              child: Container(
                width: 135.w,
                height: 135.h,
                decoration: BoxDecoration(
                  color: Theme.of(context).customColors.selectImageBgColor,
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16.r),
                  child: _selectedImage != null
                      ? Image.file(
                          File(_selectedImage!.path),
                          width: 220.w,
                          height: 100.h,
                          fit: BoxFit.cover,
                        )
                      : Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 37.w,
                              height: 37.h,
                              child: CustomImageView(
                                  imagePath: Assets.images.svg.authentication.svgUploadLogo.path, fit: BoxFit.contain),
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              'Select Image',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontSize: 15.sp,
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                          ],
                        ),
                ),
              ),
            ),
          ),
        ),
        if (_imageError != null)
          Padding(
            padding: EdgeInsets.only(top: 8.0),
            child: Center(
              child: Text(
                textAlign: TextAlign.center,
                _imageError ?? "",
                style: TextStyle(color: Theme.of(context).colorScheme.error, fontSize: 12.sp),
              ),
            ),
          ),
        buildSizedBoxH(10.0),
        Text(
          "Upload Your Logo*",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w400,
                fontSize: 16.sp,
              ),
        ),
      ],
    );
  }

  Widget _buildBrandNameField(
    BuildContext context,
    TextEditingController brandNameController,
  ) {
    return FlowkarTextFormField(
      context: context,
      labelText: "Brand Name",
      textInputType: TextInputType.emailAddress,
      controller: brandNameController,
      filled: true,
      validator: (p0) => AppValidations.validateName(p0),
      textInputAction: TextInputAction.next,
    );
  }

  Widget _buildBrandEmailField(
    BuildContext context,
    TextEditingController emailController,
  ) {
    return FlowkarTextFormField(
      context: context,
      labelText: "Email",
      textInputType: TextInputType.emailAddress,
      controller: emailController,
      filled: true,
      validator: (p0) => AppValidations.validateEmail(p0),
      textInputAction: TextInputAction.next,
    );
  }

  Widget _buildBrandOfficialDomainField(
    BuildContext context,
    TextEditingController domainController,
  ) {
    return FlowkarTextFormField(
      context: context,
      labelText: "Official Domain",
      textInputType: TextInputType.emailAddress,
      controller: domainController,
      filled: true,
      textInputAction: TextInputAction.done,
    );
  }

  Widget _submitButton() {
    return CustomElevatedButton(
      width: 159.w,
      text: widget.isNewBrand == false ? 'Next' : "Submit",
      brderRadius: 10.r,
      isLoading: context.read<AuthBloc>().state.iseditbrandLiading,
      isDisabled: context.read<AuthBloc>().state.iseditbrandLiading,
      iconSpacing: widget.isNewBrand == false ? 20.w : 0,
      rightIcon: widget.isNewBrand == false
          ? CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackIcon.path,
            )
          : SizedBox.shrink(),
      onPressed: () {
        if (_selectedImage == null) {
          setState(() {
            _imageError = "Please upload your brand logo.";
          });
        } else {
          _imageError = null;
        }
        if (_imageError == null) {
          if (formKey.currentState?.validate() ?? false) {
            FocusScope.of(context).requestFocus(FocusNode());
            if (widget.isNewBrand == true) {
              context.read<AuthBloc>().add(RegisterBrandsAPI(
                  email: emailController.text.trim(),
                  name: brandNameController.text.trim(),
                  domain: domainController.text.isNotEmpty ? domainController.text : "",
                  filepath: _selectedImage!,
                  context: context));
            } else {
              context.read<AuthBloc>().add(EditBrandAPI(
                  email: emailController.text.trim(),
                  name: brandNameController.text.trim(),
                  brandid: widget.brandId ?? 0,
                  domain: domainController.text.isNotEmpty ? domainController.text : "",
                  filepath: _selectedImage!));
            }
          }
        }
      },
    );
  }
}
