import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/authentication/bloc/auth_bloc.dart';
import 'package:flowkar/features/widgets/common/flowkar_background/flowkar_background.dart';

class CreateNewPasswordPage extends StatelessWidget {
  const CreateNewPasswordPage({super.key});

  static Widget builder(BuildContext context) {
    return const CreateNewPasswordPage();
  }

  @override
  Widget build(BuildContext context) {
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();
    final TextEditingController newPasswordController = TextEditingController();
    final FocusNode newPasswordFocusNode = FocusNode();
    final TextEditingController confirmPasswordController =
        TextEditingController();
    final FocusNode confirmpasswordFocusNode = FocusNode();

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: BackgroundImage(
        imagePath: Assets.images.pngs.authentication.pngAuthBg2.path,
        child: InkWell(
          focusColor: Colors.transparent,
          onTap: () {
            FocusManager.instance.primaryFocus?.unfocus();
          },
          child: BlocBuilder<AuthBloc, AuthState>(builder: (context, state) {
            return _buildPasswordForm(
              context,
              formKey,
              newPasswordController,
              confirmPasswordController,
              newPasswordFocusNode,
              confirmpasswordFocusNode,
              state,
              isNewPasswordVisible: state.isShowPassword,
              isConfirmPasswordVisible: state.isConfirmShowPassword,
            );
          }),
        ),
      ),
    );
  }

  Widget _buildPasswordForm(
    BuildContext context,
    GlobalKey<FormState> formKey,
    TextEditingController newPasswordController,
    TextEditingController confirmPasswordController,
    FocusNode newPasswordFocusNode,
    FocusNode confirmpasswordFocusNode,
    AuthState state, {
    bool isNewPasswordVisible = false,
    bool isConfirmPasswordVisible = false,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0.w),
      child: Column(
        children: [
          _buildAppBar(),
          buildSizedBoxH(100),
          _buildTitleText(context),
          Text(
            textAlign: TextAlign.center,
            "\nEnter your new password.",
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Theme.of(context).customColors.authsubtitlecolor,
                  fontWeight: FontWeight.w400,
                  fontSize: 16.sp,
                ),
          ),
          buildSizedBoxH(73),
          AbsorbPointer(
            absorbing: state.isResetPasswordLoading,
            child: _buildFormFields(
                context,
                formKey,
                newPasswordController,
                newPasswordFocusNode,
                confirmPasswordController,
                confirmpasswordFocusNode,
                isNewPasswordVisible,
                isConfirmPasswordVisible,
                state),
          ),
          buildSizedBoxH(16),
        ],
      ),
    );
  }

  Widget _buildTitleText(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: 'Reset',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 34.sp,
                  ),
            ),
            TextSpan(
              text: ' Password',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w400,
                    fontSize: 34.sp,
                  ),
            ),
          ],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  // AppBar Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppbar(
      height: 16.0.h,
      leadingImagePath: Assets.images.svg.authentication.icBackArrow.path,
      hasLeadingIcon: true,
    );
  }

  Widget _buildFormFields(
    BuildContext context,
    GlobalKey<FormState> formKey,
    TextEditingController newPasswordController,
    FocusNode newPasswordFocusNode,
    TextEditingController confirmPasswordController,
    FocusNode confirmpasswordFocusNode,
    bool isNewPasswordVisible,
    bool isConfirmPasswordVisible,
    AuthState state,
  ) {
    return Form(
      key: formKey,
      child: Column(
        children: [
          FlowkarTextFormField(
            context: context,
            labelText: Lang.of(context).lbl_new_password,
            prefixIcon: CustomImageView(
              imagePath: AssetConstants.pngPassword,
              height: 24.0.h,
              margin: EdgeInsets.all(16.0),
            ),
            suffixIcon: CustomImageView(
              onTap: () => context.read<AuthBloc>().add(
                    PasswordVisibilityEvent(value: !isNewPasswordVisible),
                  ),
              imagePath: isNewPasswordVisible
                  ? AssetConstants.pngSeen
                  : AssetConstants.pngHide,
              height: 21.0.h,
              margin: EdgeInsets.all(16.0),
            ),
            obscureText: !isNewPasswordVisible,
            controller: newPasswordController,
            focusNode: newPasswordFocusNode,
            validator: AppValidations.validatePassword,
            textInputAction: TextInputAction.next,
            onFieldSubmitted: (_) {
              FocusScope.of(context).requestFocus(confirmpasswordFocusNode);
            },
          ),
          buildSizedBoxH(20),
          FlowkarTextFormField(
            context: context,
            labelText: Lang.of(context).lbl_confirm_password,
            prefixIcon: CustomImageView(
              imagePath: AssetConstants.pngPassword,
              height: 24.0.h,
              margin: EdgeInsets.all(16.0),
            ),
            suffixIcon: CustomImageView(
              onTap: () => context.read<AuthBloc>().add(
                    ConfirmPasswordVisibilityEventS(
                        value: !isConfirmPasswordVisible),
                  ),
              imagePath: isConfirmPasswordVisible
                  ? AssetConstants.pngSeen
                  : AssetConstants.pngHide,
              height: 21.0.h,
              margin: EdgeInsets.all(16.0),
            ),
            obscureText: !isConfirmPasswordVisible,
            controller: confirmPasswordController,
            focusNode: confirmpasswordFocusNode,
            validator: (value) => AppValidations.validateConfirmPassword(
                value, newPasswordController.text),
            textInputAction: TextInputAction.done,
            onFieldSubmitted: (_) {
              FocusScope.of(context).unfocus();
            },
          ),
          buildSizedBoxH(75),
          _buildSaveButton(context, formKey, newPasswordController,
              confirmPasswordController, state),
        ],
      ),
    );
  }

  Widget _buildSaveButton(
    BuildContext context,
    GlobalKey<FormState> formKey,
    TextEditingController newPasswordController,
    TextEditingController confirmPasswordController,
    AuthState state,
  ) {
    return CustomElevatedButton(
      width: 159.w,
      text: 'Reset',
      isLoading: state.isResetPasswordLoading,
      isDisabled: state.isResetPasswordLoading,
      brderRadius: 10.r,
      iconSpacing: 20.w,
      // rightIcon: CustomImageView(
      //   imagePath: Assets.images.svg.authentication.icBackIcon.path,
      // ),
      onPressed: () {
        if (formKey.currentState?.validate() ?? false) {
          FocusScope.of(context).requestFocus(FocusNode());
          context.read<AuthBloc>().add(ResetPasswordEvent(
                newpassword: confirmPasswordController.text,
              ));
        }
      },
    );
  }
}
