import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/authentication/bloc/auth_bloc.dart';
import 'package:flowkar/features/widgets/common/flowkar_background/flowkar_background.dart';
import 'package:url_launcher/url_launcher.dart';

class SignUpPage extends StatefulWidget {
  const SignUpPage({super.key});

  static Widget builder(BuildContext context) {
    return const SignUpPage();
  }

  @override
  State<SignUpPage> createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPage> {
  @override
  Widget build(BuildContext context) {
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();
    final TextEditingController nameController = TextEditingController();
    final TextEditingController userNameController = TextEditingController();
    final TextEditingController emailController = TextEditingController();
    final TextEditingController passwordController = TextEditingController();
    final TextEditingController confirmPasswordController = TextEditingController();

    final FocusNode nameFocusNode = FocusNode();
    final FocusNode userNameFocusNode = FocusNode();
    final FocusNode emailFocusNode = FocusNode();
    final FocusNode passwordFocusNode = FocusNode();
    final FocusNode confirmPasswordFocusNode = FocusNode();

    return Scaffold(
      extendBodyBehindAppBar: true,
      // appBar: _buildSignUpAppBar(),
      resizeToAvoidBottomInset: false,

      body: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          return BackgroundImage(
            imagePath: Assets.images.pngs.authentication.pngAuthBg1.path,
            child: SafeArea(
              child: Stack(
                children: [
                  SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          buildSizedBoxH(40),
                          InkWell(
                            focusColor: Colors.transparent,
                            onTap: () {
                              FocusManager.instance.primaryFocus?.unfocus();
                            },
                            child: _buildSignUpForm(
                              context,
                              formKey,
                              nameController,
                              emailController,
                              passwordController,
                              confirmPasswordController,
                              nameFocusNode,
                              emailFocusNode,
                              passwordFocusNode,
                              confirmPasswordFocusNode,
                              state,
                              userNameController,
                              userNameFocusNode,
                              isChoosePasswordVisible: state.isChooseShowPassword,
                              isConfirmPasswordVisible: state.isConfirmShowPassword,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  backIcon(context),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSignUpForm(
    BuildContext context,
    GlobalKey<FormState> formKey,
    TextEditingController nameController,
    TextEditingController emailController,
    TextEditingController passwordController,
    TextEditingController confirmPasswordController,
    FocusNode nameFocusNode,
    FocusNode emailFocusNode,
    FocusNode passwordFocusNode,
    FocusNode confirmPasswordFocusNode,
    AuthState state,
    TextEditingController userNameController,
    FocusNode userNameFocusNode, {
    bool isChoosePasswordVisible = false,
    bool isConfirmPasswordVisible = false,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // buildSizedBoxH(50),
          _buildTaglineText(context),
          Text(
            textAlign: TextAlign.center,
            "\n${Lang.of(context).lbl_create_an_account_and_start_exploring}",
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Theme.of(context).customColors.authsubtitlecolor,
                  fontWeight: FontWeight.w400,
                  fontSize: 14.sp,
                ),
          ),
          // buildSizedBoxH(40),
          // _buildSignUpHeaderImage(),
          buildSizedBoxH(45),
          AbsorbPointer(
            absorbing: state.isSignUpLoading,
            child: _buildSignUpFormFields(
              context,
              formKey,
              nameController,
              emailController,
              passwordController,
              confirmPasswordController,
              nameFocusNode,
              emailFocusNode,
              passwordFocusNode,
              confirmPasswordFocusNode,
              state,
              userNameController,
              userNameFocusNode,
              isChoosePasswordVisible: isChoosePasswordVisible,
              // isConfirmPasswordVisible: isConfirmPasswordVisible
            ),
          ),
          // buildSizedBoxH(25),
          // _buildTermsAndPrivacy(context),
          buildSizedBoxH(60),
          _buildAlreadyHaveanaccount(context),
          buildSizedBoxH(20),
        ],
      ),
    );
  }

  Widget _buildTaglineText(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Text.rich(
        TextSpan(
          children: [
            TextSpan(
              // text: Lang.of(context).lbl_sign,
              text: 'Sign',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 34.sp,
                  ),
            ),
            TextSpan(
              // text: Lang.of(context).lbl_in,
              text: ' Up',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w400,
                    fontSize: 34.sp,
                  ),
            ),
          ],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildSignUpFormFields(
    BuildContext context,
    GlobalKey<FormState> formKey,
    TextEditingController nameController,
    TextEditingController emailController,
    TextEditingController passwordController,
    TextEditingController confirmPasswordController,
    FocusNode nameFocusNode,
    FocusNode emailFocusNode,
    FocusNode passwordFocusNode,
    FocusNode confirmPasswordFocusNode,
    AuthState state,
    TextEditingController userNameController,
    FocusNode userNameFocusNode, {
    bool isChoosePasswordVisible = false,
    // bool isConfirmPasswordVisible = false,
  }) {
    return Form(
      key: formKey,
      child: Column(
        children: [
          _buildNameInputField(context, nameController, nameFocusNode, emailFocusNode),
          buildSizedBoxH(20),
          _buildUserNameInputField(
            context,
            userNameController,
            userNameFocusNode,
          ),
          buildSizedBoxH(20),
          _buildEmailInputField(context, emailController, emailFocusNode, passwordFocusNode),
          buildSizedBoxH(20),
          _buildPasswordInputField(
            context,
            passwordController,
            passwordFocusNode,
            isChoosePasswordVisible,
          ),
          buildSizedBoxH(20),
          _buildConfirmPasswordInputField(
            context,
            passwordController,
            confirmPasswordController,
            confirmPasswordFocusNode,
          ),
          buildSizedBoxH(10),
          _buildTermsCheckbox(context),
          buildSizedBoxH(60),
          _buildSignUpButton(context, formKey, emailController, passwordController, nameController, state,
              userNameController, confirmPasswordController),
        ],
      ),
    );
  }

  Widget backIcon(BuildContext context) {
    return Row(
      children: [
        Container(
          decoration:
              BoxDecoration(color: Theme.of(context).customColors.white, borderRadius: BorderRadius.circular(10.r)),
          child: InkWell(
            onTap: () {
              PersistentNavBarNavigator.pop(context);
            },
            child: CustomImageView(
              imagePath: Assets.images.pngs.authentication.pngBack.path,
              height: 16.0.h,
              margin: EdgeInsets.symmetric(horizontal: 20.0.w, vertical: 16.0.h),
            ),
          ),
        ),
      ],
    );
  }

  // PreferredSizeWidget _buildSignUpAppBar() {
  //   return CustomAppbar(
  //     height: 20.h,
  //     width: 20.w,
  //     leadingImagePath: Assets.images.pngs.authentication.pngBack.path,
  //     hasLeadingIcon: true,
  //   );
  // }

  Widget _buildUserNameInputField(
    BuildContext context,
    TextEditingController userNameController,
    FocusNode userNameFocusNode,
  ) {
    return FlowkarTextFormField(
      fillColor: Theme.of(context).customColors.white,
      filled: true,
      context: context,
      prefixIcon: CustomImageView(
        imagePath: AssetConstants.pngUser,
        height: 24.0.h,
        margin: EdgeInsets.all(16.0),
      ),
      labelText: Lang.of(context).lbl_username,
      contentPadding: EdgeInsets.all(16.0),
      controller: userNameController,
      focusNode: userNameFocusNode,
      validator: AppValidations.validateUsername,
      textInputAction: TextInputAction.next,
      inputFormatters: [
        FilteringTextInputFormatter.deny(RegExp(r'\s')),
      ],
    );
  }

  Widget _buildNameInputField(
    BuildContext context,
    TextEditingController nameController,
    FocusNode nameFocusNode,
    FocusNode emailFocusNode,
  ) {
    return FlowkarTextFormField(
      filled: true,
      context: context,
      isCapitalized: true,
      labelText: Lang.of(context).lbl_name,
      prefixIcon: CustomImageView(
        imagePath: AssetConstants.pngUser,
        height: 24.0.h,
        margin: EdgeInsets.all(16.0),
      ),
      controller: nameController,
      focusNode: nameFocusNode,
      validator: AppValidations.validateName,
      textInputAction: TextInputAction.next,
      onFieldSubmitted: (_) {
        FocusScope.of(context).requestFocus(emailFocusNode);
      },
    );
  }

  Widget _buildEmailInputField(
    BuildContext context,
    TextEditingController emailController,
    FocusNode emailFocusNode,
    FocusNode passwordFocusNode,
  ) {
    return FlowkarTextFormField(
      filled: true,
      context: context,
      labelText: Lang.of(context).lbl_email,
      prefixIcon: CustomImageView(
        imagePath: AssetConstants.pngEmail,
        width: 23.0.w,
        margin: EdgeInsets.all(16.0),
      ),
      controller: emailController,
      focusNode: emailFocusNode,
      textInputType: TextInputType.emailAddress,
      validator: AppValidations.validateEmail,
      textInputAction: TextInputAction.next,
      onFieldSubmitted: (_) {
        FocusScope.of(context).requestFocus(passwordFocusNode);
      },
    );
  }

  Widget _buildPasswordInputField(
    BuildContext context,
    TextEditingController passwordController,
    FocusNode passwordFocusNode,
    bool isChoosePasswordVisible,
  ) {
    return FlowkarTextFormField(
      filled: true,
      context: context,
      labelText: Lang.of(context).lbl_choose_password,
      textInputAction: TextInputAction.next,
      prefixIcon: CustomImageView(
        imagePath: AssetConstants.pngPassword,
        height: 24.0.h,
        margin: EdgeInsets.all(16.0),
      ),
      suffixIcon: InkWell(
        onTap: () {
          context.read<AuthBloc>().add(
                ChoosePasswordVisibilityEvent(value: !isChoosePasswordVisible),
              );
        },
        child: CustomImageView(
          imagePath: isChoosePasswordVisible ? AssetConstants.pngSeen : AssetConstants.pngHide,
          height: isChoosePasswordVisible ? 19.91.h : 21.0.h,
          width: isChoosePasswordVisible ? 19.91.h : 21.0.h,
          margin: EdgeInsets.all(16.0),
        ),
      ),
      obscureText: !isChoosePasswordVisible,
      controller: passwordController,
      focusNode: passwordFocusNode,
      validator: AppValidations.validatePassword,
    );
  }

  Widget _buildConfirmPasswordInputField(
    BuildContext context,
    TextEditingController passwordController,
    TextEditingController confirmPasswordController,
    FocusNode confirmPasswordFocusNode,
  ) {
    return FlowkarTextFormField(
      filled: true,
      context: context,
      labelText: "Referral Code",
      prefixIcon: CustomImageView(
        imagePath: Assets.images.pngs.authentication.pngReferral.path,
        height: 24.0.h,
        margin: EdgeInsets.all(16.0),
      ),

      controller: confirmPasswordController,
      textInputAction: TextInputAction.done,
      focusNode: confirmPasswordFocusNode,
      // validator: (value) => AppValidations.validateConfirmPassword(value, passwordController.text),
    );
  }

  Widget _buildTermsCheckbox(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        ValueListenableBuilder<bool>(
          valueListenable: isTermsAccepted,
          builder: (context, termsAccepted, widget) {
            return Checkbox(
              value: termsAccepted,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              visualDensity: VisualDensity(horizontal: -4, vertical: -4),
              activeColor: Theme.of(context).primaryColor,
              side: BorderSide(
                color: Theme.of(context).primaryColor.withOpacity(0.5),
                width: 1.0,
              ),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.r)),
              onChanged: (value) {
                isTermsAccepted.value = value ?? false;
              },
            );
          },
        ),
        buildSizedBoxW(8),
        Flexible(
          child: RichText(
            maxLines: 2,
            text: TextSpan(
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 15.sp),
              children: [
                TextSpan(
                  text: 'I agree to all the ',
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.copyWith(fontSize: 14.sp, color: Theme.of(context).primaryColor),
                ),
                TextSpan(
                  text: Lang.of(context).lbl_terms_of_use,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontSize: 14.sp,
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.w600,
                        decoration: TextDecoration.underline,
                      ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      final Uri url = Uri.parse(APIConfig.termsOfUse);
                      launchUrl(url, mode: LaunchMode.platformDefault);
                    },
                ),
                TextSpan(
                  text: ' and ',
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.copyWith(fontSize: 14.sp, color: Theme.of(context).primaryColor),
                ),
                TextSpan(
                  text: Lang.of(context).lbl_privacy_policy,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontSize: 14.sp,
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.w600,
                        decoration: TextDecoration.underline,
                      ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      final Uri url = Uri.parse(APIConfig.policyurl);
                      launchUrl(url, mode: LaunchMode.platformDefault);
                    },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget buildTermsAndPrivacy(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        InkWell(
          onTap: () {
            NavigatorService.pushNamed(AppRoutes.policywebview, arguments: [APIConfig.termsOfUse]);
          },
          child: Text(
            Lang.of(context).lbl_terms_of_use,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 16.5.sp),
          ),
        ),
        buildSizedBoxW(8),
        Text('|'),
        buildSizedBoxW(8),
        InkWell(
          onTap: () {
            NavigatorService.pushNamed(AppRoutes.policywebview, arguments: [APIConfig.policyurl]);
          },
          child: Text(
            Lang.of(context).lbl_privacy_policy,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 16.5.sp),
          ),
        ),
      ],
    );
  }

  Widget _buildSignUpButton(
    BuildContext context,
    GlobalKey<FormState> formKey,
    TextEditingController emailController,
    TextEditingController passwordController,
    TextEditingController nameController,
    AuthState state,
    TextEditingController userNameController,
    TextEditingController confirmPasswordController,
  ) {
    return ValueListenableBuilder<bool>(
      valueListenable: isTermsAccepted,
      builder: (context, termsAccepted, widget) {
        return CustomElevatedButton(
          width: 159.w,
          isDisabled: state.isSignUpLoading || !termsAccepted,
          isLoading: state.isSignUpLoading,
          brderRadius: 10.r,
          iconSpacing: 20.w,
          // rightIcon: CustomImageView(
          //   imagePath: Assets.images.svg.authentication.icBackIcon.path,
          // ),
          onPressed: !termsAccepted
              ? null
              : () async {
                  if (formKey.currentState!.validate()) {
                    context.read<AuthBloc>().add(
                          SignUpAPIEvent(
                            name: nameController.text.trim(),
                            userName: userNameController.text.trim(),
                            email: emailController.text.trim(),
                            confirmPassword: passwordController.text.trim(),
                            refferenceCode:
                                confirmPasswordController.text.isNotEmpty ? confirmPasswordController.text.trim() : "",
                          ),
                        );
                  }
                },
          text: Lang.of(context).lbl_signup,
        );
      },
    );
  }

  Widget _buildAlreadyHaveanaccount(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: Lang.of(context).lbl_already_have_an_account,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontSize: 14.0.sp, fontWeight: FontWeight.w400),
            ),
            TextSpan(
              text: " ${Lang.of(context).lbl_signin}",
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontSize: 14.0.sp, fontWeight: FontWeight.w700),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  NavigatorService.goBack();
                },
            ),
          ],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
