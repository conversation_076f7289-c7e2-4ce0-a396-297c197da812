import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/authentication/bloc/auth_bloc.dart';
import 'package:flowkar/features/widgets/common/flowkar_background/flowkar_background.dart';

class VerifyEmailPage extends StatefulWidget {
  final String email;
  const VerifyEmailPage({super.key, required this.email});

  static Widget builder(BuildContext context) {
    final email = ModalRoute.of(context)?.settings.arguments as String;
    return VerifyEmailPage(email: email);
  }

  @override
  State<VerifyEmailPage> createState() => _VerifyEmailPageState();
}

class _VerifyEmailPageState extends State<VerifyEmailPage> {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController otpcontroller = TextEditingController();

  final FocusNode otpfocusnode = FocusNode();
  bool isOtpComplete = false;

  @override
  void initState() {
    context.read<AuthBloc>().add(StartOtpTimerEvent());
    // Add listener to track OTP completion
    otpcontroller.addListener(_onOtpChanged);
    super.initState();
  }

  @override
  void dispose() {
    otpcontroller.removeListener(_onOtpChanged);
    super.dispose();
  }

  void _onOtpChanged() {
    setState(() {
      isOtpComplete = otpcontroller.text.length == 6;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          return BackgroundImage(
            imagePath: Assets.images.pngs.authentication.pngAuthBg2.path,
            child: SafeArea(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  backIcon(context),
                  InkWell(
                      focusColor: Colors.transparent,
                      onTap: () {
                        FocusManager.instance.primaryFocus?.unfocus();
                      },
                      child: _buildOtpState(context, state)),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget backIcon(BuildContext context) {
    return InkWell(
      onTap: () {
        PersistentNavBarNavigator.pop(context);
      },
      child: CustomImageView(
        imagePath: Assets.images.pngs.authentication.pngBack.path,
        height: 16.0.h,
        margin: EdgeInsets.symmetric(horizontal: 20.0.w, vertical: 16.0.h),
      ),
    );
  }

  Widget _buildOtpState(BuildContext context, AuthState state) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0.w),
      child: Column(
        children: [
          buildSizedBoxH(100),
          _buildTaglineText(context),
          Text(
            textAlign: TextAlign.center,
            "\nPlease enter the verification code, that we \nhave sent on your registered email.",
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Theme.of(context).customColors.authsubtitlecolor,
                  fontWeight: FontWeight.w400,
                  fontSize: 14.sp,
                ),
          ),
          buildSizedBoxH(58),
          _buildVerifyEmailForm(context, state),
          buildSizedBoxH(75),
          _buildVerifyButton(context, state),
          buildSizedBoxH(16),
        ],
      ),
    );
  }

  Widget _buildVerifyEmailForm(BuildContext context, AuthState state) {
    return Form(
      key: formKey,
      child: Column(
        children: [
          _buildOtpTextField(context),
          // buildSizedBoxH(10),
          // state.remainingSeconds != 0 ? _buildRequestNewCode(context, state) : SizedBox.shrink(),
          buildSizedBoxH(35),
          _buildResendCode(context, state),
        ],
      ),
    );
  }

  Widget _buildOtpTextField(BuildContext context) {
    return Center(
      child: Pinput(
        length: 6,
        controller: otpcontroller,
        focusNode: otpfocusnode,
        pinAnimationType: PinAnimationType.slide,
        errorTextStyle: GoogleFonts.ubuntu(fontSize: 18.0.sp),
        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
        hapticFeedbackType: HapticFeedbackType.lightImpact,
        keyboardType: TextInputType.number,
        closeKeyboardWhenCompleted: true,
        showCursor: true,
        preFilledWidget: Text(
          '0',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).primaryColor.withOpacity(0.2),
                fontSize: 17.sp,
                fontWeight: FontWeight.w700,
              ),
        ),
        validator: AppValidations.validateOTP,
        focusedPinTheme: _buildFocusedPinTheme(context),
        submittedPinTheme: _buildFocusedPinTheme(context),
        defaultPinTheme: _buildDefaultPinTheme(context),
        errorPinTheme: _buildErrorPinTheme(context),
        errorText: "OTP must be exactly 6 digits.",
        errorBuilder: (errorText, pin) {
          return Padding(
            padding: EdgeInsets.only(top: 5.h),
            child: Text(
              errorText.toString(),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: Theme.of(context).colorScheme.error,
                  ),
            ),
          );
        },
      ),
    );
  }

  PinTheme _buildDefaultPinTheme(BuildContext context) {
    return PinTheme(
      height: 50.h,
      width: 50.w,
      textStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Theme.of(context).primaryColor.withOpacity(0.2), fontWeight: FontWeight.w700, fontSize: 17.sp),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Theme.of(context).customColors.otpBorderColor),
      ),
    );
  }

  PinTheme _buildFocusedPinTheme(BuildContext context) {
    return PinTheme(
      height: 50.h,
      width: 50.w,
      textStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 17.sp, fontWeight: FontWeight.w700),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Theme.of(context).primaryColor),
      ),
    );
  }

  PinTheme _buildErrorPinTheme(BuildContext context) {
    return PinTheme(
      height: 50.h,
      width: 50.w,
      textStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.error,
            fontSize: 17.sp,
            fontWeight: FontWeight.w700,
          ),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Theme.of(context).colorScheme.error),
      ),
    );
  }

  Widget _buildResendCode(BuildContext context, AuthState state) {
    final isOtpTimerRunning = state.remainingSeconds > 0;

    return InkWell(
      onTap: isOtpTimerRunning
          ? null
          : () async {
              await _handleResendCode();
            },
      child: Text(
        state.remainingSeconds != 0
            ? " 00:${state.remainingSeconds.toString().padLeft(2, '0')}"
            : Lang.of(context).lbl_resend_code,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).customColors.subHeadingcolor,
              fontWeight: FontWeight.w700,
            ),
      ),
    );
  }

  Future<void> _handleResendCode() async {
    if (!mounted) return;
    // Clear the OTP input field
    otpcontroller.clear();
    context.read<AuthBloc>().add(ResendOtpEvent(email: widget.email));
    await Future.delayed(Duration(seconds: 1));
    if (mounted) {
      context.read<AuthBloc>().add(StartOtpTimerEvent());
    }
  }

  Widget _buildVerifyButton(BuildContext context, AuthState state) {
    return CustomElevatedButton(
      width: 159.w,
      text: Lang.of(context).lbl_verify,
      isDisabled: state.isVerifyEmailLoading || !isOtpComplete,
      isLoading: state.isVerifyEmailLoading,
      iconSpacing: 20.w,
      brderRadius: 10.r,
      // rightIcon: CustomImageView(
      //   imagePath: Assets.images.svg.authentication.icBackIcon.path,
      // ),
      onPressed: () {
        if (isOtpComplete && (formKey.currentState?.validate() ?? false)) {
          FocusScope.of(context).requestFocus(FocusNode());
          context.read<AuthBloc>().add(
                VerifyOtpEvent(
                  email: widget.email,
                  userotp: otpcontroller.text.trim(),
                ),
              );
        }
      },
    );
  }

  Widget _buildTaglineText(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: 'Enter ',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w400,
                    fontSize: 34.sp,
                  ),
            ),
            TextSpan(
              text: 'Code',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 34.sp,
                  ),
            ),
          ],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
