import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/widgets/common/flowkar_background/flowkar_background.dart';

class ResetPasswordSuccessScreen extends StatelessWidget {
  const ResetPasswordSuccessScreen({super.key});
  static Widget builder(BuildContext context) {
    return const ResetPasswordSuccessScreen();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BackgroundImage(
        imagePath: Assets.images.pngs.authentication.pngAuthBg2.path,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.0.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomImageView(
                height: 218.h,
                width: 228.w,
                imagePath: Assets.images.pngs.authentication.pngResetPasswordSuccess.path,
              ),
              buildSizedBoxH(64),
              _buildTitleText(context),
              buildSizedBoxH(25),
              Text(
                textAlign: TextAlign.center,
                "Password updated successfully. You can now log in \nwith your new password.\nStay secure!",
                // "You could use this section to narrate a short and crisp description for the Title/Page. This is for when you use section title.",
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w400,
                    fontSize: 14.sp,
                    color: Theme.of(context).customColors.authsubtitlecolor),
              ),
              buildSizedBoxH(36),
              _buildSaveButton(),
              buildSizedBoxH(16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTitleText(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: 'Reset',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 34.sp,
                  ),
            ),
            TextSpan(
              text: ' Password Successfully!',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w400,
                    fontSize: 34.sp,
                  ),
            ),
          ],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildSaveButton() {
    return CustomElevatedButton(
      width: 159.w,
      text: 'Continue',
      brderRadius: 10.r,
      iconSpacing: 20.w,
      // rightIcon: CustomImageView(
      //   imagePath: Assets.images.svg.authentication.icBackIcon.path,
      // ),
      onPressed: () {
        Prefobj.preferences?.delete(Prefkeys.TMPAUTHTOKEN);
        NavigatorService.pushAndRemoveUntil(AppRoutes.signinPage);
      },
    );
  }
}
