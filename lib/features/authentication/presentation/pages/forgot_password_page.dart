import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/authentication/bloc/auth_bloc.dart';
import 'package:flowkar/features/widgets/common/flowkar_background/flowkar_background.dart';

class ForgotPasswordPage extends StatelessWidget {
  const ForgotPasswordPage({super.key});

  static Widget builder(BuildContext context) {
    return const ForgotPasswordPage();
  }

  @override
  Widget build(BuildContext context) {
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();
    final TextEditingController emailController = TextEditingController();
    final FocusNode emailFocusNode = FocusNode();

    return Scaffold(
      resizeToAvoidBottomInset: false,
      // appBar: _buildAppBar(),
      body: BackgroundImage(
        imagePath: Assets.images.pngs.authentication.pngAuthBg2.path,
        child: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              backIcon(context),
              Expanded(
                child: InkWell(
                  focusColor: Colors.transparent,
                  onTap: () {
                    FocusManager.instance.primaryFocus?.unfocus();
                  },
                  child: BlocBuilder<AuthBloc, AuthState>(
                    builder: (context, state) {
                      return _buildInitialState(context, formKey,
                          emailController, emailFocusNode, state);
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

// Initial State UI
  Widget _buildInitialState(
    BuildContext context,
    GlobalKey<FormState> formKey,
    TextEditingController emailController,
    FocusNode emailFocusNode,
    AuthState state,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          buildSizedBoxH(80),
          _buildTitleText(context),
          Text(
            textAlign: TextAlign.center,
            "\nEnter your email to get the\n verification code.",
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Theme.of(context).customColors.authsubtitlecolor,
                  fontWeight: FontWeight.w400,
                  fontSize: 16.sp,
                ),
          ),
          buildSizedBoxH(58),
          AbsorbPointer(
            absorbing: state.isForgotPasswordLoading,
            child: _buildForgotPasswordForm(
                context, formKey, emailController, emailFocusNode, state),
          ),
          Spacer(),
          _buildBackToSignIn(context),
          buildSizedBoxH(20),
        ],
      ),
    );
  }

// AppBar Widget
  // PreferredSizeWidget _buildAppBar() {
  //   return CustomAppbar(
  //     // height: 20.h,
  //     // width: 20.w,
  //     leadingImagePath: Assets.images.svg.authentication.icBackArrow.path,
  //     hasLeadingIcon: true,
  //   );
  // }

  Widget backIcon(BuildContext context) {
    return InkWell(
      onTap: () {
        PersistentNavBarNavigator.pop(context);
      },
      child: CustomImageView(
        imagePath: Assets.images.pngs.authentication.pngBack.path,
        height: 16.0.h,
        margin: EdgeInsets.symmetric(horizontal: 20.0.w, vertical: 16.0.h),
      ),
    );
  }

// Title Text Widget

  Widget _buildTitleText(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: 'Forgot',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 34.sp,
                  ),
            ),
            TextSpan(
              text: ' Password?',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w400,
                    fontSize: 34.sp,
                  ),
            ),
          ],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

// Forgot Password Form Widget
  Widget _buildForgotPasswordForm(
    BuildContext context,
    GlobalKey<FormState> formKey,
    TextEditingController emailController,
    FocusNode emailFocusNode,
    AuthState state,
  ) {
    return Form(
      key: formKey,
      child: Column(
        children: [
          FlowkarTextFormField(
            textInputType: TextInputType.emailAddress,
            context: context,
            labelText: Lang.of(context).lbl_email,
            prefixIcon: CustomImageView(
              imagePath: AssetConstants.pngEmail,
              width: 23.0.w,
              margin: EdgeInsets.all(16.0),
            ),
            controller: emailController,
            focusNode: emailFocusNode,
            validator: AppValidations.validateEmail,
            textInputAction: TextInputAction.done,
          ),
          buildSizedBoxH(75),
          _buildSendButton(context, formKey, emailController, state),
          buildSizedBoxH(16),
        ],
      ),
    );
  }

  // Send Button Widget
  Widget _buildSendButton(
    BuildContext context,
    GlobalKey<FormState> formKey,
    TextEditingController emailController,
    AuthState state,
  ) {
    return CustomElevatedButton(
      width: 159.w,
      text: 'Continue',
      isDisabled: state.isForgotPasswordLoading,
      isLoading: state.isForgotPasswordLoading,
      brderRadius: 10.r,
      iconSpacing: 20.w,
      // rightIcon: CustomImageView(
      //   imagePath: Assets.images.svg.authentication.icBackIcon.path,
      // ),
      onPressed: () {
        if (formKey.currentState?.validate() ?? false) {
          FocusScope.of(context).requestFocus(FocusNode());
          context.read<AuthBloc>().add(ForgotpasswordEvent(
              email: emailController.text.trim(), context: context));
        }
      },
    );
  }

  Widget _buildBackToSignIn(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: "Back to",
              style: Theme.of(context)
                  .textTheme
                  .bodyLarge
                  ?.copyWith(fontSize: 14.0.sp, fontWeight: FontWeight.w400),
            ),
            TextSpan(
              text: " Sign In",
              style: Theme.of(context)
                  .textTheme
                  .bodyLarge
                  ?.copyWith(fontSize: 14.0.sp, fontWeight: FontWeight.w700),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  NavigatorService.goBack();
                },
            ),
          ],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
