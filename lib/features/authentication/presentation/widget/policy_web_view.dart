import 'package:flowkar/core/utils/exports.dart';
import 'package:webview_flutter/webview_flutter.dart';

class PolicyWebView extends StatefulWidget {
  final String initialUrl;

  static Widget builder(BuildContext context) {
    final List args = ModalRoute.of(context)?.settings.arguments as List;
    return PolicyWebView(
      initialUrl: args[0],
    );
  }

  const PolicyWebView({super.key, required this.initialUrl});

  @override
  State<PolicyWebView> createState() => _PolicyWebViewState();
}

class _PolicyWebViewState extends State<PolicyWebView> {
  late final WebViewController controller;

  @override
  void initState() {
    controller = WebViewController()
      ..clearCache()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(Uri.parse(widget.initialUrl));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildSignUpAppBar(),
      body: WebViewWidget(
        controller: controller,
      ),
    );
  }

  PreferredSizeWidget _buildSignUpAppBar() {
    return CustomAppbar(
      hasLeadingIcon: true,
      onLeadingTap: () {
        Navigator.pop(context);
      },
    );
  }
}
