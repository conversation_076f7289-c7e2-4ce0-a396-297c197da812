<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <compositeConfiguration>
          <compositeBuild compositeDefinitionSource="SCRIPT">
            <builds>
              <build path="$PROJECT_DIR$/../../../../flutter_sdk/flutter/packages/flutter_tools/gradle" name="gradle">
                <projects>
                  <project path="$PROJECT_DIR$/../../../../flutter_sdk/flutter/packages/flutter_tools/gradle" />
                </projects>
              </build>
            </builds>
          </compositeBuild>
        </compositeConfiguration>
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleJvm" value="#GRADLE_LOCAL_JAVA_HOME" />
        <option name="modules">
          <set>
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/app_links-6.4.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/audio_session-0.1.25/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/audio_waveforms-1.3.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/camera_android-0.10.10+1/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/device_info_plus-11.3.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/easy_video_editor-0.0.9/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/emoji_picker_flutter-4.3.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_core-3.12.1/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.4/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_local_notifications-19.0.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.27/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_sound-9.28.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_webrtc-0.12.12+hotfix.1/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+22/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/in_app_update-4.2.3/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/insta_assets_crop-0.1.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/just_audio-0.9.46/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/just_waveform-0.0.7/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/mobile_scanner-6.0.7/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_android-2.2.16/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/permission_handler-6.1.3/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/photo_manager-3.6.4/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/quick_actions_android-1.0.21/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sensors_plus-6.1.1/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/share_plus-10.1.4/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.8/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.15/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vibration-3.1.3/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/video_player_android-2.8.2/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/video_thumbnail-0.5.3/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/webview_flutter_android-4.3.3/android" />
            <option value="$PROJECT_DIR$/../../../../flutter_sdk/flutter/packages/flutter_tools/gradle" />
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/app" />
          </set>
        </option>
        <option name="resolveExternalAnnotations" value="false" />
      </GradleProjectSettings>
    </option>
  </component>
</project>